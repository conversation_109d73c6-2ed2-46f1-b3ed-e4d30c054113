<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <classPathEntry
            location="C:\Users\<USER>\.m2\repository\com\oracle\ojdbc6\11.1.0.7.0\ojdbc6-11.1.0.7.0.jar"/>
    <context id="context1">
    <property name="javaFileEncoding" value="UTF-8" />
		<commentGenerator type="com.epaylinks.efps.TxsCommentGenerator" >
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
    <jdbcConnection connectionURL="*******************************************"
			driverClass="oracle.jdbc.driver.OracleDriver" password="efps#123"
			userId="efps">
			<!-- 针对oracle数据库 -->
			<property name="remarksReporting" value="true"></property>
	</jdbcConnection>
    <javaModelGenerator targetPackage="com.epaylinks.efps.txs.transaction.model" targetProject="E:/eplinks_svn/V1.0/030-Coding/010-Source/011-txs/devTrunk/src/main/java/" />
    <sqlMapGenerator targetPackage="com.epaylinks.efps.txs.transaction.dao" targetProject="E:/eplinks_svn/V1.0/030-Coding/010-Source/011-txs/devTrunk/src/main/resources" />
    <javaClientGenerator targetPackage="com.epaylinks.efps.txs.transaction.dao" targetProject="E:/eplinks_svn/V1.0/030-Coding/010-Source/011-txs/devTrunk/src/main/java/" type="XMLMAPPER" />
	
	<table tableName="TXS_SPLIT_RELATION" domainObjectName="TxsSplitRelation"
		enableCountByExample="false" enableUpdateByExample="false"
		enableDeleteByExample="false" enableSelectByExample="false"
		selectByExampleQueryId="false">
    </table>
        <!-- <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection connectionURL="*******************************************"
                        driverClass="oracle.jdbc.driver.OracleDriver" password="efps#123" userId="efps"/>

        <javaModelGenerator targetPackage="com.epaylinks.efps.txs.transaction.model"
                            targetProject="E:\test"/>

        <sqlMapGenerator targetPackage="com.epaylinks.efps.txs.transaction.dao"
                         targetProject="E:\test"/>
        <javaClientGenerator targetPackage="com.epaylinks.efps.txs.transaction.dao"
                             targetProject="E:\test" type="XMLMAPPER"/>

        <table tableName="TXS_BILL" domainObjectName="TxsBill" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table> -->
    </context>
</generatorConfiguration>