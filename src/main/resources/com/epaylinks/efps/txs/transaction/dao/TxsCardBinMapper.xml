<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsCardBinMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.common.business.txs.TxsCardBin">
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="RECORD_ID" jdbcType="DECIMAL" property="recordId" />
    <result column="CARD_NO_RANGE" jdbcType="VARCHAR" property="cardNoRange" />
    <result column="CARD_NO_RANGE_LEN" jdbcType="DECIMAL" property="cardNoRangeLen" />
    <result column="ISSUE_BANK_NO" jdbcType="VARCHAR" property="issueBankNo" />
    <result column="BANK_ICON" jdbcType="VARCHAR" property="bankIcon" />
    <result column="ISSUE_BANK_NAME" jdbcType="VARCHAR" property="issueBankName" />
    <result column="CARD_NAME" jdbcType="VARCHAR" property="cardName" />
    <result column="APPLY_RANGE" jdbcType="DECIMAL" property="applyRange" />
    <result column="CARD_NO_LEN" jdbcType="DECIMAL" property="cardNoLen" />
    <result column="CARD_TYPE" jdbcType="VARCHAR" property="cardType" />
    <result column="ISSUE_BANK_ACCOUNT" jdbcType="VARCHAR" property="issueBankAccount" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, RECORD_ID, CARD_NO_RANGE, CARD_NO_RANGE_LEN, ISSUE_BANK_NO, BANK_ICON,
    ISSUE_BANK_NAME, CARD_NAME, APPLY_RANGE, CARD_NO_LEN, CARD_TYPE, ISSUE_BANK_ACCOUNT
  </sql>
  <insert id="insert" parameterType="com.epaylinks.efps.common.business.txs.TxsCardBin">
    insert into TXS_CARD_BIN (ID, RECORD_ID, CARD_NO_RANGE, 
      CARD_NO_RANGE_LEN, ISSUE_BANK_NO, BANK_ICON, 
      ISSUE_BANK_NAME, CARD_NAME, APPLY_RANGE, 
      CARD_NO_LEN, CARD_TYPE, ISSUE_BANK_ACCOUNT
      )
    values (#{id,jdbcType=DECIMAL}, #{recordId,jdbcType=DECIMAL}, #{cardNoRange,jdbcType=VARCHAR}, 
      #{cardNoRangeLen,jdbcType=DECIMAL}, #{issueBankNo,jdbcType=VARCHAR}, #{bankIcon,jdbcType=VARCHAR}, 
      #{issueBankName,jdbcType=VARCHAR}, #{cardName,jdbcType=VARCHAR}, #{applyRange,jdbcType=DECIMAL}, 
      #{cardNoLen,jdbcType=DECIMAL}, #{cardType,jdbcType=VARCHAR}, #{issueBankAccount,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.common.business.txs.TxsCardBin">
    insert into TXS_CARD_BIN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="recordId != null">
        RECORD_ID,
      </if>
      <if test="cardNoRange != null">
        CARD_NO_RANGE,
      </if>
      <if test="cardNoRangeLen != null">
        CARD_NO_RANGE_LEN,
      </if>
      <if test="issueBankNo != null">
        ISSUE_BANK_NO,
      </if>
      <if test="bankIcon != null">
        BANK_ICON,
      </if>
      <if test="issueBankName != null">
        ISSUE_BANK_NAME,
      </if>
      <if test="cardName != null">
        CARD_NAME,
      </if>
      <if test="applyRange != null">
        APPLY_RANGE,
      </if>
      <if test="cardNoLen != null">
        CARD_NO_LEN,
      </if>
      <if test="cardType != null">
        CARD_TYPE,
      </if>
      <if test="issueBankAccount != null">
        ISSUE_BANK_ACCOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=DECIMAL},
      </if>
      <if test="cardNoRange != null">
        #{cardNoRange,jdbcType=VARCHAR},
      </if>
      <if test="cardNoRangeLen != null">
        #{cardNoRangeLen,jdbcType=DECIMAL},
      </if>
      <if test="issueBankNo != null">
        #{issueBankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankIcon != null">
        #{bankIcon,jdbcType=VARCHAR},
      </if>
      <if test="issueBankName != null">
        #{issueBankName,jdbcType=VARCHAR},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="applyRange != null">
        #{applyRange,jdbcType=DECIMAL},
      </if>
      <if test="cardNoLen != null">
        #{cardNoLen,jdbcType=DECIMAL},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="issueBankAccount != null">
        #{issueBankAccount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectByCardNoRange"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_CARD_BIN
    where CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR}
    and CARD_NO_LEN = #{cardNoLen,jdbcType=DECIMAL}
  </select>
  <select id="selectByBankIconList"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_CARD_BIN
    where BANK_ICON in 
    <foreach item="item" index="index" collection="bankIconList" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="selectByBankIcon"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_CARD_BIN
    where BANK_ICON = #{bankIcon,jdbcType=VARCHAR}
  </select>
  <select id="selectAll"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_CARD_BIN
  </select>
</mapper>