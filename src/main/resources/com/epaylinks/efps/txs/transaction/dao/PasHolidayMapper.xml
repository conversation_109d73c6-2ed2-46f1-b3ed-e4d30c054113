<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.PasHolidayMapper">

    <resultMap id="BaseResultMap1" type="com.epaylinks.efps.txs.transaction.model.PasHoliday">
        <result column="DATE_STR" jdbcType="VARCHAR" property="dateStr"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
    </resultMap>



    <select id="selectByDate" resultMap="BaseResultMap1" parameterType="String">
        SELECT
        DATE_STR,TYPE
        FROM pas_holiday
        WHERE DATE_STR = #{date,jdbcType = VARCHAR}
    </select>
</mapper>