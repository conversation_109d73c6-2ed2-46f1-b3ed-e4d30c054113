<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsSplitRecordMapper">
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsSplitRecord">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo"/>
        <result column="SOURCE_CUSTOMER_CODE" jdbcType="VARCHAR" property="sourceCustomerCode"/>
        <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode"/>
        <result column="STATE" jdbcType="CHAR" property="state"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="PROCEDUREFEE" jdbcType="DECIMAL" property="procedurefee"/>
        <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId"/>
        <result column="REFUND_FEE" jdbcType="DECIMAL" property="refundFee"/>
        <result column="REFUNDING_FEE" jdbcType="DECIMAL" property="refundingFee"/>
        <result column="ORIG_AMOUNT" jdbcType="DECIMAL" property="origAmount"/>
        <result column="ORIG_REFUND_FEE" jdbcType="DECIMAL" property="origRefundFee"/>
        <result column="ORIG_REFUNDING_FEE" jdbcType="DECIMAL" property="origRefundingFee"/>
        <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId"/>
        <result column="CUSTOMERNAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="PAYTRANSACTION_NO" jdbcType="VARCHAR" property="payTransactionNo"/>
        <result column="PAY_AMOUNT" jdbcType="DECIMAL" property="payAmount"/>
        <result column="PAY_PROCEDUREFEE" jdbcType="DECIMAL" property="payProcedureFee"/>
        <result column="SOURCE_CUSTOMERNAME" jdbcType="VARCHAR" property="sourceCustomerName"/>
        <result column="split_ratio" jdbcType="DOUBLE" property="splitRatio"/>
        <result column="SPLIT_PROCEDURE_FEE" jdbcType="DECIMAL" property="splitProcedureFee"/>
        <result column="SPLIT_MAIN" jdbcType="VARCHAR" property="splitMain"/>
        <result column="SPLIT_MAIN_NAME" jdbcType="VARCHAR" property="splitMainName"/>
        <result column="SPLIT_MODEL" jdbcType="VARCHAR" property="splitModel"/>
        <result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR"/>
        <result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL"/>
        <result column="cash_amount" property="cashAmount" jdbcType="DECIMAL"/>
        <result column="coupon_amount" property="couponAmount" jdbcType="DECIMAL"/>
        <result column="REVOKE_TRANSACTION_NO" jdbcType="VARCHAR" property="revokeTransactionNo"/>
        <result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType"/>
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
        <result column="PAY_CASH_AMOUNT" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="SPLIT_ATTR" property="splitAttr" jdbcType="VARCHAR" />
        <result column="IS_FIRST" jdbcType="VARCHAR" property="isFirst" />
        <result column="OUT_SPLIT_TRADE_NO" jdbcType="VARCHAR" property="outSplitTradeNo" />
        <result column="SETTLE_CYCLE" jdbcType="DECIMAL" property="settleCycle" />
        <result column="SETTLE_STATE" jdbcType="VARCHAR" property="settleState" />
        <result column="SETTLE_CYCLE_TYPE" jdbcType="VARCHAR" property="settleCycleType" />
        <result column="CONTROLLING" jdbcType="VARCHAR" property="controlling" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, TRANSACTION_NO, SOURCE_CUSTOMER_CODE, CUSTOMER_CODE, STATE, AMOUNT, PROCEDUREFEE,
        ERROR_CODE, CREATE_TIME, UPDATE_TIME,BUSINESS_INST_ID, REFUND_FEE, REFUNDING_FEE,
        ORIG_AMOUNT, ORIG_REFUND_FEE, ORIG_REFUNDING_FEE,CUSTOMERNAME, PAYTRANSACTION_NO,
        PAY_AMOUNT, PAY_PROCEDUREFEE, SOURCE_CUSTOMERNAME,SPLIT_RATIO,SPLIT_PROCEDURE_FEE,
        SPLIT_MAIN, SPLIT_MAIN_NAME, SPLIT_MODEL,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,
        CASH_AMOUNT,COUPON_AMOUNT,REVOKE_TRANSACTION_NO, TRANSACTION_TYPE,PAY_CASH_AMOUNT,OUT_TRADE_NO,SPLIT_ATTR,
        IS_FIRST, OUT_SPLIT_TRADE_NO, SETTLE_CYCLE, SETTLE_STATE, SETTLE_CYCLE_TYPE, CONTROLLING
    </sql>

    <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitRecord">
        insert into TXS_SPLIT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="transactionNo != null">
                TRANSACTION_NO,
            </if>
            <if test="sourceCustomerCode != null">
                SOURCE_CUSTOMER_CODE,
            </if>
            <if test="customerCode != null">
                CUSTOMER_CODE,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="procedurefee != null">
                PROCEDUREFEE,
            </if>
            <if test="errorCode != null">
                ERROR_CODE,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="businessInstId != null">
                BUSINESS_INST_ID,
            </if>
            <if test="refundFee != null">
                REFUND_FEE,
            </if>
            <if test="refundingFee != null">
                REFUNDING_FEE,
            </if>
            <if test="origAmount != null">
                ORIG_AMOUNT,
            </if>
            <if test="origRefundFee != null">
                ORIG_REFUND_FEE,
            </if>
            <if test="origRefundingFee != null">
                ORIG_REFUNDING_FEE,
            </if>
            <if test="customerName != null">
                CUSTOMERNAME,
            </if>
            <if test="payTransactionNo != null">
                PAYTRANSACTION_NO,
            </if>
            <if test="payAmount != null">
                PAY_AMOUNT,
            </if>
            <if test="payProcedureFee != null">
                PAY_PROCEDUREFEE,
            </if>
            <if test="sourceCustomerName != null">
                SOURCE_CUSTOMERNAME,
            </if>
            <if test="splitRatio != null">
                split_ratio,
            </if>
            <if test="splitProcedureFee != null">
                SPLIT_PROCEDURE_FEE,
            </if>
            <if test="splitMain != null">
                SPLIT_MAIN,
            </if>
            <if test="splitMainName != null">
                SPLIT_MAIN_NAME,
            </if>
            <if test="splitModel != null">
                SPLIT_MODEL,
            </if>
            <if test="businessMan != null">
                BUSINESS_MAN,
            </if>
            <if test="businessManId != null">
                BUSINESS_MAN_ID,
            </if>
            <if test="companyName != null">
                COMPANY_NAME,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="cashAmount != null">
                cash_amount,
            </if>
            <if test="couponAmount != null">
                coupon_amount,
            </if>
            <if test="revokeTransactionNo != null">
                REVOKE_TRANSACTION_NO,
            </if>
            <if test="transactionType != null">
                TRANSACTION_TYPE,
            </if>
            <if test="splitAttr != null">
                SPLIT_ATTR,
            </if>
            <if test="outTradeNo != null">
                OUT_TRADE_NO,
            </if>
            <if test="payCashAmount != null">
                PAY_CASH_AMOUNT,
            </if>
            <if test="isFirst != null" >
                IS_FIRST,
            </if>
            <if test="outSplitTradeNo != null" >
                OUT_SPLIT_TRADE_NO,
            </if>
            <if test="settleCycle != null" >
                SETTLE_CYCLE,
            </if>
            <if test="settleState != null" >
                SETTLE_STATE,
            </if>
            <if test="settleCycleType != null" >
                SETTLE_CYCLE_TYPE,
            </if>
            <if test="controlling != null" >
                CONTROLLING,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=DECIMAL},
            </if>
            <if test="transactionNo != null">
                #{transactionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceCustomerCode != null">
                #{sourceCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null">
                #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=CHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="procedurefee != null">
                #{procedurefee,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessInstId != null">
                #{businessInstId,jdbcType=VARCHAR},
            </if>
            <if test="refundFee != null">
                #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="refundingFee != null">
                #{refundingFee,jdbcType=DECIMAL},
            </if>
            <if test="origAmount != null">
                #{origAmount,jdbcType=DECIMAL},
            </if>
            <if test="origRefundFee != null">
                #{origRefundFee,jdbcType=DECIMAL},
            </if>
            <if test="origRefundingFee != null">
                #{origRefundingFee,jdbcType=DECIMAL},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="payTransactionNo != null">
                #{payTransactionNo,jdbcType=VARCHAR},
            </if>
            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payProcedureFee != null">
                #{payProcedureFee,jdbcType=DECIMAL},
            </if>
            <if test="sourceCustomerName != null">
                #{sourceCustomerName,jdbcType=VARCHAR},
            </if>
            <if test="splitRatio != null">
                #{splitRatio,jdbcType=DOUBLE},
            </if>
            <if test="splitProcedureFee != null">
                #{splitProcedureFee,jdbcType=DECIMAL},
            </if>
            <if test="splitMain != null">
                #{splitMain,jdbcType=VARCHAR},
            </if>
            <if test="splitMainName != null">
                #{splitMainName,jdbcType=VARCHAR},
            </if>
            <if test="splitModel != null">
                #{splitModel,jdbcType=VARCHAR},
            </if>
            <if test="businessMan != null">
                #{businessMan,jdbcType=VARCHAR},
            </if>
            <if test="businessManId != null">
                #{businessManId,jdbcType=DECIMAL},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=DECIMAL},
            </if>
            <if test="cashAmount != null">
                #{cashAmount,jdbcType=DECIMAL},
            </if>
            <if test="couponAmount != null">
                #{couponAmount,jdbcType=DECIMAL},
            </if>
            <if test="revokeTransactionNo != null">
                #{revokeTransactionNo,jdbcType=VARCHAR},
            </if>
            <if test="transactionType != null">
                #{transactionType,jdbcType=VARCHAR},
            </if>
            <if test="splitAttr != null">
                #{splitAttr,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null">
                #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="isFirst != null" >
                #{isFirst,jdbcType=VARCHAR},
            </if>
            <if test="outSplitTradeNo != null" >
                #{outSplitTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="settleCycle != null" >
                #{settleCycle,jdbcType=DECIMAL},
            </if>
            <if test="settleState != null" >
                #{settleState,jdbcType=VARCHAR},
            </if>
            <if test="settleCycleType != null" >
                #{settleCycleType,jdbcType=VARCHAR},
            </if>
            <if test="controlling != null" >
                #{controlling,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitRecord">
        update TXS_SPLIT_RECORD
        <set>
            <if test="transactionNo != null">
                TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceCustomerCode != null">
                SOURCE_CUSTOMER_CODE = #{sourceCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null">
                CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=CHAR},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="procedurefee != null">
                PROCEDUREFEE = #{procedurefee,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessInstId != null">
                BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
            </if>
            <if test="refundFee != null">
                REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="refundingFee != null">
                REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
            </if>
            <if test="origAmount != null">
                ORIG_AMOUNT = #{origAmount,jdbcType=DECIMAL},
            </if>
            <if test="origRefundFee != null">
                ORIG_REFUND_FEE = #{origRefundFee,jdbcType=DECIMAL},
            </if>
            <if test="origRefundingFee != null">
                ORIG_REFUNDING_FEE = #{origRefundingFee,jdbcType=DECIMAL},
            </if>
            <if test="customerName != null">
                CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="payTransactionNo != null">
                PAYTRANSACTION_NO = #{payTransactionNo,jdbcType=VARCHAR},
            </if>
            <if test="payAmount != null">
                PAY_AMOUNT = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payProcedureFee != null">
                PAY_PROCEDUREFEE = #{payProcedureFee,jdbcType=DECIMAL},
            </if>
            <if test="sourceCustomerName != null">
                SOURCE_CUSTOMERNAME = #{sourceCustomerName,jdbcType=VARCHAR},
            </if>
            <if test="splitRatio != null">
                split_ratio = #{splitRatio,jdbcType=DOUBLE},
            </if>
            <if test="splitProcedureFee != null">
                SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
            </if>
            <if test="splitMain != null">
                SPLIT_MAIN = #{splitMain,jdbcType=VARCHAR},
            </if>
            <if test="splitMainName != null">
                SPLIT_MAIN_NAME = #{splitMainName,jdbcType=VARCHAR},
            </if>
            <if test="splitModel != null">
                SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
            </if>
            <if test="businessMan != null">
                BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
            </if>
            <if test="businessManId != null">
                BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
            </if>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=DECIMAL},
            </if>
            <if test="cashAmount != null">
                cash_amount = #{cashAmount,jdbcType=DECIMAL},
            </if>
            <if test="couponAmount != null">
                coupon_amount = #{couponAmount,jdbcType=DECIMAL},
            </if>
            <if test="revokeTransactionNo != null">
                REVOKE_TRANSACTION_NO = #{revokeTransactionNo,jdbcType=VARCHAR},
            </if>
            <if test="transactionType != null">
                TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
            </if>
            <if test="splitAttr != null">
                SPLIT_ATTR = #{splitAttr,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null">
                OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="payCashAmount != null">
                PAY_CASH_AMOUNT = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="isFirst != null" >
                IS_FIRST = #{isFirst,jdbcType=VARCHAR},
            </if>
            <if test="outSplitTradeNo != null" >
                OUT_SPLIT_TRADE_NO = #{outSplitTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="settleCycle != null" >
                SETTLE_CYCLE = #{settleCycle,jdbcType=DECIMAL},
            </if>
            <if test="settleState != null" >
                SETTLE_STATE = #{settleState,jdbcType=VARCHAR},
            </if>
            <if test="settleCycleType != null" >
                SETTLE_CYCLE_TYPE = #{settleCycleType,jdbcType=VARCHAR},
            </if>
            <if test="controlling != null" >
                CONTROLLING = #{controlling,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>
    <select id="selectByTransactionNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_SPLIT_RECORD
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByTransactionAndCustomerCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_SPLIT_RECORD
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} and
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByBusinessInstId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_SPLIT_RECORD where
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
    </select>

    <select id="fenBuFenZhangHasRecords" resultType="java.lang.Boolean">
        select
        count(*)
        from TXS_SPLIT_RECORD where
        transaction_no = #{transactionNo,jdbcType=VARCHAR}
        and exists (select 1 from txs_split_order where transaction_no = #{transactionNo,jdbcType=VARCHAR} and split_type = '1')
        and rownum = 1
    </select>

    <update id="updateStateNotSuccess" >
        update TXS_SPLIT_RECORD
          set
                STATE = #{state,jdbcType=CHAR},
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=DECIMAL}
          and state != '3'
    </update>

    <update id="updatePaytransactionNoByTransactionNoWhenNull" >
        update TXS_SPLIT_RECORD
          set
                paytransaction_no = #{paytransactionNo,jdbcType=VARCHAR}
        where transaction_no = #{transactionNo,jdbcType=VARCHAR}
          and paytransaction_no is null
    </update>

    <update id="updateSetControlling" >
        update TXS_SPLIT_RECORD
          set
                CONTROLLING = #{controlling,jdbcType=VARCHAR}
        where transaction_no = #{transactionNo,jdbcType=VARCHAR}
    </update>

</mapper>