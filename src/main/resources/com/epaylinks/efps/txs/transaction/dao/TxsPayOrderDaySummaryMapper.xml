<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsPayOrderDaySummaryMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsPayOrderDaySummary">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CUSTOMERCODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="PAYMETHOD" jdbcType="VARCHAR" property="payMethod" />
    <result column="PAYSTATE" jdbcType="VARCHAR" property="payState" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="COUNT" jdbcType="DECIMAL" property="count" />
    <result column="CREATEDATETIME" jdbcType="TIMESTAMP" property="createDateTime" />
    <result column="CYCLETYPE" jdbcType="VARCHAR" property="cycleType" />
    <result column="CYCLESTARTTIME" jdbcType="TIMESTAMP" property="cycleStartTime" />
    <result column="CYCLEENDTIME" jdbcType="TIMESTAMP" property="cycleEndTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CUSTOMERCODE, PAYMETHOD, PAYSTATE, AMOUNT, COUNT, CREATEDATETIME, CYCLETYPE, 
    CYCLESTARTTIME, CYCLEENDTIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_PAY_ORDER_DAY_SUMMARY
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    delete from TXS_PAY_ORDER_DAY_SUMMARY
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayOrderDaySummary">
    insert into TXS_PAY_ORDER_DAY_SUMMARY (ID, CUSTOMERCODE, PAYMETHOD, 
      PAYSTATE, AMOUNT, COUNT, 
      CREATEDATETIME, CYCLETYPE, CYCLESTARTTIME, 
      CYCLEENDTIME)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, 
      #{payState,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, #{count,jdbcType=DECIMAL}, 
      #{createDateTime,jdbcType=TIMESTAMP}, #{cycleType,jdbcType=VARCHAR}, #{cycleStartTime,jdbcType=TIMESTAMP}, 
      #{cycleEndTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayOrderDaySummary">
    insert into TXS_PAY_ORDER_DAY_SUMMARY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="customerCode != null">
        CUSTOMERCODE,
      </if>
      <if test="payMethod != null">
        PAYMETHOD,
      </if>
      <if test="payState != null">
        PAYSTATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="count != null">
        COUNT,
      </if>
      <if test="createDateTime != null">
        CREATEDATETIME,
      </if>
      <if test="cycleType != null">
        CYCLETYPE,
      </if>
      <if test="cycleStartTime != null">
        CYCLESTARTTIME,
      </if>
      <if test="cycleEndTime != null">
        CYCLEENDTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="count != null">
        #{count,jdbcType=DECIMAL},
      </if>
      <if test="createDateTime != null">
        #{createDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleType != null">
        #{cycleType,jdbcType=VARCHAR},
      </if>
      <if test="cycleStartTime != null">
        #{cycleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleEndTime != null">
        #{cycleEndTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayOrderDaySummary">
    update TXS_PAY_ORDER_DAY_SUMMARY
    <set>
      <if test="customerCode != null">
        CUSTOMERCODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        PAYMETHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        PAYSTATE = #{payState,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="count != null">
        COUNT = #{count,jdbcType=DECIMAL},
      </if>
      <if test="createDateTime != null">
        CREATEDATETIME = #{createDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleType != null">
        CYCLETYPE = #{cycleType,jdbcType=VARCHAR},
      </if>
      <if test="cycleStartTime != null">
        CYCLESTARTTIME = #{cycleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleEndTime != null">
        CYCLEENDTIME = #{cycleEndTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayOrderDaySummary">
    update TXS_PAY_ORDER_DAY_SUMMARY
    set CUSTOMERCODE = #{customerCode,jdbcType=VARCHAR},
      PAYMETHOD = #{payMethod,jdbcType=VARCHAR},
      PAYSTATE = #{payState,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      COUNT = #{count,jdbcType=DECIMAL},
      CREATEDATETIME = #{createDateTime,jdbcType=TIMESTAMP},
      CYCLETYPE = #{cycleType,jdbcType=VARCHAR},
      CYCLESTARTTIME = #{cycleStartTime,jdbcType=TIMESTAMP},
      CYCLEENDTIME = #{cycleEndTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <resultMap id="allInfo" type="com.epaylinks.efps.txs.transaction.domain.orderSummary.OrderDaySummary">
      <result column="sum(AMOUNT)" property="amount" jdbcType="DECIMAL"/>
      <result column="sum(COUNT)" property="count" jdbcType="DECIMAL"/>
      <result column="CYCLESTARTTIME" property="date" jdbcType="TIMESTAMP"/>
      <result column="PAYMETHOD" jdbcType="VARCHAR" property="payMethod" />
      <result column="PAYSTATE" jdbcType="VARCHAR" property="payState" />
  </resultMap>
  <select id="selectOrderSummaryByDate" resultMap="allInfo"  parameterType="java.util.Map">
  	select 
	sum(AMOUNT), sum(COUNT), CYCLESTARTTIME
	<if test="payState != null and payState !=''">
		,PAYSTATE
	</if>
	<if test="payMethod != null and payMethod !=''">
		,PAYMETHOD
	</if>
	from TXS_PAY_ORDER_DAY_SUMMARY 
	<where>
		<if test="customerCode != null and customerCode !=''">
			AND CUSTOMERCODE = #{customerCode,jdbcType=VARCHAR} 
		</if>
		<if test="beginDate!=null ">
   		 	AND CREATEDATETIME <![CDATA[ >= ]]>  #{beginDate,jdbcType=TIMESTAMP} 
		</if>
		<if test="endDate!=null ">
   			AND CREATEDATETIME <![CDATA[ <= ]]>  #{endDate,jdbcType=TIMESTAMP} 
		</if> 			 
	</where>
	group by CUSTOMERCODE, CYCLESTARTTIME
	<if test="payState != null and payState !=''">
		,PAYSTATE
	</if>
	<if test="payMethod != null and payMethod !=''">
		,PAYMETHOD
	</if>
	order by CYCLESTARTTIME
  </select>	
</mapper>