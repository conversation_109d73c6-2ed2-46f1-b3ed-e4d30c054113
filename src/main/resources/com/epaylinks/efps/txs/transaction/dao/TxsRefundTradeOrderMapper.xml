<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="com.epaylinks.efps.txs.transaction.dao.TxsRefundTradeOrderMapper">
	<resultMap id="BaseResultMap"
		type="com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder">
		<id column="ID" property="id" jdbcType="DECIMAL" />
		<result column="TRANSACTION_NO" property="transactionNo"
			jdbcType="VARCHAR" />
		<result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
		<result column="OUT_REFUND_NO" property="outRefundNo" jdbcType="VARCHAR" />
		<result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
		<result column="TERMINAL_NO" property="terminalNo" jdbcType="VARCHAR" />
		<result column="REFUND_DESC" property="refundDesc" jdbcType="VARCHAR" />
		<result column="TOTAL_FEE" property="totalFee" jdbcType="DECIMAL" />
		<result column="REFUND_FEE" property="refundFee" jdbcType="DECIMAL" />
		<result column="REFUND_CURRENCY" property="refundCurrency"
			jdbcType="VARCHAR" />
		<result column="CHANNEL_TYPE" property="channelType" jdbcType="CHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="PAY_STATE" property="payState" jdbcType="CHAR" />
		<result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
		<result column="REDIRECT_URL" property="redirectUrl" jdbcType="VARCHAR" />
		<result column="CUSTOMERNAME" property="customerName" jdbcType="VARCHAR" />
		<result column="BUSINESS_INST_ID" property="businessInstId" jdbcType="VARCHAR" />
		<result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
		<result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
   		<result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
   		<result column="BACKPAY_PROCEDUREFEE" jdbcType="DECIMAL" property="backpayProcedurefee" />
		<result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR" />
		<result column="TERMINAL_NAME" jdbcType="VARCHAR" property="terminalName" />
	</resultMap>
	<sql id="Base_Column_List">
		ID, TRANSACTION_NO, OUT_TRADE_NO, OUT_REFUND_NO, CUSTOMER_CODE,
		TERMINAL_NO,
		REFUND_DESC,
		TOTAL_FEE, REFUND_FEE,
		REFUND_CURRENCY,
		CHANNEL_TYPE,
		CREATE_TIME, PAY_STATE, NOTIFY_URL,
		REDIRECT_URL,CUSTOMERNAME, BUSINESS_INST_ID, BUSINESS_CODE, ERROR_CODE,
		UPDATE_TIME, PROCEDURE_FEE, BACKPAY_PROCEDUREFEE, SOURCE_TYPE,TERMINAL_NAME
	</sql>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER
		where ID = #{id,jdbcType=DECIMAL}

	</select>

	<select id="selectBySelective" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER 
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} 
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR} 
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>
	
	<select id="selectByNotPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
			<if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND CUSTOMER_CODE in 
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">  
         		 #{item}  
      		    </foreach> 
			</if>
		</where>
		order by CREATE_TIME desc
	</select>

	<select id="selectByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
		select A.*, rownum RN
		from (
		select * from TXS_REFUND_TRADE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND CUSTOMER_CODE in 
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">  
         		 #{item}  
      		    </foreach> 
			</if>
			<if test="beginTime!=null and beginTime !=''">
				AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null and endTime !=''">
				AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
		order by CREATE_TIME desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

	</select>
	
	<select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		select count(ID)
		from TXS_REFUND_TRADE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND CUSTOMER_CODE in 
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">  
         		 #{item}  
      		    </foreach> 
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from
		TXS_REFUND_TRADE_ORDER
		where ID = #{id,jdbcType=DECIMAL}
	</delete>
	<insert id="insert"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder">
		insert into TXS_REFUND_TRADE_ORDER (ID, TRANSACTION_NO,
		OUT_TRADE_NO,
		OUT_REFUND_NO,
		CUSTOMER_CODE, TERMINAL_NO, REFUND_DESC,
		TOTAL_FEE, REFUND_FEE,
		REFUND_CURRENCY,
		CHANNEL_TYPE, CREATE_TIME, PAY_STATE,
		NOTIFY_URL,
		REDIRECT_URL,CUSTOMERNAME, BUSINESS_INST_ID, BUSINESS_CODE, ERROR_CODE, 
		UPDATE_TIME, PROCEDURE_FEE, BACKPAY_PROCEDUREFEE, SOURCE_TYPE,TERMINAL_NAME)
		values
		(#{id,jdbcType=DECIMAL},#{transactionNo,jdbcType=VARCHAR},
		#{outTradeNo,jdbcType=VARCHAR},
		#{outRefundNo,jdbcType=VARCHAR},
		#{customerCode,jdbcType=VARCHAR}, #{terminalNo,jdbcType=VARCHAR},
		#{refundDesc,jdbcType=VARCHAR},
		#{totalFee,jdbcType=DECIMAL},
		#{refundFee,jdbcType=DECIMAL},
		#{refundCurrency,jdbcType=VARCHAR},
		#{channelType,jdbcType=CHAR},#{createTime,jdbcType=TIMESTAMP},
		#{payState,jdbcType=CHAR},
		#{notifyUrl,jdbcType=VARCHAR},
		#{redirectUrl,jdbcType=VARCHAR},
		#{customerCode,jdbcType=VARCHAR},
		#{businessInstId,jdbcType=VARCHAR},
		#{businessCode,jdbcType=VARCHAR},
		#{errorCode,jdbcType=VARCHAR},
		#{updateTime,jdbcType=TIMESTAMP},
        #{procedureFee,jdbcType=DECIMAL},
        #{backpayProcedurefee,jdbcType=DECIMAL},
        #{sourceType,jdbcType=VARCHAR},
       #{terminalName,jdbcType=VARCHAR}
		)
	</insert>
	<insert id="insertSelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder">
		insert into TXS_REFUND_TRADE_ORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				ID,
			</if>
			<if test="transactionNo != null">
				TRANSACTION_NO,
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO,
			</if>
			<if test="outRefundNo != null">
				OUT_REFUND_NO,
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE,
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO,
			</if>
			<if test="refundDesc != null">
				REFUND_DESC,
			</if>
			<if test="totalFee != null">
				TOTAL_FEE,
			</if>
			<if test="refundFee != null">
				REFUND_FEE,
			</if>
			<if test="refundCurrency != null">
				REFUND_CURRENCY,
			</if>
			<if test="channelType != null">
				CHANNEL_TYPE,
			</if>
			<if test="createTime != null">
				CREATE_TIME,
			</if>
			<if test="payState != null">
				PAY_STATE,
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL,
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL,
			</if>
			<if test="customerName != null">
				CUSTOMERNAME,
			</if>
			<if test="businessInstId != null">
				BUSINESS_INST_ID,
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE,
			</if>
			<if test="errorCode != null">
				ERROR_CODE,
			</if>
			<if test="updateTime != null">
                UPDATE_TIME,
      		</if>
      		<if test="procedureFee != null">
       		    PROCEDURE_FEE,
     		</if>
      		<if test="backpayProcedurefee != null">
       		    BACKPAY_PROCEDUREFEE,
     		</if>
      		<if test="sourceType != null">
       		    SOURCE_TYPE,
     		</if>
     		<if test="txsTransactionNo != null">
       		    txs_transaction_no,
     		</if>
     		<if test="terminalName != null">
	      		TERMINAL_NAME,
	        </if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=DECIMAL},
			</if>
			<if test="transactionNo != null">
				#{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				#{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="outRefundNo != null">
				#{outRefundNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				#{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				#{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="refundDesc != null">
				#{refundDesc,jdbcType=VARCHAR},
			</if>
			<if test="totalFee != null">
				#{totalFee,jdbcType=DECIMAL},
			</if>
			<if test="refundFee != null">
				#{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundCurrency != null">
				#{refundCurrency,jdbcType=VARCHAR},
			</if>
			<if test="channelType != null">
				#{channelType,jdbcType=CHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				#{payState,jdbcType=CHAR},
			</if>
			<if test="notifyUrl != null">
				#{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				#{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="customerName != null">
				#{customerName,jdbcType=VARCHAR},
			</if>
			<if test="businessInstId != null">
				#{businessInstId,jdbcType=VARCHAR},
			</if>
			<if test="businessCode != null">
				#{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="errorCode != null">
				#{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
        		#{updateTime,jdbcType=TIMESTAMP},
      		</if>
      		<if test="procedureFee != null">
       			#{procedureFee,jdbcType=DECIMAL},
    		</if>
      		<if test="backpayProcedurefee != null">
       			#{backpayProcedurefee,jdbcType=DECIMAL},
    		</if>
    		<if test="sourceType != null">
				#{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="txsTransactionNo != null">
				#{txsTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="terminalName != null">
	      	#{terminalName,jdbcType=VARCHAR},
	   	   </if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder">
		update TXS_REFUND_TRADE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="outRefundNo != null">
				OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="refundDesc != null">
				REFUND_DESC = #{refundDesc,jdbcType=VARCHAR},
			</if>
			<if test="totalFee != null">
				TOTAL_FEE = #{totalFee,jdbcType=DECIMAL},
			</if>
			<if test="refundFee != null">
				REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundCurrency != null">
				REFUND_CURRENCY = #{refundCurrency,jdbcType=VARCHAR},
			</if>
			<if test="channelType != null">
				CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="businessInstId != null">
				BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
		        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
		    </if>
		    <if test="procedureFee != null">
		        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
		    </if>
		    <if test="backpayProcedurefee != null">
		        BACKPAY_PROCEDUREFEE = #{backpayProcedurefee,jdbcType=DECIMAL},
		    </if>
		    <if test="sourceType != null">
				SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="terminalName != null">
		      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
		   	   </if>
		</set>
		where ID = #{id,jdbcType=DECIMAL}
	</update>
	<update id="updateByPrimaryKey"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder">
		update TXS_REFUND_TRADE_ORDER
		set TRANSACTION_NO =
		#{transactionNo,jdbcType=VARCHAR},OUT_TRADE_NO =
		#{outTradeNo,jdbcType=VARCHAR},
		OUT_REFUND_NO =
		#{outRefundNo,jdbcType=VARCHAR},
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
		TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
		REFUND_DESC =
		#{refundDesc,jdbcType=VARCHAR},
		TOTAL_FEE =
		#{totalFee,jdbcType=DECIMAL},
		REFUND_FEE =
		#{refundFee,jdbcType=DECIMAL},
		REFUND_CURRENCY =
		#{refundCurrency,jdbcType=VARCHAR},
		CHANNEL_TYPE =
		#{channelType,jdbcType=CHAR},
		CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
		PAY_STATE =
		#{payState,jdbcType=CHAR},
		NOTIFY_URL =
		#{notifyUrl,jdbcType=VARCHAR},
		REDIRECT_URL =
		#{redirectUrl,jdbcType=VARCHAR},
		CUSTOMERNAME =
		#{customerName,jdbcType=VARCHAR},
		BUSINESS_INST_ID =
		#{businessInstId,jdbcType=VARCHAR},
		BUSINESS_CODE =
		#{businessCode,jdbcType=VARCHAR},
		ERROR_CODE =
		#{errorCode,jdbcType=VARCHAR},
		UPDATE_TIME = 
		#{updateTime,jdbcType=TIMESTAMP},
      	PROCEDURE_FEE = 
      	#{procedureFee,jdbcType=DECIMAL},
      	BACKPAY_PROCEDUREFEE = 
      	#{backpayProcedurefee,jdbcType=DECIMAL},
		SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
		TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR}
		where ID =
		#{id,jdbcType=DECIMAL}
	</update>
	
	<select id="selectByOutRefundNo" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER
		where OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}

	</select>
	
	<select id="selectByOutTradeNoAndPayState" resultMap="BaseResultMap" >
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
		and PAY_STATE = #{payState,jdbcType=CHAR}
	</select>
	
	<select id="selectByBusinessInstId" resultMap="BaseResultMap" >
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER
		where BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
	</select>

	<select id="queryByOutRefundNoList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER where out_refund_no in
		<foreach item="item" index="index" collection="outRefundNoList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
	<select id="refundQueryByCustomerAndOutRefundNo" resultMap="BaseResultMap" >
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_TRADE_ORDER
		where OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
		and CUSTOMER_CODE = #{customerCode,jdbcType=CHAR}
	</select>	
</mapper>