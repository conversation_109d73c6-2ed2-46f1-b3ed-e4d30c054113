<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsConsistcheckTradeMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsConsistcheckTrade" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="CURRENCY_TYPE" property="currencyType" jdbcType="VARCHAR" />
    <result column="PROCEDURE_FEE" property="procedureFee" jdbcType="DECIMAL" />
    <result column="PAYER_TYPE" property="payerType" jdbcType="VARCHAR" />
    <result column="PAYER_ID" property="payerId" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR" />
    <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
    <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR" />
    <result column="CERT_NO" property="certNo" jdbcType="VARCHAR" />
    <result column="CERT_NAME" property="certName" jdbcType="VARCHAR" />
    <result column="MOBILE_NO" property="mobileNo" jdbcType="VARCHAR" />
    <result column="CREDIT_EXP" property="creditExp" jdbcType="VARCHAR" />
    <result column="CREDIT_CVN" property="creditCvn" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="TRANSACTION_TYPE" property="transactionType" jdbcType="VARCHAR" />
    <result column="PAYER" property="payer" jdbcType="VARCHAR" />
    <result column="PAYEE_ID" property="payeeId" jdbcType="VARCHAR" />
    <result column="PAYEE_TYPE" property="payeeType" jdbcType="VARCHAR" />
    <result column="PAYEE" property="payee" jdbcType="VARCHAR" />
    <result column="BUSINESS_INST_ID" property="businessInstId" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
    <result column="CONSISTCHK_STATE" property="consistchkState" jdbcType="VARCHAR" />
    <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
    <result column="CHNL_TRANSACTION_NO" property="chnlTransactionNo" jdbcType="VARCHAR" />
    <result column="CHANL_PROCEDURE_FEE" property="chnlProcedureFee" jdbcType="DECIMAL" />  
    <result column="CARD_NO_ENC" property="cardNoEnc" jdbcType="VARCHAR" /> 
    <result column="CERT_NO_ENC" property="certNoEnc" jdbcType="VARCHAR" /> 
    <result column="CERT_NAME_ENC" property="certNameEnc" jdbcType="VARCHAR" /> 
    <result column="MOBILE_NO_ENC" property="mobileNoEnc" jdbcType="VARCHAR" /> 
    <result column="CREDIT_CVN_ENC" property="creditCvnEnc" jdbcType="VARCHAR" /> 
    <result column="CREDIT_EXP_ENC" property="creditExpEnc" jdbcType="VARCHAR" />  
  </resultMap>
  <sql id="Base_Column_List" >
    ID, OUT_TRADE_NO, TRANSACTION_NO, CUSTOMER_CODE, PAY_METHOD, CURRENCY_TYPE, PROCEDURE_FEE, 
    PAYER_TYPE, PAYER_ID, CREATE_TIME, STATE, UPDATE_TIME, ERROR_CODE, END_TIME, CARD_NO, 
    CERT_NO, CERT_NAME, MOBILE_NO, CREDIT_EXP, CREDIT_CVN, REMARK, TRANSACTION_TYPE, 
    PAYER, PAYEE_ID, PAYEE_TYPE, PAYEE, BUSINESS_INST_ID, BUSINESS_CODE, RETURN_CODE, 
    CONSISTCHK_STATE, RETURN_MSG, CHNL_TRANSACTION_NO, CHANL_PROCEDURE_FEE, CARD_NO_ENC, CERT_NO_ENC, 
    CERT_NAME_ENC, MOBILE_NO_ENC, CREDIT_CVN_ENC, CREDIT_EXP_ENC
  </sql>
  
  
  <select id="selectBySelective" resultMap="BaseResultMap"
	parameterType="com.epaylinks.efps.txs.transaction.model.TxsConsistcheckTrade">
	select
	<include refid="Base_Column_List" />
	from TXS_CONSISTCHECK_TRADE
	where 1 = 1 
		<if test="outTradeNo != null and outTradeNo !=''">
			AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
		</if>
		<if test="transactionNo != null and transactionNo !=''">
			AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
		</if>
		<if test="customerCode != null and customerCode !=''">
			AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
		</if>
		<if test="payMethod != null and payMethod !=''">
			AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
		</if>
		<if test="currencyType != null and currencyType !=''">
			AND CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR}
		</if>
		<if test="procedureFee != null and procedureFee !=''">
			AND PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL}
		</if>
		<if test="payerType != null and payerType !=''">
			AND PAYER_TYPE = #{payerType,jdbcType=VARCHAR}
		</if>
		<if test="payerId != null and payerId !=''">
			AND PAYER_ID = #{payerId,jdbcType=VARCHAR}
		</if>
		<if test="createTime != null ">
			AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="state != null and state !=''">
			AND STATE = #{state,jdbcType=VARCHAR}
		</if>
		<if test="updateTime != null">
			AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="errorCode != null and errorCode !=''">
			AND ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
		</if>
		<if test="endTime != null ">
			AND END_TIME = #{endTime,jdbcType=TIMESTAMP}
		</if>
		<if test="cardNo != null and cardNo !=''">
			AND CARD_NO = #{cardNo,jdbcType=VARCHAR}
		</if>
		<if test="certNo != null and certNo !=''">
			AND CERT_NO = #{certNo,jdbcType=VARCHAR}
		</if>
		<if test="certName != null and certName !=''">
			AND CERT_NAME = #{certName,jdbcType=VARCHAR}
		</if>
		<if test="mobileNo != null and mobileNo !=''">
			AND MOBILE_NO = #{mobileNo,jdbcType=VARCHAR}
		</if>
		<if test="creditExp != null and creditExp !=''">
			AND CREDIT_EXP = #{creditExp,jdbcType=VARCHAR}
		</if>
		<if test="creditCvn != null and creditCvn !=''">
			AND CREDIT_CVN = #{creditCvn,jdbcType=VARCHAR}
		</if>
		<if test="remark != null and remark !=''">
			AND REMARK = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="transactionType != null and transactionType !=''">
			AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
		</if>
		<if test="payer != null and payer !=''">
			AND PAYER = #{payer,jdbcType=VARCHAR}
		</if>
		<if test="payeeId != null and payeeId !=''">
			AND PAYEE_ID = #{payeeId,jdbcType=VARCHAR}
		</if>
		<if test="payeeType != null and payeeType !=''">
			AND PAYEE_TYPE= #{payeeType,jdbcType=VARCHAR}
		</if>
		<if test="payee != null and payee !=''">
			AND PAYEE = #{payee,jdbcType=VARCHAR}
		</if>
		<if test="businessInstId != null and businessInstId !=''">
			AND BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
		</if>
		<if test="businessCode != null and businessCode !=''">
			AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
		</if>
		<if test="returnCode != null and returnCode !=''">
			AND RETURN_CODE = #{returnCode,jdbcType=VARCHAR}
		</if>	
		<if test="consistchkState != null and consistchkState !=''">
			AND CONSISTCHK_STATE = #{consistchkState,jdbcType=VARCHAR}
		</if>
		<if test="returnMsg != null and returnMsg !=''">
			AND RETURN_MSG = #{returnMsg,jdbcType=VARCHAR}
		</if>
		<if test="chnlTransactionNo != null and chnlTransactionNo !=''">
			AND CHNL_TRANSACTION_NO = #{chnlTransactionNo,jdbcType=VARCHAR}
		</if>
		<if test="chnlProcedureFee != null and chnlProcedureFee !=''">
			AND CHANL_PROCEDURE_FEE = #{chnlProcedureFee,jdbcType=DECIMAL}
		</if>
		<if test="cardNoEnc != null and cardNoEnc !=''">
			AND CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR}
		</if>
		<if test="certNoEnc != null and certNoEnc !=''">
			AND CERT_NO_ENC = #{certNoEnc,jdbcType=VARCHAR}
		</if>
		<if test="certNameEnc != null and certNameEnc !=''">
			AND CERT_NAME_ENC = #{certNameEnc,jdbcType=VARCHAR}
		</if>
		<if test="mobileNoEnc != null and mobileNoEnc !=''">
			AND MOBILE_NO_ENC = #{mobileNoEnc,jdbcType=VARCHAR}
		</if>
		<if test="creditCvnEnc != null and creditCvnEnc !=''">
			AND CREDIT_CVN_ENC = #{creditCvnEnc,jdbcType=VARCHAR}
		</if>
		<if test="creditExpEnc != null and creditExpEnc !=''">
			AND CREDIT_EXP_ENC = #{creditExpEnc,jdbcType=VARCHAR}
		</if>												
  </select>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from TXS_CONSISTCHECK_TRADE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from TXS_CONSISTCHECK_TRADE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsConsistcheckTrade" >
    insert into TXS_CONSISTCHECK_TRADE (ID, OUT_TRADE_NO, TRANSACTION_NO, 
      CUSTOMER_CODE, PAY_METHOD, CURRENCY_TYPE, 
      PROCEDURE_FEE, PAYER_TYPE, PAYER_ID, 
      CREATE_TIME, STATE, UPDATE_TIME, 
      ERROR_CODE, END_TIME, CARD_NO, 
      CERT_NO, CERT_NAME, MOBILE_NO, 
      CREDIT_EXP, CREDIT_CVN, REMARK, 
      TRANSACTION_TYPE, PAYER, PAYEE_ID, 
      PAYEE_TYPE, PAYEE, BUSINESS_INST_ID, 
      BUSINESS_CODE, RETURN_CODE, CONSISTCHK_STATE, 
      RETURN_MSG, CHNL_TRANSACTION_NO, CHANL_PROCEDURE_FEE, CARD_NO_ENC, CERT_NO_ENC, 
    CERT_NAME_ENC, MOBILE_NO_ENC, CREDIT_CVN_ENC, CREDIT_EXP_ENC)
    values (#{id,jdbcType=DECIMAL}, #{outTradeNo,jdbcType=VARCHAR}, #{transactionNo,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, #{currencyType,jdbcType=VARCHAR}, 
      #{procedureFee,jdbcType=DECIMAL}, #{payerType,jdbcType=VARCHAR}, #{payerId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{state,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{errorCode,jdbcType=VARCHAR}, #{endTime,jdbcType=TIMESTAMP}, #{cardNo,jdbcType=VARCHAR}, 
      #{certNo,jdbcType=VARCHAR}, #{certName,jdbcType=VARCHAR}, #{mobileNo,jdbcType=VARCHAR}, 
      #{creditExp,jdbcType=VARCHAR}, #{creditCvn,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{transactionType,jdbcType=VARCHAR}, #{payer,jdbcType=VARCHAR}, #{payeeId,jdbcType=VARCHAR}, 
      #{payeeType,jdbcType=VARCHAR}, #{payee,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR}, 
      #{businessCode,jdbcType=VARCHAR}, #{returnCode,jdbcType=VARCHAR}, #{consistchkState,jdbcType=VARCHAR}, 
      #{returnMsg,jdbcType=VARCHAR}, #{chnlTransactionNo,jdbcType=VARCHAR}, #{chnlProcedureFee,jdbcType=DECIMAL},
      #{cardNoEnc,jdbcType=VARCHAR},#{certNoEnc,jdbcType=VARCHAR},#{certNameEnc,jdbcType=VARCHAR},
      #{mobileNoEnc,jdbcType=VARCHAR}, #{creditCvnEnc,jdbcType=VARCHAR}, #{creditExpEnc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsConsistcheckTrade" >
    insert into TXS_CONSISTCHECK_TRADE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="payMethod != null" >
        PAY_METHOD,
      </if>
      <if test="currencyType != null" >
        CURRENCY_TYPE,
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE,
      </if>
      <if test="payerType != null" >
        PAYER_TYPE,
      </if>
      <if test="payerId != null" >
        PAYER_ID,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="errorCode != null" >
        ERROR_CODE,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="cardNo != null" >
        CARD_NO,
      </if>
      <if test="certNo != null" >
        CERT_NO,
      </if>
      <if test="certName != null" >
        CERT_NAME,
      </if>
      <if test="mobileNo != null" >
        MOBILE_NO,
      </if>
      <if test="creditExp != null" >
        CREDIT_EXP,
      </if>
      <if test="creditCvn != null" >
        CREDIT_CVN,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE,
      </if>
      <if test="payer != null" >
        PAYER,
      </if>
      <if test="payeeId != null" >
        PAYEE_ID,
      </if>
      <if test="payeeType != null" >
        PAYEE_TYPE,
      </if>
      <if test="payee != null" >
        PAYEE,
      </if>
      <if test="businessInstId != null" >
        BUSINESS_INST_ID,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="returnCode != null" >
        RETURN_CODE,
      </if>
      <if test="consistchkState != null" >
        CONSISTCHK_STATE,
      </if>
      <if test="returnMsg != null" >
        RETURN_MSG,
      </if>
      <if test="chnlTransactionNo != null" >
        CHNL_TRANSACTION_NO,
      </if>
      <if test="chnlProcedureFee != null" >
        CHANL_PROCEDURE_FEE,
      </if>
      <if test="cardNoEnc != null" >
        CARD_NO_ENC,
      </if>
      <if test="certNoEnc != null" >
        CERT_NO_ENC,
      </if>
      <if test="certNameEnc != null" >
        CERT_NAME_ENC,
      </if>
      <if test="mobileNoEnc != null" >
        MOBILE_NO_ENC,
      </if>
      <if test="creditCvnEnc != null" >
        CREDIT_CVN_ENC,
      </if>
      <if test="creditExpEnc != null" >
        CREDIT_EXP_ENC,
      </if>                                    
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="currencyType != null" >
        #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="procedureFee != null" >
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="payerType != null" >
        #{payerType,jdbcType=VARCHAR},
      </if>
      <if test="payerId != null" >
        #{payerId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        #{state,jdbcType=CHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null" >
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardNo != null" >
        #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null" >
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certName != null" >
        #{certName,jdbcType=VARCHAR},
      </if>
      <if test="mobileNo != null" >
        #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="creditExp != null" >
        #{creditExp,jdbcType=VARCHAR},
      </if>
      <if test="creditCvn != null" >
        #{creditCvn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null" >
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="payer != null" >
        #{payer,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null" >
        #{payeeId,jdbcType=VARCHAR},
      </if>
      <if test="payeeType != null" >
        #{payeeType,jdbcType=VARCHAR},
      </if>
      <if test="payee != null" >
        #{payee,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null" >
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="returnCode != null" >
        #{returnCode,jdbcType=VARCHAR},
      </if>
      <if test="consistchkState != null" >
        #{consistchkState,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null" >
        #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="chnlTransactionNo != null" >
        #{chnlTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="chnlProcedureFee != null" >
        #{chnlProcedureFee,jdbcType=DECIMAL},
      </if>     
      <if test="cardNoEnc != null" >
        #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="certNoEnc != null" >
        #{certNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="certNameEnc != null" >
        #{certNameEnc,jdbcType=VARCHAR},
      </if>
      <if test="mobileNoEnc != null" >
        #{mobileNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="creditCvnEnc != null" >
        #{creditCvnEnc,jdbcType=VARCHAR},
      </if>
      <if test="creditExpEnc != null" >
        #{creditExpEnc,jdbcType=VARCHAR},
      </if>                                           
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsConsistcheckTrade" >
    update TXS_CONSISTCHECK_TRADE
    <set >
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="currencyType != null" >
        CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="payerType != null" >
        PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
      </if>
      <if test="payerId != null" >
        PAYER_ID = #{payerId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null" >
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardNo != null" >
        CARD_NO = #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null" >
        CERT_NO = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certName != null" >
        CERT_NAME = #{certName,jdbcType=VARCHAR},
      </if>
      <if test="mobileNo != null" >
        MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="creditExp != null" >
        CREDIT_EXP = #{creditExp,jdbcType=VARCHAR},
      </if>
      <if test="creditCvn != null" >
        CREDIT_CVN = #{creditCvn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="payer != null" >
        PAYER = #{payer,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null" >
        PAYEE_ID = #{payeeId,jdbcType=VARCHAR},
      </if>
      <if test="payeeType != null" >
        PAYEE_TYPE = #{payeeType,jdbcType=VARCHAR},
      </if>
      <if test="payee != null" >
        PAYEE = #{payee,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null" >
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="returnCode != null" >
        RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
      </if>
      <if test="consistchkState != null" >
        CONSISTCHK_STATE = #{consistchkState,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null" >
        RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="chnlTransactionNo != null" >
        CHNL_TRANSACTION_NO = #{chnlTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="chnlProcedureFee != null" >
        CHANL_PROCEDURE_FEE = #{chnlProcedureFee,jdbcType=VARCHAR},
      </if>      
      <if test="cardNoEnc != null" >
        CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="certNoEnc != null" >
        CERT_NO_ENC = #{certNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="certNameEnc != null" >
        CERT_NAME_ENC = #{certNameEnc,jdbcType=VARCHAR},
      </if>
      <if test="mobileNoEnc != null" >
        MOBILE_NO_ENC = #{mobileNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="creditCvnEnc != null" >
        CREDIT_CVN_ENC = #{creditCvnEnc,jdbcType=VARCHAR},
      </if>
      <if test="creditExpEnc != null" >
        CREDIT_EXP_ENC = #{creditExpEnc,jdbcType=VARCHAR},
      </if>                                          
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsConsistcheckTrade">
    update TXS_CONSISTCHECK_TRADE
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
      PAYER_ID = #{payerId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      STATE = #{state,jdbcType=CHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      CARD_NO = #{cardNo,jdbcType=VARCHAR},
      CERT_NO = #{certNo,jdbcType=VARCHAR},
      CERT_NAME = #{certName,jdbcType=VARCHAR},
      MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
      CREDIT_EXP = #{creditExp,jdbcType=VARCHAR},
      CREDIT_CVN = #{creditCvn,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      PAYER = #{payer,jdbcType=VARCHAR},
      PAYEE_ID = #{payeeId,jdbcType=VARCHAR},
      PAYEE_TYPE = #{payeeType,jdbcType=VARCHAR},
      PAYEE = #{payee,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
      CONSISTCHK_STATE = #{consistchkState,jdbcType=VARCHAR},
      RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
      CHNL_TRANSACTION_NO = #{chnlTransactionNo,jdbcType=VARCHAR},
      CHANL_PROCEDURE_FEE = #{chnlProcedureFee,jdbcType=DECIMAL}, 
      CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      CERT_NO_ENC = #{certNoEnc,jdbcType=VARCHAR},
      CERT_NAME_ENC = #{certNameEnc,jdbcType=VARCHAR},
      MOBILE_NO_ENC = #{mobileNoEnc,jdbcType=VARCHAR},
      CREDIT_CVN_ENC = #{creditCvnEnc,jdbcType=VARCHAR},
      CREDIT_EXP_ENC = #{creditExpEnc,jdbcType=VARCHAR}          
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateStateAsFailure">
  	update TXS_CONSISTCHECK_TRADE
  	set STATE = #{state, jdbcType=VARCHAR},
  		ERROR_CODE = #{errorCode, jdbcType=VARCHAR},
  	    UPDATE_TIME = #{updateTime, jdbcType=TIMESTAMP}
  	where ID = #{id, jdbcType=DECIMAL}
  </update>  
  <update id="updateState">
  	update TXS_CONSISTCHECK_TRADE
  	set STATE = #{state, jdbcType=VARCHAR},
  		UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
  	where ID = #{id, jdbcType=INTEGER}
  </update>  
  
	<select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		select count(ID) from TXS_CONSISTCHECK_TRADE 
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
	</select>  
	
	<select id="selectByNotPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select 
		<include refid="Base_Column_List" />
		from TXS_CONSISTCHECK_TRADE 
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
		order by CREATE_TIME desc 
	</select>	
	
	<select id="selectByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
		select A.*, rownum RN
		from (
		select * from TXS_CONSISTCHECK_TRADE
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
		order by CREATE_TIME desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>	
	
	 <sql id="tradeQueryCondition">
		1=1
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} 
			</if>
			<if test="chnlTransactionNo != null and chnlTransactionNo !=''">
				AND CHNL_TRANSACTION_NO = #{chnlTransactionNo,jdbcType=VARCHAR} 
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>			
			<if test="chkState != null and chkState !=''">
				AND CONSISTCHK_STATE = #{chkState,jdbcType=CHAR} 
			</if>
			<if test="beginTime!= null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime != null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="state != null and state !=''">
				AND STATE =  #{state,jdbcType=VARCHAR}
			</if>
		    <if
				test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND (CUSTOMER_CODE in
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">
					<if  test="(index % 999) == 998">
					 	NULL) OR CUSTOMER_CODE IN (
					</if>
					#{item, jdbcType=VARCHAR}  
				</foreach>
				)
			</if>			
	</sql>	
</mapper>