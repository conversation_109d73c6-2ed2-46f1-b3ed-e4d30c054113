<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsNfcRcModeMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.common.business.txs.TxsNfcRcMode" >
    <id column="RC_MODE" property="rcMode" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="VALID" property="valid" jdbcType="VARCHAR" />
    <result column="BY_CUSTOMER_CODE" property="byCustomerCode" jdbcType="VARCHAR" />
    <result column="BY_CARD_TYPE" property="byCardType" jdbcType="VARCHAR" />
    <result column="BY_CARD_NO_ENC" property="byCardNoEnc" jdbcType="VARCHAR" />
    <result column="JUDGE_TYPE" property="judgeType" jdbcType="VARCHAR" />
    <result column="VALUE" property="value" jdbcType="DECIMAL" />
    <result column="MARK_STRING" property="markString" jdbcType="VARCHAR" />
    <result column="BAND_OUT_COME" property="bandOutCome" jdbcType="VARCHAR" />
    <result column="BAND_BUSINESS" property="bandBusiness" jdbcType="VARCHAR" />
    <result column="BAND_TYPE" property="bandType" jdbcType="VARCHAR" />
    <result column="AUTO_OPEN_PAY" property="autoOpenPay" jdbcType="VARCHAR" />
    <result column="AUTO_OPEN_BIZ" property="autoOpenBiz" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    RC_MODE, CREATE_TIME, VALID, BY_CUSTOMER_CODE, BY_CARD_TYPE, BY_CARD_NO_ENC, JUDGE_TYPE, 
    VALUE, MARK_STRING, BAND_OUT_COME, BAND_BUSINESS, BAND_TYPE, AUTO_OPEN_PAY, AUTO_OPEN_BIZ
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from TXS_NFC_RC_MODE
    where RC_MODE = #{rcMode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from TXS_NFC_RC_MODE
    where RC_MODE = #{rcMode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcMode" >
    insert into TXS_NFC_RC_MODE (RC_MODE, CREATE_TIME, VALID, 
      BY_CUSTOMER_CODE, BY_CARD_TYPE, BY_CARD_NO_ENC, 
      JUDGE_TYPE, VALUE, MARK_STRING, BAND_OUT_COME, BAND_BUSINESS, BAND_TYPE, 
      AUTO_OPEN_PAY, AUTO_OPEN_BIZ
      )
    values (#{rcMode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=VARCHAR}, 
      #{byCustomerCode,jdbcType=VARCHAR}, #{byCardType,jdbcType=VARCHAR}, #{byCardNoEnc,jdbcType=VARCHAR}, 
      #{judgeType,jdbcType=VARCHAR}, #{value,jdbcType=DECIMAL}, #{markString,jdbcType=VARCHAR}, 
      #{bandOutCome,jdbcType=VARCHAR}, #{bandBusiness,jdbcType=VARCHAR}, #{bandType,jdbcType=VARCHAR},
      #{autoOpenPay,jdbcType=VARCHAR}, #{autoOpenBiz,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcMode" >
    insert into TXS_NFC_RC_MODE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rcMode != null" >
        RC_MODE,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="valid != null" >
        VALID,
      </if>
      <if test="byCustomerCode != null" >
        BY_CUSTOMER_CODE,
      </if>
      <if test="byCardType != null" >
        BY_CARD_TYPE,
      </if>
      <if test="byCardNoEnc != null" >
        BY_CARD_NO_ENC,
      </if>
      <if test="judgeType != null" >
        JUDGE_TYPE,
      </if>
      <if test="value != null" >
        VALUE,
      </if>
      <if test="markString != null" >
        MARK_STRING,
      </if>
      <if test="bandOutCome != null" >
        BAND_OUT_COME,
      </if>
      <if test="bandBusiness != null" >
        BAND_BUSINESS,
      </if>
      <if test="bandType != null" >
        BAND_TYPE,
      </if>     
      <if test="autoOpenPay != null" >
        AUTO_OPEN_PAY,
      </if>  
      <if test="autoOpenBiz != null" >
        AUTO_OPEN_BIZ,
      </if>                           
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rcMode != null" >
        #{rcMode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=VARCHAR},
      </if>
      <if test="byCustomerCode != null" >
        #{byCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="byCardType != null" >
        #{byCardType,jdbcType=VARCHAR},
      </if>
      <if test="byCardNoEnc != null" >
        #{byCardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="judgeType != null" >
        #{judgeType,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=DECIMAL},
      </if>
      <if test="markString != null" >
        #{markString,jdbcType=VARCHAR},
      </if>
      <if test="bandOutCome != null" >
        #{bandOutCome,jdbcType=VARCHAR},
      </if>
      <if test="bandBusiness != null" >
        #{bandBusiness,jdbcType=VARCHAR},
      </if>
      <if test="bandType != null" >
        #{bandType,jdbcType=VARCHAR},
      </if>
      <if test="autoOpenPay != null" >
        #{autoOpenPay,jdbcType=VARCHAR},
      </if> 
      <if test="autoOpenBiz != null" >
        #{autoOpenBiz,jdbcType=VARCHAR},
      </if>                               
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcMode" >
    update TXS_NFC_RC_MODE
    <set >
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        VALID = #{valid,jdbcType=VARCHAR},
      </if>
      <if test="byCustomerCode != null" >
        BY_CUSTOMER_CODE = #{byCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="byCardType != null" >
        BY_CARD_TYPE = #{byCardType,jdbcType=VARCHAR},
      </if>
      <if test="byCardNoEnc != null" >
        BY_CARD_NO_ENC = #{byCardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="judgeType != null" >
        JUDGE_TYPE = #{judgeType,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        VALUE = #{value,jdbcType=DECIMAL},
      </if>
      <if test="markString != null" >
        MARK_STRING = #{markString,jdbcType=VARCHAR},
      </if>
      <if test="bandOutCome != null" >
        BAND_OUT_COME = #{bandOutCome,jdbcType=VARCHAR},
      </if>
      <if test="bandBusiness != null" >
        BAND_BUSINESS = #{bandBusiness,jdbcType=VARCHAR},
      </if>
      <if test="bandType != null" >
        BAND_TYPE = #{bandType,jdbcType=VARCHAR},
      </if>       
      <if test="autoOpenPay != null" >
        AUTO_OPEN_PAY = #{autoOpenPay,jdbcType=VARCHAR},
      </if>  
      <if test="autoOpenBiz != null" >
        AUTO_OPEN_BIZ = #{autoOpenBiz,jdbcType=VARCHAR},
      </if>                         
    </set>
    where RC_MODE = #{rcMode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcMode" >
    update TXS_NFC_RC_MODE
    set CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      VALID = #{valid,jdbcType=VARCHAR},
      BY_CUSTOMER_CODE = #{byCustomerCode,jdbcType=VARCHAR},
      BY_CARD_TYPE = #{byCardType,jdbcType=VARCHAR},
      BY_CARD_NO_ENC = #{byCardNoEnc,jdbcType=VARCHAR},
      JUDGE_TYPE = #{judgeType,jdbcType=VARCHAR},
      VALUE = #{value,jdbcType=DECIMAL},
      MARK_STRING = #{markString,jdbcType=VARCHAR},
      BAND_OUT_COME = #{bandOutCome,jdbcType=VARCHAR},
      BAND_BUSINESS = #{bandBusiness,jdbcType=VARCHAR},
      BAND_TYPE = #{bandType,jdbcType=VARCHAR},
      AUTO_OPEN_PAY = #{autoOpenPay,jdbcType=VARCHAR},
      AUTO_OPEN_BIZ = #{autoOpenBiz,jdbcType=VARCHAR}
    where RC_MODE = #{rcMode,jdbcType=VARCHAR}
  </update>
  
    <select id="selectAllValidRecord" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from TXS_NFC_RC_MODE
    where VALID = '1'
  </select>
</mapper>