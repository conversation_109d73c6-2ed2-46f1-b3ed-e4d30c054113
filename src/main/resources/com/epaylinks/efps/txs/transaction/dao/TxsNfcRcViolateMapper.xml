<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsNfcRcViolateMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.common.business.txs.TxsNfcRcViolate" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="RC_MODE" property="rcMode" jdbcType="VARCHAR" />
    <result column="RC_DATE" property="rcDate" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="BAND_PAY_OR_BUZ" property="bandPayOrBuz" jdbcType="VARCHAR" />
    <result column="BANDED_TIME" property="bandedTime" jdbcType="TIMESTAMP" />
    <result column="AUTO_OPEN" property="autoOpen" jdbcType="VARCHAR" />
    <result column="OPEND_TIME" property="opendTime" jdbcType="TIMESTAMP" />
    <result column="RC_RECORD_ID" property="rcRecordId" jdbcType="DECIMAL" />
    <result column="BAND_RESULT_CODE" property="bandResultCode" jdbcType="VARCHAR" />
    <result column="OPEN_RESULT_CODE" property="openResultCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, RC_MODE, RC_DATE, CREATE_TIME, CUSTOMER_CODE, BAND_PAY_OR_BUZ, BANDED_TIME, AUTO_OPEN, 
    OPEND_TIME, RC_RECORD_ID, BAND_RESULT_CODE, OPEN_RESULT_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TXS_NFC_RC_VIOLATE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_NFC_RC_VIOLATE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcViolate" >
    insert into TXS_NFC_RC_VIOLATE (ID, RC_MODE, RC_DATE, CREATE_TIME, 
      CUSTOMER_CODE, BAND_PAY_OR_BUZ, BANDED_TIME, 
      AUTO_OPEN, OPEND_TIME, RC_RECORD_ID, 
      BAND_RESULT_CODE, OPEN_RESULT_CODE)
    values (#{id,jdbcType=DECIMAL}, #{rcMode,jdbcType=VARCHAR}, #{rcDate,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{customerCode,jdbcType=VARCHAR}, #{bandPayOrBuz,jdbcType=VARCHAR}, #{bandedTime,jdbcType=TIMESTAMP}, 
      #{autoOpen,jdbcType=VARCHAR}, #{opendTime,jdbcType=TIMESTAMP}, #{rcRecordId,jdbcType=DECIMAL}, 
      #{bandResultCode,jdbcType=VARCHAR}, #{openResultCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcViolate" >
    insert into TXS_NFC_RC_VIOLATE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="rcMode != null" >
        RC_MODE,
      </if>      
      <if test="rcDate != null" >
        RC_DATE,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="bandPayOrBuz != null" >
        BAND_PAY_OR_BUZ,
      </if>
      <if test="bandedTime != null" >
        BANDED_TIME,
      </if>
      <if test="autoOpen != null" >
        AUTO_OPEN,
      </if>
      <if test="opendTime != null" >
        OPEND_TIME,
      </if>
      <if test="rcRecordId != null" >
        RC_RECORD_ID,
      </if>
      <if test="bandResultCode != null" >
        BAND_RESULT_CODE,
      </if>
      <if test="openResultCode != null" >
        OPEN_RESULT_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="rcMode != null" >
        #{rcMode,jdbcType=VARCHAR},
      </if>      
      <if test="rcDate != null" >
        #{rcDate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="bandPayOrBuz != null" >
        #{bandPayOrBuz,jdbcType=VARCHAR},
      </if>
      <if test="bandedTime != null" >
        #{bandedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoOpen != null" >
        #{autoOpen,jdbcType=VARCHAR},
      </if>
      <if test="opendTime != null" >
        #{opendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rcRecordId != null" >
        #{rcRecordId,jdbcType=DECIMAL},
      </if>
      <if test="bandResultCode != null" >
        #{bandResultCode,jdbcType=VARCHAR},
      </if>
      <if test="openResultCode != null" >
        #{openResultCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcViolate" >
    update TXS_NFC_RC_VIOLATE
    <set >
      <if test="rcMode != null" >
        RC_MODE = #{rcMode,jdbcType=VARCHAR},
      </if>    
      <if test="rcDate != null" >
        RC_DATE = #{rcDate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="bandPayOrBuz != null" >
        BAND_PAY_OR_BUZ = #{bandPayOrBuz,jdbcType=VARCHAR},
      </if>
      <if test="bandedTime != null" >
        BANDED_TIME = #{bandedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoOpen != null" >
        AUTO_OPEN = #{autoOpen,jdbcType=VARCHAR},
      </if>
      <if test="opendTime != null" >
        OPEND_TIME = #{opendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rcRecordId != null" >
        RC_RECORD_ID = #{rcRecordId,jdbcType=DECIMAL},
      </if>
      <if test="bandResultCode != null" >
        BAND_RESULT_CODE = #{bandResultCode,jdbcType=VARCHAR},
      </if>
      <if test="openResultCode != null" >
        OPEN_RESULT_CODE = #{openResultCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcViolate" >
    update TXS_NFC_RC_VIOLATE
    set RC_MODE = #{rcMode,jdbcType=VARCHAR},
 	  RC_DATE = #{rcDate,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      BAND_PAY_OR_BUZ = #{bandPayOrBuz,jdbcType=VARCHAR},
      BANDED_TIME = #{bandedTime,jdbcType=TIMESTAMP},
      AUTO_OPEN = #{autoOpen,jdbcType=VARCHAR},
      OPEND_TIME = #{opendTime,jdbcType=TIMESTAMP},
      RC_RECORD_ID = #{rcRecordId,jdbcType=DECIMAL},
      BAND_RESULT_CODE = #{bandResultCode,jdbcType=VARCHAR},
      OPEN_RESULT_CODE = #{openResultCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectNeedOpenAccountCustomerCodes" resultType="java.lang.String" >
    select 
    	distinct(CUSTOMER_CODE)
    from 
    	TXS_NFC_RC_VIOLATE
    where 
    	RC_DATE = #{rcDate,jdbcType=VARCHAR}
    	and BAND_PAY_OR_BUZ = '1'
    	and AUTO_OPEN = '1'
  </select>
  <select id="selectNeedOpenBusinessCustomerCodes" resultType="java.lang.String" >
    select 
    	distinct(CUSTOMER_CODE)
    from 
    	TXS_NFC_RC_VIOLATE
    where 
    	RC_DATE = #{rcDate,jdbcType=VARCHAR}
    	and BAND_PAY_OR_BUZ = '2'
    	and AUTO_OPEN = '1'
  </select>  
  <update id="updateOpenAccountResultByCustomerAndRcDate" parameterType="java.lang.String">
    update TXS_NFC_RC_VIOLATE
    set 
      OPEND_TIME = systimestamp,
      OPEN_RESULT_CODE = #{openResultCode,jdbcType=VARCHAR}
    where 
        RC_DATE = #{rcDate,jdbcType=VARCHAR}
    	and BAND_PAY_OR_BUZ = '1'
    	and AUTO_OPEN = '1'
    	and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </update>  
  <update id="updateOpenBusinessResultCustomerAndRcDate" parameterType="java.lang.String">
    update TXS_NFC_RC_VIOLATE
    set 
      OPEND_TIME = systimestamp,
      OPEN_RESULT_CODE = #{openResultCode,jdbcType=VARCHAR}
    where 
        RC_DATE = #{rcDate,jdbcType=VARCHAR}
    	and BAND_PAY_OR_BUZ = '2'
    	and AUTO_OPEN = '1'
    	and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </update>   
</mapper>