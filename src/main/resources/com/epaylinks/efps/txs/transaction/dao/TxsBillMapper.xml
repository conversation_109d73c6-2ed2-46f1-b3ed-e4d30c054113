<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsBillMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsBill">
    <result column="FILENAME" jdbcType="VARCHAR" property="filename" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CUSTOMERCODE" jdbcType="VARCHAR" property="customercode" />
    <result column="BILLDATE" jdbcType="TIMESTAMP" property="billdate" />
    <result column="UNIQUEID" jdbcType="VARCHAR" property="uniqueid" />
    <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType" />
  </resultMap>
  <sql id="Base_Column_List">
    FILENAME,CREATE_TIME,CUSTOMERCODE,BILLDATE,UNIQUEID,BILL_TYPE
  </sql>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsBill">
    insert into TXS_BILL (FILENAME, CREATE_TIME, CUSTOMERCODE, 
      BILLDATE, UNIQUEID,BILL_TYPE)
    values (#{filename,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{customercode,jdbcType=VARCHAR}, 
      #{billdate,jdbcType=TIMESTAMP}, #{uniqueid,jdbcType=VARCHAR},#{billType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsBill">
    insert into TXS_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="filename != null">
        FILENAME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="customercode != null">
        CUSTOMERCODE,
      </if>
      <if test="billdate != null">
        BILLDATE,
      </if>
      <if test="uniqueid != null">
        UNIQUEID,
      </if>
      <if test="billType != null">
         BILL_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="filename != null">
        #{filename,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customercode != null">
        #{customercode,jdbcType=VARCHAR},
      </if>
      <if test="billdate != null">
        #{billdate,jdbcType=TIMESTAMP},
      </if>
      <if test="uniqueid != null">
        #{uniqueid,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <select id="selectByCustomerCodeAndBillDate" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" /> 
  	from TXS_BILL where CUSTOMERCODE = #{customerCode , jdbcType = VARCHAR} 
  	and BILLDATE = #{billDate , jdbcType = TIMESTAMP}
  </select>
  
  <select id="selectByCustomerCodeAndTime" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" /> 
  	from TXS_BILL where CUSTOMERCODE = #{customerCode , jdbcType = VARCHAR} 
  	and BILLDATE <![CDATA[ >= ]]> #{billBeginTime,jdbcType=TIMESTAMP}
	AND BILLDATE <![CDATA[ <= ]]> #{billEndTime,jdbcType=TIMESTAMP}
  </select>

    <select id="selectByCustomerCodeAndTimeAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from TXS_BILL where CUSTOMERCODE = #{customerCode , jdbcType = VARCHAR}
        and BILLDATE <![CDATA[ >= ]]> #{billBeginTime,jdbcType=TIMESTAMP}
        AND BILLDATE <![CDATA[ <= ]]> #{billEndTime,jdbcType=TIMESTAMP}
        AND BILL_TYPE = #{billType,jdbcType=VARCHAR}
    </select>

    <select id="selectByCustomerCodeAndBillDateAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from TXS_BILL where CUSTOMERCODE = #{customerCode , jdbcType = VARCHAR}
        and to_char(BILLDATE,'yyyyMMdd') = to_char(#{billDate , jdbcType = TIMESTAMP},'yyyyMMdd')
        AND BILL_TYPE = #{billType , jdbcType = TIMESTAMP}
    </select>


  
  <select id="selectByCustomerCodeOrderByBillDateDesc" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List" /> 
  	from (select 
  	<include refid="Base_Column_List" /> 
  	from TXS_BILL where CUSTOMERCODE = #{customerCode , jdbcType = VARCHAR} 
  	order by BILLDATE desc) 
  	where rownum = 1
  </select>
</mapper>