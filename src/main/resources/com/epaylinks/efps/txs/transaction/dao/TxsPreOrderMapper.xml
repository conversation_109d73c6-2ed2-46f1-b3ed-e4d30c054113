<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsPreOrderMapper">
	<resultMap id="BaseResultMap"
		type="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		<id column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
		<result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
		<result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
		<result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
		<result column="COMMISSIONED_CUSTOMER_CODE" jdbcType="VARCHAR" property="commissionedCustomerCode" />
		<result column="TARGET_CUSTOMER_CODE" jdbcType="VARCHAR" property="targetCustomerCode" />
		<result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType" />
		<result column="PAYER" jdbcType="VARCHAR" property="payer" />
		<result column="PAYER_TYPE" jdbcType="VARCHAR" property="payerType" />
		<result column="PAYER_ID" jdbcType="VARCHAR" property="payerId" />
		<result column="TERMINAL_NO" jdbcType="VARCHAR" property="terminalNo" />
		<result column="CLIENT_IP" jdbcType="VARCHAR" property="clientIp" />
		<result column="SRC_CHANNEL_TYPE" jdbcType="VARCHAR" property="srcChannelType" />
		<result column="PAY_METHOD" jdbcType="VARCHAR" property="payMethod" />
		<result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
		<result column="CURRENCY_TYPE" jdbcType="VARCHAR" property="currencyType" />
		<result column="ORDER_INFO" jdbcType="VARCHAR" property="orderInfo" />
		<result column="NOTIFY_URL" jdbcType="VARCHAR" property="notifyUrl" />
		<result column="REDIRECT_URL" jdbcType="VARCHAR" property="redirectUrl" />
		<result column="ATTACH_DATA" jdbcType="VARCHAR" property="attachData" />
		<result column="TRANSACTION_START_TIME" jdbcType="TIMESTAMP"
			property="transactionStartTime" />
		<result column="TRANSACTION_END_TIME" jdbcType="TIMESTAMP"
			property="transactionEndTime" />
		<result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
		<result column="PAY_STATE" jdbcType="CHAR" property="payState" />
		<result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
		<result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
		<result column="REMARK" jdbcType="VARCHAR" property="remark" />
		<result column="SETTLEMENT_STATE" jdbcType="VARCHAR" property="settlementState" />
		<result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
		<result column="ENCRY_RESULT" jdbcType="VARCHAR" property="encryResult" />
		<result column="SETT_CYCLE_START_TIME" jdbcType="TIMESTAMP"
			property="settCycleStartTime" />
		<result column="SETT_CYCLE_END_TIME" jdbcType="TIMESTAMP"
			property="settCycleEndTime" />
		<result column="CUSTOMERNAME" jdbcType="VARCHAR" property="customerName" />
		<result column="CHANNEL_ORDER" jdbcType="VARCHAR" property="channelOrder" />
		<result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
		<result column="REFUND_FEE" jdbcType="DECIMAL" property="refundFee" />
		<result column="REFUNDING_FEE" jdbcType="DECIMAL" property="refundingFee" />
		<result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
		<result column="WITHDRAW_TRANSACTION_NO" jdbcType="VARCHAR" property="withdrawTransactionNo" />
		<result column="AGENT_CUSTOMERCODE" jdbcType="VARCHAR" property="agentCustomerCode" />
		<result column="PAY_PASSWAY" jdbcType="VARCHAR" property="payPassWay" />
		<result column="AGENT_CUSTOMERNAME" jdbcType="VARCHAR" property="agentCustomerName" />
		<result column="REDIRECT_FAILURL" jdbcType="VARCHAR" property="redirectFailUrl" />
		<result column="QUICKPAY_CARDNO" jdbcType="VARCHAR" property="quickpayCardno" />
		<result column="NO_CREDITCARDS" jdbcType="VARCHAR" property="noCreditCards" />
		<result column="PAY_CHANNEL_ID" property="payChannelId" jdbcType="DECIMAL" />
		<result column="ACQ_ORG_CODE" property="acqOrgCode" jdbcType="VARCHAR" />
	    <result column="ACQ_SP_ID" property="acqSpId" jdbcType="VARCHAR" />
	    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
	    <result column="TERM_INFO" property="termInfo" jdbcType="VARCHAR" />
	    <result column="AREA_INFO" property="areaInfo" jdbcType="VARCHAR" />
	    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
	    <result column="SUB_APP_ID" property="subAppId" jdbcType="VARCHAR" />
	    <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR" />
	    <result column="PLATFORM_CUSTOMER_CODE" property="platformCustomerCode" jdbcType="VARCHAR" />
	    <result column="STORE_ID" property="storeId" jdbcType="VARCHAR" />
	    <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR" />
	    <result column="REQ_RESERVED" property="reqReserved" jdbcType="VARCHAR" />
	    <result column="CUPS_REQ_RESERVED" property="cupsReqReserved" jdbcType="VARCHAR" />
	    <result column="PAYER_APP_NAME" property="payerAppName" jdbcType="VARCHAR" />
	    <result column="ACTUAL_PAY_AMOUNT" property="actualPayAmount" jdbcType="DECIMAL" />
	    <result column="DISCOUNTABLE_AMOUNT" property="discountableAmount" jdbcType="DECIMAL" />
	    <result column="SETTLEMENT_AMOUNT" property="settlementAmount" jdbcType="DECIMAL" />
	    <result column="SOURCE_CHANNEL" property="sourceChannel" jdbcType="VARCHAR" />
	    <result column="QR_CODE_ISSUER" property="qrCodeIssuer" jdbcType="VARCHAR" />
	    <result column="PROCEDURE_RATE" property="procedureRate" jdbcType="VARCHAR" />
    	<result column="RATE_MODE" property="rateMode" jdbcType="DECIMAL" /> 
	    <result column="CARD_NO_MOSAIC" property="cardNoMosaic" jdbcType="VARCHAR" />
	    <result column="CARD_NO_ENC" property="cardNoEnc" jdbcType="VARCHAR" />
	    <result column="CARD_TYPE" property="cardType" jdbcType="VARCHAR" />    	
	    <result column="max_profit" property="maxProfit" jdbcType="VARCHAR" />
   	    <result column="CANCEL_STATE" property="cancelState" jdbcType="VARCHAR" />
   	    <result column="CANCLE_NO" property="cancelNo" jdbcType="VARCHAR" />
   	    <result column="CANCEL_RETURN_MSG" property="cancelReturnMsg" jdbcType="VARCHAR" />
   	    <result column="CANCEL_UPDATE_TIME" property="cancelUpdateTime" jdbcType="TIMESTAMP" />
   	    <result column="CARD_OWNER" property="cardOwner" jdbcType="VARCHAR" />
        <result column="CARD_OWNER_MOSAIC" property="cardOwnerMosaic" jdbcType="VARCHAR" />
        <result column="CERT_NO" property="certNo" jdbcType="VARCHAR" />
        <result column="CERT_NO_MOSAIC" property="certNoMosaic" jdbcType="VARCHAR" />
        <result column="CARD_OWNER_HASH" property="cardOwnerHash" jdbcType="VARCHAR" />
        <result column="CERT_NO_HASH" property="certNoHash" jdbcType="VARCHAR" />
        <result column="SPLIT_RELATION_ID" property="splitRelationId" jdbcType="VARCHAR" />
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
        <result column="CHANNEL_RESP_CODE" property="channelRespCode" jdbcType="VARCHAR" />
        <result column="CHANNEL_RESP_MSG" property="channelRespMsg" jdbcType="VARCHAR" />
        <result column="TERMINAL_NAME" jdbcType="VARCHAR" property="terminalName" />
		<result column="MEMBER_ID" jdbcType="VARCHAR" property="memberId" />
		<result column="SPLIT_MODEL" jdbcType="VARCHAR" property="splitModel" />
		<result column="SUB_CUSTOMER_CODE" jdbcType="VARCHAR" property="subCustomerCode" />
		<result column="trade_customer_code" jdbcType="VARCHAR" property="tradeCustomerCode" />
		<result column="SPLIT_PROCEDURE_FEE" jdbcType="DECIMAL" property="splitProcedureFee" />
		<result column="SPLIT_PROCEDURE_RATE" property="splitProcedureRate" jdbcType="VARCHAR" />
		<result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
		<result column="BUYER_LOGON_ID" property="buyerLogonId" jdbcType="VARCHAR" /> 	
		<result column="BUYER_USER_ID" property="buyerUserId" jdbcType="VARCHAR" /> 
		<result column="business_inst_id" property="businessInstId" jdbcType="VARCHAR" /> 	
		<result column="sett_cycle_rule_code" property="settCycleRuleCode" jdbcType="VARCHAR" />
		<result column="SCENE" property="scene" jdbcType="VARCHAR" />
		<result column="REQUEST_SRC" property="requestSrc" jdbcType="VARCHAR" />
		<result column="auth_customer_code" property="authCustomerCode" jdbcType="VARCHAR" />
		<result column="trade_source" property="tradeSource" jdbcType="VARCHAR" />
		<result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR" />
		<result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL" />
		<result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
		<result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
		<result column="max_fee" jdbcType="DECIMAL" property="maxFee" />
    	<result column="min_fee" jdbcType="DECIMAL" property="minFee" />
    	<result column="NFC_TAG_ID" property="nfcTagId" jdbcType="VARCHAR" />
		<result column="FEE_PER" jdbcType="DECIMAL" property="feePer" />
		<result column="D1_HOLIDAY_CHARGE_RATE" jdbcType="VARCHAR" property="D1HolidayChargeRate" />
		<result column="D1_HOLIDAY_CHARGE_PER" jdbcType="DECIMAL" property="D1HolidayChargePer" />
		<result column="D1_HOIDAY_CHARGED" jdbcType="VARCHAR" property="D1HolidayCharged" />
		<result column="CHARGED_BY_BANK_CODE_AREA" jdbcType="VARCHAR" property="chargedByBankCodeArea" />		
		<result column="fund_channel" jdbcType="VARCHAR" property="fundChannel" />		
		<result column="cash_amount" property="cashAmount" jdbcType="DECIMAL" />
	    <result column="coupon_amount" property="couponAmount" jdbcType="DECIMAL" />	
	    <result column="BUSINESS_CODE_FST" property="businessCodeFst" jdbcType="VARCHAR" />
	    <result column="BUSINESS_INST_ID_FST" property="businessInstIdFst" jdbcType="VARCHAR" />
	    <result column="SECOND_CHARGED" property="secondCharged" jdbcType="VARCHAR" />	
	    <result column="industry_code_type" property="industryCodeType" jdbcType="VARCHAR" />
	    <result column="industry_name" property="industryName" jdbcType="VARCHAR" />
	    <result column="machine_code" property="machineCode" jdbcType="VARCHAR" />
	    <result column="terminal_type" property="terminalType" jdbcType="VARCHAR" />
		<result column="ENABLE_PAY_CHANNELS" property="enablePayChannels" jdbcType="VARCHAR"/>
		<result column="INSTALMENTS_NUM" property="instalmentsNum" jdbcType="DECIMAL"/>
		<result column="INSTALMENTS_SELLER_PERCENT" property="instalmentsSellerPercent" jdbcType="VARCHAR"/>
		<result column="batch_no" property="batchNo" jdbcType="VARCHAR" />
		<result column="SUB_OPEN_ID" property="subOpenId" jdbcType="VARCHAR" />

		<result column="ORI_TRANSACTION_NO" property="oriTransactionNo" jdbcType="VARCHAR" />
		<result column="AUTH_FINISH_AMOUNT" property="authFinishAmount" jdbcType="DECIMAL" />
		<result column="AUTHING_AMOUNT" property="authingAmount" jdbcType="DECIMAL" />
		<result column="AUTH_CONFIRM_MODE" property="authConfirmMode" jdbcType="VARCHAR" />
		<result column="AUTH_STATUS" property="authStatus" jdbcType="VARCHAR" />
		<result column="SETTLE_TYPE" property="settleType" jdbcType="VARCHAR" />
		<result column="IP_MODE" property="ipMode" jdbcType="VARCHAR" />
		<result column="IP_PROCEDURE_FEE" property="ipProcedureFee" jdbcType="DECIMAL" />
		<result column="IS_INSTALMENTS" property="isInstalments" jdbcType="VARCHAR" />
		<result column="IP_BUSINESS_INST_ID" property="ipBusinessInstId" jdbcType="VARCHAR" />
		<result column="IP_PROCEDURE_RATE" property="ipProcedureRate" jdbcType="VARCHAR" />
		<result column="ext_user_info" property="extUserInfoJson" jdbcType="VARCHAR" />
		<result column="PROCEDURE_CUSTOMERCODE" property="procedureCustomercode" jdbcType="VARCHAR" />
		<result column="IS_ORDER_SPLIT" property="isOrderSplit" jdbcType="VARCHAR" />
		<result column="DELAY_DAYS" property="delayDays" jdbcType="VARCHAR" />

		<result column="LOCAL_COUPON" property="localCoupon" jdbcType="DECIMAL" />
		<result column="FULL_REDUCE" property="fullReduce" jdbcType="DECIMAL" />
		<result column="PAYER_AMOUNT" property="payerAmount" jdbcType="DECIMAL" />
		<result column="DIVIDED_AMOUNT" property="dividedAmount" jdbcType="DECIMAL" />
		<result column="DIVIDING_AMOUNT" property="dividingAmount" jdbcType="DECIMAL" />
		<result column="IS_TICKET" property="isTicket" jdbcType="VARCHAR" />
		<result column="COUPONS" property="coupons" jdbcType="VARCHAR" />
		<result column="PAYER_CASH_AMOUNT" property="payerCashAmount" jdbcType="DECIMAL" />
		<result column="TICKET_AMOUNT" property="ticketAmount" jdbcType="DECIMAL" />
	</resultMap>
	
	<resultMap id="PageResultMap"
		type="com.epaylinks.efps.common.util.page.PageResult">
	    <result column="total" property="total" jdbcType="DECIMAL" />
	    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
	    <result column="total_procedure_fee" property="totalProcedureFee" jdbcType="DECIMAL" />
	</resultMap>
	<sql id="Base_Column_List">
		ORDER_ID, TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE,COMMISSIONED_CUSTOMER_CODE,TARGET_CUSTOMER_CODE, TRANSACTION_TYPE, PAYER,
		PAYER_TYPE,
		PAYER_ID, TERMINAL_NO, CLIENT_IP, SRC_CHANNEL_TYPE, PAY_METHOD, AMOUNT,
		CURRENCY_TYPE,
		ORDER_INFO, NOTIFY_URL, REDIRECT_URL, ATTACH_DATA, TRANSACTION_START_TIME,
		TRANSACTION_END_TIME,
		CREATE_TIME, PAY_STATE, UPDATE_TIME, END_TIME, ERROR_CODE, REMARK,
		SETTLEMENT_STATE, PROCEDURE_FEE,
		ENCRY_RESULT,SETT_CYCLE_START_TIME,SETT_CYCLE_END_TIME, CUSTOMERNAME, CHANNEL_ORDER, CHANNEL_NAME,
		REFUND_FEE, REFUNDING_FEE, BUSINESS_CODE, WITHDRAW_TRANSACTION_NO, AGENT_CUSTOMERCODE, PAY_PASSWAY,
		AGENT_CUSTOMERNAME, REDIRECT_FAILURL, QUICKPAY_CARDNO, NO_CREDITCARDS, PAY_CHANNEL_ID,
		ACQ_ORG_CODE, ACQ_SP_ID, ORDER_TYPE, TERM_INFO, AREA_INFO, USER_ID, SUB_APP_ID, ERROR_MSG,
	    PLATFORM_CUSTOMER_CODE, STORE_ID, OPERATOR_ID, REQ_RESERVED, CUPS_REQ_RESERVED, PAYER_APP_NAME,
	    ACTUAL_PAY_AMOUNT, DISCOUNTABLE_AMOUNT, SETTLEMENT_AMOUNT, SOURCE_CHANNEL, QR_CODE_ISSUER,
	    PROCEDURE_RATE, RATE_MODE, CARD_NO_MOSAIC, CARD_NO_ENC, CARD_TYPE, max_profit,
	    CANCEL_STATE, CANCLE_NO, CANCEL_RETURN_MSG, CANCEL_UPDATE_TIME, CARD_OWNER, CARD_OWNER_MOSAIC, CERT_NO, CERT_NO_MOSAIC,
        CARD_OWNER_HASH, CERT_NO_HASH, SPLIT_RELATION_ID,BANK_CODE,
        CHANNEL_RESP_CODE, CHANNEL_RESP_MSG,TERMINAL_NAME,MEMBER_ID,SPLIT_MODEL,SUB_CUSTOMER_CODE,trade_customer_code,
        SPLIT_PROCEDURE_FEE,SPLIT_PROCEDURE_RATE, OPEN_ID, BUYER_LOGON_ID, BUYER_USER_ID, business_inst_id, sett_cycle_rule_code,SCENE,
        REQUEST_SRC,auth_customer_code,trade_source,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,max_fee,min_fee,
        NFC_TAG_ID, FEE_PER,
        D1_HOLIDAY_CHARGE_RATE, D1_HOLIDAY_CHARGE_PER, D1_HOIDAY_CHARGED, CHARGED_BY_BANK_CODE_AREA,fund_channel,cash_amount,coupon_amount,
        industry_code_type,industry_name,machine_code,
        BUSINESS_CODE_FST, BUSINESS_INST_ID_FST, SECOND_CHARGED,terminal_type,ENABLE_PAY_CHANNELS,INSTALMENTS_NUM,INSTALMENTS_SELLER_PERCENT,
        batch_no, SUB_OPEN_ID, ORI_TRANSACTION_NO, AUTH_FINISH_AMOUNT, AUTHING_AMOUNT, AUTH_CONFIRM_MODE, AUTH_STATUS,SETTLE_TYPE,
        IP_MODE, IP_PROCEDURE_FEE, IS_INSTALMENTS, IP_BUSINESS_INST_ID, IP_PROCEDURE_RATE,ext_user_info,
        PROCEDURE_CUSTOMERCODE, IS_ORDER_SPLIT, DELAY_DAYS,
        LOCAL_COUPON, FULL_REDUCE, PAYER_AMOUNT, DIVIDED_AMOUNT, DIVIDING_AMOUNT, IS_TICKET, COUPONS, PAYER_CASH_AMOUNT, TICKET_AMOUNT

	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.Long"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where ORDER_ID = #{orderId,jdbcType=DECIMAL}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from TXS_PRE_ORDER
		where ORDER_ID = #{orderId,jdbcType=DECIMAL}
	</delete>
	<insert id="insert"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		insert into TXS_PRE_ORDER (ORDER_ID, TRANSACTION_NO, OUT_TRADE_NO,
		CUSTOMER_CODE,COMMISSIONED_CUSTOMER_CODE,TARGET_CUSTOMER_CODE, TRANSACTION_TYPE, PAYER,
		PAYER_TYPE, PAYER_ID, TERMINAL_NO,
		CLIENT_IP, SRC_CHANNEL_TYPE, PAY_METHOD,
		AMOUNT, CURRENCY_TYPE, ORDER_INFO,
		NOTIFY_URL, REDIRECT_URL, ATTACH_DATA,
		TRANSACTION_START_TIME, TRANSACTION_END_TIME,
		CREATE_TIME, PAY_STATE, UPDATE_TIME,
		END_TIME, ERROR_CODE, REMARK, SETTLEMENT_STATE, PROCEDURE_FEE, ENCRY_RESULT,SETT_CYCLE_START_TIME,SETT_CYCLE_END_TIME
		, CUSTOMERNAME, CHANNEL_ORDER, CHANNEL_NAME, REFUND_FEE, REFUNDING_FEE, BUSINESS_CODE, WITHDRAW_TRANSACTION_NO,
		AGENT_CUSTOMERCODE, PAY_PASSWAY, AGENT_CUSTOMERNAME, REDIRECT_FAILURL, QUICKPAY_CARDNO, NO_CREDITCARDS, PAY_CHANNEL_ID,
		ACQ_ORG_CODE, ACQ_SP_ID, ORDER_TYPE, 
      	TERM_INFO, AREA_INFO, USER_ID, 
      	SUB_APP_ID, ERROR_MSG, PLATFORM_CUSTOMER_CODE, 
      	STORE_ID, OPERATOR_ID, REQ_RESERVED, 
      	CUPS_REQ_RESERVED, PAYER_APP_NAME, ACTUAL_PAY_AMOUNT, 
      	DISCOUNTABLE_AMOUNT, SETTLEMENT_AMOUNT, SOURCE_CHANNEL, QR_CODE_ISSUER, PROCEDURE_RATE, RATE_MODE,
      	CARD_NO_MOSAIC, CARD_NO_ENC, CARD_TYPE, max_profit, 
      	CANCEL_STATE, CANCLE_NO, CANCEL_RETURN_MSG, CANCEL_UPDATE_TIME, 
        CARD_OWNER, CARD_OWNER_MOSAIC, CERT_NO, 
        CERT_NO_MOSAIC, CARD_OWNER_HASH, CERT_NO_HASH, SPLIT_RELATION_ID,BANK_CODE,TERMINAL_NAME,MEMBER_ID,SPLIT_MODEL,SUB_CUSTOMER_CODE,trade_customer_code,
        SPLIT_PROCEDURE_FEE,SPLIT_PROCEDURE_RATE, 
        OPEN_ID, BUYER_LOGON_ID, BUYER_USER_ID,business_inst_id, sett_cycle_rule_code,SCENE, 
        REQUEST_SRC,auth_customer_code,trade_source,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,max_fee,min_fee, 
        NFC_TAG_ID, FEE_PER, 

        D1_HOLIDAY_CHARGE_RATE, D1_HOLIDAY_CHARGE_PER, D1_HOIDAY_CHARGED, CHARGED_BY_BANK_CODE_AREA,fund_channel,cash_amount,coupon_amount,
        industry_code_type,industry_name,machine_code,
        BUSINESS_CODE_FST, BUSINESS_INST_ID_FST, SECOND_CHARGED,terminal_type,
        PROCEDURE_CUSTOMERCODE)
		values (#{orderId,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR},
		#{outTradeNo,jdbcType=VARCHAR},
		#{customerCode,jdbcType=VARCHAR},
		#{commissionedCustomerCode,jdbcType=VARCHAR},
		#{targetCustomerCode,jdbcType=VARCHAR},
		 #{transactionType,jdbcType=VARCHAR}, #{payer,jdbcType=VARCHAR},
		#{payerType,jdbcType=VARCHAR}, #{payerId,jdbcType=VARCHAR},
		#{terminalNo,jdbcType=VARCHAR},
		#{clientIp,jdbcType=VARCHAR}, #{srcChannelType,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR},
		#{amount,jdbcType=DECIMAL}, #{currencyType,jdbcType=VARCHAR},
		#{orderInfo,jdbcType=VARCHAR},
		#{notifyUrl,jdbcType=VARCHAR}, #{redirectUrl,jdbcType=VARCHAR}, #{attachData,jdbcType=VARCHAR},
		#{transactionStartTime,jdbcType=TIMESTAMP},
		#{transactionEndTime,jdbcType=TIMESTAMP},
		#{createTime,jdbcType=TIMESTAMP}, #{payState,jdbcType=CHAR},
		#{updateTime,jdbcType=TIMESTAMP},
		#{endTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
		#{settlementState,jdbcType=VARCHAR},
		#{procedureFee,jdbcType=DECIMAL},#{encryResult,jdbcType=VARCHAR},
		#{settCycleStartTime,jdbcType=TIMESTAMP},#{settCycleEndTime,jdbcType=TIMESTAMP},
		#{customerName,jdbcType=VARCHAR},#{channelOrder,jdbcType=VARCHAR},#{channelName,jdbcType=VARCHAR},
		#{refundFee,jdbcType=DECIMAL}, #{refundingFee,jdbcType=DECIMAL}, #{businessCode,jdbcType=VARCHAR},
		#{withdrawTransactionNo,jdbcType=VARCHAR}, #{agentCustomerCode,jdbcType=VARCHAR}, #{payPassWay,jdbcType=VARCHAR},
		#{agentCustomerName,jdbcType=VARCHAR}, #{redirectFailUrl,jdbcType=VARCHAR}, #{quickpayCardno,jdbcType=VARCHAR},
		#{noCreditCards,jdbcType=VARCHAR},#{payChannelId,jdbcType=DECIMAL},
		#{acqOrgCode,jdbcType=VARCHAR}, #{acqSpId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      	#{termInfo,jdbcType=VARCHAR}, #{areaInfo,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      	#{subAppId,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR}, #{platformCustomerCode,jdbcType=VARCHAR}, 
      	#{storeId,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, #{reqReserved,jdbcType=VARCHAR}, 
      	#{cupsReqReserved,jdbcType=VARCHAR}, #{payerAppName,jdbcType=VARCHAR}, #{actualPayAmount,jdbcType=DECIMAL}, 
      	#{discountableAmount,jdbcType=DECIMAL}, #{settlementAmount,jdbcType=DECIMAL}, #{sourceChannel,jdbcType=VARCHAR},
      	#{qrCodeIssuer,jdbcType=VARCHAR},#{procedureRate,jdbcType=VARCHAR}, #{rateMode,jdbcType=DECIMAL},
      	#{cardNoMosaic,jdbcType=VARCHAR}, #{cardNoEnc,jdbcType=VARCHAR}, #{cardType,jdbcType=VARCHAR}, #{maxProfit,jdbcType=VARCHAR},
      	#{cancelState,jdbcType=VARCHAR},#{cancelNo,jdbcType=VARCHAR},#{cancelReturnMsg,jdbcType=VARCHAR},#{cancelUpdateTime,jdbcType=TIMESTAMP}, 
        #{cardOwner,jdbcType=VARCHAR}, #{cardOwnerMosaic,jdbcType=VARCHAR}, #{certNo,jdbcType=VARCHAR}, 
        #{certNoMosaic,jdbcType=VARCHAR}, #{cardOwnerHash,jdbcType=VARCHAR}, #{certNoHash,jdbcType=VARCHAR}, #{splitRelationId,jdbcType=VARCHAR},
        #{bankCode,jdbcType=VARCHAR}, #{channelRespCode,jdbcType=VARCHAR}, #{channelRespMsg,jdbcType=VARCHAR},
        #{terminalName,jdbcType=VARCHAR},#{memberId,jdbcType=VARCHAR},#{splitModel,jdbcType=VARCHAR},#{subCustomerCode,jdbcType=VARCHAR},
        #{tradeCustomerCode,jdbcType=VARCHAR},#{splitProcedureFee,jdbcType=DECIMAL},#{splitProcedureRate,jdbcType=VARCHAR}, 
        #{openId,jdbcType=VARCHAR}, #{buyerLogonId,jdbcType=VARCHAR},  #{buyerUserId,jdbcType=VARCHAR},
        #{businessInstId,jdbcType=VARCHAR},  #{settCycleRuleCode,jdbcType=VARCHAR}, #{scene,jdbcType=VARCHAR}, 
        #{requestSrc,jdbcType=VARCHAR},#{authCustomerCode,jdbcType=VARCHAR},#{tradeSource,jdbcType=VARCHAR},#{businessMan,jdbcType=VARCHAR},#{businessManId,jdbcType=DECIMAL},#{companyName,jdbcType=VARCHAR},#{companyId,jdbcType=DECIMAL},
        #{maxFee,jdbcType=DECIMAL},#{minFee,jdbcType=DECIMAL}, 
        #{nfcTagId,jdbcType=VARCHAR}, #{feePer,jdbcType=DECIMAL}, 
        #{D1HolidayChargeRate,jdbcType=VARCHAR},  #{D1HolidayChargePer,jdbcType=DECIMAL}, #{D1HolidayCharged,jdbcType=VARCHAR}, 
        #{chargedByBankCodeArea,jdbcType=VARCHAR},#{fundChannel,jdbcType=VARCHAR},#{cashAmount,jdbcType=DECIMAL},

        #{couponAmount,jdbcType=DECIMAL},#{industryCodeType,jdbcType=VARCHAR},#{industryName,jdbcType=VARCHAR},
        #{machineCode,jdbcType=VARCHAR},
        #{businessCodeFst,jdbcType=VARCHAR}, #{businessInstIdFst,jdbcType=VARCHAR}, #{secondCharged,jdbcType=VARCHAR},
        #{terminalType,jdbcType=VARCHAR},
        #{procedureCustomercode,jdbcType=VARCHAR})

	</insert>
	<insert id="insertSelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		insert into TXS_PRE_ORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="orderId != null">
				ORDER_ID,
			</if>
			<if test="transactionNo != null">
				TRANSACTION_NO,
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO,
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE,
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE,
			</if>
			<if test="targetCustomerCode != null">
				TARGET_CUSTOMER_CODE,
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE,
			</if>
			<if test="payer != null">
				PAYER,
			</if>
			<if test="payerType != null">
				PAYER_TYPE,
			</if>
			<if test="payerId != null">
				PAYER_ID,
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO,
			</if>
			<if test="clientIp != null">
				CLIENT_IP,
			</if>
			<if test="srcChannelType != null">
				SRC_CHANNEL_TYPE,
			</if>
			<if test="payMethod != null">
				PAY_METHOD,
			</if>
			<if test="amount != null">
				AMOUNT,
			</if>
			<if test="currencyType != null">
				CURRENCY_TYPE,
			</if>
			<if test="orderInfo != null">
				ORDER_INFO,
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL,
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL,
			</if>
			<if test="attachData != null">
				ATTACH_DATA,
			</if>
			<if test="transactionStartTime != null">
				TRANSACTION_START_TIME,
			</if>
			<if test="transactionEndTime != null">
				TRANSACTION_END_TIME,
			</if>
			<if test="createTime != null">
				CREATE_TIME,
			</if>
			<if test="payState != null">
				PAY_STATE,
			</if>
			<if test="updateTime != null">
				UPDATE_TIME,
			</if>
			<if test="endTime != null">
				END_TIME,
			</if>
			<if test="errorCode != null">
				ERROR_CODE,
			</if>
			<if test="remark != null">
				REMARK,
			</if>
			<if test="settlementState != null">
				SETTLEMENT_STATE,
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE,
			</if>
			<if test="encryResult != null">
				ENCRY_RESULT,
			</if>
			<if test="settCycleStartTime != null">
				SETT_CYCLE_START_TIME,
			</if>
			<if test="settCycleEndTime != null">
				SETT_CYCLE_END_TIME,
			</if>
			<if test="customerName != null">
				CUSTOMERNAME,
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER,
			</if>
			<if test="channelName != null">
				CHANNEL_NAME,
			</if>
			<if test="refundFee != null">
				REFUND_FEE,
			</if>
			<if test="refundingFee != null">
				REFUNDING_FEE,
			</if>
			<if test="businessCode != null">
       			BUSINESS_CODE,
      		</if>
      		<if test="withdrawTransactionNo != null">
       			WITHDRAW_TRANSACTION_NO,
      		</if>
      		<if test="agentCustomerCode != null">
       			AGENT_CUSTOMERCODE,
      		</if>
      		<if test="payPassWay != null">
       			PAY_PASSWAY,
      		</if>
      		<if test="agentCustomerName != null">
       			AGENT_CUSTOMERNAME,
      		</if>
      		<if test="redirectFailUrl != null">
       			REDIRECT_FAILURL,
      		</if>
      		<if test="quickpayCardno != null">
       			QUICKPAY_CARDNO,
      		</if>
      		<if test="noCreditCards != null">
       			NO_CREDITCARDS,
      		</if>
      		<if test="payChannelId != null">
				PAY_CHANNEL_ID,
			</if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE,
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID,
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE,
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO,
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO,
	      </if>
	      <if test="userId != null" >
	        USER_ID,
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID,
	      </if>
	      <if test="errorMsg != null" >
	        ERROR_MSG,
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE,
	      </if>
	      <if test="storeId != null" >
	        STORE_ID,
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID,
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED,
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED,
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME,
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT,
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT,
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT,
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL,
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER,
	      </if>
	      <if test="procedureRate != null" >
	        PROCEDURE_RATE,
	      </if>
	      <if test="rateMode != null" >
	        RATE_MODE,
	      </if>
	      <if test="cardNoMosaic != null" >
	        CARD_NO_MOSAIC,
	      </if>  
	      <if test="cardNoEnc != null" >
	        CARD_NO_ENC,
	      </if>  
	      <if test="cardType != null" >
	        CARD_TYPE,
	      </if> 
	      <if test="maxProfit != null" >
	        max_profit,
	      </if> 
	      <if test="cancelState != null" >
	        CANCEL_STATE,
	      </if> 
	      <if test="cancelNo != null" >
	        CANCLE_NO,
	      </if> 
	      <if test="cancelReturnMsg != null" >
	        CANCEL_RETURN_MSG,
	      </if> 
	      <if test="cancelUpdateTime != null" >
	        CANCEL_UPDATE_TIME,
	      </if>
	      <if test="cardOwner != null" >
	        CARD_OWNER,
	      </if>
	      <if test="cardOwnerMosaic != null" >
	        CARD_OWNER_MOSAIC,
	      </if>
	      <if test="certNo != null" >
	        CERT_NO,
	      </if>
	      <if test="certNoMosaic != null" >
	        CERT_NO_MOSAIC,
	      </if>
	      <if test="cardOwnerHash != null" >
	        CARD_OWNER_HASH,
	      </if>
	      <if test="certNoHash != null" >
	        CERT_NO_HASH,
	      </if> 
	      <if test="splitRelationId != null" >
	        SPLIT_RELATION_ID,
	      </if> 	      	      	      	      	      
	      <if test="bankCode != null" >
        	BANK_CODE,
          </if> 
	      <if test="channelRespCode != null" >
        	CHANNEL_RESP_CODE,
          </if> 
	      <if test="channelRespMsg != null" >
        	CHANNEL_RESP_MSG,
          </if>
          <if test="terminalName != null">
	      	TERMINAL_NAME,
	      </if>
          <if test="memberId != null">
				MEMBER_ID,
          </if>
          <if test="splitModel != null">
				SPLIT_MODEL,
          </if>
          <if test="subCustomerCode != null">
				SUB_CUSTOMER_CODE,
		  </if>
			<if test="tradeCustomerCode != null">
				trade_customer_code,
			</if>
			<if test="splitProcedureFee != null">
				SPLIT_PROCEDURE_FEE
			</if>
			<if test="splitProcedureRate != null">
				SPLIT_PROCEDURE_RATE
			</if>
	      <if test="openId != null" >
	        OPEN_ID,
	      </if>  
	      <if test="buyerLogonId != null" >
	        BUYER_LOGON_ID,
	      </if> 
	      <if test="buyerUserId != null" >
	        BUYER_USER_ID,
	      </if>	 
	       <if test="businessInstId != null" >
	        business_inst_id,
	      </if> 
	      <if test="settCycleRuleCode != null" >
	        sett_cycle_rule_code,
	      </if>
		<if test="scene != null" >
			SCENE,
		</if>
		<if test="requestSrc != null" >
			REQUEST_SRC,
		</if>
		<if test="authCustomerCode != null" >
			auth_customer_code,
		</if>	
		<if test="tradeSource != null" >
			trade_source,
		</if>
		<if test="businessMan != null" >
				BUSINESS_MAN,
		</if>
		<if test="businessManId != null" >
			BUSINESS_MAN_ID,
		</if>
		<if test="companyName != null" >
			COMPANY_NAME,
		</if>
		<if test="companyId != null" >
			COMPANY_ID,
		</if>
		<if test="maxFee != null" >
	            max_fee,
		</if>
		<if test="minFee != null" >
			min_fee,
		</if>
		<if test="nfcTagId != null" >
		  NFC_TAG_ID,
		</if>
		<if test="feePer != null" >
			FEE_PER,
		</if>
		<if test="D1HolidayChargeRate != null" >
			D1_HOLIDAY_CHARGE_RATE,
		</if>
		<if test="D1HolidayChargePer != null" >
			D1_HOLIDAY_CHARGE_PER,
		</if>
		<if test="D1HolidayCharged != null" >
			D1_HOIDAY_CHARGED,
		</if>	
		<if test="chargedByBankCodeArea != null" >
			CHARGED_BY_BANK_CODE_AREA,
		</if>	
		<if test="fundChannel != null" >
			fund_channel,
		</if>
			<if test="cashAmount != null" >
        cash_amount,
      </if> 
      <if test="couponAmount != null" >
        coupon_amount,
      </if>  	
      <if test="industryCodeType != null" >
        industry_code_type,
      </if>
      <if test="industryName != null" >
        industry_name,
      </if>
      <if test="machineCode != null" >
        machine_code,
      </if>					
      <if test="businessCodeFst != null" >
        BUSINESS_CODE_FST,
      </if> 
      <if test="businessInstIdFst != null" >
        BUSINESS_INST_ID_FST,
      </if> 
      <if test="secondCharged != null" >
        SECOND_CHARGED,
      </if>     
      <if test="terminalType != null" >
        terminal_type,
      </if>
		<if test="procedureCustomercode != null" >
			PROCEDURE_CUSTOMERCODE,
		</if>

		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="orderId != null">
				#{orderId,jdbcType=DECIMAL},
			</if>
			<if test="transactionNo != null">
				#{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				#{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				#{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				#{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="targetCustomerCode != null">
				#{targetCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				#{transactionType,jdbcType=VARCHAR},
			</if>
			<if test="payer != null">
				#{payer,jdbcType=VARCHAR},
			</if>
			<if test="payerType != null">
				#{payerType,jdbcType=VARCHAR},
			</if>
			<if test="payerId != null">
				#{payerId,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				#{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="clientIp != null">
				#{clientIp,jdbcType=VARCHAR},
			</if>
			<if test="srcChannelType != null">
				#{srcChannelType,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				#{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				#{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyType != null">
				#{currencyType,jdbcType=VARCHAR},
			</if>
			<if test="orderInfo != null">
				#{orderInfo,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				#{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				#{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="attachData != null">
				#{attachData,jdbcType=VARCHAR},
			</if>
			<if test="transactionStartTime != null">
				#{transactionStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionEndTime != null">
				#{transactionEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				#{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				#{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="errorCode != null">
				#{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="settlementState != null">
				#{settlementState,jdbcType=VARCHAR},
			</if>
			<if test="procedureFee != null">
				#{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="encryResult != null">
				#{encryResult,jdbcType=VARCHAR},
			</if>
			<if test="settCycleStartTime != null">
				#{settCycleStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="settCycleEndTime != null">
				#{settCycleEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="customerName != null">
				#{customerName,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				#{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				#{channelName,jdbcType=VARCHAR},
			</if>
			<if test="refundFee != null">
				#{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundingFee != null">
				#{refundingFee,jdbcType=DECIMAL},
			</if>
			<if test="businessCode != null">
       		    #{businessCode,jdbcType=VARCHAR},
      		</if>
      		<if test="withdrawTransactionNo != null">
				#{withdrawTransactionNo,jdbcType=VARCHAR},
			</if>
      		<if test="agentCustomerCode != null">
				#{agentCustomerCode,jdbcType=VARCHAR},
			</if>
      		<if test="payPassWay != null">
				#{payPassWay,jdbcType=VARCHAR},
			</if>
      		<if test="agentCustomerName != null">
				#{agentCustomerName,jdbcType=VARCHAR},
			</if>
      		<if test="redirectFailUrl != null">
				#{redirectFailUrl,jdbcType=VARCHAR},
			</if>
      		<if test="quickpayCardno != null">
				#{quickpayCardno,jdbcType=VARCHAR},
			</if>
      		<if test="noCreditCards != null">
				#{noCreditCards,jdbcType=VARCHAR},
			</if>
			<if test="payChannelId != null">
				#{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="acqOrgCode != null" >
	        #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="errorMsg != null" >
	        #{errorMsg,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	      <if test="procedureRate != null" >
	        #{procedureRate,jdbcType=VARCHAR},
	      </if>
	      <if test="rateMode != null" >
	        #{rateMode,jdbcType=DECIMAL},
	      </if>
		     <if test="cardNoMosaic != null" >
		      	#{cardNoMosaic,jdbcType=VARCHAR},
		    </if>
		    <if test="cardNoEnc != null" >
		      	#{cardNoEnc,jdbcType=VARCHAR},
		    </if>
		    <if test="cardType != null" >
		      	#{cardType,jdbcType=VARCHAR},
		    </if> 	
		    <if test="maxProfit != null" >
	       		#{maxProfit,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelState != null" >
	       		#{cancelState,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelNo != null" >
	       		#{cancelNo,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelReturnMsg != null" >
	       		#{cancelReturnMsg,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelUpdateTime != null" >
	       		#{cancelUpdateTime,jdbcType=TIMESTAMP},
	      	</if>
	      	<if test="cardOwner != null" >
	          #{cardOwner,jdbcType=VARCHAR},
	        </if>
	        <if test="cardOwnerMosaic != null" >
	          #{cardOwnerMosaic,jdbcType=VARCHAR},
	        </if>
	        <if test="certNo != null" >
	          #{certNo,jdbcType=VARCHAR},
	        </if>
	        <if test="certNoMosaic != null" >
	          #{certNoMosaic,jdbcType=VARCHAR},
	        </if>
	        <if test="cardOwnerHash != null" >
	          #{cardOwnerHash,jdbcType=VARCHAR},
	        </if>
	        <if test="certNoHash != null" >
	          #{certNoHash,jdbcType=VARCHAR},
	        </if> 
	        <if test="splitRelationId != null" >
	          #{splitRelationId,jdbcType=VARCHAR},
	        </if> 	      		      		      		      	      
	        <if test="bankCode != null" >
	          #{bankCode,jdbcType=VARCHAR},
	        </if> 	  
	        <if test="channelRespCode != null" >
	          #{channelRespCode,jdbcType=VARCHAR},
	        </if> 
	        <if test="channelRespMsg != null" >
	          #{channelRespMsg,jdbcType=VARCHAR},
	        </if>
	        <if test="terminalName != null">
	      	#{terminalName,jdbcType=VARCHAR},
	   	   </if>
			<if test="memberId != null">
				#{memberId,jdbcType=VARCHAR},
			</if>
			<if test="splitModel != null">
				#{splitModel,jdbcType=VARCHAR},
			</if>
			<if test="subCustomerCode != null">
				#{subCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeCustomerCode != null">
				#{tradeCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="splitProcedureFee != null">
				#{splitProcedureFee,jdbcType=DECIMAL},
			</if>
			<if test="splitProcedureRate != null">
				#{splitProcedureRate,jdbcType=VARCHAR},
			</if>
	      <if test="openId != null" >
	        #{openId,jdbcType=VARCHAR},
	      </if>  
	      <if test="buyerLogonId != null" >
	        #{buyerLogonId,jdbcType=VARCHAR},
	      </if> 
	      <if test="buyerUserId != null" >
	        #{buyerUserId,jdbcType=VARCHAR},
	      </if> 
	       <if test="businessInstId != null" >
	        #{businessInstId,jdbcType=VARCHAR},
	      </if> 
	      <if test="settCycleRuleCode != null" >
	        #{settCycleRuleCode,jdbcType=VARCHAR},
	      </if>
			<if test="scene != null" >
				#{scene,jdbcType=VARCHAR},
			</if>
			<if test="requestSrc != null" >
				#{requestSrc,jdbcType=VARCHAR},
			</if>	
			<if test="authCustomerCode != null" >
			#{authCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeSource != null" >
			#{tradeSource,jdbcType=VARCHAR},
			</if>
			<if test="businessMan != null" >
				#{businessMan,jdbcType=VARCHAR},
			</if>
			<if test="businessManId != null" >
				#{businessManId,jdbcType=DECIMAL},
			</if>
			<if test="companyName != null" >
				#{companyName,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				#{companyId,jdbcType=DECIMAL},
			</if>
			  <if test="maxFee != null" >
		            #{maxFee,jdbcType=DECIMAL},
		      </if>
		      <if test="minFee != null" >
		            #{minFee,jdbcType=DECIMAL},
		      </if>
   			<if test="nfcTagId != null">
				#{nfcTagId,jdbcType=VARCHAR},
			</if>
			<if test="feePer != null">
				#{feePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayChargeRate != null">
				#{D1HolidayChargeRate,jdbcType=VARCHAR},
			</if>
			<if test="D1HolidayChargePer != null">
				#{D1HolidayChargePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayCharged != null">
				#{D1HolidayCharged,jdbcType=VARCHAR},
			</if>	
			<if test="chargedByBankCodeArea != null">
				#{chargedByBankCodeArea,jdbcType=VARCHAR},
			</if>
			<if test="fundChannel != null" >
				#{fundChannel,jdbcType=VARCHAR},
			</if>
		  <if test="cashAmount != null" >
	        #{cashAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="couponAmount != null" >
	        #{couponAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="industryCodeType != null" >
	        #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        #{machineCode,jdbcType=VARCHAR},
	      </if>													

			<if test="businessCodeFst != null" >
				#{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				#{businessInstIdFst,jdbcType=VARCHAR},
			</if>
			<if test="secondCharged != null" >
				#{secondCharged,jdbcType=VARCHAR},
			</if>	
			<if test="terminalType != null" >
	        #{terminalType,jdbcType=VARCHAR},
	      </if>
			<if test="procedureCustomercode != null" >
				#{procedureCustomercode,jdbcType=VARCHAR},
			</if>

		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="targetCustomerCode != null">
				TARGET_CUSTOMER_CODE  = #{targetCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
			</if>
			<if test="payer != null">
				PAYER = #{payer,jdbcType=VARCHAR},
			</if>
			<if test="payerType != null">
				PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
			</if>
			<if test="payerId != null">
				PAYER_ID = #{payerId,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="clientIp != null">
				CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
			</if>
			<if test="srcChannelType != null">
				SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyType != null">
				CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
			</if>
			<if test="orderInfo != null">
				ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="attachData != null">
				ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
			</if>
			<if test="transactionStartTime != null">
				TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionEndTime != null">
				TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="settlementState != null">
				SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="encryResult != null">
				ENCRY_RESULT = #{encryResult,jdbcType=VARCHAR},
			</if>
			<if test="settCycleStartTime != null">
				SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="settCycleEndTime != null">
				SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
			</if>
			<if test="refundFee != null">
				REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundingFee != null">
				REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
			</if>
			<if test="businessCode != null">
                BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="withdrawTransactionNo != null">
				WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
			</if>
            <if test="payPassWay != null">
				PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
			</if>
            <if test="agentCustomerCode != null">
				AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
			</if>
            <if test="agentCustomerName != null">
				AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
			</if>
            <if test="redirectFailUrl != null">
				REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
			</if>
            <if test="quickpayCardno != null">
				QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
			</if>
            <if test="noCreditCards != null">
				NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
			</if>
			<if test="payChannelId != null">
				PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        USER_ID = #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="errorMsg != null" >
	        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        STORE_ID = #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	      <if test="procedureRate != null" >
	        PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
	      </if>
	      <if test="rateMode != null" >
	        RATE_MODE = #{rateMode,jdbcType=DECIMAL},
	      </if>
		     <if test="cardNoMosaic != null" >
		      	CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
		    </if>
		    <if test="cardNoEnc != null" >
		      	CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
		    </if>
		    <if test="cardType != null" >
		      	CARD_TYPE = #{cardType,jdbcType=VARCHAR},
		    </if>
		    <if test="maxProfit != null" >
	       		max_profit = #{maxProfit,jdbcType=VARCHAR},
	      	</if>  	    
		    <if test="cancelState != null" >
	       		CANCEL_STATE = #{cancelState,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelNo != null" >
	       		CANCLE_NO = #{cancelNo,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelReturnMsg != null" >
	       		CANCEL_RETURN_MSG = #{cancelReturnMsg,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelUpdateTime != null" >
	       		CANCEL_UPDATE_TIME = #{cancelUpdateTime,jdbcType=TIMESTAMP},
	      	</if>
	      	<if test="cardOwner != null" >
		        CARD_OWNER = #{cardOwner,jdbcType=VARCHAR},
		      </if>
		      <if test="cardOwnerMosaic != null" >
		        CARD_OWNER_MOSAIC = #{cardOwnerMosaic,jdbcType=VARCHAR},
		      </if>
		      <if test="certNo != null" >
		        CERT_NO = #{certNo,jdbcType=VARCHAR},
		      </if>
		      <if test="certNoMosaic != null" >
		        CERT_NO_MOSAIC = #{certNoMosaic,jdbcType=VARCHAR},
		      </if>
		      <if test="cardOwnerHash != null" >
		        CARD_OWNER_HASH = #{cardOwnerHash,jdbcType=VARCHAR},
		      </if>
		      <if test="certNoHash != null" >
		        CERT_NO_HASH = #{certNoHash,jdbcType=VARCHAR},
		      </if> 
		      <if test="splitRelationId != null" >
	           SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
	          </if> 	      		      		      		      	  
		      <if test="bankCode != null" >
		        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
		      </if> 	
		      <if test="channelRespCode != null" >
		        CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
		      </if> 
		      <if test="channelRespMsg != null" >
		        CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
		      </if>
		      <if test="terminalName != null">
		      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
		   	   </if>
			<if test="memberId != null">
				MEMBER_ID = #{memberId,jdbcType=VARCHAR},
			</if>
			<if test="splitModel != null">
				SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
			</if>
			<if test="subCustomerCode != null">
				SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeCustomerCode != null">
				trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="splitProcedureFee != null">
				SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
			</if>
			<if test="splitProcedureRate != null">
				SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR},
			</if>
		      <if test="openId != null" >
		        OPEN_ID = #{openId,jdbcType=VARCHAR},
		      </if> 
		      <if test="buyerLogonId != null" >
		        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
		      </if> 	
		      <if test="buyerUserId != null" >
		        BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
		      </if> 
		       <if test="businessInstId != null" >
		        business_inst_id = #{businessInstId,jdbcType=VARCHAR},
		      </if> 
		      <if test="settCycleRuleCode != null" >
		        sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
		      </if>
			<if test="scene != null" >
				SCENE = #{scene,jdbcType=VARCHAR},
			</if>
			<if test="requestSrc != null" >
				REQUEST_SRC = #{requestSrc,jdbcType=VARCHAR},
			</if>
			<if test="authCustomerCode != null" >
			auth_customer_code = #{authCustomerCode,jdbcType=VARCHAR},
			</if>	
			<if test="tradeSource != null" >
			trade_source = #{tradeSource,jdbcType=VARCHAR},
			</if>
			<if test="businessMan != null" >
				BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
			</if>
			<if test="businessManId != null" >
				BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
			</if>
			<if test="companyName != null" >
				COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=DECIMAL},
			</if>
			  <if test="maxFee != null" >
		            max_fee = #{maxFee,jdbcType=DECIMAL},
		      </if>
		      <if test="minFee != null" >
		            min_fee = #{minFee,jdbcType=DECIMAL},
		      </if>
			<if test="nfcTagId != null">
				NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
			</if>
			<if test="feePer != null">
				FEE_PER = #{feePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayChargeRate != null">
				D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
			</if>
			<if test="D1HolidayChargePer != null">
				D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayCharged != null">
				D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
			</if>	
			<if test="chargedByBankCodeArea != null">
				CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
			</if>		
			<if test="fundChannel != null" >
				fund_channel = #{fundChannel,jdbcType=VARCHAR},
			</if>			
			<if test="cashAmount != null" >
	        cash_amount = #{cashAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="couponAmount != null" >
	        coupon_amount = #{couponAmount,jdbcType=DECIMAL},

	      </if>	
	      <if test="industryCodeType != null" >
	        industry_code_type = #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        industry_name = #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        machine_code = #{machineCode,jdbcType=VARCHAR},
	      </if>								

			<if test="businessCodeFst != null" >
				BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
			</if>
			<if test="secondCharged != null" >
				SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
			</if>	
			<if test="terminalType != null" >
	        terminal_type = #{terminalType,jdbcType=VARCHAR},
	      </if>
			<if test="procedureCustomercode != null" >
				PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR},
			</if>

		</set>
		where ORDER_ID = #{orderId,jdbcType=DECIMAL}
	</update>
	<update id="updateByPrimaryKey"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
		OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
		COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
		TARGET_CUSTOMER_CODE  = #{targetCustomerCode,jdbcType=VARCHAR},
		TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
		PAYER = #{payer,jdbcType=VARCHAR},
		PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
		PAYER_ID = #{payerId,jdbcType=VARCHAR},
		TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
		CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
		SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
		PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
		AMOUNT = #{amount,jdbcType=DECIMAL},
		CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
		ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
		NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
		REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
		ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
		TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
		TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
		CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
		PAY_STATE = #{payState,jdbcType=CHAR},
		UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
		END_TIME = #{endTime,jdbcType=TIMESTAMP},
		ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
		REMARK = #{remark,jdbcType=VARCHAR},
		SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
		PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
		ENCRY_RESULT = #{encryResult,jdbcType=VARCHAR},
		SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
		SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
		CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
		CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
		CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
		REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
		REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
		BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
		WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
		PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
		AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
		AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
		REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
		QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
		NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
		PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
		ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      USER_ID = #{userId,jdbcType=VARCHAR},
	      SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
	      PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      STORE_ID = #{storeId,jdbcType=VARCHAR},
	      OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
      	  RATE_MODE = #{rateMode,jdbcType=DECIMAL},
      CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
      CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      CARD_TYPE = #{cardType,jdbcType=VARCHAR},
      max_profit = #{maxProfit,jdbcType=VARCHAR},
      CANCEL_STATE = #{cancelState,jdbcType=VARCHAR},
      CANCLE_NO = #{cancelNo,jdbcType=VARCHAR},
      CANCEL_RETURN_MSG = #{cancelReturnMsg,jdbcType=VARCHAR},
      CANCEL_UPDATE_TIME = #{cancelUpdateTime,jdbcType=TIMESTAMP},
      CARD_OWNER = #{cardOwner,jdbcType=VARCHAR},
      CARD_OWNER_MOSAIC = #{cardOwnerMosaic,jdbcType=VARCHAR},
      CERT_NO = #{certNo,jdbcType=VARCHAR},
      CERT_NO_MOSAIC = #{certNoMosaic,jdbcType=VARCHAR},
      CARD_OWNER_HASH = #{cardOwnerHash,jdbcType=VARCHAR},
      CERT_NO_HASH = #{certNoHash,jdbcType=VARCHAR},
      SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
      BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
      CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
      TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
	  MEMBER_ID = #{memberId,jdbcType=VARCHAR},
	  SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
	  SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
	  trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
	  SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
	  SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR}, 
      OPEN_ID = #{openId,jdbcType=VARCHAR},
      BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
      BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
      business_inst_id = #{businessInstId,jdbcType=VARCHAR},
      sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
      SCENE = #{scene,jdbcType=VARCHAR},
      REQUEST_SRC = #{requestSrc,jdbcType=VARCHAR},
      auth_customer_code = #{authCustomerCode,jdbcType=VARCHAR},
      trade_source = #{tradeSource,jdbcType=VARCHAR},
      BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
	  BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
	  COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
	  COMPANY_ID = #{companyId,jdbcType=DECIMAL},
	  max_fee = #{maxFee,jdbcType=DECIMAL},
	  min_fee = #{minFee,jdbcType=DECIMAL},
	  NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
	  FEE_PER = #{feePer,jdbcType=DECIMAL},
	  D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
	  D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
	  D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
	  CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
	  fund_channel = #{fundChannel,jdbcType=VARCHAR},
	  cash_amount = #{cashAmount,jdbcType=DECIMAL},

	  coupon_amount = #{couponAmount,jdbcType=DECIMAL},
	  industry_code_type = #{industryCodeType,jdbcType=VARCHAR},	 
	   industry_name = #{industryName,jdbcType=VARCHAR},
	    machine_code = #{machineCode,jdbcType=VARCHAR}, 	  	  

	  BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
	  BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
	  SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
	  terminal_type = #{terminalType,jdbcType=VARCHAR},
	  PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR}

	  where ORDER_ID = #{orderId,jdbcType=DECIMAL}
	</update>
	<update id="updateByPrimaryKeySelectiveForTradeOrderSuccess"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="targetCustomerCode != null">
				TARGET_CUSTOMER_CODE  = #{targetCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
			</if>
			<if test="payer != null">
				PAYER = #{payer,jdbcType=VARCHAR},
			</if>
			<if test="payerType != null">
				PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
			</if>
			<if test="payerId != null">
				PAYER_ID = #{payerId,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="clientIp != null">
				CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
			</if>
			<if test="srcChannelType != null">
				SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyType != null">
				CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
			</if>
			<if test="orderInfo != null">
				ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="attachData != null">
				ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
			</if>
			<if test="transactionStartTime != null">
				TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionEndTime != null">
				TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="settlementState != null">
				SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="encryResult != null">
				ENCRY_RESULT = #{encryResult,jdbcType=VARCHAR},
			</if>
			<if test="settCycleStartTime != null">
				SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="settCycleEndTime != null">
				SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
			</if>
			<if test="refundFee != null">
				REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundingFee != null">
				REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="withdrawTransactionNo != null">
				WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="payPassWay != null">
				PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerCode != null">
				AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerName != null">
				AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
			</if>
			<if test="redirectFailUrl != null">
				REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
			</if>
			<if test="quickpayCardno != null">
				QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
			</if>
			<if test="noCreditCards != null">
				NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
			</if>
			<if test="payChannelId != null">
				PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        USER_ID = #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="errorMsg != null" >
	        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        STORE_ID = #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	     <if test="procedureRate != null" >
	       PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
	    </if>	      
	     <if test="rateMode != null" >
	       RATE_MODE = #{rateMode,jdbcType=DECIMAL},
	    </if>   	      
	     <if test="cardNoMosaic != null" >
	       CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
	    </if>
	    <if test="cardNoEnc != null" >
	      	CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
	    </if>
	    <if test="cardType != null" >
	      	CARD_TYPE = #{cardType,jdbcType=VARCHAR},
	    </if> 	
	    <if test="splitRelationId != null" >
	        SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
	    </if>  
	    <if test="bankCode != null" >
        	BANK_CODE = #{bankCode,jdbcType=VARCHAR},
         </if> 
	    <if test="channelRespCode != null" >
	        CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
	    </if> 
	    <if test="channelRespMsg != null" >
	        CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
	    </if>
	    <if test="terminalName != null">
	      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
	   	   </if>
			<if test="memberId != null">
				MEMBER_ID = #{memberId,jdbcType=VARCHAR},
			</if>
			<if test="splitModel != null">
				SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
			</if>
			<if test="subCustomerCode != null">
				SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeCustomerCode != null">
				trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="splitProcedureFee != null">
				SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
			</if>
			<if test="splitProcedureRate != null">
				SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR},
			</if>
		      <if test="openId != null" >
		        OPEN_ID = #{openId,jdbcType=VARCHAR},
		      </if> 
		      <if test="buyerLogonId != null" >
		        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
		      </if>  		
		      <if test="buyerUserId != null" >
		        BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
		      </if>		
		       <if test="businessInstId != null" >
		        business_inst_id = #{businessInstId,jdbcType=VARCHAR},
		      </if> 
		      <if test="settCycleRuleCode != null" >
		        sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
		      </if>
			<if test="scene != null" >
				SCENE = #{scene,jdbcType=VARCHAR},
			</if>
			<if test="requestSrc != null" >
				REQUEST_SRC = #{requestSrc,jdbcType=VARCHAR},
			</if>	
			 <if test="authCustomerCode != null" >
		        auth_customer_code =  #{authCustomerCode,jdbcType=VARCHAR},
		      </if>	
		      <if test="tradeSource != null" >
			trade_source = #{tradeSource,jdbcType=VARCHAR},
			</if>
			<if test="businessMan != null" >
				BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
			</if>
			<if test="businessManId != null" >
				BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
			</if>
			<if test="companyName != null" >
				COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=DECIMAL},
			</if>
			<if test="maxFee != null" >
            		max_fee = #{maxFee,jdbcType=DECIMAL},
		      </if>
		      <if test="minFee != null" >
		            min_fee = #{minFee,jdbcType=DECIMAL},
		      </if>
			<if test="nfcTagId != null" >
				NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
			</if>
			<if test="feePer != null" >
				FEE_PER = #{feePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayChargeRate != null" >
				D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
			</if>
			<if test="D1HolidayChargePer != null" >
				D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayCharged != null" >
				D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
			</if>	
			<if test="chargedByBankCodeArea != null" >
				CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
			</if>
			<if test="fundChannel != null" >
				fund_channel = #{fundChannel,jdbcType=VARCHAR},
			</if>	
		  <if test="cashAmount != null" >
	        cash_amount = #{cashAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="couponAmount != null" >
	        coupon_amount = #{couponAmount,jdbcType=DECIMAL},
	      </if>	
	      <if test="industryCodeType != null" >
	        industry_code_type = #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        industry_name = #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        machine_code = #{machineCode,jdbcType=VARCHAR},
	      </if>											

			<if test="businessCodeFst != null" >
				BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
			</if>	
			<if test="businessInstIdFst != null" >
				BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
			</if>	
			<if test="secondCharged != null" >
				SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
			</if>	
			<if test="terminalType != null" >
	        terminal_type = #{terminalType,jdbcType=VARCHAR},
	      </if>
			<if test="procedureCustomercode != null" >
				PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR},
			</if>

		</set>
		where ORDER_ID = #{orderId,jdbcType=DECIMAL}
		and PAY_STATE <![CDATA[ <> ]]> '1'
		and PAY_STATE <![CDATA[ <> ]]> '2'
	</update>
	
	<update id="updateFinnalStateForUnCashier"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="targetCustomerCode != null">
				TARGET_CUSTOMER_CODE  = #{targetCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
			</if>
			<if test="payer != null">
				PAYER = #{payer,jdbcType=VARCHAR},
			</if>
			<if test="payerType != null">
				PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
			</if>
			<if test="payerId != null">
				PAYER_ID = #{payerId,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="clientIp != null">
				CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
			</if>
			<if test="srcChannelType != null">
				SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyType != null">
				CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
			</if>
			<if test="orderInfo != null">
				ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="attachData != null">
				ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
			</if>
			<if test="transactionStartTime != null">
				TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionEndTime != null">
				TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="settlementState != null">
				SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="encryResult != null">
				ENCRY_RESULT = #{encryResult,jdbcType=VARCHAR},
			</if>
			<if test="settCycleStartTime != null">
				SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="settCycleEndTime != null">
				SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
			</if>
			<if test="refundFee != null">
				REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundingFee != null">
				REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="withdrawTransactionNo != null">
				WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="payPassWay != null">
				PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerCode != null">
				AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerName != null">
				AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
			</if>
			<if test="redirectFailUrl != null">
				REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
			</if>
			<if test="quickpayCardno != null">
				QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
			</if>
			<if test="noCreditCards != null">
				NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
			</if>
			<if test="payChannelId != null">
				PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        USER_ID = #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="errorMsg != null" >
	        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        STORE_ID = #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	     <if test="cardNoMosaic != null" >
	       CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
	    </if>
	    <if test="cardNoEnc != null" >
	      	CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
	    </if>
	    <if test="cardType != null" >
	      	CARD_TYPE = #{cardType,jdbcType=VARCHAR},
	    </if> 	
	    <if test="splitRelationId != null" >
	        SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
	    </if>  
	    <if test="channelRespCode != null" >
	        CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
	    </if> 
	    <if test="channelRespMsg != null" >
	        CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
	    </if>
	    <if test="terminalName != null">
	      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
	   	   </if>
			<if test="memberId != null">
				MEMBER_ID = #{memberId,jdbcType=VARCHAR},
			</if>
			<if test="splitModel != null">
				SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
			</if>
			<if test="subCustomerCode != null">
				SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeCustomerCode != null">
				trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="splitProcedureFee != null">
				SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
			</if>
			<if test="splitProcedureRate != null">
				SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR},
			</if>
		      <if test="openId != null" >
		        OPEN_ID = #{openId,jdbcType=VARCHAR},
		      </if> 
		      <if test="buyerLogonId != null" >
		        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
		      </if>  		
		      <if test="buyerUserId != null" >
		        BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
		      </if>		
		       <if test="businessInstId != null" >
		        business_inst_id = #{businessInstId,jdbcType=VARCHAR},
		      </if> 
		      <if test="settCycleRuleCode != null" >
		        sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
		      </if>
			<if test="scene != null" >
				SCENE = #{scene,jdbcType=VARCHAR},
			</if>
			<if test="requestSrc != null" >
				REQUEST_SRC = #{requestSrc,jdbcType=VARCHAR},
			</if>	
			 <if test="authCustomerCode != null" >
		        auth_customer_code =  #{authCustomerCode,jdbcType=VARCHAR},
		      </if>	
		      <if test="tradeSource != null" >
			trade_source = #{tradeSource,jdbcType=VARCHAR},
			</if>	
			 <if test="maxFee != null" >
            		max_fee = #{maxFee,jdbcType=DECIMAL},
		      </if>
		      <if test="minFee != null" >
		            min_fee = #{minFee,jdbcType=DECIMAL},
		      </if>
		      <if test="nfcTagId != null" >
			NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
			</if>
			<if test="feePer != null" >
				FEE_PER = #{feePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayChargeRate != null" >
				D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
			</if>
			<if test="D1HolidayChargePer != null" >
				D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayCharged != null" >
				D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
			</if>		
			<if test="chargedByBankCodeArea != null" >
				CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
			</if>	
			<if test="fundChannel != null" >
				fund_channel = #{fundChannel,jdbcType=VARCHAR},
			</if>
		  <if test="cashAmount != null" >
	        cash_amount = #{cashAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="couponAmount != null" >
	        coupon_amount = #{couponAmount,jdbcType=DECIMAL},

	      </if>
	      <if test="industryCodeType != null" >
	        industry_code_type = #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        industry_name = #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        machine_code = #{machineCode,jdbcType=VARCHAR},
	      </if>				

			<if test="businessCodeFst != null" >
				BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
			</if>	
			<if test="secondCharged != null" >
				SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
			</if>
			<if test="terminalType != null" >
	        terminal_type = #{terminalType,jdbcType=VARCHAR},
	      </if>
			<if test="procedureCustomercode != null" >
				PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR},
			</if>

		</set>
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			and	CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			and request_src='0'
		and PAY_STATE <![CDATA[ <> ]]> '1'
		and PAY_STATE <![CDATA[ <> ]]> '2'
	</update>
	<update id="updateByPrimaryKeySelectiveForRepeatRequest"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
			</if>
			<if test="payer != null">
				PAYER = #{payer,jdbcType=VARCHAR},
			</if>
			<if test="payerType != null">
				PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
			</if>
			<if test="payerId != null">
				PAYER_ID = #{payerId,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="clientIp != null">
				CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
			</if>
			<if test="srcChannelType != null">
				SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyType != null">
				CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
			</if>
			<if test="orderInfo != null">
				ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="attachData != null">
				ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
			</if>
			<if test="transactionStartTime != null">
				TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionEndTime != null">
				TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="settlementState != null">
				SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="encryResult != null">
				ENCRY_RESULT = #{encryResult,jdbcType=VARCHAR},
			</if>
			<if test="settCycleStartTime != null">
				SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="settCycleEndTime != null">
				SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
			</if>
			<if test="refundFee != null">
				REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundingFee != null">
				REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="withdrawTransactionNo != null">
				WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="payPassWay != null">
				PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerCode != null">
				AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerName != null">
				AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
			</if>
			<if test="redirectFailUrl != null">
				REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
			</if>
			<if test="quickpayCardno != null">
				QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
			</if>
			<if test="noCreditCards != null">
				NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
			</if>
			<if test="payChannelId != null">
				PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        USER_ID = #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="errorMsg != null" >
	        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        STORE_ID = #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	      <if test="splitRelationId != null" >
	        SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
	    </if>  
	    <if test="channelRespCode != null" >
	        CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
	    </if> 
	    <if test="channelRespMsg != null" >
	        CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
	    </if>
	    <if test="terminalName != null">
	      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
	   	   </if>
			<if test="memberId != null">
				MEMBER_ID = #{memberId,jdbcType=VARCHAR},
			</if>
			<if test="splitModel != null">
				SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
			</if>
			<if test="subCustomerCode != null">
				SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeCustomerCode != null">
				trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="splitProcedureFee != null">
				SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
			</if>
			<if test="splitProcedureRate != null">
				SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR},
			</if>
		      <if test="openId != null" >
		        OPEN_ID = #{openId,jdbcType=VARCHAR},
		      </if> 
		      <if test="buyerLogonId != null" >
		        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
		      </if>  
		      <if test="buyerUserId != null" >
		        BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
		      </if> 	
		        <if test="businessInstId != null" >
		        business_inst_id = #{businessInstId,jdbcType=VARCHAR},
		      </if> 
		      <if test="settCycleRuleCode != null" >
		        sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
		      </if>
			<if test="scene != null" >
				SCENE = #{scene,jdbcType=VARCHAR},
			</if>
			<if test="requestSrc != null" >
				REQUEST_SRC = #{requestSrc,jdbcType=VARCHAR},
			</if>	
			 <if test="authCustomerCode != null" >
		        auth_customer_code =  #{authCustomerCode,jdbcType=VARCHAR},
		      </if>		
		      <if test="tradeSource != null" >
			trade_source = #{tradeSource,jdbcType=VARCHAR},
			</if>
			<if test="businessMan != null" >
				BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
			</if>
			<if test="businessManId != null" >
				BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
			</if>
			<if test="companyName != null" >
				COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=DECIMAL},
			</if>
			<if test="maxFee != null" >
            		max_fee = #{maxFee,jdbcType=DECIMAL},
		      </if>
		      <if test="minFee != null" >
		            min_fee = #{minFee,jdbcType=DECIMAL},
		      </if>
			<if test="nfcTagId != null" >
				NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
			</if>
			<if test="feePer != null" >
				FEE_PER = #{feePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayChargeRate != null" >
				D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
			</if>
			<if test="D1HolidayChargePer != null" >
				D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayCharged != null" >
				D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
			</if>	
			<if test="chargedByBankCodeArea != null" >
				CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
			</if>	
			<if test="fundChannel != null" >
				fund_channel = #{fundChannel,jdbcType=VARCHAR},
			</if>
		  <if test="cashAmount != null" >
	        cash_amount = #{cashAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="couponAmount != null" >
	        coupon_amount = #{couponAmount,jdbcType=DECIMAL},

	      </if>	
	      <if test="industryCodeType != null" >
	        industry_code_type = #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        industry_name = #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        machine_code = #{machineCode,jdbcType=VARCHAR},
	      </if>				

			<if test="businessCodeFst != null" >
				BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
			</if>	
			<if test="secondCharged != null" >
				SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
			</if>
			<if test="terminalType != null" >
	        terminal_type = #{terminalType,jdbcType=VARCHAR},
	      </if>
			<if test="procedureCustomercode != null" >
				PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR},
			</if>

		</set>
		where ORDER_ID = #{orderId,jdbcType=DECIMAL}
		and PAY_STATE <![CDATA[ <> ]]> '1'
	</update>
	<select id="selectByTransactionNo" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	</select>
	<select id="selectByParam" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} and
		TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
	</select>
	<select id="selectByOutTradeNoAndCustomerCode" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>
	<select id="selectCountSuccessOrFail" resultType="java.lang.Integer">
		select
		count(*)
		from TXS_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
		and (PAY_STATE = '1' or PAY_STATE = '2')
	</select>	
	<update id="updateStateByOutTradeNoAndCustomerCode"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="targetCustomerCode != null">
				TARGET_CUSTOMER_CODE  = #{targetCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
			</if>
			<if test="payer != null">
				PAYER = #{payer,jdbcType=VARCHAR},
			</if>
			<if test="payerType != null">
				PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
			</if>
			<if test="payerId != null">
				PAYER_ID = #{payerId,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="clientIp != null">
				CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
			</if>
			<if test="srcChannelType != null">
				SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyType != null">
				CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
			</if>
			<if test="orderInfo != null">
				ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="attachData != null">
				ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
			</if>
			<if test="transactionStartTime != null">
				TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionEndTime != null">
				TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="settlementState != null">
				SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="encryResult != null">
				ENCRY_RESULT = #{encryResult,jdbcType=VARCHAR},
			</if>
			<if test="settCycleStartTime != null">
				SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
			</if>
			<if test="settCycleEndTime != null">
				SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
			</if>
			<if test="refundFee != null">
				REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
			</if>
			<if test="refundingFee != null">
				REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="withdrawTransactionNo != null">
				WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="payPassWay != null">
				PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerCode != null">
				AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="agentCustomerName != null">
				AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
			</if>
			<if test="redirectFailUrl != null">
				REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
			</if>
			<if test="quickpayCardno != null">
				QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
			</if>
			<if test="noCreditCards != null">
				NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
			</if>
			<if test="payChannelId != null">
				PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        USER_ID = #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="errorMsg != null" >
	        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        STORE_ID = #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	      <if test="splitRelationId != null" >
	        SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
	    </if>  
	    <if test="channelRespCode != null" >
	        CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
	    </if> 
	    <if test="channelRespMsg != null" >
	        CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
	    </if>
	    <if test="terminalName != null">
		      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
		   	   </if>
			<if test="memberId != null">
				MEMBER_ID = #{memberId,jdbcType=VARCHAR},
			</if>
			<if test="splitModel != null">
				SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
			</if>
			<if test="subCustomerCode != null">
				SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeCustomerCode != null">
				trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="splitProcedureFee != null">
				SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
			</if>
			<if test="splitProcedureRate != null">
				SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR},
			</if>
			<if test="openId != null" >
		        OPEN_ID = #{openId,jdbcType=VARCHAR},
		      </if> 
		      <if test="buyerLogonId != null" >
		        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
		      </if>  
		      <if test="buyerUserId != null" >
		        BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
		      </if> 
		        <if test="businessInstId != null" >
		        business_inst_id = #{businessInstId,jdbcType=VARCHAR},
		      </if> 
		      <if test="settCycleRuleCode != null" >
		        sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
		      </if>
			<if test="scene != null" >
				SCENE = #{scene,jdbcType=VARCHAR},
			</if>
			<if test="requestSrc != null" >
				REQUEST_SRC = #{requestSrc,jdbcType=VARCHAR},
			</if>	
			 <if test="authCustomerCode != null" >
		        auth_customer_code =  #{authCustomerCode,jdbcType=VARCHAR},
		      </if>		
		      <if test="tradeSource != null" >
			trade_source = #{tradeSource,jdbcType=VARCHAR},
			</if>
			<if test="businessMan != null" >
				BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
			</if>
			<if test="businessManId != null" >
				BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
			</if>
			<if test="companyName != null" >
				COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=DECIMAL},
			</if>
			  <if test="maxFee != null" >
            		max_fee = #{maxFee,jdbcType=DECIMAL},
		      </if>
		      <if test="minFee != null" >
		            min_fee = #{minFee,jdbcType=DECIMAL},
		      </if>
   			<if test="nfcTagId != null" >
				NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
			</if>
			<if test="feePer != null" >
				FEE_PER = #{feePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayChargeRate != null" >
				D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
			</if>
			<if test="D1HolidayChargePer != null" >
				D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
			</if>
			<if test="D1HolidayCharged != null" >
				D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
			</if>		
			<if test="chargedByBankCodeArea != null" >
				CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
			</if>	
			<if test="fundChannel != null" >
				fund_channel = #{fundChannel,jdbcType=VARCHAR},
			</if>	
		  <if test="cashAmount != null" >
	        cash_amount = #{cashAmount,jdbcType=DECIMAL},
	      </if> 
	      <if test="couponAmount != null" >
	        coupon_amount = #{couponAmount,jdbcType=DECIMAL},
	      </if>	
	      <if test="industryCodeType != null" >
	        industry_code_type = #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        industry_name = #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        machine_code = #{machineCode,jdbcType=VARCHAR},
	      </if>

			<if test="businessCodeFst != null" >
				BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
			</if>	
			<if test="secondCharged != null" >
				SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
			</if>	 
			<if test="terminalType != null" >
	        terminal_type = #{terminalType,jdbcType=VARCHAR},
	      </if>
			<if test="procedureCustomercode != null" >
				PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR},
			</if>
		</set>

		where  OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
		and PAY_STATE <![CDATA[ <> ]]> #{payState,jdbcType=VARCHAR}
	</update>
	<update id="updateCancelInfoByOutTradeNoAndCustomerCode"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>
			<if test="cancelUpdateTime != null">
				CANCEL_UPDATE_TIME = #{cancelUpdateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="cancelReturnMsg != null">
				CANCEL_RETURN_MSG = #{cancelReturnMsg,jdbcType=VARCHAR},
			</if>
			<if test="cancelNo != null">
				CANCLE_NO = #{cancelNo,jdbcType=VARCHAR},
			</if>
			<if test="cancelState != null">
				CANCEL_STATE = #{cancelState,jdbcType=VARCHAR},
			</if>
		</set>
		where  
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			and	CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			and (CANCEL_STATE is null or CANCEL_STATE != '00')
	</update>	
	<!-- 根据交易单号和客户编码查询 -->
	<select id="selectTxsPreOrderByTransactionNoAndCustomerCode"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>
	<!-- 根据交易单号或者外部单号和客户编码查询 -->
	<select id="selectTxsPreOrderByThreeParam" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where (TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} or OUT_TRADE_NO =
		#{outTradeNo,jdbcType=VARCHAR})and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>
	<!-- 根据外部单号和客户编码查询状态为成功的预下单 -->
	<select id="selectByOutTradeNoToResultQuery" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and PAY_STATE =
		#{payState,jdbcType=CHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>
	<select id="selectTxsPreOrderByOutTradeNoAndCustomerCode"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>
	<update id="updateByTransactionNo"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		set SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR}
		where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	</update>

	<update id="updateStateByEndTime"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		set PAY_STATE = #{updatePayState,jdbcType=CHAR}, UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		where ORDER_ID = #{orderId,jdbcType=DECIMAL}
	</update>

	<select id="selectOrderByStateAndEndTime" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		where TRANSACTION_END_TIME <![CDATA[ <= ]]>
		#{now,jdbcType=TIMESTAMP} and
		TRANSACTION_END_TIME <![CDATA[ >= ]]>
		#{beginTime,jdbcType=TIMESTAMP} and
		PAY_STATE = #{payState,jdbcType=CHAR}
	</select>

	<select id="selectByNotPage" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_PRE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
		order by CREATE_TIME desc
	</select>


	<select id="selectByPage" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
		select A.*, rownum RN
		from (
		select * from TXS_PRE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
		order by CREATE_TIME desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;=
		#{beginRowNo,jdbcType=DECIMAL}

	</select>
	
	<select id="selectCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		select count(ORDER_ID)
		from TXS_PRE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
	</select>
	
	<select id="selectSumProcedureFee" resultType="java.lang.Long"
		parameterType="java.util.Map">
		select COALESCE(SUM(PROCEDURE_FEE),0)
		from TXS_PRE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
	</select>
	
	<select id="selectSumAmount" resultType="java.lang.Long"
		parameterType="java.util.Map">
		select COALESCE(SUM(AMOUNT),0)
		from TXS_PRE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
	</select>
	
	<select id="selectPageCountInfo" resultMap="PageResultMap"
		parameterType="java.util.Map">
		select COALESCE(SUM(AMOUNT),0) total_amount, COALESCE(SUM(PROCEDURE_FEE),0) total_procedure_fee, count(ORDER_ID) total
		from TXS_PRE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
	</select>
	
	<update id="updateAmountByTransactionNo">
    update TXS_PRE_ORDER
    set 
    REFUNDING_FEE = NVL(REFUNDING_FEE , 0) + #{currencyRefundFee,jdbcType=DECIMAL} 
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} and 
    AMOUNT >=  NVL(REFUND_FEE , 0) + NVL(REFUNDING_FEE , 0) + #{currencyRefundFee,jdbcType=DECIMAL}
  </update>
  
	<update id="updateAddAmountById" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
    update TXS_PRE_ORDER
    set 
    REFUNDING_FEE = NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL},
    REFUND_FEE = NVL(REFUND_FEE , 0) + #{refundingFee,jdbcType=DECIMAL}
    where ORDER_ID = #{orderId,jdbcType=DECIMAL} 
    and NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL} >= 0
  </update>
  
	<update id="updateReduceAmountById" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
    update TXS_PRE_ORDER
    set 
    REFUNDING_FEE = NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL}
    where ORDER_ID = #{orderId,jdbcType=DECIMAL} 
    and NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL} >= 0
  </update>
  
  <select id="selectByCustomerCodeAndStateAndEndTimeInDate" resultMap="BaseResultMap">
  	select
	<include refid="Base_Column_List" />
	from TXS_PRE_ORDER
	where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
	and PAY_STATE = #{payState , jdbcType = VARCHAR} 
	and to_char(END_TIME,'yyyyMMdd') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMMdd')
  </select>
  
  <select id="selectByCustomerCodeAndStateAndTypeAndEndTimeInDate" resultMap="BaseResultMap">
  	select
	<include refid="Base_Column_List" />
	from TXS_PRE_ORDER
	where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
	and TRANSACTION_TYPE = #{transactionType , jdbcType =  VARCHAR}
	and PAY_STATE = #{payState , jdbcType = VARCHAR} 
	and to_char(END_TIME,'yyyyMMdd') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMMdd')
  </select>
  
  <select id="selectSumAmountByOneDayAndCustomerCode" resultType="java.lang.Long">
  	select sum(amount)
  	from TXS_PRE_ORDER
  	where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR}
  	and CREATE_TIME <![CDATA[ >= ]]> #{beginTime,jdbcType=TIMESTAMP}
	and CREATE_TIME <![CDATA[ < ]]> #{endTime,jdbcType=TIMESTAMP}
	and (PAY_STATE = 1 or PAY_STATE = 0)
  </select>
  
  	<select id="selectTotalRefundAmt" resultType="java.lang.Integer">
		select NVL(REFUNDING_FEE , 0) + NVL(REFUND_FEE , 0)
		from TXS_PRE_ORDER
		where 
		OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>
  
  <select id="selectByCustomerCodeAndEndTimeInDate" resultMap="BaseResultMap">
  	select
	<include refid="Base_Column_List" />
	from TXS_PRE_ORDER
	where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
	and to_char(END_TIME,'yyyyMMdd') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMMdd')
  </select>
  <sql id="tradeQueryCondition">
	<if test="transactionType != null and transactionType !=''">
		AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
	</if>
	<if test="transactionNo != null and transactionNo !=''">
		AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	</if>
	<if test="payMethod != null and payMethod !=''">
		AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
	</if>
	<if test="outTradeNo != null and outTradeNo !=''">
		AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
	</if>
	<if test="customerCode != null and customerCode !=''">
		AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</if>
	<if
		test="commissionedCustomerCode != null and commissionedCustomerCode !=''">
		AND COMMISSIONED_CUSTOMER_CODE =
		#{commissionedCustomerCode,jdbcType=VARCHAR}
	</if>
	<if test="customerName != null and customerName !=''">
		AND CUSTOMERNAME like concat(#{customerName},'%')
	</if>
	<if test="businessCode != null and businessCode !=''">
		AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
	</if>
	<if test="channelName != null and channelName !=''">
		AND CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
	</if>
	<if test="channelOrder != null and channelOrder !=''">
		AND CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR}
	</if>
	<if test="payState != null and payState !=''">
		AND PAY_STATE = #{payState,jdbcType=CHAR}
	</if>
	<if
		test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
		AND CUSTOMER_CODE in
		<foreach item="item" index="index"
			collection="expandCustomerCodes" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if
		test="commissionedCustomerCode != null and commissionedCustomerCode !=''">
		AND COMMISSIONED_CUSTOMER_CODE =
		#{commissionedCustomerCode,jdbcType=VARCHAR}
	</if>
	<if test="payPassWay != null and payPassWay !=''">
		AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
	</if>
	<if test="agentCustomerCode != null and agentCustomerCode !=''">
		AND AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR}
	</if>
	<if test="agentCustomerName != null and agentCustomerName !=''">
		AND AGENT_CUSTOMERNAME like concat(#{agentCustomerName},'%')
	</if>
	<if test="beginTime!=null">
		AND CREATE_TIME <![CDATA[ >= ]]>
		#{beginTime,jdbcType=TIMESTAMP}
	</if>
	<if test="endTime!=null">
		AND CREATE_TIME <![CDATA[ < ]]>
		#{endTime,jdbcType=TIMESTAMP}
	</if>
	<if test="payerId!=null">
		AND payer_id = #{payerId,jdbcType=VARCHAR}
	</if>
	<if test="payChannelId != null">
		AND PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL}
	</if>
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if> 
      <if test="buyerLogonId != null" >
        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
      </if>  	
</sql>
	<select id="selectByChannelOrder" resultType="java.lang.Integer">
		select
		count (*) 
		from TXS_PRE_ORDER
		where CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR}
	</select>
	<update id="updateAmountsByOutTradeNoAndCustomerCode"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPreOrder">
		update TXS_PRE_ORDER
		<set>

			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
	
	        <if test="actualPayAmount != null" >
	        	ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	        </if>
		    <if test="settlementAmount != null" >
	        	SETTLEMENT_AMOUNT  = #{settlementAmount,jdbcType=DECIMAL},
	        </if>

		</set>

		where  OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</update>


	<update id="updateFirstMultiSplit" >
			update TXS_PRE_ORDER
			set
					dividing_amount = #{multiOrderAmount,jdbcType=DECIMAL}
			where
			ORDER_ID = #{orderId,jdbcType=DECIMAL}
			and split_model = '4'
			and (dividing_amount is null or dividing_amount =0)
			and (divided_amount is null or divided_amount = 0)
	</update>

	<select id="selectTotalDivideAmount" resultType="java.lang.Long">
		select
		nvl(sum(nvl(divided_amount, 0)+nvl(dividing_amount, 0)), 0)
		from TXS_PRE_ORDER
		where
			ORDER_ID = #{orderId,jdbcType=DECIMAL}
	</select>

	<update id="updateAddDividIngAmountById">
    update TXS_PRE_ORDER
    set
    dividing_amount = NVL(dividing_amount , 0) + #{dividingAmountToAdd,jdbcType=DECIMAL}
    where ORDER_ID = #{orderId,jdbcType=DECIMAL}
    and SPLIT_MODEL = '4'
    and (AMOUNT - NVL(dividing_amount , 0) - NVL(divided_amount, 0) - #{dividingAmountToAdd,jdbcType=DECIMAL}) >= 0
  </update>

	<update id="updateAddAddDividedForSuccess">
    update TXS_PRE_ORDER
    set
    dividing_amount = (NVL(dividing_amount , 0) - #{amount,jdbcType=DECIMAL}),
    divided_amount = (NVL(divided_amount , 0) + #{amount,jdbcType=DECIMAL})
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    and SPLIT_MODEL = '4'
    and (NVL(dividing_amount , 0) - #{amount,jdbcType=DECIMAL}) >= 0
  </update>

	<update id="updateAddReduceDividingForFail">
    update TXS_PRE_ORDER
    set
    	dividing_amount = (NVL(dividing_amount , 0) - #{amount,jdbcType=DECIMAL})
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    and SPLIT_MODEL = '4'
    and (NVL(dividing_amount , 0) - #{amount,jdbcType=DECIMAL}) >= 0
  </update>

	<update id="adjustDividing">
    update TXS_PRE_ORDER a
    set
    	dividing_amount =  (select sum(amount) from txs_split_order
    		where customer_code = a.customer_code
    			and out_trade_no = a.out_trade_no
    			and state in ('51', '03'))
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    and SPLIT_MODEL = '4'
  </update>

	<update id="adjustDivided">
    update TXS_PRE_ORDER a
    set
    	divided_amount =  (select sum(amount) from txs_split_order
    		where customer_code = a.customer_code
    			and out_trade_no = a.out_trade_no
    			and state = '00')
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    and SPLIT_MODEL = '4'
  </update>


	<update id="updateSyncDivToPayTradeOrder">
			merge into
				TXS_PAY_TRADE_ORDER A
			USING (SELECT * FROM TXS_PRE_ORDER
					WHERE OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
						and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}) B
				ON (A.TRANSACTION_NO = B.transaction_no)
			WHEN MATCHED
				THEN UPDATE
				set
					A.dividing_amount = B.dividing_amount,
					A.divided_amount = B.divided_amount
  </update>

	<select id="selectOutTradeCusomerForMulitiOrder" resultType="java.util.Map">
		SELECT OUT_TRADE_NO AS "OUTTRADENO",
		CUSTOMER_CODE AS "CUSTOMERCODE"
		FROM TXS_PRE_ORDER T
		WHERE
		T.END_TIME <![CDATA[ >= ]]> SYSDATE - #{daysAgo,jdbcType=DECIMAL} - #{searchDays,jdbcType=DECIMAL}
		AND T.END_TIME <![CDATA[ < ]]> SYSDATE - #{daysAgo,jdbcType=DECIMAL}
		AND T.SPLIT_MODEL = '4'
		AND T.PAY_STATE = '1'
		AND (T.AMOUNT != (NVL(T.DIVIDED_AMOUNT, 0)+NVL(T.DIVIDING_AMOUNT, 0)))
		AND T.SETTLEMENT_STATE = '00'
	</select>


</mapper>