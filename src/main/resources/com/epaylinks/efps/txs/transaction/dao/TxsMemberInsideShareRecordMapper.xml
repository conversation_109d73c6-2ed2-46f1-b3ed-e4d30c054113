<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsMemberInsideShareRecordMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideShareRecord">
    <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="R_AMOUNT" jdbcType="DECIMAL" property="rAmount" />
    <result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="TARGET_CUSTOMER_CODE" jdbcType="VARCHAR" property="targetCustomerCode" />
    <result column="V_AMOUNT" jdbcType="DECIMAL" property="vAmount" />
    <result column="SRC_CUSTOMER_CODE" jdbcType="VARCHAR" property="srcCustomerCode" />
    <result column="V_CURRENCY" jdbcType="VARCHAR" property="vCurrency" />
    <result column="R_CURRENCY" jdbcType="VARCHAR" property="rCurrency" />
  </resultMap>
  <sql id="Base_Column_List">
		TRANSACTION_NO, R_AMOUNT, PROCEDURE_FEE, 
      END_TIME, ERROR_CODE, CREATE_TIME, 
      UPDATE_TIME, BUSINESS_INST_ID, BUSINESS_CODE, 
      STATE, TARGET_CUSTOMER_CODE, V_AMOUNT, 
      SRC_CUSTOMER_CODE, V_CURRENCY, R_CURRENCY
	</sql>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideShareRecord">
    insert into TXS_MEMBER_INSIDE_SHARE_RECORD (TRANSACTION_NO, R_AMOUNT, PROCEDURE_FEE, 
      END_TIME, ERROR_CODE, CREATE_TIME, 
      UPDATE_TIME, BUSINESS_INST_ID, BUSINESS_CODE, 
      STATE, TARGET_CUSTOMER_CODE, V_AMOUNT, 
      SRC_CUSTOMER_CODE, V_CURRENCY, R_CURRENCY
      )
    values (#{transactionNo,jdbcType=VARCHAR}, #{rAmount,jdbcType=DECIMAL}, #{procedureFee,jdbcType=DECIMAL}, 
      #{endTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{businessInstId,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, 
      #{state,jdbcType=VARCHAR}, #{targetCustomerCode,jdbcType=VARCHAR}, #{vAmount,jdbcType=DECIMAL}, 
      #{srcCustomerCode,jdbcType=VARCHAR}, #{vCurrency,jdbcType=VARCHAR}, #{rCurrency,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideShareRecord">
    insert into TXS_MEMBER_INSIDE_SHARE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="rAmount != null">
        R_AMOUNT,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="targetCustomerCode != null">
        TARGET_CUSTOMER_CODE,
      </if>
      <if test="vAmount != null">
        V_AMOUNT,
      </if>
      <if test="srcCustomerCode != null">
        SRC_CUSTOMER_CODE,
      </if>
      <if test="vCurrency != null">
        V_CURRENCY,
      </if>
      <if test="rCurrency != null">
        R_CURRENCY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="rAmount != null">
        #{rAmount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="targetCustomerCode != null">
        #{targetCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="vAmount != null">
        #{vAmount,jdbcType=DECIMAL},
      </if>
      <if test="srcCustomerCode != null">
        #{srcCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="vCurrency != null">
        #{vCurrency,jdbcType=VARCHAR},
      </if>
      <if test="rCurrency != null">
        #{rCurrency,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <update id="updateByInsideTransactionSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideShareRecord">
    update TXS_MEMBER_INSIDE_SHARE_RECORD
    <set>
      <if test="rAmount != null">
        R_AMOUNT = #{rAmount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="targetCustomerCode != null">
        TARGET_CUSTOMER_CODE = #{targetCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="vAmount != null">
        V_AMOUNT = #{vAmount,jdbcType=DECIMAL},
      </if>
      <if test="srcCustomerCode != null">
        SRC_CUSTOMER_CODE = #{srcCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="vCurrency != null">
        V_CURRENCY = #{vCurrency,jdbcType=VARCHAR},
      </if>
      <if test="rCurrency != null">
        R_CURRENCY = #{rCurrency,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  
  <select id="selectByBusinessInstId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_INSIDE_SHARE_RECORD
    where BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
  </select>
</mapper>