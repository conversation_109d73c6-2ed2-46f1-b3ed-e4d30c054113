<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsNfcRcRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.common.business.txs.TxsNfcRcRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="RC_MODE" property="rcMode" jdbcType="VARCHAR" />
    <result column="MARK_STRING" property="markString" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="KEY" property="key" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="CARD_TYPE" property="cardType" jdbcType="VARCHAR" />
    <result column="CARD_NO_ENC" property="cardNoEnc" jdbcType="VARCHAR" />
    <result column="JUDGE_TYPE" property="judgeType" jdbcType="VARCHAR" />
    <result column="RC_DATE" property="rcDate" jdbcType="VARCHAR" />
    <result column="RC_VALUE" property="rcValue" jdbcType="DECIMAL" />
    <result column="VALUE" property="value" jdbcType="DECIMAL" />
    <result column="RC_ACCOUT_RESULT" property="rcAccoutResult" jdbcType="VARCHAR" />
    <result column="RC_NFC_RESULT" property="rcNfcResult" jdbcType="VARCHAR" />
    <result column="BY_CUSTOMER_CODE" property="byCustomerCode" jdbcType="VARCHAR" />
    <result column="BY_CARD_TYPE" property="byCardType" jdbcType="VARCHAR" />
    <result column="BY_CARD_NO_ENC" property="byCardNoEnc" jdbcType="VARCHAR" />
    <result column="CARD_NO_MOSAIC" property="cardNoMosaic" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, RC_MODE, MARK_STRING, CREATE_TIME, KEY, CUSTOMER_CODE, CARD_TYPE, CARD_NO_ENC, 
    JUDGE_TYPE, RC_DATE, RC_VALUE, VALUE, RC_ACCOUT_RESULT, RC_NFC_RESULT, 
    BY_CUSTOMER_CODE, BY_CARD_TYPE, BY_CARD_NO_ENC, CARD_NO_MOSAIC, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TXS_NFC_RC_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_NFC_RC_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcRecord" >
    insert into TXS_NFC_RC_RECORD (ID, RC_MODE, MARK_STRING, 
      CREATE_TIME, KEY, CUSTOMER_CODE, 
      CARD_TYPE, CARD_NO_ENC, JUDGE_TYPE, 
      RC_DATE, RC_VALUE, VALUE, 
      RC_ACCOUT_RESULT, RC_NFC_RESULT, 
      BY_CUSTOMER_CODE, BY_CARD_TYPE, BY_CARD_NO_ENC, CARD_NO_MOSAIC, UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{rcMode,jdbcType=VARCHAR}, #{markString,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{key,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=VARCHAR}, #{cardNoEnc,jdbcType=VARCHAR}, #{judgeType,jdbcType=VARCHAR}, 
      #{rcDate,jdbcType=VARCHAR}, #{rcValue,jdbcType=DECIMAL}, #{value,jdbcType=DECIMAL}, 
      #{rcAccoutResult,jdbcType=VARCHAR}, #{rcNfcResult,jdbcType=VARCHAR}, 
      #{byCustomerCode,jdbcType=VARCHAR}, #{byCardType,jdbcType=VARCHAR}, #{byCardNoEnc,jdbcType=VARCHAR}, 
      #{cardNoMosaic,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcRecord" >
    insert into TXS_NFC_RC_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="rcMode != null" >
        RC_MODE,
      </if>
      <if test="markString != null" >
        MARK_STRING,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="key != null" >
        KEY,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="cardType != null" >
        CARD_TYPE,
      </if>
      <if test="cardNoEnc != null" >
        CARD_NO_ENC,
      </if>
      <if test="judgeType != null" >
        JUDGE_TYPE,
      </if>
      <if test="rcDate != null" >
        RC_DATE,
      </if>
      <if test="rcValue != null" >
        RC_VALUE,
      </if>
      <if test="value != null" >
        VALUE,
      </if>
      <if test="rcAccoutResult != null" >
        RC_ACCOUT_RESULT,
      </if>
      <if test="rcNfcResult != null" >
        RC_NFC_RESULT,
      </if>
      <if test="byCustomerCode != null" >
        BY_CUSTOMER_CODE,
      </if>
      <if test="byCardType != null" >
        BY_CARD_TYPE,
      </if>
      <if test="byCardNoEnc != null" >
        BY_CARD_NO_ENC,
      </if>
      <if test="cardNoMosaic != null" >
        CARD_NO_MOSAIC,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>                              
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="rcMode != null" >
        #{rcMode,jdbcType=VARCHAR},
      </if>
      <if test="markString != null" >
        #{markString,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="key != null" >
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null" >
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="cardNoEnc != null" >
        #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="judgeType != null" >
        #{judgeType,jdbcType=VARCHAR},
      </if>
      <if test="rcDate != null" >
        #{rcDate,jdbcType=VARCHAR},
      </if>
      <if test="rcValue != null" >
        #{rcValue,jdbcType=DECIMAL},
      </if>
      <if test="value != null" >
        #{value,jdbcType=DECIMAL},
      </if>
      <if test="rcAccoutResult != null" >
        #{rcAccoutResult,jdbcType=VARCHAR},
      </if>
      <if test="rcNfcResult != null" >
        #{rcNfcResult,jdbcType=VARCHAR},
      </if>
      <if test="byCustomerCode != null" >
        #{byCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="byCardType != null" >
        #{byCardType,jdbcType=VARCHAR},
      </if>
      <if test="byCardNoEnc != null" >
        #{byCardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="cardNoMosaic != null" >
        #{cardNoMosaic,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>                              
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcRecord" >
    update TXS_NFC_RC_RECORD
    <set >
      <if test="rcMode != null" >
        RC_MODE = #{rcMode,jdbcType=VARCHAR},
      </if>
      <if test="markString != null" >
        MARK_STRING = #{markString,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="key != null" >
        KEY = #{key,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null" >
        CARD_TYPE = #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="cardNoEnc != null" >
        CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="judgeType != null" >
        JUDGE_TYPE = #{judgeType,jdbcType=VARCHAR},
      </if>
      <if test="rcDate != null" >
        RC_DATE = #{rcDate,jdbcType=VARCHAR},
      </if>
      <if test="rcValue != null" >
        RC_VALUE = #{rcValue,jdbcType=DECIMAL},
      </if>
      <if test="value != null" >
        VALUE = #{value,jdbcType=DECIMAL},
      </if>
      <if test="rcAccoutResult != null" >
        RC_ACCOUT_RESULT = #{rcAccoutResult,jdbcType=VARCHAR},
      </if>
      <if test="rcNfcResult != null" >
        RC_NFC_RESULT = #{rcNfcResult,jdbcType=VARCHAR},
      </if>      
      <if test="byCustomerCode != null" >
        BY_CUSTOMER_CODE = #{byCustomerCode,jdbcType=VARCHAR},
      </if>

      <if test="byCardType != null" >
        BY_CARD_TYPE = #{byCardType,jdbcType=VARCHAR},
      </if>
      <if test="byCardNoEnc != null" >
        BY_CARD_NO_ENC = #{byCardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="cardNoMosaic != null" >
        CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>                              
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.common.business.txs.TxsNfcRcRecord" >
    update TXS_NFC_RC_RECORD
    set RC_MODE = #{rcMode,jdbcType=VARCHAR},
      MARK_STRING = #{markString,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      KEY = #{key,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CARD_TYPE = #{cardType,jdbcType=VARCHAR},
      CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      JUDGE_TYPE = #{judgeType,jdbcType=VARCHAR},
      RC_DATE = #{rcDate,jdbcType=VARCHAR},
      RC_VALUE = #{rcValue,jdbcType=DECIMAL},
      VALUE = #{value,jdbcType=DECIMAL},
      RC_ACCOUT_RESULT = #{rcAccoutResult,jdbcType=VARCHAR},
      RC_NFC_RESULT = #{rcNfcResult,jdbcType=VARCHAR},
      BY_CUSTOMER_CODE = #{byCustomerCode,jdbcType=VARCHAR},
      BY_CARD_TYPE = #{byCardType,jdbcType=VARCHAR},
      BY_CARD_NO_ENC = #{byCardNoEnc,jdbcType=VARCHAR},
      CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>