<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsFukuanRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsFukuanRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
    <result column="CARD_ATTR" property="cardAttr" jdbcType="VARCHAR" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="RESP_CODE" property="respCode" jdbcType="VARCHAR" />
    <result column="RESP_MSG" property="respMsg" jdbcType="VARCHAR" />
    <result column="ORIG_RESP_CODE" property="origRespCode" jdbcType="VARCHAR" />
    <result column="ORIG_RESP_MSG" property="origRespMsg" jdbcType="VARCHAR" />
    <result column="CHANNEL_SERVICE_ID" property="channelServiceId" jdbcType="VARCHAR" />
    <result column="INSTITUTION_ID" property="institutionId" jdbcType="DECIMAL" />
    <result column="INSTITUTION_CODE" property="institutionCode" jdbcType="VARCHAR" />
    <result column="INSTITUTION_NAME" property="institutionName" jdbcType="VARCHAR" />
    <result column="CHANNEL_INST_CODE" property="channelInstCode" jdbcType="VARCHAR" />
    <result column="MER_ID" property="merId" jdbcType="VARCHAR" />
    <result column="MER_CAT_CODE" property="merCatCode" jdbcType="VARCHAR" />
    <result column="MER_NAME" property="merName" jdbcType="VARCHAR" />
    <result column="TERM_ID" property="termId" jdbcType="VARCHAR" />
    <result column="QR_NO" property="qrNo" jdbcType="VARCHAR" />
    <result column="QR_VALID_TIME" property="qrValidTime" jdbcType="VARCHAR" />
    <result column="QR_CODE" property="qrCode" jdbcType="VARCHAR" />
    <result column="SETTLE_KEY" property="settleKey" jdbcType="VARCHAR" />
    <result column="VOUCHER_NUM" property="voucherNum" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
    <result column="QR_ORDER_TIME" property="qrOrderTime" jdbcType="VARCHAR" />
    <result column="PAY_TYPE" property="payType" jdbcType="VARCHAR" />
    <result column="PROCESS_STAGE" property="processStage" jdbcType="VARCHAR" />
    <result column="TXN_NO" property="txnNo" jdbcType="VARCHAR" />
    <result column="TOTAL_DISCOUN_AMT" property="totalDiscounAmt" jdbcType="DECIMAL" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="TXN_AMT" property="txnAmt" jdbcType="DECIMAL" />
    <result column="COUPONINFO" property="couponinfo" jdbcType="VARCHAR" />
    <result column="ORIG_REQ_TYPE" property="origReqType" jdbcType="VARCHAR" />
    <result column="CARD_NO_MOSAIC" property="cardNoMosaic" jdbcType="VARCHAR" />
    <result column="CARD_NO_ENC" property="cardNoEnc" jdbcType="VARCHAR" />
    <result column="PROCEDURE_RATE" property="procedureRate" jdbcType="VARCHAR" />
    <result column="FEE_PER" property="feePer" jdbcType="DECIMAL" />
    <result column="PROCEDURE_FEE" property="procedureFee" jdbcType="DECIMAL" />
    <result column="TRANSACTION_TYPE" property="transactionType" jdbcType="VARCHAR" />
    <result column="ACCOUNT_VOUCHER_NO" property="accountVoucherNo" jdbcType="VARCHAR" />
    <result column="ACC_ERROR_CODE" property="accErrorCode" jdbcType="VARCHAR" />
    <result column="ACC_STATUS" property="accStatus" jdbcType="VARCHAR" />
    <result column="PROTOCOL" property="procotol" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, OUT_TRADE_NO, CARD_ATTR, PAY_METHOD, TRANSACTION_NO, ORDER_NO, 
    RESP_CODE, RESP_MSG, ORIG_RESP_CODE, ORIG_RESP_MSG, CHANNEL_SERVICE_ID, INSTITUTION_ID, 
    INSTITUTION_CODE, INSTITUTION_NAME, CHANNEL_INST_CODE, MER_ID, MER_CAT_CODE, MER_NAME, 
    TERM_ID, QR_NO, QR_VALID_TIME, QR_CODE, SETTLE_KEY, VOUCHER_NUM, STATE, CREATE_TIME, 
    UPDATE_TIME, END_TIME, QR_ORDER_TIME, PAY_TYPE, PROCESS_STAGE, TXN_NO, TOTAL_DISCOUN_AMT, 
    AMOUNT, TXN_AMT, COUPONINFO, ORIG_REQ_TYPE, CARD_NO_MOSAIC, CARD_NO_ENC, PROCEDURE_RATE, 
    FEE_PER, PROCEDURE_FEE, TRANSACTION_TYPE, ACCOUNT_VOUCHER_NO, ACC_ERROR_CODE, ACC_STATUS, 
    PROTOCOL
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from TXS_FUKUAN_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_FUKUAN_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsFukuanRecord" >
    insert into TXS_FUKUAN_RECORD (ID, CUSTOMER_CODE, OUT_TRADE_NO, 
      CARD_ATTR, PAY_METHOD, TRANSACTION_NO, 
      ORDER_NO, RESP_CODE, RESP_MSG, 
      ORIG_RESP_CODE, ORIG_RESP_MSG, CHANNEL_SERVICE_ID, 
      INSTITUTION_ID, INSTITUTION_CODE, INSTITUTION_NAME, 
      CHANNEL_INST_CODE, MER_ID, MER_CAT_CODE, 
      MER_NAME, TERM_ID, QR_NO, 
      QR_VALID_TIME, QR_CODE, SETTLE_KEY, 
      VOUCHER_NUM, STATE, CREATE_TIME, 
      UPDATE_TIME, END_TIME, QR_ORDER_TIME, 
      PAY_TYPE, PROCESS_STAGE, TXN_NO, 
      TOTAL_DISCOUN_AMT, AMOUNT, TXN_AMT, 
      COUPONINFO, ORIG_REQ_TYPE, CARD_NO_MOSAIC, 
      CARD_NO_ENC, PROCEDURE_RATE, FEE_PER, 
      PROCEDURE_FEE, TRANSACTION_TYPE, ACCOUNT_VOUCHER_NO, 
      ACC_ERROR_CODE, ACC_STATUS, PROTOCOL)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{outTradeNo,jdbcType=VARCHAR}, 
      #{cardAttr,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, #{transactionNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{respCode,jdbcType=VARCHAR}, #{respMsg,jdbcType=VARCHAR}, 
      #{origRespCode,jdbcType=VARCHAR}, #{origRespMsg,jdbcType=VARCHAR}, #{channelServiceId,jdbcType=VARCHAR}, 
      #{institutionId,jdbcType=DECIMAL}, #{institutionCode,jdbcType=VARCHAR}, #{institutionName,jdbcType=VARCHAR}, 
      #{channelInstCode,jdbcType=VARCHAR}, #{merId,jdbcType=VARCHAR}, #{merCatCode,jdbcType=VARCHAR}, 
      #{merName,jdbcType=VARCHAR}, #{termId,jdbcType=VARCHAR}, #{qrNo,jdbcType=VARCHAR}, 
      #{qrValidTime,jdbcType=VARCHAR}, #{qrCode,jdbcType=VARCHAR}, #{settleKey,jdbcType=VARCHAR}, 
      #{voucherNum,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{qrOrderTime,jdbcType=VARCHAR}, 
      #{payType,jdbcType=VARCHAR}, #{processStage,jdbcType=VARCHAR}, #{txnNo,jdbcType=VARCHAR}, 
      #{totalDiscounAmt,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, #{txnAmt,jdbcType=DECIMAL}, 
      #{couponinfo,jdbcType=VARCHAR}, #{origReqType,jdbcType=VARCHAR}, #{cardNoMosaic,jdbcType=VARCHAR}, 
      #{cardNoEnc,jdbcType=VARCHAR}, #{procedureRate,jdbcType=VARCHAR}, #{feePer,jdbcType=DECIMAL}, 
      #{procedureFee,jdbcType=DECIMAL}, #{transactionType,jdbcType=VARCHAR}, #{accountVoucherNo,jdbcType=VARCHAR}, 
      #{accErrorCode,jdbcType=VARCHAR}, #{accStatus,jdbcType=VARCHAR}, #{procotol,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsFukuanRecord" >
    insert into TXS_FUKUAN_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO,
      </if>
      <if test="cardAttr != null" >
        CARD_ATTR,
      </if>
      <if test="payMethod != null" >
        PAY_METHOD,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="respCode != null" >
        RESP_CODE,
      </if>
      <if test="respMsg != null" >
        RESP_MSG,
      </if>
      <if test="origRespCode != null" >
        ORIG_RESP_CODE,
      </if>
      <if test="origRespMsg != null" >
        ORIG_RESP_MSG,
      </if>
      <if test="channelServiceId != null" >
        CHANNEL_SERVICE_ID,
      </if>
      <if test="institutionId != null" >
        INSTITUTION_ID,
      </if>
      <if test="institutionCode != null" >
        INSTITUTION_CODE,
      </if>
      <if test="institutionName != null" >
        INSTITUTION_NAME,
      </if>
      <if test="channelInstCode != null" >
        CHANNEL_INST_CODE,
      </if>
      <if test="merId != null" >
        MER_ID,
      </if>
      <if test="merCatCode != null" >
        MER_CAT_CODE,
      </if>
      <if test="merName != null" >
        MER_NAME,
      </if>
      <if test="termId != null" >
        TERM_ID,
      </if>
      <if test="qrNo != null" >
        QR_NO,
      </if>
      <if test="qrValidTime != null" >
        QR_VALID_TIME,
      </if>
      <if test="qrCode != null" >
        QR_CODE,
      </if>
      <if test="settleKey != null" >
        SETTLE_KEY,
      </if>
      <if test="voucherNum != null" >
        VOUCHER_NUM,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="qrOrderTime != null" >
        QR_ORDER_TIME,
      </if>
      <if test="payType != null" >
        PAY_TYPE,
      </if>
      <if test="processStage != null" >
        PROCESS_STAGE,
      </if>
      <if test="txnNo != null" >
        TXN_NO,
      </if>
      <if test="totalDiscounAmt != null" >
        TOTAL_DISCOUN_AMT,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="txnAmt != null" >
        TXN_AMT,
      </if>
      <if test="couponinfo != null" >
        COUPONINFO,
      </if>
      <if test="origReqType != null" >
        ORIG_REQ_TYPE,
      </if>
      <if test="cardNoMosaic != null" >
        CARD_NO_MOSAIC,
      </if>
      <if test="cardNoEnc != null" >
        CARD_NO_ENC,
      </if>
      <if test="procedureRate != null" >
        PROCEDURE_RATE,
      </if>
      <if test="feePer != null" >
        FEE_PER,
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE,
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE,
      </if>
      <if test="accountVoucherNo != null" >
        ACCOUNT_VOUCHER_NO,
      </if>
      <if test="accErrorCode != null" >
        ACC_ERROR_CODE,
      </if>
      <if test="accStatus != null" >
        ACC_STATUS,
      </if>
      <if test="procotol != null" >
        PROTOCOL,
      </if>      
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="cardAttr != null" >
        #{cardAttr,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="respCode != null" >
        #{respCode,jdbcType=VARCHAR},
      </if>
      <if test="respMsg != null" >
        #{respMsg,jdbcType=VARCHAR},
      </if>
      <if test="origRespCode != null" >
        #{origRespCode,jdbcType=VARCHAR},
      </if>
      <if test="origRespMsg != null" >
        #{origRespMsg,jdbcType=VARCHAR},
      </if>
      <if test="channelServiceId != null" >
        #{channelServiceId,jdbcType=VARCHAR},
      </if>
      <if test="institutionId != null" >
        #{institutionId,jdbcType=DECIMAL},
      </if>
      <if test="institutionCode != null" >
        #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="institutionName != null" >
        #{institutionName,jdbcType=VARCHAR},
      </if>
      <if test="channelInstCode != null" >
        #{channelInstCode,jdbcType=VARCHAR},
      </if>
      <if test="merId != null" >
        #{merId,jdbcType=VARCHAR},
      </if>
      <if test="merCatCode != null" >
        #{merCatCode,jdbcType=VARCHAR},
      </if>
      <if test="merName != null" >
        #{merName,jdbcType=VARCHAR},
      </if>
      <if test="termId != null" >
        #{termId,jdbcType=VARCHAR},
      </if>
      <if test="qrNo != null" >
        #{qrNo,jdbcType=VARCHAR},
      </if>
      <if test="qrValidTime != null" >
        #{qrValidTime,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="settleKey != null" >
        #{settleKey,jdbcType=VARCHAR},
      </if>
      <if test="voucherNum != null" >
        #{voucherNum,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="qrOrderTime != null" >
        #{qrOrderTime,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="processStage != null" >
        #{processStage,jdbcType=VARCHAR},
      </if>
      <if test="txnNo != null" >
        #{txnNo,jdbcType=VARCHAR},
      </if>
      <if test="totalDiscounAmt != null" >
        #{totalDiscounAmt,jdbcType=DECIMAL},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="txnAmt != null" >
        #{txnAmt,jdbcType=DECIMAL},
      </if>
      <if test="couponinfo != null" >
        #{couponinfo,jdbcType=VARCHAR},
      </if>
      <if test="origReqType != null" >
        #{origReqType,jdbcType=VARCHAR},
      </if>
      <if test="cardNoMosaic != null" >
        #{cardNoMosaic,jdbcType=VARCHAR},
      </if>
      <if test="cardNoEnc != null" >
        #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="procedureRate != null" >
        #{procedureRate,jdbcType=VARCHAR},
      </if>
      <if test="feePer != null" >
        #{feePer,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null" >
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="transactionType != null" >
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="accountVoucherNo != null" >
        #{accountVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="accErrorCode != null" >
        #{accErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="accStatus != null" >
        #{accStatus,jdbcType=VARCHAR},
      </if>
      <if test="procotol != null" >
        #{procotol,jdbcType=VARCHAR},
      </if>      
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsFukuanRecord" >
    update TXS_FUKUAN_RECORD
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="cardAttr != null" >
        CARD_ATTR = #{cardAttr,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="respCode != null" >
        RESP_CODE = #{respCode,jdbcType=VARCHAR},
      </if>
      <if test="respMsg != null" >
        RESP_MSG = #{respMsg,jdbcType=VARCHAR},
      </if>
      <if test="origRespCode != null" >
        ORIG_RESP_CODE = #{origRespCode,jdbcType=VARCHAR},
      </if>
      <if test="origRespMsg != null" >
        ORIG_RESP_MSG = #{origRespMsg,jdbcType=VARCHAR},
      </if>
      <if test="channelServiceId != null" >
        CHANNEL_SERVICE_ID = #{channelServiceId,jdbcType=VARCHAR},
      </if>
      <if test="institutionId != null" >
        INSTITUTION_ID = #{institutionId,jdbcType=DECIMAL},
      </if>
      <if test="institutionCode != null" >
        INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="institutionName != null" >
        INSTITUTION_NAME = #{institutionName,jdbcType=VARCHAR},
      </if>
      <if test="channelInstCode != null" >
        CHANNEL_INST_CODE = #{channelInstCode,jdbcType=VARCHAR},
      </if>
      <if test="merId != null" >
        MER_ID = #{merId,jdbcType=VARCHAR},
      </if>
      <if test="merCatCode != null" >
        MER_CAT_CODE = #{merCatCode,jdbcType=VARCHAR},
      </if>
      <if test="merName != null" >
        MER_NAME = #{merName,jdbcType=VARCHAR},
      </if>
      <if test="termId != null" >
        TERM_ID = #{termId,jdbcType=VARCHAR},
      </if>
      <if test="qrNo != null" >
        QR_NO = #{qrNo,jdbcType=VARCHAR},
      </if>
      <if test="qrValidTime != null" >
        QR_VALID_TIME = #{qrValidTime,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        QR_CODE = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="settleKey != null" >
        SETTLE_KEY = #{settleKey,jdbcType=VARCHAR},
      </if>
      <if test="voucherNum != null" >
        VOUCHER_NUM = #{voucherNum,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="qrOrderTime != null" >
        QR_ORDER_TIME = #{qrOrderTime,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        PAY_TYPE = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="processStage != null" >
        PROCESS_STAGE = #{processStage,jdbcType=VARCHAR},
      </if>
      <if test="txnNo != null" >
        TXN_NO = #{txnNo,jdbcType=VARCHAR},
      </if>
      <if test="totalDiscounAmt != null" >
        TOTAL_DISCOUN_AMT = #{totalDiscounAmt,jdbcType=DECIMAL},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="txnAmt != null" >
        TXN_AMT = #{txnAmt,jdbcType=DECIMAL},
      </if>
      <if test="couponinfo != null" >
        COUPONINFO = #{couponinfo,jdbcType=VARCHAR},
      </if>
      <if test="origReqType != null" >
        ORIG_REQ_TYPE = #{origReqType,jdbcType=VARCHAR},
      </if>
      <if test="cardNoMosaic != null" >
        CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
      </if>
      <if test="cardNoEnc != null" >
        CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="procedureRate != null" >
        PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
      </if>
      <if test="feePer != null" >
        FEE_PER = #{feePer,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="accountVoucherNo != null" >
        ACCOUNT_VOUCHER_NO = #{accountVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="accErrorCode != null" >
        ACC_ERROR_CODE = #{accErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="accStatus != null" >
        ACC_STATUS = #{accStatus,jdbcType=VARCHAR},
      </if>
      <if test="procotol != null" >
        PROTOCOL = #{procotol,jdbcType=VARCHAR},
      </if>      
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsFukuanRecord" >
    update TXS_FUKUAN_RECORD
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      CARD_ATTR = #{cardAttr,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      RESP_CODE = #{respCode,jdbcType=VARCHAR},
      RESP_MSG = #{respMsg,jdbcType=VARCHAR},
      ORIG_RESP_CODE = #{origRespCode,jdbcType=VARCHAR},
      ORIG_RESP_MSG = #{origRespMsg,jdbcType=VARCHAR},
      CHANNEL_SERVICE_ID = #{channelServiceId,jdbcType=VARCHAR},
      INSTITUTION_ID = #{institutionId,jdbcType=DECIMAL},
      INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      INSTITUTION_NAME = #{institutionName,jdbcType=VARCHAR},
      CHANNEL_INST_CODE = #{channelInstCode,jdbcType=VARCHAR},
      MER_ID = #{merId,jdbcType=VARCHAR},
      MER_CAT_CODE = #{merCatCode,jdbcType=VARCHAR},
      MER_NAME = #{merName,jdbcType=VARCHAR},
      TERM_ID = #{termId,jdbcType=VARCHAR},
      QR_NO = #{qrNo,jdbcType=VARCHAR},
      QR_VALID_TIME = #{qrValidTime,jdbcType=VARCHAR},
      QR_CODE = #{qrCode,jdbcType=VARCHAR},
      SETTLE_KEY = #{settleKey,jdbcType=VARCHAR},
      VOUCHER_NUM = #{voucherNum,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      QR_ORDER_TIME = #{qrOrderTime,jdbcType=VARCHAR},
      PAY_TYPE = #{payType,jdbcType=VARCHAR},
      PROCESS_STAGE = #{processStage,jdbcType=VARCHAR},
      TXN_NO = #{txnNo,jdbcType=VARCHAR},
      TOTAL_DISCOUN_AMT = #{totalDiscounAmt,jdbcType=DECIMAL},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      TXN_AMT = #{txnAmt,jdbcType=DECIMAL},
      COUPONINFO = #{couponinfo,jdbcType=VARCHAR},
      ORIG_REQ_TYPE = #{origReqType,jdbcType=VARCHAR},
      CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
      CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
      FEE_PER = #{feePer,jdbcType=DECIMAL},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      ACCOUNT_VOUCHER_NO = #{accountVoucherNo,jdbcType=VARCHAR},
      ACC_ERROR_CODE = #{accErrorCode,jdbcType=VARCHAR},
      ACC_STATUS = #{accStatus,jdbcType=VARCHAR},
      PROTOCOL = #{procotol,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <update id="updateStateAsFailure">
  	update TXS_FUKUAN_RECORD
  	set STATE = #{state, jdbcType=VARCHAR},
  		ERROR_CODE = #{errorCode, jdbcType=VARCHAR},
  	    UPDATE_TIME = #{updateTime, jdbcType=TIMESTAMP}
  	where ID = #{id, jdbcType=DECIMAL}
  </update>  
  
  <select id="selectByTransactionNo" resultMap="BaseResultMap" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_FUKUAN_RECORD
	    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
 <update id="updateProcessingByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsFukuanRecord" >
    update TXS_FUKUAN_RECORD
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="cardAttr != null" >
        CARD_ATTR = #{cardAttr,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="respCode != null" >
        RESP_CODE = #{respCode,jdbcType=VARCHAR},
      </if>
      <if test="respMsg != null" >
        RESP_MSG = #{respMsg,jdbcType=VARCHAR},
      </if>
      <if test="origRespCode != null" >
        ORIG_RESP_CODE = #{origRespCode,jdbcType=VARCHAR},
      </if>
      <if test="origRespMsg != null" >
        ORIG_RESP_MSG = #{origRespMsg,jdbcType=VARCHAR},
      </if>
      <if test="channelServiceId != null" >
        CHANNEL_SERVICE_ID = #{channelServiceId,jdbcType=VARCHAR},
      </if>
      <if test="institutionId != null" >
        INSTITUTION_ID = #{institutionId,jdbcType=DECIMAL},
      </if>
      <if test="institutionCode != null" >
        INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="institutionName != null" >
        INSTITUTION_NAME = #{institutionName,jdbcType=VARCHAR},
      </if>
      <if test="channelInstCode != null" >
        CHANNEL_INST_CODE = #{channelInstCode,jdbcType=VARCHAR},
      </if>
      <if test="merId != null" >
        MER_ID = #{merId,jdbcType=VARCHAR},
      </if>
      <if test="merCatCode != null" >
        MER_CAT_CODE = #{merCatCode,jdbcType=VARCHAR},
      </if>
      <if test="merName != null" >
        MER_NAME = #{merName,jdbcType=VARCHAR},
      </if>
      <if test="termId != null" >
        TERM_ID = #{termId,jdbcType=VARCHAR},
      </if>
      <if test="qrNo != null" >
        QR_NO = #{qrNo,jdbcType=VARCHAR},
      </if>
      <if test="qrValidTime != null" >
        QR_VALID_TIME = #{qrValidTime,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        QR_CODE = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="settleKey != null" >
        SETTLE_KEY = #{settleKey,jdbcType=VARCHAR},
      </if>
      <if test="voucherNum != null" >
        VOUCHER_NUM = #{voucherNum,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="qrOrderTime != null" >
        QR_ORDER_TIME = #{qrOrderTime,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        PAY_TYPE = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="processStage != null" >
        PROCESS_STAGE = #{processStage,jdbcType=VARCHAR},
      </if>
      <if test="txnNo != null" >
        TXN_NO = #{txnNo,jdbcType=VARCHAR},
      </if>
      <if test="totalDiscounAmt != null" >
        TOTAL_DISCOUN_AMT = #{totalDiscounAmt,jdbcType=DECIMAL},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="txnAmt != null" >
        TXN_AMT = #{txnAmt,jdbcType=DECIMAL},
      </if>
      <if test="couponinfo != null" >
        COUPONINFO = #{couponinfo,jdbcType=VARCHAR},
      </if>
      <if test="origReqType != null" >
        ORIG_REQ_TYPE = #{origReqType,jdbcType=VARCHAR},
      </if>
      <if test="cardNoMosaic != null" >
        CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
      </if>
      <if test="cardNoEnc != null" >
        CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
      </if>
      <if test="procedureRate != null" >
        PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
      </if>
      <if test="feePer != null" >
        FEE_PER = #{feePer,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="accountVoucherNo != null" >
        ACCOUNT_VOUCHER_NO = #{accountVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="accErrorCode != null" >
        ACC_ERROR_CODE = #{accErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="accStatus != null" >
        ACC_STATUS = #{accStatus,jdbcType=VARCHAR},
      </if>
      <if test="procotol != null" >
        PROTOCOL = #{procotol,jdbcType=VARCHAR},
      </if>      
    </set>
    where ID = #{id,jdbcType=DECIMAL} and state != '00' and state != '01'
  </update>  
  
  <select id="selectByCustomerAndOutTradeNo" resultMap="BaseResultMap" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_FUKUAN_RECORD
	    where CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	    	and OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
  </select>  
  
</mapper>