<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="com.epaylinks.efps.txs.transaction.dao.TxsWithdrawTradeOrderMapper">
	<resultMap id="BaseResultMap"
		type="com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder">
		<id column="ID" property="id" jdbcType="DECIMAL" />
		<result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
		<result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
		<result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
		<result column="COMMISSIONED_CUSTOMER_CODE" jdbcType="VARCHAR" property="commissionedCustomerCode" />
		<result column="TERMINAL_NO" property="terminalNo" jdbcType="VARCHAR" />
		<result column="TOTAL_FEE" property="totalFee" jdbcType="DECIMAL" />
		<result column="PAY_CURRENCY" property="payCurrency" jdbcType="VARCHAR" />
		<result column="ARRIVAL_TYPE" property="arrivalType" jdbcType="VARCHAR" />
		<result column="ACTUAL_FEE" property="actualFee" jdbcType="DECIMAL" />
		<result column="PROCEDURE_FEE" property="procedureFee" jdbcType="DECIMAL" />
		<result column="PRODUCE_RATE" property="produceRate" jdbcType="VARCHAR" />
		<result column="CARD_ID" property="cardId" jdbcType="DECIMAL" />
		<result column="CARD_NO" property="cardNo" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="BEGIN_TIME" property="beginTime" jdbcType="TIMESTAMP" />
		<result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
		<result column="PAY_STATE" property="payState" jdbcType="CHAR" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
		<result column="CHANNEL_TYPE" property="channelType" jdbcType="CHAR" />
		<result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
		<result column="REDIRECT_URL" property="redirectUrl" jdbcType="VARCHAR" />
		<result column="BUSINESS_INST_ID" property="businessInstId" jdbcType="VARCHAR" />
		<result column="CHANNEL_ORDER" property="channelOrder" jdbcType="VARCHAR" />
		<result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR" />
		<result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR" />
		<result column="BANK_ACCOUNT_TYPE" property="bankAccountType" jdbcType="VARCHAR" />
		<result column="CUSTOMERNAME" jdbcType="VARCHAR" property="customerName" />
		<result column="ACCOUNT_TYPE_CODE" jdbcType="VARCHAR" property="accountTypeCode"/>
		<result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode"/>
		<result column="TH_STATE" jdbcType="VARCHAR" property="thState"/>
		<result column="BANK_USER_NAME" jdbcType="VARCHAR" property="bankUserName"/>
		<result column="BANK_USER_CERT" jdbcType="VARCHAR" property="bankUserCert"/>
		<result column="BANK_NAME" jdbcType="VARCHAR" property="bankName"/>
		<result column="BANK_PROVINCE" jdbcType="VARCHAR" property="bankProvince"/>
		<result column="BANK_CITY" jdbcType="VARCHAR" property="bankCity"/>
		<result column="BANK_SUB" jdbcType="VARCHAR" property="bankSub"/>
		<result column="BANK_NO" jdbcType="VARCHAR" property="bankNo"/>
		<result column="BATCH_NO" jdbcType="VARCHAR" property="batchNo"/>
		<result column="OPR_REMARK" property="oprRemark" jdbcType="VARCHAR"/>
		<result column="OPR_ID" property="oprId" jdbcType="VARCHAR"/>
		<result column="OPR_TIME"  property="oprTime"  jdbcType="TIMESTAMP"/>
		<result column="OPR_OPERATED" property="oprOperated" jdbcType="VARCHAR"/>
		<result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR"/>
		<result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR"/>
		<result column="PAY_PASSWAY" jdbcType="VARCHAR" property="payPassWay" />
		<result column="PAY_CHANNEL_ID" property="payChannelId" jdbcType="DECIMAL" />
		<result column="CHANNEL_CATEGORY_ID" property="channelCategoryId" jdbcType="DECIMAL" />
		<result column="card_no_cipher" property="cardNoCipher" jdbcType="VARCHAR"/>
		<result column="bank_user_name_full" property="bankUserNameFull" jdbcType="VARCHAR"/>
		<result column="bank_user_cert_full" property="bankUserCertFull" jdbcType="VARCHAR"/>
		<result column="CHANNEL_RESP_CODE" property="channelRespCode" jdbcType="VARCHAR"/>
		<result column="CHANNEL_RESP_MSG" property="channelRespMsg" jdbcType="VARCHAR"/>
		<result column="channel_query_code" property="channelQueryCode" jdbcType="VARCHAR"/>
		<result column="channel_query_msg" property="channelQueryMsg" jdbcType="VARCHAR" />
		<result column="SERVICE_FEE" property="serviceFee" jdbcType="DECIMAL" />
		<result column="SERVICE_FEE_STATUS" property="serviceFeeStatus" jdbcType="DECIMAL" />
		<result column="auth_customer_code" property="authCustomerCode" jdbcType="VARCHAR" />
		<result column="trade_source" property="tradeSource" jdbcType="VARCHAR" />
		<result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR" />
		<result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL" />
		<result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
		<result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
		<result column="FEE_PER" property="feePer" jdbcType="DECIMAL" />
		<result column="RATE_MODE" property="rateMode" jdbcType="DECIMAL" />
		<result column="SERVICE_FEE_TRANSACTION_NO" property="serviceFeeTransactionNo" jdbcType="VARCHAR" />
		<result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType"/>
	</resultMap>
	
	<resultMap id="PageResultMap"
		type="com.epaylinks.efps.common.util.page.PageResult">
	    <result column="total" property="total" jdbcType="DECIMAL" />
	    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
	    <result column="total_procedure_fee" property="totalProcedureFee" jdbcType="DECIMAL" />
	    <result column="total_zh_amount" property="totalZhAmount" jdbcType="DECIMAL" />
		<result column="total_service_fee" property="totalServiceFee" jdbcType="DECIMAL" />
	</resultMap>
	<sql id="Base_Column_List">
		ID, TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE,COMMISSIONED_CUSTOMER_CODE, TERMINAL_NO,
		TOTAL_FEE, PAY_CURRENCY, ARRIVAL_TYPE, ACTUAL_FEE, PROCEDURE_FEE, 
		PRODUCE_RATE, CARD_ID, CARD_NO, CREATE_TIME, BEGIN_TIME, 
		END_TIME, PAY_STATE, REMARK, CHANNEL_TYPE, NOTIFY_URL, REDIRECT_URL,BUSINESS_INST_ID,
		CHANNEL_ORDER, CHANNEL_NAME, SOURCE_TYPE, BANK_ACCOUNT_TYPE, CUSTOMERNAME , 
		ACCOUNT_TYPE_CODE , BUSINESS_CODE, TH_STATE, BATCH_NO, OPR_REMARK, OPR_ID, OPR_TIME, OPR_OPERATED,
		ERROR_CODE,PAY_METHOD,PAY_PASSWAY,PAY_CHANNEL_ID,CHANNEL_CATEGORY_ID,BANK_USER_NAME,BANK_USER_CERT,
		BANK_NAME,BANK_PROVINCE,BANK_CITY,BANK_SUB,BANK_NO,card_no_cipher,bank_user_name_full,bank_user_cert_full,
		CHANNEL_RESP_CODE,CHANNEL_RESP_MSG,channel_query_code,channel_query_msg,SERVICE_FEE,SERVICE_FEE_STATUS,
		auth_customer_code,trade_source,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,FEE_PER,RATE_MODE,SERVICE_FEE_TRANSACTION_NO,TRANSACTION_TYPE
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where ID = #{id,jdbcType=DECIMAL}
	</select>
	
	<select id="selectByTransaction" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	</select>
	
	<select id="selectByCustomerCodeAndOutTradeNo" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
	</select>

	<select id="selectByOutTradeNo" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
	</select>
	<select id="selectBySelective" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		<where>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="terminalNo != null and terminalNo !=''">
				AND TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR} 
			</if>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} 
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR} 
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
			<if test="businessInstId!=null ">
   			 	AND BUSINESS_INST_ID =  #{businessInstId,jdbcType=VARCHAR}
			</if>
			<if test="channelOrder!=null ">
				AND CHANNEL_ORDER =  #{channelOrder,jdbcType=VARCHAR}
			</if>
			<if test="channelName!=null ">
				AND CHANNEL_NAME =  #{channelName,jdbcType=VARCHAR}
			</if>
			<if test="sourceType!=null ">
				AND SOURCE_TYPE =  #{sourceType,jdbcType=VARCHAR}
			</if>
			<if test="accountTypeCode != null">
				AND ACCOUNT_TYPE_CODE =  #{accountTypeCode,jdbcType=VARCHAR}
			</if>
			<if test="businessCode != null">
				AND BUSINESS_CODE =  #{businessCode,jdbcType=VARCHAR}
			</if>
			<if test="thState != null">
				AND TH_STATE =  #{thState,jdbcType=VARCHAR}
			</if>
			<if test="batchNo != null">
				AND BATCH_NO =  #{batchNo,jdbcType=VARCHAR}
			</if>
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
			</if>
			<if test="payPassWay != null and payPassWay !=''">
				AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
			</if>
			<if test="payChannelId != null">
				AND PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL}
			</if>
			<if test="channelCategoryId != null">
				AND CHANNEL_CATEGORY_ID = #{channelCategoryId,jdbcType=DECIMAL}
			</if>
			<if test="cardNoCipher != null">
				AND card_no_cipher = #{cardNoCipher,jdbcType=VARCHAR}
			</if>
			<if test="serviceFee != null">
				AND SERVICE_FEE = #{serviceFee,jdbcType=VARCHAR}
			</if>
			<if test="serviceFeeStatus != null">
				AND SERVICE_FEE_STATUS = #{serviceFeeStatus,jdbcType=VARCHAR}
			</if>
			<if test="transactionType != null">
				and TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	<select id="selectByNotPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select 
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER 
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
		order by CREATE_TIME desc 
	</select>

	<select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		select count(ID) from TXS_WITHDRAW_TRADE_ORDER 
		<where>
			<include refid="tradeQueryCondition"/>
			
		</where>
	</select>

	<select id="selectByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
		select A.*, rownum RN
		from (
		select * from TXS_WITHDRAW_TRADE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
		order by CREATE_TIME desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from
		TXS_WITHDRAW_TRADE_ORDER
		where ID = #{id,jdbcType=DECIMAL}
	</delete>
	<insert id="insert"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder">
		insert into TXS_WITHDRAW_TRADE_ORDER (ID, TRANSACTION_NO, OUT_TRADE_NO, 
			CUSTOMER_CODE,COMMISSIONED_CUSTOMER_CODE, TERMINAL_NO, TOTAL_FEE, PAY_CURRENCY, ARRIVAL_TYPE, 
			ACTUAL_FEE, PROCEDURE_FEE, PRODUCE_RATE, CARD_ID, CARD_NO, CREATE_TIME, 
			BEGIN_TIME, END_TIME, PAY_STATE, REMARK, CHANNEL_TYPE, NOTIFY_URL, REDIRECT_URL,BUSINESS_INST_ID,
			CHANNEL_ORDER, CHANNEL_NAME, SOURCE_TYPE, BANK_ACCOUNT_TYPE, CUSTOMERNAME , 
			ACCOUNT_TYPE_CODE , BUSINESS_CODE, TH_STATE, BATCH_NO, OPR_REMARK, OPR_ID, OPR_TIME, OPR_OPERATED,
			ERROR_CODE,PAY_METHOD,PAY_PASSWAY,card_no_cipher,bank_user_name_full,bank_user_cert_full,
			CHANNEL_RESP_CODE,CHANNEL_RESP_MSG,channel_query_code,channel_query_msg,SERVICE_FEE,SERVICE_FEE_STATUS,
			auth_customer_code,trade_source,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,FEE_PER,RATE_MODE,SERVICE_FEE_TRANSACTION_NO,TRANSACTION_TYPE
		)
		values (#{id,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, 
		#{outTradeNo,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR},
		#{commissionedCustomerCode,jdbcType=VARCHAR},
		#{terminalNo,jdbcType=VARCHAR}, #{totalFee,jdbcType=DECIMAL}, 
		#{payCurrency,jdbcType=VARCHAR}, #{arrivalType,jdbcType=VARCHAR},
		#{actualFee,jdbcType=DECIMAL}, #{procedureFee,jdbcType=DECIMAL}, 
		#{produceRate,jdbcType=VARCHAR}, #{cardId,jdbcType=DECIMAL}, 
		#{cardNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
		#{beginTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
		#{payState,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR},
		#{channelType,jdbcType=CHAR}, #{notifyUrl,jdbcType=VARCHAR},
		#{redirectUrl,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR},
		#{channelOrder,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR},
		#{sourceType,jdbcType=VARCHAR}, #{bankAccountType,jdbcType=VARCHAR},
		#{customerName,jdbcType=VARCHAR} , #{accountTypeCode , jdbcType = VARCHAR} , 
		#{businessCode , jdbcType = VARCHAR}, #{thState , jdbcType = VARCHAR},
		#{batchNo , jdbcType = VARCHAR}, #{oprRemark , jdbcType = VARCHAR},
		#{oprId , jdbcType = VARCHAR},#{oprTime , jdbcType = TIMESTAMP},
		#{oprOperated , jdbcType = VARCHAR},#{errorCode , jdbcType = VARCHAR},
		#{payMethod , jdbcType = VARCHAR},#{payPassWay , jdbcType = VARCHAR},
		#{payChannelId,jdbcType=DECIMAL},#{channelCategoryId,jdbcType=DECIMAL},
		#{cardNoCipher , jdbcType = VARCHAR},#{bankUserNameFull , jdbcType = VARCHAR},
		#{bankUserCertFull , jdbcType = VARCHAR},#{channelRespCode,jdbcType=VARCHAR}, 
        #{channelRespMsg,jdbcType=VARCHAR},#{channelQueryCode,jdbcType=VARCHAR},#{channelQueryMsg,jdbcType=VARCHAR},#{serviceFee,jdbcType=DECIMAL},#{serviceFeeStatus,jdbcType=DECIMAL},
        #{authCustomerCode,jdbcType=VARCHAR},#{tradeSource,jdbcType=VARCHAR},#{businessMan,jdbcType=VARCHAR},#{businessManId,jdbcType=DECIMAL},#{companyName,jdbcType=VARCHAR},#{companyId,jdbcType=DECIMAL},
		#{feePer,jdbcType=DECIMAL},#{rateMode,jdbcType=DECIMAL},#{serviceFeeTransactionNo,jdbcType=VARCHAR}, #{transactionType,jdbcType=VARCHAR})
	</insert>
	<insert id="insertSelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder">
		insert into TXS_WITHDRAW_TRADE_ORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				ID,
			</if>
			<if test="transactionNo != null">
				TRANSACTION_NO,
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO,
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE,
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE,
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO,
			</if>
			<if test="totalFee != null">
				TOTAL_FEE,
			</if>
			<if test="payCurrency != null">
				PAY_CURRENCY,
			</if>
			<if test="arrivalType != null">
				ARRIVAL_TYPE,
			</if>
			<if test="actualFee != null">
				ACTUAL_FEE,
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE,
			</if>
			<if test="produceRate != null">
				PRODUCE_RATE,
			</if>
			<if test="cardId != null">
				CARD_ID,
			</if>
			<if test="cardNo != null">
				CARD_NO,
			</if>
			<if test="createTime != null">
				CREATE_TIME,
			</if>
			<if test="beginTime != null">
				BEGIN_TIME,
			</if>
			<if test="endTime != null">
				END_TIME,
			</if>
			<if test="payState != null">
				PAY_STATE,
			</if>
			<if test="remark != null">
				REMARK,
			</if>
			<if test="channelType != null">
				CHANNEL_TYPE,
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL,
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL,
			</if>
			<if test="businessInstId != null">
				BUSINESS_INST_ID,
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER,
			</if>
			<if test="channelName != null">
				CHANNEL_NAME,
			</if>
			<if test="sourceType != null">
				SOURCE_TYPE,
			</if>
			<if test="bankAccountType != null">
				BANK_ACCOUNT_TYPE,
			</if>
			<if test="customerName != null">
				CUSTOMERNAME,
			</if>
			<if test="accountTypeCode != null">
				ACCOUNT_TYPE_CODE,
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE,
			</if>
			<if test="thState != null">
				TH_STATE,
			</if>
			<if test="batchNo != null">
				BATCH_NO,
			</if>
			<if test="bankUserName != null">
				bank_user_name,
			</if>
			<if test="bankUserCert != null">
				bank_user_cert,
			</if>
			<if test="bankName != null">
				bank_name,
			</if>
			<if test="bankProvince != null">
				bank_province,
			</if>
			<if test="bankCity != null">
				bank_city,
			</if>
			<if test="bankSub != null">
				bank_sub,
			</if>
			<if test="bankNo != null">
				bank_no,
			</if>
			<if test="oprRemark != null">
				OPR_REMARK,
			</if>
			<if test="oprId != null">
				OPR_ID,
			</if>
			<if test="oprTime != null">
				OPR_TIME,
			</if>
			<if test="oprOperated != null">
				OPR_OPERATED,
			</if>
			<if test="errorCode != null">
				ERROR_CODE,
			</if>
			<if test="payMethod != null">
				PAY_METHOD,
			</if>
			<if test="payPassWay != null">
				PAY_PASSWAY,
			</if>
			<if test="payChannelId != null">
				PAY_CHANNEL_ID,
			</if>
			<if test="channelCategoryId != null">
				CHANNEL_CATEGORY_ID,
			</if>
			<if test="cardNoCipher != null">
				card_no_cipher,
			</if>
			<if test="bankUserNameFull != null">
				bank_user_name_full,
			</if>
			<if test="bankUserCertFull != null">
				bank_user_cert_full,
			</if>
			 <if test="channelRespCode != null">
               CHANNEL_RESP_CODE,
            </if>
            <if test="channelRespMsg != null">
                CHANNEL_RESP_MSG,
            </if>
 			<if test="channelQueryCode != null">
                channel_query_code,
            </if> 
            <if test="channelQueryMsg != null" >
        		channel_query_msg,
      		</if>
			<if test="serviceFee != null" >
				SERVICE_FEE,
			</if>
			<if test="serviceFeeStatus != null" >
				SERVICE_FEE_STATUS,
			</if>
			<if test="authCustomerCode != null" >
				auth_customer_code,
			</if>	
			<if test="tradeSource != null" >
				trade_source,
			</if>
            <if test="businessMan != null" >
                BUSINESS_MAN,
            </if>
            <if test="businessManId != null" >
                BUSINESS_MAN_ID,
            </if>
            <if test="companyName != null" >
                COMPANY_NAME,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
			<if test="feePer != null" >
				FEE_PER,
			</if>
			<if test="rateMode != null" >
				RATE_MODE,
			</if>
			<if test="serviceFeeTransactionNo != null" >
				SERVICE_FEE_TRANSACTION_NO,
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=DECIMAL},
			</if>
			<if test="transactionNo != null">
				#{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				#{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				#{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				#{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				#{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="totalFee != null">
				#{totalFee,jdbcType=DECIMAL},
			</if>
			<if test="payCurrency != null">
				#{payCurrency,jdbcType=VARCHAR},
			</if>
			<if test="arrivalType != null">
				#{arrivalType,jdbcType=VARCHAR},
			</if>
			<if test="actualFee != null">
				#{actualFee,jdbcType=DECIMAL},
			</if>
			<if test="procedureFee != null">
				#{procedureFee,jdbcType=DECIMAL}, 
			</if>
			<if test="produceRate != null">
				#{produceRate,jdbcType=VARCHAR}, 
			</if>
			<if test="cardId != null">
				#{cardId,jdbcType=DECIMAL}, 
			</if>
			<if test="cardNo != null">
				#{cardNo,jdbcType=VARCHAR},  
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},  
			</if>
			<if test="beginTime != null">
				#{beginTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				#{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				#{payState,jdbcType=CHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="channelType != null">
				#{channelType,jdbcType=CHAR},
			</if>
			<if test="notifyUrl != null">
				#{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				#{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="businessInstId != null">
				#{businessInstId,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				#{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				#{channelName,jdbcType=VARCHAR},
			</if>
			<if test="sourceType != null">
				#{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="bankAccountType != null">
				#{bankAccountType,jdbcType=VARCHAR},
			</if>
			<if test="customerName != null">
				#{customerName,jdbcType=VARCHAR},
			</if>
			<if test="accountTypeCode != null">
				#{accountTypeCode,jdbcType=VARCHAR},
			</if>
			<if test="businessCode != null">
				#{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="thState != null">
				#{thState,jdbcType=VARCHAR},
			</if>
			<if test="batchNo != null">
				#{batchNo,jdbcType=VARCHAR},
			</if>
			<if test="bankUserName != null">
				#{bankUserName,jdbcType=VARCHAR},
			</if>
			<if test="bankUserCert != null">
				#{bankUserCert,jdbcType=VARCHAR},
			</if>
			<if test="bankName != null">
				#{bankName,jdbcType=VARCHAR},
			</if>
			<if test="bankProvince != null">
				#{bankProvince,jdbcType=VARCHAR},
			</if>
			<if test="bankCity != null">
				#{bankCity,jdbcType=VARCHAR},
			</if>
			<if test="bankSub != null">
				#{bankSub,jdbcType=VARCHAR},
			</if>
			<if test="bankNo != null">
				#{bankNo,jdbcType=VARCHAR},
			</if>
			<if test="oprRemark != null">
				#{oprRemark , jdbcType = VARCHAR},
			</if>
			<if test="oprId != null">
				#{oprId , jdbcType = VARCHAR},
			</if>
			<if test="oprTime != null">
				#{oprTime , jdbcType = TIMESTAMP},
			</if>
			<if test="oprOperated != null">
				#{oprOperated , jdbcType = VARCHAR},
			</if>
			<if test="errorCode != null">
				#{errorCode , jdbcType = VARCHAR},
			</if>
			<if test="payMethod != null">
				#{payMethod , jdbcType = VARCHAR},
			</if>
			<if test="payPassWay != null">
				#{payPassWay , jdbcType = VARCHAR},
			</if>
			<if test="payChannelId != null">
				#{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="channelCategoryId != null">
				#{channelCategoryId,jdbcType=DECIMAL},
			</if>
			<if test="cardNoCipher != null">
				#{cardNoCipher , jdbcType = VARCHAR},
			</if>
			<if test="bankUserNameFull != null">
				#{bankUserNameFull , jdbcType = VARCHAR},
			</if>
			<if test="bankUserCertFull != null">
				#{bankUserCertFull , jdbcType = VARCHAR},
			</if>
			 <if test="channelRespCode != null">
                #{channelRespCode,jdbcType=VARCHAR},
            </if>
            <if test="channelRespMsg != null">
                #{channelRespMsg,jdbcType=VARCHAR},
            </if>
 			<if test="channelQueryCode != null">
                #{channelQueryCode,jdbcType=VARCHAR},
            </if> 
            <if test="channelQueryMsg != null" >
        		#{channelQueryMsg,jdbcType=VARCHAR},
      		</if>
			<if test="serviceFee != null" >
				#{serviceFee,jdbcType=DECIMAL},
			</if>
			<if test="serviceFeeStatus != null" >
				#{serviceFeeStatus,jdbcType=DECIMAL},
			</if>
			<if test="authCustomerCode != null" >
				#{authCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeSource != null" >
				#{tradeSource,jdbcType=VARCHAR},
			</if>
            <if test="businessMan != null" >
                #{businessMan,jdbcType=VARCHAR},
            </if>
            <if test="businessManId != null" >
                #{businessManId,jdbcType=DECIMAL},
            </if>
            <if test="companyName != null" >
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=DECIMAL},
            </if>
			<if test="feePer != null" >
				#{feePer,jdbcType=DECIMAL},
			</if>
			<if test="rateMode != null" >
				#{rateMode,jdbcType=DECIMAL},
			</if>
			<if test="serviceFeeTransactionNo != null" >
				#{serviceFeeTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				#{transactionType,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder">
		update TXS_WITHDRAW_TRADE_ORDER
		<set>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="commissionedCustomerCode != null">
				COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="totalFee != null">
				TOTAL_FEE = #{totalFee,jdbcType=DECIMAL},
			</if>
			<if test="payCurrency != null">
				PAY_CURRENCY = #{payCurrency,jdbcType=VARCHAR},
			</if>
			<if test="arrivalType != null">
				ARRIVAL_TYPE = #{arrivalType,jdbcType=VARCHAR},
			</if>
			<if test="actualFee != null">
				ACTUAL_FEE = #{actualFee,jdbcType=DECIMAL},
			</if>
			<if test="procedureFee != null">
				PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
			</if>
			<if test="produceRate != null">
				PRODUCE_RATE = #{produceRate,jdbcType=VARCHAR},
			</if>
			<if test="cardId != null">
				CARD_ID = #{cardId,jdbcType=DECIMAL},
			</if>
			<if test="cardNo != null">
				CARD_NO = #{cardNo,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="beginTime != null">
				BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="channelType != null">
				CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="businessInstId != null">
				BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
			</if>
			<if test="channelOrder != null">
				CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
			</if>
			<if test="channelName != null">
				CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
			</if>
			<if test="sourceType != null">
				SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="bankAccountType != null">
				BANK_ACCOUNT_TYPE = #{bankAccountType,jdbcType=VARCHAR},
			</if>
			<if test="customerName != null">
				CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="accountTypeCode != null">
				ACCOUNT_TYPE_CODE = #{accountTypeCode,jdbcType=VARCHAR},
			</if>
			<if test="businessCode != null">
				BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
			</if>
			<if test="thState != null">
				TH_STATE = #{thState,jdbcType=VARCHAR},
			</if>
			<if test="batchNo != null">
				BATCH_NO = #{batchNo,jdbcType=VARCHAR},
			</if>
			<if test="oprRemark != null">
				OPR_REMARK = #{oprRemark , jdbcType = VARCHAR},
			</if>
			<if test="oprId != null">
				OPR_TIME = OPR_ID = #{oprId , jdbcType = VARCHAR},
			</if>
			<if test="oprTime != null">
				OPR_TIME = #{oprTime , jdbcType = TIMESTAMP},
			</if>
			<if test="oprOperated != null">
				OPR_OPERATED = #{oprOperated , jdbcType = VARCHAR},
			</if>
			<if test="errorCode != null">
				ERROR_CODE = #{errorCode , jdbcType = VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod , jdbcType = VARCHAR},
			</if>
			<if test="payPassWay != null">
				PAY_PASSWAY = #{payPassWay , jdbcType = VARCHAR},
			</if>
			<if test="payChannelId != null">
				pay_channel_id = #{payChannelId,jdbcType=DECIMAL},
			</if>
			<if test="channelCategoryId != null">
				channel_category_id = #{channelCategoryId,jdbcType=DECIMAL},
			</if>
			<if test="cardNoCipher != null">
				card_no_cipher = #{cardNoCipher , jdbcType = VARCHAR},
			</if>
			<if test="bankUserNameFull != null">
				bank_user_name_full = #{bankUserNameFull , jdbcType = VARCHAR},
			</if>
			<if test="bankUserCertFull != null">
				bank_user_cert_full = #{bankUserCertFull , jdbcType = VARCHAR},
			</if>
			 <if test="channelRespCode != null">
                CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
            </if>
            <if test="channelRespMsg != null">
                CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
            </if>
 			<if test="channelQueryCode != null">
               channel_query_code = #{channelQueryCode,jdbcType=VARCHAR},
            </if> 
            <if test="channelQueryMsg != null" >
        		channel_query_msg = #{channelQueryMsg,jdbcType=VARCHAR},
      		</if>
			<if test="serviceFee != null" >
				SERVICE_FEE = #{serviceFee,jdbcType=DECIMAL},
			</if>
			<if test="serviceFeeStatus != null" >
				SERVICE_FEE_STATUS = #{serviceFeeStatus,jdbcType=DECIMAL},
			</if>
			<if test="authCustomerCode != null" >
				auth_customer_code = #{authCustomerCode,jdbcType=VARCHAR},
			</if>
			<if test="tradeSource != null" >
				trade_source = #{tradeSource,jdbcType=VARCHAR},
			</if>
            <if test="businessMan != null" >
                BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
            </if>
            <if test="businessManId != null" >
                BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
            </if>
            <if test="companyName != null" >
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=DECIMAL},
            </if>
			<if test="feePer != null" >
				FEE_PER = #{feePer,jdbcType=DECIMAL},
			</if>
			<if test="rateMode != null" >
				RATE_MODE = #{rateMode,jdbcType=DECIMAL},
			</if>
			<if test="serviceFeeTransactionNo != null" >
				SERVICE_FEE_TRANSACTION_NO = #{serviceFeeTransactionNo,jdbcType=VARCHAR},
			</if>
			<if test="transactionType != null">
				TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
			</if>
		</set>
		where ID = #{id,jdbcType=DECIMAL}
	</update>
	<update id="updateByPrimaryKey"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder">
		update TXS_WITHDRAW_TRADE_ORDER
		set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
		OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
		COMMISSIONED_CUSTOMER_CODE = #{commissionedCustomerCode,jdbcType=VARCHAR},
		TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
		TOTAL_FEE = #{totalFee,jdbcType=DECIMAL},
		PAY_CURRENCY = #{payCurrency,jdbcType=VARCHAR},
		ARRIVAL_TYPE = #{arrivalType,jdbcType=VARCHAR},
		ACTUAL_FEE = #{actualFee,jdbcType=DECIMAL},
		PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
		PRODUCE_RATE = #{produceRate,jdbcType=VARCHAR},
		CARD_ID = #{cardId,jdbcType=DECIMAL},
		CARD_NO = #{cardNo,jdbcType=VARCHAR},
		CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
		BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
		END_TIME = #{endTime,jdbcType=TIMESTAMP},
		PAY_STATE = #{payState,jdbcType=CHAR},
		REMARK = #{remark,jdbcType=VARCHAR},
		CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
		NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
		REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
		BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
		CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
		CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
		SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
		BANK_ACCOUNT_TYPE = #{bankAccountType,jdbcType=VARCHAR},
		CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
		ACCOUNT_TYPE_CODE = #{accountTypeCode,jdbcType=VARCHAR},
		BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
		TH_STATE = #{thState,jdbcType=VARCHAR},
		BATCH_NO = #{batchNo,jdbcType=VARCHAR},
		OPR_REMARK = #{oprRemark , jdbcType = VARCHAR},
		OPR_TIME = OPR_ID = #{oprId , jdbcType = VARCHAR},
		OPR_TIME = #{oprTime , jdbcType = TIMESTAMP},
		OPR_OPERATED = #{oprOperated , jdbcType = VARCHAR},
		ERROR_CODE = #{errorCode , jdbcType = VARCHAR},
		PAY_METHOD = #{payMethod , jdbcType = VARCHAR},
		PAY_PASSWAY = #{payPassWay , jdbcType = VARCHAR},
		PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL},
		CHANNEL_CATEGORY_ID = #{channelCategoryId,jdbcType=DECIMAL},
		card_no_cipher = #{cardNoCipher , jdbcType = VARCHAR},
		bank_user_name_full = #{bankUserNameFull , jdbcType = VARCHAR},
		bank_user_cert_full = #{bankUserCertFull , jdbcType = VARCHAR},
		CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
		CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
		channel_query_code = #{channelQueryCode,jdbcType=VARCHAR},
		channel_query_msg = #{channelQueryMsg,jdbcType=VARCHAR},
		SERVICE_FEE = #{serviceFee,jdbcType=DECIMAL},
		SERVICE_FEE_STATUS = #{serviceFeeStatus,jdbcType=DECIMAL},
		auth_customer_code = #{authCustomerCode,jdbcType=VARCHAR},
		trade_source = #{tradeSource,jdbcType=VARCHAR},
        BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
	    BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
	    COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
	    COMPANY_ID = #{companyId,jdbcType=DECIMAL},
		FEE_PER = #{feePer,jdbcType=DECIMAL},
		RATE_MODE = #{rateMode,jdbcType=DECIMAL},
		SERVICE_FEE_TRANSACTION_NO = #{serviceFeeTransactionNo,jdbcType=VARCHAR},
		TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
		where ID = #{id,jdbcType=DECIMAL}
	</update>
	
	<update id="updateOprRemarkByTansactionNo"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder">
		update TXS_WITHDRAW_TRADE_ORDER
		set OPR_REMARK = #{oprRemark,jdbcType=VARCHAR},
			OPR_TIME = #{oprTime,jdbcType=TIMESTAMP},
			OPR_ID  = #{oprId,jdbcType=VARCHAR},
			OPR_OPERATED =  #{oprOperated,jdbcType=VARCHAR}
		where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	</update>
	
	<select id="selectByBusinessInstId" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
	</select>
	
	<select id="selectByCustomerCodeAndStateAndCreateTimeInDate" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
		and PAY_STATE = #{payState , jdbcType = VARCHAR} 
		and to_char(END_TIME,'yyyyMMdd') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMMdd')
	</select>
	
	<select id="selectRepeatOrderNoByCustomerCodeAndOutTradeNo" resultType="String">
		select
		OUT_TRADE_NO
		from TXS_WITHDRAW_TRADE_ORDER
		where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
		and OUT_TRADE_NO in 
		<foreach item="item" index="index" collection="outTradeNoList" open="(" separator="," close=")">  
       		 <if  test="(index % 999) == 998"> NULL) OR OUT_TRADE_NO IN (</if>#{item}  
    	</foreach>
	</select>
	
	<select id="selectCountOrderByBatchAndState" resultType="java.lang.Integer">
		select
		count(*)
		from TXS_WITHDRAW_TRADE_ORDER
		where BATCH_NO = #{batchNo,jdbcType=VARCHAR} and 
		<if test="payState != null">
			PAY_STATE = #{payState,jdbcType=CHAR},
		</if>
	</select>
	<select id="selectSumAmountByBatchAndState" resultType="java.lang.Long">
		select
		sum(ACTUAL_FEE)
		from TXS_WITHDRAW_TRADE_ORDER
		where BATCH_NO = #{batchNo,jdbcType=VARCHAR} and 
		<if test="payState != null">
			PAY_STATE = #{payState,jdbcType=CHAR},
		</if>
	</select>
	
	<select id="selectByBatchNo" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where BATCH_NO = #{batchNo,jdbcType=VARCHAR}
	</select>
	
	<select id="selectByParentCustomerAndDate" resultMap="BaseResultMap">
		select * from (
			select
			t.*
			from TXS_WITHDRAW_TRADE_ORDER t,CUM_CUSTOMER_INFO c
			where c.parent_customer_code = #{customerCode}
			AND t.customer_code = c.customer_code
			and t.end_time BETWEEN #{startTime} AND #{endTime}
			AND t.pay_state = '00'
			AND c.customer_category = 'EFPS_CUSTOMER_MEMBER'
			UNION
			SELECT
			t.*
			from TXS_WITHDRAW_TRADE_ORDER t
			WHERE t.customer_code = #{customerCode}
			and t.end_time BETWEEN #{startTime} AND #{endTime}
			AND t.pay_state = '00'
		)
		ORDER BY create_time
	</select>
	  <select id="selectPageCountInfo" resultMap="PageResultMap"
		parameterType="java.util.Map">
		select COALESCE(SUM(actual_fee),0) total_amount, COALESCE(SUM(procedure_fee),0) total_procedure_fee,COALESCE(SUM(total_fee),0) total_zh_amount, count(1) total, COALESCE(SUM(service_fee),0) total_service_fee
		from TXS_WITHDRAW_TRADE_ORDER
		<where>
			<include refid="tradeQueryCondition"/>
		</where>
	</select>

	<sql id="tradeQueryCondition">
		<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="customerName != null and customerName !=''">
				AND CUSTOMERNAME  like concat(#{customerName},'%')
			</if>
			<if test="bankName != null and bankName !=''">
				AND BANK_NAME  like concat(#{bankName},'%')
			</if>
			<if test="channelName != null and channelName !=''">
				AND CHANNEL_NAME = #{channelName,jdbcType=VARCHAR} 
			</if>
			<if test="beginTime!=null ">
				AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
				AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
			<if test="businessInstId!=null ">
   			 	AND BUSINESS_INST_ID =  #{businessInstId,jdbcType=VARCHAR}
			</if>
			<if test="channelOrder!=null ">
				AND CHANNEL_ORDER =  #{channelOrder,jdbcType=VARCHAR}
			</if>
			<if test="channelName!=null ">
				AND CHANNEL_NAME =  #{channelName,jdbcType=VARCHAR}
			</if>
			<if test="accountTypeCode!=null ">
				AND ACCOUNT_TYPE_CODE =  #{accountTypeCode,jdbcType=VARCHAR}
			</if>
			<if test="businessCode!=null ">
				AND BUSINESS_CODE =  #{businessCode,jdbcType=VARCHAR}
			</if>
			<if test="sourceType!=null ">
				AND SOURCE_TYPE =  #{sourceType,jdbcType=VARCHAR}
			</if>
			<if test="batchNo!=null ">
				AND BATCH_NO =  #{batchNo,jdbcType=VARCHAR}
			</if>
			<if test="oprOperated!=null ">
				AND OPR_OPERATED =  #{oprOperated,jdbcType=VARCHAR}
			</if>	
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
			</if>
			<if test="payPassWay != null and payPassWay !=''">
				AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
			</if>
			<if test="payChannelId != null">
				AND PAY_CHANNEL_ID = #{payChannelId,jdbcType=DECIMAL}
			</if>
			<if test="channelCategoryId != null">
				AND CHANNEL_CATEGORY_ID = #{channelCategoryId,jdbcType=DECIMAL}
			</if>
			<if test="exandCodeList != null">
				AND CUSTOMER_CODE in
				<foreach item="item" index="index" collection="exandCodeList" open="(" separator="," close=")">  
	       		 #{item, jdbcType=VARCHAR}  
	    		</foreach>
    		</if>	
	</sql>

	<select id="selectByCustomerCodeAndEndTimeInDateByMonth" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR}
		and to_char(END_TIME,'yyyyMM') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMM')
	</select>

	<select id="selectAuditList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_WITHDRAW_TRADE_ORDER
		WHERE CREATE_TIME &gt;= #{startTime}
		AND CREATE_TIME &lt;= #{endTime}
		AND PAY_STATE = 03
		AND (TRANSACTION_NO like 'TX%' or TRANSACTION_NO like '44%')
	</select>
</mapper>