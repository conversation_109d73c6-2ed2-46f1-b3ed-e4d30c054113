<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsEnterpriseDetailMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsEnterpriseDetail" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="CURRENCY_TYPE" property="currencyType" jdbcType="VARCHAR" />
    <result column="PROCEDURE_FEE" property="procedureFee" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR" />
    <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="TRANSACTION_TYPE" property="transactionType" jdbcType="VARCHAR" />
    <result column="BUSINESS_INST_ID" property="businessInstId" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
    <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
    <result column="CHNL_TRANSACTION_NO" property="chnlTransactionNo" jdbcType="VARCHAR" />
    <result column="CHANL_PROCEDURE_FEE" property="chanlProcedureFee" jdbcType="DECIMAL" />
    <result column="ABU_ITEM" property="abuItem" jdbcType="VARCHAR" />
    <result column="ENT_ENG_NAME" property="entEngName" jdbcType="VARCHAR" />
    <result column="ORI_REG_NO" property="oriRegNo" jdbcType="VARCHAR" />
    <result column="ORG_CODES" property="orgCodes" jdbcType="VARCHAR" />
    <result column="REG_NO" property="regNo" jdbcType="VARCHAR" />
    <result column="REC_CAP" property="recCap" jdbcType="VARCHAR" />
    <result column="CREDIT_CODE" property="creditCode" jdbcType="VARCHAR" />
    <result column="OP_TO" property="opTo" jdbcType="VARCHAR" />
    <result column="OP_FROM" property="opFrom" jdbcType="VARCHAR" />
    <result column="ENT_NAME" property="entName" jdbcType="VARCHAR" />
    <result column="REG_CAP" property="regCap" jdbcType="VARCHAR" />
    <result column="ES_DATE" property="esDate" jdbcType="VARCHAR" />
    <result column="LEGAL_PERSON_NAME" property="legalPersonName" jdbcType="VARCHAR" />
    <result column="REG_ORG" property="regOrg" jdbcType="VARCHAR" />
    <result column="ENT_STATUS" property="entStatus" jdbcType="VARCHAR" />
    <result column="ENT_TYPE" property="entType" jdbcType="VARCHAR" />
    <result column="REG_CAP_CURRENCY" property="regCapCurrency" jdbcType="VARCHAR" />
    <result column="AUCH_YEAR" property="auchYear" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="RVE_DATE" property="rveDate" jdbcType="VARCHAR" />
    <result column="CAN_DATE" property="canDate" jdbcType="VARCHAR" />
    <result column="REG_ORG_CODE" property="regOrgCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_SCOPE" property="businessScope" jdbcType="VARCHAR" />
    <result column="APPR_DATE" property="apprDate" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, OUT_TRADE_NO, TRANSACTION_NO, CUSTOMER_CODE, PAY_METHOD, CURRENCY_TYPE, PROCEDURE_FEE, 
    CREATE_TIME, STATE, UPDATE_TIME, ERROR_CODE, END_TIME, REMARK, TRANSACTION_TYPE, 
    BUSINESS_INST_ID, BUSINESS_CODE, RETURN_CODE, RETURN_MSG, CHNL_TRANSACTION_NO, CHANL_PROCEDURE_FEE, 
    ABU_ITEM, ENT_ENG_NAME, ORI_REG_NO, ORG_CODES, REG_NO, REC_CAP, CREDIT_CODE, OP_TO, 
    OP_FROM, ENT_NAME, REG_CAP, ES_DATE, LEGAL_PERSON_NAME, REG_ORG, ENT_STATUS, ENT_TYPE, 
    REG_CAP_CURRENCY, AUCH_YEAR, ADDRESS, RVE_DATE, CAN_DATE, REG_ORG_CODE, BUSINESS_SCOPE, 
    APPR_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TXS_ENTERPRISE_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_ENTERPRISE_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsEnterpriseDetail" >
    insert into TXS_ENTERPRISE_DETAIL (ID, OUT_TRADE_NO, TRANSACTION_NO, 
      CUSTOMER_CODE, PAY_METHOD, CURRENCY_TYPE, 
      PROCEDURE_FEE, CREATE_TIME, STATE, 
      UPDATE_TIME, ERROR_CODE, END_TIME, 
      REMARK, TRANSACTION_TYPE, BUSINESS_INST_ID, 
      BUSINESS_CODE, RETURN_CODE, RETURN_MSG, 
      CHNL_TRANSACTION_NO, CHANL_PROCEDURE_FEE, ABU_ITEM, 
      ENT_ENG_NAME, ORI_REG_NO, ORG_CODES, 
      REG_NO, REC_CAP, CREDIT_CODE, 
      OP_TO, OP_FROM, ENT_NAME, 
      REG_CAP, ES_DATE, LEGAL_PERSON_NAME, 
      REG_ORG, ENT_STATUS, ENT_TYPE, 
      REG_CAP_CURRENCY, AUCH_YEAR, ADDRESS, 
      RVE_DATE, CAN_DATE, REG_ORG_CODE, 
      BUSINESS_SCOPE, APPR_DATE)
    values (#{id,jdbcType=DECIMAL}, #{outTradeNo,jdbcType=VARCHAR}, #{transactionNo,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, #{currencyType,jdbcType=VARCHAR}, 
      #{procedureFee,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{state,jdbcType=CHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR}, #{endTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{transactionType,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR}, 
      #{businessCode,jdbcType=VARCHAR}, #{returnCode,jdbcType=VARCHAR}, #{returnMsg,jdbcType=VARCHAR}, 
      #{chnlTransactionNo,jdbcType=VARCHAR}, #{chanlProcedureFee,jdbcType=DECIMAL}, #{abuItem,jdbcType=VARCHAR}, 
      #{entEngName,jdbcType=VARCHAR}, #{oriRegNo,jdbcType=VARCHAR}, #{orgCodes,jdbcType=VARCHAR}, 
      #{regNo,jdbcType=VARCHAR}, #{recCap,jdbcType=VARCHAR}, #{creditCode,jdbcType=VARCHAR}, 
      #{opTo,jdbcType=VARCHAR}, #{opFrom,jdbcType=VARCHAR}, #{entName,jdbcType=VARCHAR}, 
      #{regCap,jdbcType=VARCHAR}, #{esDate,jdbcType=VARCHAR}, #{legalPersonName,jdbcType=VARCHAR}, 
      #{regOrg,jdbcType=VARCHAR}, #{entStatus,jdbcType=VARCHAR}, #{entType,jdbcType=VARCHAR}, 
      #{regCapCurrency,jdbcType=VARCHAR}, #{auchYear,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{rveDate,jdbcType=VARCHAR}, #{canDate,jdbcType=VARCHAR}, #{regOrgCode,jdbcType=VARCHAR}, 
      #{businessScope,jdbcType=VARCHAR}, #{apprDate,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsEnterpriseDetail" >
    insert into TXS_ENTERPRISE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="payMethod != null" >
        PAY_METHOD,
      </if>
      <if test="currencyType != null" >
        CURRENCY_TYPE,
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="errorCode != null" >
        ERROR_CODE,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE,
      </if>
      <if test="businessInstId != null" >
        BUSINESS_INST_ID,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="returnCode != null" >
        RETURN_CODE,
      </if>
      <if test="returnMsg != null" >
        RETURN_MSG,
      </if>
      <if test="chnlTransactionNo != null" >
        CHNL_TRANSACTION_NO,
      </if>
      <if test="chanlProcedureFee != null" >
        CHANL_PROCEDURE_FEE,
      </if>
      <if test="abuItem != null" >
        ABU_ITEM,
      </if>
      <if test="entEngName != null" >
        ENT_ENG_NAME,
      </if>
      <if test="oriRegNo != null" >
        ORI_REG_NO,
      </if>
      <if test="orgCodes != null" >
        ORG_CODES,
      </if>
      <if test="regNo != null" >
        REG_NO,
      </if>
      <if test="recCap != null" >
        REC_CAP,
      </if>
      <if test="creditCode != null" >
        CREDIT_CODE,
      </if>
      <if test="opTo != null" >
        OP_TO,
      </if>
      <if test="opFrom != null" >
        OP_FROM,
      </if>
      <if test="entName != null" >
        ENT_NAME,
      </if>
      <if test="regCap != null" >
        REG_CAP,
      </if>
      <if test="esDate != null" >
        ES_DATE,
      </if>
      <if test="legalPersonName != null" >
        LEGAL_PERSON_NAME,
      </if>
      <if test="regOrg != null" >
        REG_ORG,
      </if>
      <if test="entStatus != null" >
        ENT_STATUS,
      </if>
      <if test="entType != null" >
        ENT_TYPE,
      </if>
      <if test="regCapCurrency != null" >
        REG_CAP_CURRENCY,
      </if>
      <if test="auchYear != null" >
        AUCH_YEAR,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="rveDate != null" >
        RVE_DATE,
      </if>
      <if test="canDate != null" >
        CAN_DATE,
      </if>
      <if test="regOrgCode != null" >
        REG_ORG_CODE,
      </if>
      <if test="businessScope != null" >
        BUSINESS_SCOPE,
      </if>
      <if test="apprDate != null" >
        APPR_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="currencyType != null" >
        #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="procedureFee != null" >
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        #{state,jdbcType=CHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null" >
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null" >
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null" >
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="returnCode != null" >
        #{returnCode,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null" >
        #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="chnlTransactionNo != null" >
        #{chnlTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="chanlProcedureFee != null" >
        #{chanlProcedureFee,jdbcType=DECIMAL},
      </if>
      <if test="abuItem != null" >
        #{abuItem,jdbcType=VARCHAR},
      </if>
      <if test="entEngName != null" >
        #{entEngName,jdbcType=VARCHAR},
      </if>
      <if test="oriRegNo != null" >
        #{oriRegNo,jdbcType=VARCHAR},
      </if>
      <if test="orgCodes != null" >
        #{orgCodes,jdbcType=VARCHAR},
      </if>
      <if test="regNo != null" >
        #{regNo,jdbcType=VARCHAR},
      </if>
      <if test="recCap != null" >
        #{recCap,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null" >
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="opTo != null" >
        #{opTo,jdbcType=VARCHAR},
      </if>
      <if test="opFrom != null" >
        #{opFrom,jdbcType=VARCHAR},
      </if>
      <if test="entName != null" >
        #{entName,jdbcType=VARCHAR},
      </if>
      <if test="regCap != null" >
        #{regCap,jdbcType=VARCHAR},
      </if>
      <if test="esDate != null" >
        #{esDate,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonName != null" >
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="regOrg != null" >
        #{regOrg,jdbcType=VARCHAR},
      </if>
      <if test="entStatus != null" >
        #{entStatus,jdbcType=VARCHAR},
      </if>
      <if test="entType != null" >
        #{entType,jdbcType=VARCHAR},
      </if>
      <if test="regCapCurrency != null" >
        #{regCapCurrency,jdbcType=VARCHAR},
      </if>
      <if test="auchYear != null" >
        #{auchYear,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="rveDate != null" >
        #{rveDate,jdbcType=VARCHAR},
      </if>
      <if test="canDate != null" >
        #{canDate,jdbcType=VARCHAR},
      </if>
      <if test="regOrgCode != null" >
        #{regOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null" >
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="apprDate != null" >
        #{apprDate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsEnterpriseDetail" >
    update TXS_ENTERPRISE_DETAIL
    <set >
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="currencyType != null" >
        CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="procedureFee != null" >
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null" >
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null" >
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null" >
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="returnCode != null" >
        RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null" >
        RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="chnlTransactionNo != null" >
        CHNL_TRANSACTION_NO = #{chnlTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="chanlProcedureFee != null" >
        CHANL_PROCEDURE_FEE = #{chanlProcedureFee,jdbcType=DECIMAL},
      </if>
      <if test="abuItem != null" >
        ABU_ITEM = #{abuItem,jdbcType=VARCHAR},
      </if>
      <if test="entEngName != null" >
        ENT_ENG_NAME = #{entEngName,jdbcType=VARCHAR},
      </if>
      <if test="oriRegNo != null" >
        ORI_REG_NO = #{oriRegNo,jdbcType=VARCHAR},
      </if>
      <if test="orgCodes != null" >
        ORG_CODES = #{orgCodes,jdbcType=VARCHAR},
      </if>
      <if test="regNo != null" >
        REG_NO = #{regNo,jdbcType=VARCHAR},
      </if>
      <if test="recCap != null" >
        REC_CAP = #{recCap,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null" >
        CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="opTo != null" >
        OP_TO = #{opTo,jdbcType=VARCHAR},
      </if>
      <if test="opFrom != null" >
        OP_FROM = #{opFrom,jdbcType=VARCHAR},
      </if>
      <if test="entName != null" >
        ENT_NAME = #{entName,jdbcType=VARCHAR},
      </if>
      <if test="regCap != null" >
        REG_CAP = #{regCap,jdbcType=VARCHAR},
      </if>
      <if test="esDate != null" >
        ES_DATE = #{esDate,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonName != null" >
        LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="regOrg != null" >
        REG_ORG = #{regOrg,jdbcType=VARCHAR},
      </if>
      <if test="entStatus != null" >
        ENT_STATUS = #{entStatus,jdbcType=VARCHAR},
      </if>
      <if test="entType != null" >
        ENT_TYPE = #{entType,jdbcType=VARCHAR},
      </if>
      <if test="regCapCurrency != null" >
        REG_CAP_CURRENCY = #{regCapCurrency,jdbcType=VARCHAR},
      </if>
      <if test="auchYear != null" >
        AUCH_YEAR = #{auchYear,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="rveDate != null" >
        RVE_DATE = #{rveDate,jdbcType=VARCHAR},
      </if>
      <if test="canDate != null" >
        CAN_DATE = #{canDate,jdbcType=VARCHAR},
      </if>
      <if test="regOrgCode != null" >
        REG_ORG_CODE = #{regOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null" >
        BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="apprDate != null" >
        APPR_DATE = #{apprDate,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsEnterpriseDetail" >
    update TXS_ENTERPRISE_DETAIL
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      STATE = #{state,jdbcType=CHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
      RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
      CHNL_TRANSACTION_NO = #{chnlTransactionNo,jdbcType=VARCHAR},
      CHANL_PROCEDURE_FEE = #{chanlProcedureFee,jdbcType=DECIMAL},
      ABU_ITEM = #{abuItem,jdbcType=VARCHAR},
      ENT_ENG_NAME = #{entEngName,jdbcType=VARCHAR},
      ORI_REG_NO = #{oriRegNo,jdbcType=VARCHAR},
      ORG_CODES = #{orgCodes,jdbcType=VARCHAR},
      REG_NO = #{regNo,jdbcType=VARCHAR},
      REC_CAP = #{recCap,jdbcType=VARCHAR},
      CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},
      OP_TO = #{opTo,jdbcType=VARCHAR},
      OP_FROM = #{opFrom,jdbcType=VARCHAR},
      ENT_NAME = #{entName,jdbcType=VARCHAR},
      REG_CAP = #{regCap,jdbcType=VARCHAR},
      ES_DATE = #{esDate,jdbcType=VARCHAR},
      LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR},
      REG_ORG = #{regOrg,jdbcType=VARCHAR},
      ENT_STATUS = #{entStatus,jdbcType=VARCHAR},
      ENT_TYPE = #{entType,jdbcType=VARCHAR},
      REG_CAP_CURRENCY = #{regCapCurrency,jdbcType=VARCHAR},
      AUCH_YEAR = #{auchYear,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      RVE_DATE = #{rveDate,jdbcType=VARCHAR},
      CAN_DATE = #{canDate,jdbcType=VARCHAR},
      REG_ORG_CODE = #{regOrgCode,jdbcType=VARCHAR},
      BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      APPR_DATE = #{apprDate,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  	<select id="selectByEnterpriseName" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_ENTERPRISE_DETAIL
		where ENT_NAME = #{enterpriseName,jdbcType=VARCHAR}
	</select>
	
  	<select id="selectSuccRecordByEnterpriseName" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_ENTERPRISE_DETAIL
		where ENT_NAME = #{enterpriseName,jdbcType=VARCHAR}
		and STATE = '00'
	</select>
  	<select id="selectSuccRecordBycreditCode" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_ENTERPRISE_DETAIL
		where credit_Code = #{creditCode,jdbcType=VARCHAR}
		and STATE = '00'
	</select>
  	<select id="selectSuccRecordByRegNo" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_ENTERPRISE_DETAIL
		where reg_No = #{regNo,jdbcType=VARCHAR}
		and STATE = '00'
	</select>	
  <update id="updateStateAsFailure">
  	update TXS_ENTERPRISE_DETAIL
  	set STATE = #{state, jdbcType=VARCHAR},
  		ERROR_CODE = #{errorCode, jdbcType=VARCHAR},
  	    UPDATE_TIME = #{updateTime, jdbcType=TIMESTAMP}
  	where ID = #{id, jdbcType=DECIMAL}
  </update> 	
</mapper>