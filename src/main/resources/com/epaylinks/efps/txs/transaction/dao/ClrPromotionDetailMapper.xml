<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.ClrPromotionDetailMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.common.business.clr.ClrPromotionDetail" >
    <id column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="PROMOTION_DETAIL" property="promotionDetail" jdbcType="VARCHAR" />
    <result column="PROMOTION_TYPE" property="promotionType" jdbcType="VARCHAR" />
    <result column="PROMOTION_TRAN_TYPE" property="promotionTranType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRANSACTION_NO, CREATE_TIME, PROMOTION_DETAIL, PROMOTION_TYPE, PROMOTION_TRAN_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from CLR_PROMOTION_DETAIL
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from CLR_PROMOTION_DETAIL
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.common.business.clr.ClrPromotionDetail" >
    insert into CLR_PROMOTION_DETAIL (TRANSACTION_NO, CREATE_TIME, PROMOTION_DETAIL, PROMOTION_TYPE, PROMOTION_TRAN_TYPE
      )
    values (#{transactionNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{promotionDetail,jdbcType=VARCHAR}, 
    #{promotionType,jdbcType=VARCHAR}, #{promotionTranType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.common.business.clr.ClrPromotionDetail" >
    insert into CLR_PROMOTION_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="promotionDetail != null" >
        PROMOTION_DETAIL,
      </if>
      <if test="promotionType != null" >
        PROMOTION_TYPE,
      </if>
      <if test="promotionTranType != null" >
        PROMOTION_TRAN_TYPE,
      </if>            
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promotionDetail != null" >
        #{promotionDetail,jdbcType=VARCHAR},
      </if>
      <if test="promotionType != null" >
        #{promotionType,jdbcType=VARCHAR},
      </if>
      <if test="promotionTranType != null" >
        #{promotionTranType,jdbcType=VARCHAR},
      </if>            
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.common.business.clr.ClrPromotionDetail" >
    update CLR_PROMOTION_DETAIL
    <set >
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promotionDetail != null" >
        PROMOTION_DETAIL = #{promotionDetail,jdbcType=VARCHAR},
      </if>
      <if test="promotionType != null" >
        PROMOTION_TYPE = #{promotionType,jdbcType=VARCHAR},
      </if>
      <if test="promotionTranType != null" >
        PROMOTION_TRAN_TYPE = #{promotionTranType,jdbcType=VARCHAR},
      </if>            
    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.common.business.clr.ClrPromotionDetail" >
    update CLR_PROMOTION_DETAIL
    set CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      PROMOTION_DETAIL = #{promotionDetail,jdbcType=VARCHAR}, 
      PROMOTION_TYPE = #{promotionType,jdbcType=VARCHAR}, 
      PROMOTION_TRAN_TYPE = #{promotionTranType,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
</mapper>