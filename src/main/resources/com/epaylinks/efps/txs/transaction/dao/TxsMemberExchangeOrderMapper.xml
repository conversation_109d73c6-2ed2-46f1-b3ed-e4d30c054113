<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsMemberExchangeOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsMemberExchangeOrder">
    <id column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="SRC_TRADE_NO" jdbcType="VARCHAR" property="srcTradeNo" />
    <result column="MEMBER_CUSTOMER_CODE" jdbcType="VARCHAR" property="memberCustomerCode" />
    <result column="SRC_AMOUNT" jdbcType="DECIMAL" property="srcAmount" />
    <result column="PAY_STATE" jdbcType="CHAR" property="payState" />
    <result column="CREATE_TIME" jdbcType="OTHER" property="createTime" />
    <result column="END_TIME" jdbcType="OTHER" property="endTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="SRC_BUSINESS_INST_ID" jdbcType="VARCHAR" property="srcBusinessInstId" />
    <result column="PARENT_CUSTOMER_CODE" jdbcType="VARCHAR" property="parentCustomerCode" />
    <result column="TARGET_AMOUNT" jdbcType="DECIMAL" property="targetAmount" />
    <result column="SRC_BUSINESS_CODE" jdbcType="VARCHAR" property="srcBusinessCode" />
    <result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType" />
    <result column="SRC_TRADE_TYPE" jdbcType="VARCHAR" property="srcTradeType" />
    <result column="SRC_CURRENCY" jdbcType="VARCHAR" property="srcCurrency" />
    <result column="TARGET_CURRENCY" jdbcType="VARCHAR" property="targetCurrency" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="TARGET_BUSINESS_INST_ID" jdbcType="VARCHAR" property="targetBusinessInstId" />
    <result column="TARGET_BUSINESS_CODE" jdbcType="VARCHAR" property="targetBusinessCode" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSACTION_NO, SRC_TRADE_NO, MEMBER_CUSTOMER_CODE, SRC_AMOUNT, PAY_STATE, CREATE_TIME, 
    END_TIME, REMARK, SRC_BUSINESS_INST_ID, PARENT_CUSTOMER_CODE, TARGET_AMOUNT, SRC_BUSINESS_CODE, 
    TRANSACTION_TYPE, SRC_TRADE_TYPE, SRC_CURRENCY, TARGET_CURRENCY, ERROR_CODE, TARGET_BUSINESS_INST_ID, 
    TARGET_BUSINESS_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_EXCHANGE_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from TXS_MEMBER_EXCHANGE_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberExchangeOrder">
    insert into TXS_MEMBER_EXCHANGE_ORDER (TRANSACTION_NO, SRC_TRADE_NO, MEMBER_CUSTOMER_CODE, 
      SRC_AMOUNT, PAY_STATE, CREATE_TIME, 
      END_TIME, REMARK, SRC_BUSINESS_INST_ID, 
      PARENT_CUSTOMER_CODE, TARGET_AMOUNT, SRC_BUSINESS_CODE, 
      TRANSACTION_TYPE, SRC_TRADE_TYPE, SRC_CURRENCY, 
      TARGET_CURRENCY, ERROR_CODE, TARGET_BUSINESS_INST_ID, 
      TARGET_BUSINESS_CODE)
    values (#{transactionNo,jdbcType=VARCHAR}, #{srcTradeNo,jdbcType=VARCHAR}, #{memberCustomerCode,jdbcType=VARCHAR}, 
      #{srcAmount,jdbcType=DECIMAL}, #{payState,jdbcType=CHAR}, #{createTime,jdbcType=OTHER}, 
      #{endTime,jdbcType=OTHER}, #{remark,jdbcType=VARCHAR}, #{srcBusinessInstId,jdbcType=VARCHAR}, 
      #{parentCustomerCode,jdbcType=VARCHAR}, #{targetAmount,jdbcType=DECIMAL}, #{srcBusinessCode,jdbcType=VARCHAR}, 
      #{transactionType,jdbcType=VARCHAR}, #{srcTradeType,jdbcType=VARCHAR}, #{srcCurrency,jdbcType=VARCHAR}, 
      #{targetCurrency,jdbcType=VARCHAR}, #{errorCode,jdbcType=VARCHAR}, #{targetBusinessInstId,jdbcType=VARCHAR}, 
      #{targetBusinessCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberExchangeOrder">
    insert into TXS_MEMBER_EXCHANGE_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="srcTradeNo != null">
        SRC_TRADE_NO,
      </if>
      <if test="memberCustomerCode != null">
        MEMBER_CUSTOMER_CODE,
      </if>
      <if test="srcAmount != null">
        SRC_AMOUNT,
      </if>
      <if test="payState != null">
        PAY_STATE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="srcBusinessInstId != null">
        SRC_BUSINESS_INST_ID,
      </if>
      <if test="parentCustomerCode != null">
        PARENT_CUSTOMER_CODE,
      </if>
      <if test="targetAmount != null">
        TARGET_AMOUNT,
      </if>
      <if test="srcBusinessCode != null">
        SRC_BUSINESS_CODE,
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE,
      </if>
      <if test="srcTradeType != null">
        SRC_TRADE_TYPE,
      </if>
      <if test="srcCurrency != null">
        SRC_CURRENCY,
      </if>
      <if test="targetCurrency != null">
        TARGET_CURRENCY,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="targetBusinessInstId != null">
        TARGET_BUSINESS_INST_ID,
      </if>
      <if test="targetBusinessCode != null">
        TARGET_BUSINESS_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="srcTradeNo != null">
        #{srcTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="memberCustomerCode != null">
        #{memberCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="srcAmount != null">
        #{srcAmount,jdbcType=DECIMAL},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=OTHER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=OTHER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="srcBusinessInstId != null">
        #{srcBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="parentCustomerCode != null">
        #{parentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="targetAmount != null">
        #{targetAmount,jdbcType=DECIMAL},
      </if>
      <if test="srcBusinessCode != null">
        #{srcBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="srcTradeType != null">
        #{srcTradeType,jdbcType=VARCHAR},
      </if>
      <if test="srcCurrency != null">
        #{srcCurrency,jdbcType=VARCHAR},
      </if>
      <if test="targetCurrency != null">
        #{targetCurrency,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessInstId != null">
        #{targetBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessCode != null">
        #{targetBusinessCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberExchangeOrder">
    update TXS_MEMBER_EXCHANGE_ORDER
    <set>
      <if test="srcTradeNo != null">
        SRC_TRADE_NO = #{srcTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="memberCustomerCode != null">
        MEMBER_CUSTOMER_CODE = #{memberCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="srcAmount != null">
        SRC_AMOUNT = #{srcAmount,jdbcType=DECIMAL},
      </if>
      <if test="payState != null">
        PAY_STATE = #{payState,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=OTHER},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=OTHER},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="srcBusinessInstId != null">
        SRC_BUSINESS_INST_ID = #{srcBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="parentCustomerCode != null">
        PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="targetAmount != null">
        TARGET_AMOUNT = #{targetAmount,jdbcType=DECIMAL},
      </if>
      <if test="srcBusinessCode != null">
        SRC_BUSINESS_CODE = #{srcBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="srcTradeType != null">
        SRC_TRADE_TYPE = #{srcTradeType,jdbcType=VARCHAR},
      </if>
      <if test="srcCurrency != null">
        SRC_CURRENCY = #{srcCurrency,jdbcType=VARCHAR},
      </if>
      <if test="targetCurrency != null">
        TARGET_CURRENCY = #{targetCurrency,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessInstId != null">
        TARGET_BUSINESS_INST_ID = #{targetBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessCode != null">
        TARGET_BUSINESS_CODE = #{targetBusinessCode,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberExchangeOrder">
    update TXS_MEMBER_EXCHANGE_ORDER
    set SRC_TRADE_NO = #{srcTradeNo,jdbcType=VARCHAR},
      MEMBER_CUSTOMER_CODE = #{memberCustomerCode,jdbcType=VARCHAR},
      SRC_AMOUNT = #{srcAmount,jdbcType=DECIMAL},
      PAY_STATE = #{payState,jdbcType=CHAR},
      CREATE_TIME = #{createTime,jdbcType=OTHER},
      END_TIME = #{endTime,jdbcType=OTHER},
      REMARK = #{remark,jdbcType=VARCHAR},
      SRC_BUSINESS_INST_ID = #{srcBusinessInstId,jdbcType=VARCHAR},
      PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR},
      TARGET_AMOUNT = #{targetAmount,jdbcType=DECIMAL},
      SRC_BUSINESS_CODE = #{srcBusinessCode,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      SRC_TRADE_TYPE = #{srcTradeType,jdbcType=VARCHAR},
      SRC_CURRENCY = #{srcCurrency,jdbcType=VARCHAR},
      TARGET_CURRENCY = #{targetCurrency,jdbcType=VARCHAR},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      TARGET_BUSINESS_INST_ID = #{targetBusinessInstId,jdbcType=VARCHAR},
      TARGET_BUSINESS_CODE = #{targetBusinessCode,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  
  <select id="selectByBusinessInstId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_EXCHANGE_ORDER
    where TARGET_BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR} or
    SRC_BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
  </select>
</mapper>