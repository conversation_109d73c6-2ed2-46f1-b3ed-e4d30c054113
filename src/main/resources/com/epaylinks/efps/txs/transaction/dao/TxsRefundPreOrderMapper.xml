<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsRefundPreOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="OUT_REFUND_NO" jdbcType="VARCHAR" property="outRefundNo" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="TERMINAL_NO" jdbcType="VARCHAR" property="terminalNo" />
    <result column="REFUND_DESC" jdbcType="VARCHAR" property="refundDesc" />
    <result column="TOTAL_FEE" jdbcType="DECIMAL" property="totalFee" />
    <result column="REFUND_FEE" jdbcType="DECIMAL" property="refundFee" />
    <result column="REFUND_CURRENCY" jdbcType="VARCHAR" property="refundCurrency" />
    <result column="CHANNEL_TYPE" jdbcType="CHAR" property="channelType" />
    <result column="NOTIFY_URL" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="REDIRECT_URL" jdbcType="VARCHAR" property="redirectUrl" />
    <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="PAY_STATE" jdbcType="CHAR" property="payState" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CUSTOMERNAME" jdbcType="VARCHAR" property="customername" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="SPLIT_INFO_LIST" jdbcType="VARCHAR" property="splitInfoList" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="BACKPAY_PROCEDUREFEE" jdbcType="DECIMAL" property="backpayProcedurefee" />
    <result column="PAY_PROCEDUREFEE" jdbcType="DECIMAL" property="payProcedurefee" />
    <result column="PAY_TRANSACTIONNO" jdbcType="VARCHAR" property="payTransactionNo" />
    <result column="CHANNEL_ORDERNO" jdbcType="VARCHAR" property="channelOrderNo" />
    <result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
    <result column="PAY_METHOD" jdbcType="VARCHAR" property="payMethod" />
    <result column="AGENT_CUSTOMERCODE" jdbcType="VARCHAR" property="agentCustomerCode" />
	<result column="PAY_PASSWAY" jdbcType="VARCHAR" property="payPassWay" />
	<result column="AGENT_CUSTOMERNAME" jdbcType="VARCHAR" property="agentCustomerName" />
	<result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
	<result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
	<result column="SOURCE_TYPE" jdbcType="VARCHAR" property="sourceType" />
	<result column="IS_BACKREFUND_PROCEDURE" jdbcType="VARCHAR" property="isBackRefundProcedure" />
		<result column="ACQ_ORG_CODE" property="acqOrgCode" jdbcType="VARCHAR" />
	    <result column="ACQ_SP_ID" property="acqSpId" jdbcType="VARCHAR" />
	    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
	    <result column="TERM_INFO" property="termInfo" jdbcType="VARCHAR" />
	    <result column="AREA_INFO" property="areaInfo" jdbcType="VARCHAR" />
	    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
	    <result column="SUB_APP_ID" property="subAppId" jdbcType="VARCHAR" />
	    <result column="PLATFORM_CUSTOMER_CODE" property="platformCustomerCode" jdbcType="VARCHAR" />
	    <result column="STORE_ID" property="storeId" jdbcType="VARCHAR" />
	    <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR" />
	    <result column="REQ_RESERVED" property="reqReserved" jdbcType="VARCHAR" />
	    <result column="CUPS_REQ_RESERVED" property="cupsReqReserved" jdbcType="VARCHAR" />
	    <result column="PAYER_APP_NAME" property="payerAppName" jdbcType="VARCHAR" />
	    <result column="ACTUAL_PAY_AMOUNT" property="actualPayAmount" jdbcType="DECIMAL" />
	    <result column="DISCOUNTABLE_AMOUNT" property="discountableAmount" jdbcType="DECIMAL" />
	    <result column="SETTLEMENT_AMOUNT" property="settlementAmount" jdbcType="DECIMAL" />
	    <result column="SOURCE_CHANNEL" property="sourceChannel" jdbcType="VARCHAR" />
	    <result column="QR_CODE_ISSUER" property="qrCodeIssuer" jdbcType="VARCHAR" />
	    <result column="PROCEDURE_RATE" property="procedureRate" jdbcType="VARCHAR" />
    	<result column="RATE_MODE" property="rateMode" jdbcType="DECIMAL" /> 
    	<result column="CARD_NO_MOSAIC" property="cardNoMosaic" jdbcType="VARCHAR" />
	    <result column="CARD_NO_ENC" property="cardNoEnc" jdbcType="VARCHAR" />
	    <result column="CARD_TYPE" property="cardType" jdbcType="VARCHAR" />
	    <result column="TERMINAL_NAME" property="terminalName" jdbcType="VARCHAR"/>
	    <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
	    <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
	    <result column="SYSNUM" property="sysnum" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TRADE_NO" property="channelTradeNo" jdbcType="VARCHAR"/>
        <result column="BACK_SPLIT_PROCEDURE_FEE" property="backSplitProcedurefee" jdbcType="DECIMAL" />
        <result column="ORIG_TOTAL_SPLIT_PROCEDURE_FEE" property="origTotalSplitProcedurefee" jdbcType="DECIMAL" />
      <result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR" />
      <result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL" />
      <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
      <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
	<result column="CASH_AMOUNT" property="cashAmount" jdbcType="DECIMAL" />
	<result column="COUPON_AMOUNT" property="couponAmount" jdbcType="DECIMAL" />
	<result column="IS_SETT_WITH_CASH_AMOUNT" property="isSettWithCashAmount" jdbcType="VARCHAR" />    
	<result column="REAL_REFUND_FEE" property="realRefundFee" jdbcType="DECIMAL" />
	<result column="ORDER_INFO" property="orderInfo" jdbcType="VARCHAR" />
      <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OUT_TRADE_NO, OUT_REFUND_NO, CUSTOMER_CODE, TERMINAL_NO, REFUND_DESC, TOTAL_FEE, 
    REFUND_FEE, REFUND_CURRENCY, CHANNEL_TYPE, NOTIFY_URL, REDIRECT_URL, TRANSACTION_NO, 
    PAY_STATE, CREATE_TIME, CUSTOMERNAME, BUSINESS_INST_ID, BUSINESS_CODE, ERROR_CODE, 
    SPLIT_INFO_LIST, UPDATE_TIME, PROCEDURE_FEE, BACKPAY_PROCEDUREFEE, PAY_PROCEDUREFEE,
    PAY_TRANSACTIONNO, CHANNEL_ORDERNO, CHANNEL_NAME, PAY_METHOD, AGENT_CUSTOMERCODE, PAY_PASSWAY,
    AGENT_CUSTOMERNAME,error_msg,END_TIME,SOURCE_TYPE, IS_BACKREFUND_PROCEDURE, 
    ACQ_ORG_CODE, ACQ_SP_ID, ORDER_TYPE, TERM_INFO, AREA_INFO, 
    USER_ID, SUB_APP_ID, PLATFORM_CUSTOMER_CODE, 
    STORE_ID, OPERATOR_ID, REQ_RESERVED, CUPS_REQ_RESERVED, PAYER_APP_NAME, 
    ACTUAL_PAY_AMOUNT, DISCOUNTABLE_AMOUNT, SETTLEMENT_AMOUNT, SOURCE_CHANNEL, QR_CODE_ISSUER, 
    PROCEDURE_RATE, RATE_MODE, CARD_NO_MOSAIC, CARD_NO_ENC, CARD_TYPE,TERMINAL_NAME,BUSINESS_TYPE,
    BATCH_NO,SYSNUM,CHANNEL_TRADE_NO, 
    BACK_SPLIT_PROCEDURE_FEE, ORIG_TOTAL_SPLIT_PROCEDURE_FEE,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,
 	CASH_AMOUNT,COUPON_AMOUNT,IS_SETT_WITH_CASH_AMOUNT, 
 	REAL_REFUND_FEE, ORDER_INFO, REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_REFUND_PRE_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_REFUND_PRE_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder">
    insert into TXS_REFUND_PRE_ORDER (ID, OUT_TRADE_NO, OUT_REFUND_NO, 
      CUSTOMER_CODE, TERMINAL_NO, REFUND_DESC, 
      TOTAL_FEE, REFUND_FEE, REFUND_CURRENCY, 
      CHANNEL_TYPE, NOTIFY_URL, REDIRECT_URL, 
      TRANSACTION_NO, PAY_STATE, CREATE_TIME, 
      CUSTOMERNAME, BUSINESS_INST_ID, BUSINESS_CODE, 
      ERROR_CODE, SPLIT_INFO_LIST,UPDATE_TIME, PROCEDURE_FEE, BACKPAY_PROCEDUREFEE,
      PAY_PROCEDUREFEE, PAY_TRANSACTIONNO, CHANNEL_ORDERNO, CHANNEL_NAME, PAY_METHOD,
      AGENT_CUSTOMERCODE, PAY_PASSWAY, AGENT_CUSTOMERNAME,error_msg,END_TIME,SOURCE_TYPE,
      IS_BACKREFUND_PROCEDURE, 
    ACQ_ORG_CODE, ACQ_SP_ID, ORDER_TYPE, TERM_INFO, AREA_INFO, 
    USER_ID, SUB_APP_ID, PLATFORM_CUSTOMER_CODE, 
    STORE_ID, OPERATOR_ID, REQ_RESERVED, CUPS_REQ_RESERVED, PAYER_APP_NAME, 
    ACTUAL_PAY_AMOUNT, DISCOUNTABLE_AMOUNT, SETTLEMENT_AMOUNT, SOURCE_CHANNEL, QR_CODE_ISSUER, 
    PROCEDURE_RATE, RATE_MODE, CARD_NO_MOSAIC, CARD_NO_ENC, CARD_TYPE,TERMINAL_NAME,BUSINESS_TYPE,
    BATCH_NO,SYSNUM,CHANNEL_TRADE_NO, BACK_SPLIT_PROCEDURE_FEE, ORIG_TOTAL_SPLIT_PROCEDURE_FEE,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID, 
    CASH_AMOUNT,COUPON_AMOUNT,IS_SETT_WITH_CASH_AMOUNT, 
    REAL_REFUND_FEE, ORDER_INFO,REMARK)
    values (#{id,jdbcType=DECIMAL}, #{outTradeNo,jdbcType=VARCHAR}, #{outRefundNo,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{terminalNo,jdbcType=VARCHAR}, #{refundDesc,jdbcType=VARCHAR}, 
      #{totalFee,jdbcType=DECIMAL}, #{refundFee,jdbcType=DECIMAL}, #{refundCurrency,jdbcType=VARCHAR}, 
      #{channelType,jdbcType=CHAR}, #{notifyUrl,jdbcType=VARCHAR}, #{redirectUrl,jdbcType=VARCHAR}, 
      #{transactionNo,jdbcType=VARCHAR}, #{payState,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{customername,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, 
      #{errorCode,jdbcType=VARCHAR}, #{splitInfoList,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},
      #{procedureFee,jdbcType=DECIMAL}, #{backpayProcedurefee,jdbcType=DECIMAL}, #{payProcedurefee,jdbcType=DECIMAL},
      #{payTransactionNo,jdbcType=VARCHAR},#{channelOrderNo,jdbcType=VARCHAR},#{channelName,jdbcType=VARCHAR},
      #{payMethod,jdbcType=VARCHAR},#{agentCustomerCode,jdbcType=VARCHAR}, #{payPassWay,jdbcType=VARCHAR},
      #{agentCustomerName,jdbcType=VARCHAR},#{errorMsg,jdbcType=VARCHAR},#{endTime , jdbcType = TIMESTAMP},
      #{sourceType,jdbcType=VARCHAR}, #{isBackRefundProcedure,jdbcType=VARCHAR},
		#{acqOrgCode,jdbcType=VARCHAR}, #{acqSpId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      	#{termInfo,jdbcType=VARCHAR}, #{areaInfo,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      	#{subAppId,jdbcType=VARCHAR}, #{platformCustomerCode,jdbcType=VARCHAR}, 
      	#{storeId,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, #{reqReserved,jdbcType=VARCHAR}, 
      	#{cupsReqReserved,jdbcType=VARCHAR}, #{payerAppName,jdbcType=VARCHAR}, #{actualPayAmount,jdbcType=DECIMAL}, 
      	#{discountableAmount,jdbcType=DECIMAL}, #{settlementAmount,jdbcType=DECIMAL}, #{sourceChannel,jdbcType=VARCHAR},
      	#{qrCodeIssuer,jdbcType=VARCHAR},#{procedureRate,jdbcType=VARCHAR}, #{rateMode,jdbcType=DECIMAL}, 
      	#{cardNoMosaic,jdbcType=VARCHAR}, #{cardNoEnc,jdbcType=VARCHAR}, #{cardType,jdbcType=VARCHAR},
      #{terminalName,jdbcType=VARCHAR},#{businessType,jdbcType=VARCHAR},#{batchNo,jdbcType=VARCHAR},#{sysnum,jdbcType=VARCHAR},#{channelTradeNo,jdbcType=VARCHAR},
      #{backSplitProcedurefee,jdbcType=DECIMAL}, #{origTotalSplitProcedurefee,jdbcType=DECIMAL},#{businessMan,jdbcType=VARCHAR},#{businessManId,jdbcType=DECIMAL},#{companyName,jdbcType=VARCHAR},#{companyId,jdbcType=DECIMAL},
	#{cashAmount,jdbcType=DECIMAL}, #{couponAmount,jdbcType=DECIMAL}, #{isSettWithCashAmount,jdbcType=VARCHAR}, 
	#{realRefundFee,jdbcType=DECIMAL}, #{orderInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
		  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder">
    insert into TXS_REFUND_PRE_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO,
      </if>
      <if test="outRefundNo != null">
        OUT_REFUND_NO,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="terminalNo != null">
        TERMINAL_NO,
      </if>
      <if test="refundDesc != null">
        REFUND_DESC,
      </if>
      <if test="totalFee != null">
        TOTAL_FEE,
      </if>
      <if test="refundFee != null">
        REFUND_FEE,
      </if>
      <if test="refundCurrency != null">
        REFUND_CURRENCY,
      </if>
      <if test="channelType != null">
        CHANNEL_TYPE,
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL,
      </if>
      <if test="redirectUrl != null">
        REDIRECT_URL,
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="payState != null">
        PAY_STATE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="customername != null">
        CUSTOMERNAME,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="splitInfoList != null">
        SPLIT_INFO_LIST,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="backpayProcedurefee != null">
        BACKPAY_PROCEDUREFEE,
      </if>
      <if test="payProcedurefee != null">
        PAY_PROCEDUREFEE,
      </if>
      <if test="payTransactionNo != null">
        PAY_TRANSACTIONNO,
      </if>
      <if test="channelOrderNo != null">
        CHANNEL_ORDERNO,
      </if>
      <if test="channelName != null">
        CHANNEL_NAME,
      </if>
      <if test="payMethod != null">
        PAY_METHOD,
      </if>
      <if test="agentCustomerCode != null">
       	AGENT_CUSTOMERCODE,
      </if>
      <if test="payPassWay != null">
       	PAY_PASSWAY,
      </if>
      <if test="agentCustomerName != null">
       	AGENT_CUSTOMERNAME,
      </if>
      <if test="errorMsg != null">
       	error_msg,
      </if>
      <if test="endTime != null">
      	END_TIME,
      </if>
      <if test="sourceType != null">
       	SOURCE_TYPE,
      </if>
      <if test="isBackRefundProcedure != null">
       	IS_BACKREFUND_PROCEDURE,
      </if>
			<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE,
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID,
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE,
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO,
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO,
	      </if>
	      <if test="userId != null" >
	        USER_ID,
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID,
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE,
	      </if>
	      <if test="storeId != null" >
	        STORE_ID,
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID,
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED,
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED,
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME,
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT,
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT,
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT,
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL,
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER,
	      </if>
	      <if test="procedureRate != null" >
	        PROCEDURE_RATE,
	      </if>
	      <if test="rateMode != null" >
	        RATE_MODE,
	      </if>  
	      <if test="cardNoMosaic != null" >
	        CARD_NO_MOSAIC,
	      </if>  
	      <if test="cardNoEnc != null" >
	        CARD_NO_ENC,
	      </if>  
	      <if test="cardType != null" >
	        CARD_TYPE,
	      </if>
	      <if test="terminalName != null">
	      	TERMINAL_NAME,
	      </if>
	      <if test="businessType != null">
	      	BUSINESS_TYPE,
	      </if>
	      <if test="batchNo != null">
	      	BATCH_NO,
	      </if>
	      <if test="sysnum != null">
	      	SYSNUM,
	      </if>
        <if test="channelTradeNo != null">
            CHANNEL_TRADE_NO,
        </if>
        <if test="backSplitProcedurefee != null">
            BACK_SPLIT_PROCEDURE_FEE,
        </if>
        <if test="origTotalSplitProcedurefee != null">
            ORIG_TOTAL_SPLIT_PROCEDURE_FEE,
        </if>
        <if test="businessMan != null" >
            BUSINESS_MAN,
        </if>
        <if test="businessManId != null" >
            BUSINESS_MAN_ID,
        </if>
        <if test="companyName != null" >
            COMPANY_NAME,
        </if>
        <if test="companyId != null" >
            COMPANY_ID,
        </if>
        <if test="cashAmount != null" >
            CASH_AMOUNT,
        </if>
        <if test="couponAmount != null" >
            COUPON_AMOUNT,
        </if>
        <if test="isSettWithCashAmount != null" >
            IS_SETT_WITH_CASH_AMOUNT,
        </if>    
        <if test="realRefundFee != null" >
            REAL_REFUND_FEE,
        </if>       
        <if test="orderInfo != null" >
            ORDER_INFO,
        </if>
        <if test="remark != null" >
            REMARK,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="outRefundNo != null">
        #{outRefundNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="terminalNo != null">
        #{terminalNo,jdbcType=VARCHAR},
      </if>
      <if test="refundDesc != null">
        #{refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="totalFee != null">
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
        #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="refundCurrency != null">
        #{refundCurrency,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=CHAR},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customername != null">
        #{customername,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="splitInfoList != null">
        #{splitInfoList,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="backpayProcedurefee != null">
        #{backpayProcedurefee,jdbcType=DECIMAL},
      </if>
      <if test="payProcedurefee != null">
        #{payProcedurefee,jdbcType=DECIMAL},
      </if>
      <if test="payTransactionNo != null">
        #{payTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="channelOrderNo != null">
        #{channelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerCode != null">
		#{agentCustomerCode,jdbcType=VARCHAR},
	  </if>
      <if test="payPassWay != null">
		#{payPassWay,jdbcType=VARCHAR},
	  </if>
      <if test="agentCustomerName != null">
		#{agentCustomerName,jdbcType=VARCHAR},
	  </if>
	  <if test="errorMsg != null">
		#{errorMsg,jdbcType=VARCHAR},
	  </if>
	  <if test="endTime != null">
		#{endTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="sourceType != null">
		#{sourceType,jdbcType=VARCHAR},
	  </if>
	  <if test="isBackRefundProcedure != null">
		#{isBackRefundProcedure,jdbcType=VARCHAR},
	  </if>
		<if test="acqOrgCode != null" >
	        #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	      <if test="procedureRate != null" >
	        #{procedureRate,jdbcType=VARCHAR},
	      </if>
	      <if test="rateMode != null" >
	        #{rateMode,jdbcType=DECIMAL},
	      </if>
		     <if test="cardNoMosaic != null" >
		      	#{cardNoMosaic,jdbcType=VARCHAR},
		    </if>
		    <if test="cardNoEnc != null" >
		      	#{cardNoEnc,jdbcType=VARCHAR},
		    </if>
		    <if test="cardType != null" >
		      	#{cardType,jdbcType=VARCHAR},
		    </if>
		    <if test="terminalName != null">
	      	#{terminalName,jdbcType=VARCHAR},
	   	   </if>
	   	   <if test="businessType != null">
	      	#{businessType,jdbcType=VARCHAR},
	      </if>
	      <if test="batchNo != null">
	      	#{batchNo,jdbcType=VARCHAR},
	      </if>
	      <if test="sysnum != null">
	      	#{sysnum,jdbcType=VARCHAR},
	      </if>
        <if test="channelTradeNo != null">
            #{channelTradeNo,jdbcType=VARCHAR},
        </if>
      <if test="backSplitProcedurefee != null">
        #{backSplitProcedurefee,jdbcType=DECIMAL},
      </if>
      <if test="origTotalSplitProcedurefee != null">
        #{origTotalSplitProcedurefee,jdbcType=DECIMAL},
      </if>
        <if test="businessMan != null" >
            #{businessMan,jdbcType=VARCHAR},
        </if>
        <if test="businessManId != null" >
            #{businessManId,jdbcType=DECIMAL},
        </if>
        <if test="companyName != null" >
            #{companyName,jdbcType=VARCHAR},
        </if>
        <if test="companyId != null" >
            #{companyId,jdbcType=DECIMAL},
        </if>
        <if test="cashAmount != null" >
            #{cashAmount,jdbcType=DECIMAL},
        </if>
        <if test="couponAmount != null" >
            #{couponAmount,jdbcType=DECIMAL},
        </if>
        <if test="isSettWithCashAmount != null" >
            #{isSettWithCashAmount,jdbcType=VARCHAR},
        </if> 
        <if test="realRefundFee != null" >
            #{realRefundFee,jdbcType=DECIMAL},
        </if>         
        <if test="orderInfo != null" >
            #{orderInfo,jdbcType=VARCHAR},
        </if>
        <if test="remark != null" >
            #{remark,jdbcType=VARCHAR}
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder">
    update TXS_REFUND_PRE_ORDER
    <set>
      <if test="outTradeNo != null">
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="outRefundNo != null">
        OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="terminalNo != null">
        TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
      </if>
      <if test="refundDesc != null">
        REFUND_DESC = #{refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="totalFee != null">
        TOTAL_FEE = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
        REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="refundCurrency != null">
        REFUND_CURRENCY = #{refundCurrency,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        PAY_STATE = #{payState,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customername != null">
        CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="splitInfoList != null">
        SPLIT_INFO_LIST = #{splitInfoList,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="backpayProcedurefee != null">
        BACKPAY_PROCEDUREFEE = #{backpayProcedurefee,jdbcType=DECIMAL},
      </if>
      <if test="payProcedurefee != null">
        PAY_PROCEDUREFEE = #{payProcedurefee,jdbcType=DECIMAL},
      </if>
      <if test="payTransactionNo != null">
        PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="channelOrderNo != null">
        CHANNEL_ORDERNO = #{channelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="payPassWay != null">
		PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
	  </if>
      <if test="agentCustomerCode != null">
		AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
	  </if>
      <if test="agentCustomerName != null">
		AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
	  </if>
	  <if test="errorMsg != null">
		error_msg = #{errorMsg,jdbcType=VARCHAR},
	  </if>
	  <if test="endTime != null">
		END_TIME = #{endTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="sourceType != null">
		SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
	  </if>
	  <if test="isBackRefundProcedure != null">
		IS_BACKREFUND_PROCEDURE = #{isBackRefundProcedure,jdbcType=VARCHAR},
	  </if>
		<if test="acqOrgCode != null" >
	        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      </if>
	      <if test="acqSpId != null" >
	        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      </if>
	      <if test="orderType != null" >
	        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      </if>
	      <if test="termInfo != null" >
	        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="areaInfo != null" >
	        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      </if>
	      <if test="userId != null" >
	        USER_ID = #{userId,jdbcType=VARCHAR},
	      </if>
	      <if test="subAppId != null" >
	        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      </if>
	      <if test="platformCustomerCode != null" >
	        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      </if>
	      <if test="storeId != null" >
	        STORE_ID = #{storeId,jdbcType=VARCHAR},
	      </if>
	      <if test="operatorId != null" >
	        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      </if>
	      <if test="reqReserved != null" >
	        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="cupsReqReserved != null" >
	        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      </if>
	      <if test="payerAppName != null" >
	        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      </if>
	      <if test="actualPayAmount != null" >
	        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="discountableAmount != null" >
	        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="settlementAmount != null" >
	        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      </if>
	      <if test="sourceChannel != null" >
	        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      </if>
	      <if test="qrCodeIssuer != null" >
	        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      </if>
	      <if test="procedureRate != null" >
	        PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
	      </if>
	      <if test="rateMode != null" >
	        RATE_MODE = #{rateMode,jdbcType=DECIMAL},
	      </if>
		     <if test="cardNoMosaic != null" >
		      	CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
		    </if>
		    <if test="cardNoEnc != null" >
		      	CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
		    </if>
		    <if test="cardType != null" >
		      	CARD_TYPE = #{cardType,jdbcType=VARCHAR},
		    </if>
		    <if test="terminalName != null">
		      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
		   	   </if>
		   	   <if test="businessType != null">
		      	BUSINESS_TYPE= #{businessType,jdbcType=VARCHAR},
		      </if>
		      <if test="batchNo != null">
		      	BATCH_NO= #{batchNo,jdbcType=VARCHAR},
		      </if>
		      <if test="sysnum != null">
		      	SYSNUM= #{sysnum,jdbcType=VARCHAR},
		      </if>
        <if test="channelTradeNo != null">
            CHANNEL_TRADE_NO = #{channelTradeNo,jdbcType=VARCHAR},
        </if>
	      <if test="backSplitProcedurefee != null" >
	        BACK_SPLIT_PROCEDURE_FEE = #{backSplitProcedurefee,jdbcType=DECIMAL},
	      </if>
	      <if test="origTotalSplitProcedurefee != null" >
	        ORIG_TOTAL_SPLIT_PROCEDURE_FEE = #{origTotalSplitProcedurefee,jdbcType=DECIMAL},
	      </if>
        <if test="businessMan != null" >
            BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
        </if>
        <if test="businessManId != null" >
            BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
        </if>
        <if test="companyName != null" >
            COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
        </if>
        <if test="companyId != null" >
            COMPANY_ID = #{companyId,jdbcType=DECIMAL},
        </if>
		    <if test="cashAmount != null" >
		      	CASH_AMOUNT = #{cashAmount,jdbcType=DECIMAL},
		    </if>
		    <if test="couponAmount != null" >
		      	COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
		    </if>
		    <if test="isSettWithCashAmount != null" >
		      	IS_SETT_WITH_CASH_AMOUNT = #{isSettWithCashAmount,jdbcType=VARCHAR},
		    </if>
	    <if test="realRefundFee != null" >
	      	REAL_REFUND_FEE = #{realRefundFee,jdbcType=DECIMAL},
	    </if>		    
        <if test="orderInfo != null" >
            ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
        </if>	    
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder">
    update TXS_REFUND_PRE_ORDER
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
      REFUND_DESC = #{refundDesc,jdbcType=VARCHAR},
      TOTAL_FEE = #{totalFee,jdbcType=DECIMAL},
      REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      REFUND_CURRENCY = #{refundCurrency,jdbcType=VARCHAR},
      CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
      NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      PAY_STATE = #{payState,jdbcType=CHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      SPLIT_INFO_LIST = #{splitInfoList,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      BACKPAY_PROCEDUREFEE = #{backpayProcedurefee,jdbcType=DECIMAL},
      PAY_PROCEDUREFEE = #{payProcedurefee,jdbcType=DECIMAL},
      PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR},
      CHANNEL_ORDERNO = #{channelOrderNo,jdbcType=VARCHAR},
      CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
	  AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
	  AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
	  error_msg = #{errorMsg,jdbcType=VARCHAR},
	  END_TIME = #{endTime , jdbcType=TIMESTAMP},
	  SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
	  IS_BACKREFUND_PROCEDURE = #{isBackRefundProcedure,jdbcType=VARCHAR},
		ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
	      ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
	      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
	      TERM_INFO = #{termInfo,jdbcType=VARCHAR},
	      AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
	      USER_ID = #{userId,jdbcType=VARCHAR},
	      SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
	      PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
	      STORE_ID = #{storeId,jdbcType=VARCHAR},
	      OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
	      REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
	      CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
	      PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
	      ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
	      DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
	      SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
	      SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
	      QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
	      PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
      	  RATE_MODE = #{rateMode,jdbcType=DECIMAL},
	      CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
	      CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
	      CARD_TYPE = #{cardType,jdbcType=VARCHAR},
	      TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
      	BUSINESS_TYPE= #{businessType,jdbcType=VARCHAR},
      	BATCH_NO= #{batchNo,jdbcType=VARCHAR},
      	SYSNUM= #{sysnum,jdbcType=VARCHAR},
      	CHANNEL_TRADE_NO = #{channelTradeNo,jdbcType=VARCHAR},
      	BACK_SPLIT_PROCEDURE_FEE = #{backSplitProcedurefee,jdbcType=DECIMAL},
      	ORIG_TOTAL_SPLIT_PROCEDURE_FEE = #{origTotalSplitProcedurefee,jdbcType=DECIMAL},
      BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
	  BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
	  COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
	  COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      CASH_AMOUNT = #{cashAmount,jdbcType=DECIMAL} ,
      COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL} ,
      IS_SETT_WITH_CASH_AMOUNT = #{isSettWithCashAmount,jdbcType=VARCHAR} ,
      REAL_REFUND_FEE = #{realRefundFee,jdbcType=DECIMAL}, 
      ORDER_INFO = #{orderInfo,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByOutRefundNoAndCustomerCode"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_REFUND_PRE_ORDER
    where OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR} and
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByOutTradeNoAndCustomerCode"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_REFUND_PRE_ORDER
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
		select A.*, rownum RN
		from (
		select * from TXS_REFUND_PRE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="outRefundNo != null and outRefundNo !=''">
				AND OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
			</if>
			<if test="customerName != null and customerName !=''">
				AND CUSTOMERNAME  like concat(#{customerName},'%')
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="channelName != null and channelName !=''">
				AND CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
			</if>
			<if test="channelOrder != null and channelOrder !=''">
				AND CHANNEL_ORDERNO = #{channelOrder,jdbcType=VARCHAR}
			</if>
			<if test="payPassWay != null and payPassWay !=''">
				AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
			</if>
			<if test="agentCustomerCode != null and agentCustomerCode !=''">
				AND AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR}
			</if>
			<if test="payTransactionNo != null and payTransactionNo !=''">
				AND PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR}
			</if>
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
			</if>
			<if test="agentCustomerName != null and agentCustomerName !=''">
				AND AGENT_CUSTOMERNAME  like concat(#{agentCustomerName},'%')
			</if>
			<if test="beginTime!=null">
				AND CREATE_TIME <![CDATA[ >= ]]>
				#{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null">
				AND CREATE_TIME <![CDATA[ <= ]]>
				#{endTime,jdbcType=TIMESTAMP}
			</if>
			<if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND CUSTOMER_CODE in 
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">  
         		 #{item}  
      		    </foreach> 
			</if>
		</where>
		order by CREATE_TIME desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

	</select>
	
	<select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		select count(ID)
		from TXS_REFUND_PRE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="outRefundNo != null and outRefundNo !=''">
				AND OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
			</if>
			<if test="customerName != null and customerName !=''">
				AND CUSTOMERNAME  like concat(#{customerName},'%')
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="channelName != null and channelName !=''">
				AND CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
			</if>
			<if test="channelOrder != null and channelOrder !=''">
				AND CHANNEL_ORDERNO = #{channelOrder,jdbcType=VARCHAR}
			</if>
			<if test="payPassWay != null and payPassWay !=''">
				AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
			</if>
			<if test="agentCustomerName != null and agentCustomerName !=''">
				AND AGENT_CUSTOMERNAME  like concat(#{agentCustomerName},'%')
			</if>
			<if test="agentCustomerCode != null and agentCustomerCode !=''">
				AND AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR}
			</if>
			<if test="payTransactionNo != null and payTransactionNo !=''">
				AND PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR}
			</if>
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
			<if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND CUSTOMER_CODE in 
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">  
         		 #{item}  
      		    </foreach> 
			</if>
		</where>
	</select>
	
	<select id="selectByNotPage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="outRefundNo != null and outRefundNo !=''">
				AND OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
			</if>
			<if test="customerName != null and customerName !=''">
				AND CUSTOMERNAME  like concat(#{customerName},'%')
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="channelName != null and channelName !=''">
				AND CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
			</if>
			<if test="channelOrder != null and channelOrder !=''">
				AND CHANNEL_ORDERNO = #{channelOrder,jdbcType=VARCHAR}
			</if>
			<if test="payPassWay != null and payPassWay !=''">
				AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
			</if>
			<if test="agentCustomerCode != null and agentCustomerCode !=''">
				AND AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR}
			</if>
			<if test="payTransactionNo != null and payTransactionNo !=''">
				AND PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR}
			</if>
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
			</if>
			<if test="agentCustomerName != null and agentCustomerName !=''">
				AND AGENT_CUSTOMERNAME  like concat(#{agentCustomerName},'%')
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
			<if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
				AND CUSTOMER_CODE in 
				<foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">  
         		 #{item}  
      		    </foreach> 
			</if>
		</where>
		order by CREATE_TIME desc
	</select>
	
	<select id="selectByCustomerCodeAndStateAndUpdateTimeInDate" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
		and PAY_STATE = #{payState , jdbcType = VARCHAR} 
		and to_char(UPDATE_TIME,'yyyyMMdd') = to_char(#{updateTime , jdbcType = TIMESTAMP},'yyyyMMdd')
	</select>

    <select id="splitRefundQueryByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from (
        select A.*, rownum RN
        from (
        select * from TXS_REFUND_PRE_ORDER
        <where>
            out_refund_no in (select out_refund_no from TXS_REFUND_SPLIT_ORDER)
            <if test="tkTransactionNo != null and tkTransactionNo !=''">
                AND TRANSACTION_NO = #{tkTransactionNo,jdbcType=VARCHAR}
            </if>
            <if test="outRefundNo != null and outRefundNo !=''">
                AND OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="payTransactionNo != null and payTransactionNo !=''">
                AND PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR}
            </if>
            <if test="customerCode != null and customerCode !=''">
                AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
            </if>
            <if test="customerName != null and customerName !=''">
                AND CUSTOMERNAME  like concat(#{customerName},'%')
            </if>
            <if test="refundState != null and refundState !=''">
                AND PAY_STATE = #{refundState,jdbcType=CHAR}
            </if>
            <if test="beginTime!=null">
                AND CREATE_TIME <![CDATA[ >= ]]>
                #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime!=null">
                AND CREATE_TIME <![CDATA[ <= ]]>
                #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by CREATE_TIME desc
        ) A
        where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
        )
        where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
    </select>

    <select id="countSplitRefundPreOrder" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(ID)
        from TXS_REFUND_PRE_ORDER
        <where>
            out_refund_no in (select out_refund_no from TXS_REFUND_SPLIT_ORDER)
            <if test="tkTransactionNo != null and tkTransactionNo !=''">
                AND TRANSACTION_NO = #{tkTransactionNo,jdbcType=VARCHAR}
            </if>
            <if test="outRefundNo != null and outRefundNo !=''">
                AND OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="payTransactionNo != null and payTransactionNo !=''">
                AND PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR}
            </if>
            <if test="customerCode != null and customerCode !=''">
                AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
            </if>
            <if test="customerName != null and customerName !=''">
                AND CUSTOMERNAME  like concat(#{customerName},'%')
            </if>
            <if test="refundState != null and refundState !=''">
                AND PAY_STATE = #{refundState,jdbcType=CHAR}
            </if>
            <if test="beginTime!=null ">
                AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime!=null ">
                AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="splitRefundQueryNotPage" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from TXS_REFUND_PRE_ORDER
        <where>
            out_refund_no in (select out_refund_no from TXS_REFUND_SPLIT_ORDER)
            <if test="tkTransactionNo != null and tkTransactionNo !=''">
                AND TRANSACTION_NO = #{tkTransactionNo,jdbcType=VARCHAR}
            </if>
            <if test="outRefundNo != null and outRefundNo !=''">
                AND OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="payTransactionNo != null and payTransactionNo !=''">
                AND PAY_TRANSACTIONNO = #{payTransactionNo,jdbcType=VARCHAR}
            </if>
            <if test="customerCode != null and customerCode !=''">
                AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
            </if>
            <if test="customerName != null and customerName !=''">
                AND CUSTOMERNAME  like concat(#{customerName},'%')
            </if>
            <if test="refundState != null and refundState !=''">
                AND PAY_STATE = #{refundState,jdbcType=CHAR}
            </if>
            <if test="beginTime!=null ">
                AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime!=null ">
                AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="expandCustomerCodes != null and expandCustomerCodes.size > 0">
                AND CUSTOMER_CODE in
                <foreach item="item" index="index" collection="expandCustomerCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

	<select id="selectByCustomerCodeAndUpdateTimeInDate" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
		and to_char(UPDATE_TIME,'yyyyMMdd') = to_char(#{updateTime , jdbcType = TIMESTAMP},'yyyyMMdd')
	</select>
	<select id="selectByCustomerCodeAndEndTimeInDate" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
		and to_char(END_TIME,'yyyyMMdd') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMMdd')
	</select>
	
	<select id="selectByOutTradeNoAndPayState" resultMap="BaseResultMap" >
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
		and PAY_STATE = #{payState,jdbcType=CHAR}
	</select>

	<select id="selectByTransactionNo" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	</select>

    <select id="selectAuditList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        from TXS_REFUND_PRE_ORDER
        WHERE CREATE_TIME &gt;= #{startTime}
        AND CREATE_TIME &lt;= #{endTime}
        AND PAY_STATE = 03
        and pay_method != '59'
        and pay_method != '60'
    </select>
    <select id="selectSuccessAndDoingByOutTradeNo" resultMap="BaseResultMap" >
		select
		<include refid="Base_Column_List" />
		from TXS_REFUND_PRE_ORDER
		where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
		and CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
		and PAY_STATE in('00','03')
	</select>

</mapper>