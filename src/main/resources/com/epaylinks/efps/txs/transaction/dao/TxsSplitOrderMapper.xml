<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    <id column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="PROCEDURE_CUSTOMER_CODE" jdbcType="VARCHAR" property="procedureCustomerCode" />
    <result column="NOTIFY_URL" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="REAL_AMOUNT" jdbcType="DECIMAL" property="realAmount" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ATTACH_DATA" jdbcType="VARCHAR" property="attachData" />
    <result column="SPLIT_INFO_LIST" jdbcType="VARCHAR" property="splitInfoList" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="SLAVE_AMOUNT_SUM" jdbcType="DECIMAL" property="salveAmountSum" />
    <result column="REFUND_FEE" jdbcType="DECIMAL" property="refundFee" />
	<result column="REFUNDING_FEE" jdbcType="DECIMAL" property="refundingFee" />
	<result column="SPLIT_MODEL" jdbcType="VARCHAR" property="splitModel" />
	<result column="ori_business_code" jdbcType="VARCHAR" property="oriBusinessCode" />
	<result column="SPLIT_PROCEDURE_CUSTOMER_CODE" jdbcType="VARCHAR" property="splitProcedureCustomerCode" />
	<result column="SPLIT_MAIN" jdbcType="VARCHAR" property="splitMain" />
	<result column="cash_amount" property="cashAmount" jdbcType="DECIMAL" />
	<result column="coupon_amount" property="couponAmount" jdbcType="DECIMAL" />
	<result column="sett_cycle_rule_code" jdbcType="VARCHAR" property="settCycleRuleCode" />
      <result column="crossplat_customer_code" jdbcType="VARCHAR" property="crossPlatCustomerCode" />
      <result column="procedure_type" jdbcType="VARCHAR" property="procedureType" />
      <result column="REVOKE_TRANSACTION_NO" jdbcType="VARCHAR" property="revokeTransactionNo" />
      <result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType" />
      <result column="split_type" jdbcType="VARCHAR" property="splitType" />

      <result column="IS_FIRST" jdbcType="VARCHAR" property="isFirst" />
      <result column="OUT_SPLIT_TRADE_NO" jdbcType="VARCHAR" property="outSplitTradeNo" />
      <result column="CONTROLLING" jdbcType="VARCHAR" property="controlling" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE, NOTIFY_URL, STATE, AMOUNT, PROCEDURE_FEE, 
    REAL_AMOUNT, END_TIME, ERROR_CODE, CREATE_TIME, UPDATE_TIME, ATTACH_DATA, SPLIT_INFO_LIST,BUSINESS_INST_ID,
    SLAVE_AMOUNT_SUM, REFUND_FEE, REFUNDING_FEE,PROCEDURE_CUSTOMER_CODE,SPLIT_MODEL,ori_business_code, SPLIT_PROCEDURE_CUSTOMER_CODE,
    SPLIT_MAIN,cash_amount,coupon_amount,sett_cycle_rule_code,crossplat_customer_code,procedure_type,REVOKE_TRANSACTION_NO,
    TRANSACTION_TYPE,split_type, IS_FIRST, OUT_SPLIT_TRADE_NO, CONTROLLING
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from TXS_SPLIT_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    insert into TXS_SPLIT_ORDER (TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE, 
      NOTIFY_URL, STATE, AMOUNT, 
      PROCEDURE_FEE, REAL_AMOUNT, END_TIME, 
      ERROR_CODE, CREATE_TIME, UPDATE_TIME, 
      ATTACH_DATA, SPLIT_INFO_LIST,BUSINESS_INST_ID,SLAVE_AMOUNT_SUM,REFUND_FEE, REFUNDING_FEE,PROCEDURE_CUSTOMER_CODE,SPLIT_MODEL,ori_business_code,
      SPLIT_PROCEDURE_CUSTOMER_CODE, SPLIT_MAIN,cash_amount,coupon_amount,sett_cycle_rule_code,crossplat_customer_code,procedure_type,REVOKE_TRANSACTION_NO,
      TRANSACTION_TYPE,split_type, IS_FIRST, OUT_SPLIT_TRADE_NO, CONTROLLING)
    values (#{transactionNo,jdbcType=VARCHAR}, #{outTradeNo,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR}, 
      #{notifyUrl,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{procedureFee,jdbcType=DECIMAL}, #{realAmount,jdbcType=DECIMAL}, #{endTime,jdbcType=TIMESTAMP}, 
      #{errorCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{attachData,jdbcType=VARCHAR}, #{splitInfoList,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR},
      #{salveAmountSum,jdbcType=DECIMAL},#{refundFee,jdbcType=DECIMAL}, #{refundingFee,jdbcType=DECIMAL},
      #{procedureCustomerCode,jdbcType=VARCHAR},#{splitModel,jdbcType=VARCHAR},#{oriBusinessCode,jdbcType=VARCHAR}, 
      #{splitProcedureCustomerCode,jdbcType=VARCHAR}, #{splitMain,jdbcType=VARCHAR}, #{cashAmount,jdbcType=DECIMAL},
      #{couponAmount,jdbcType=DECIMAL},#{settCycleRuleCode,jdbcType=VARCHAR},#{crossPlatCustomerCode,jdbcType=VARCHAR},#{procedureType,jdbcType=VARCHAR},
      #{revokeTransactionNo,jdbcType=VARCHAR}, #{transactionType,jdbcType=VARCHAR},#{splitType,jdbcType=VARCHAR},
      #{isFirst,jdbcType=VARCHAR}, #{outSplitTradeNo,jdbcType=VARCHAR}, #{controlling,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    insert into TXS_SPLIT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="realAmount != null">
        REAL_AMOUNT,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="attachData != null">
        ATTACH_DATA,
      </if>
      <if test="splitInfoList != null">
        SPLIT_INFO_LIST,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="salveAmountSum != null">
        SLAVE_AMOUNT_SUM,
      </if>
      <if test="refundFee != null">
		REFUND_FEE,
	  </if>
	  <if test="refundingFee != null">
		REFUNDING_FEE,
	  </if>
	  <if test="procedureCustomerCode != null">
		PROCEDURE_CUSTOMER_CODE,
	  </if>
	  <if test="splitModel != null">
		split_model,
	  </if>
	   <if test="oriBusinessCode != null">
		ori_business_code,
	  </if>
  	   <if test="splitProcedureCustomerCode != null">
		SPLIT_PROCEDURE_CUSTOMER_CODE,
	  </if>
  	   <if test="splitMain != null">
		SPLIT_MAIN,
	  </if>	
	  	<if test="cashAmount != null" >
        cash_amount,
      </if> 
      <if test="couponAmount != null" >
        coupon_amount,
      </if>
      <if test="settCycleRuleCode != null" >
        sett_cycle_rule_code,
      </if>
        <if test="crossPlatCustomerCode != null" >
            crossplat_customer_code,
        </if>
        <if test="procedureType != null" >
            procedure_type,
        </if>
        <if test="revokeTransactionNo != null" >
            REVOKE_TRANSACTION_NO,
        </if>
        <if test="transactionType != null" >
            TRANSACTION_TYPE,
        </if>
        <if test="splitType != null" >
            split_type,
        </if>
        <if test="isFirst != null" >
            IS_FIRST,
        </if>
        <if test="outSplitTradeNo != null" >
            OUT_SPLIT_TRADE_NO,
        </if>
        <if test="controlling != null" >
            CONTROLLING,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attachData != null">
        #{attachData,jdbcType=VARCHAR},
      </if>
      <if test="splitInfoList != null">
        #{splitInfoList,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="salveAmountSum != null">
        #{salveAmountSum,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
		#{refundFee,jdbcType=DECIMAL},
	  </if>
	  <if test="refundingFee != null">
		#{refundingFee,jdbcType=DECIMAL},
	  </if>
	  <if test="procedureCustomerCode != null">
		#{procedureCustomerCode,jdbcType=VARCHAR},
	  </if>
	   <if test="splitModel != null">
		#{splitModel,jdbcType=VARCHAR},
	  </if>
	  <if test="oriBusinessCode != null">
	  	#{oriBusinessCode,jdbcType=VARCHAR},
	  </if>
	  <if test="splitProcedureCustomerCode != null">
	  	#{splitProcedureCustomerCode,jdbcType=VARCHAR},
	  </if>	  
	  <if test="splitMain != null">
	  	#{splitMain,jdbcType=VARCHAR},
	  </if>	 
	  <if test="cashAmount != null" >
        #{cashAmount,jdbcType=DECIMAL},
      </if> 
      <if test="couponAmount != null" >
        #{couponAmount,jdbcType=DECIMAL},
      </if>	
      <if test="settCycleRuleCode != null" >
        #{settCycleRuleCode,jdbcType=VARCHAR},
      </if>
        <if test="crossPlatCustomerCode != null" >
        #{crossPlatCustomerCode,jdbcType=VARCHAR},
        </if>
        <if test="procedureType != null" >
            #{procedureType,jdbcType=VARCHAR},
        </if>
        <if test="revokeTransactionNo != null" >
            #{revokeTransactionNo,jdbcType=VARCHAR},
        </if>
        <if test="transactionType != null" >
            #{transactionType,jdbcType=VARCHAR},
        </if>
        <if test="splitType != null" >
            #{splitType,jdbcType=VARCHAR},
        </if>
        <if test="isFirst != null" >
            #{isFirst,jdbcType=VARCHAR},
        </if>
        <if test="outSplitTradeNo != null" >
            #{outSplitTradeNo,jdbcType=VARCHAR},
        </if>
        <if test="controlling != null" >
            #{controlling,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    update TXS_SPLIT_ORDER
    <set>
      <if test="outTradeNo != null">
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attachData != null">
        ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
      </if>
      <if test="splitInfoList != null">
        SPLIT_INFO_LIST = #{splitInfoList,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="salveAmountSum != null">
        SLAVE_AMOUNT_SUM = #{salveAmountSum,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
		REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
	  </if>
	  <if test="refundingFee != null">
		REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
	  </if>
	  <if test="procedureCustomerCode != null">
		PROCEDURE_CUSTOMER_CODE = #{procedureCustomerCode,jdbcType=VARCHAR},
	  </if>
	   <if test="splitModel != null">
		split_model = #{splitModel,jdbcType=VARCHAR},
	  </if>
	  <if test="oriBusinessCode != null">
	  	ori_business_code = #{oriBusinessCode,jdbcType=VARCHAR},
	  </if>
	  <if test="splitProcedureCustomerCode != null">
	  	SPLIT_PROCEDURE_CUSTOMER_CODE = #{splitProcedureCustomerCode,jdbcType=VARCHAR},
	  </if>	  
	  <if test="splitMain != null">
	  	SPLIT_MAIN = #{splitMain,jdbcType=VARCHAR},
	  </if>	
	  <if test="cashAmount != null" >
       cash_amount = #{cashAmount,jdbcType=DECIMAL},
      </if> 
      <if test="couponAmount != null" >
       coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      </if>	 
      <if test="settCycleRuleCode != null" >
        sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
      </if>
        <if test="crossPlatCustomerCode != null" >
            crossplat_customer_code = #{crossPlatCustomerCode,jdbcType=VARCHAR},
        </if>
        <if test="procedureType != null" >
            procedure_type= #{procedureType,jdbcType=VARCHAR},
        </if>
        <if test="revokeTransactionNo != null" >
            REVOKE_TRANSACTION_NO = #{revokeTransactionNo,jdbcType=VARCHAR},
        </if>
        <if test="transactionType != null" >
            TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
        </if>
        <if test="splitType != null" >
            split_type = #{splitType,jdbcType=VARCHAR},
        </if>
        <if test="isFirst != null" >
            IS_FIRST = #{isFirst,jdbcType=VARCHAR},
        </if>
        <if test="outSplitTradeNo != null" >
            OUT_SPLIT_TRADE_NO = #{outSplitTradeNo,jdbcType=VARCHAR},
        </if>
        <if test="controlling != null" >
            CONTROLLING = #{controlling,jdbcType=VARCHAR},
        </if>

    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>



    <update id="updateConfirmByTransactionNo" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
        update TXS_SPLIT_ORDER
        <set>
            <if test="outTradeNo != null">
                OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null">
                CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null">
                NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="procedureFee != null">
                PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="errorCode != null">
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="attachData != null">
                ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
            </if>
            <if test="splitInfoList != null">
                SPLIT_INFO_LIST = #{splitInfoList,jdbcType=VARCHAR},
            </if>
            <if test="businessInstId != null">
                BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
            </if>
            <if test="salveAmountSum != null">
                SLAVE_AMOUNT_SUM = #{salveAmountSum,jdbcType=DECIMAL},
            </if>
            <if test="refundFee != null">
                REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="refundingFee != null">
                REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
            </if>
            <if test="procedureCustomerCode != null">
                PROCEDURE_CUSTOMER_CODE = #{procedureCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="splitModel != null">
                split_model = #{splitModel,jdbcType=VARCHAR},
            </if>
            <if test="oriBusinessCode != null">
                ori_business_code = #{oriBusinessCode,jdbcType=VARCHAR},
            </if>
            <if test="splitProcedureCustomerCode != null">
                SPLIT_PROCEDURE_CUSTOMER_CODE = #{splitProcedureCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="splitMain != null">
                SPLIT_MAIN = #{splitMain,jdbcType=VARCHAR},
            </if>
            <if test="cashAmount != null" >
                cash_amount = #{cashAmount,jdbcType=DECIMAL},
            </if>
            <if test="couponAmount != null" >
                coupon_amount = #{couponAmount,jdbcType=DECIMAL},
            </if>
            <if test="settCycleRuleCode != null" >
                sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
            </if>
            <if test="crossPlatCustomerCode != null" >
                crossplat_customer_code = #{crossPlatCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="procedureType != null" >
                procedure_type= #{procedureType,jdbcType=VARCHAR},
            </if>
            <if test="revokeTransactionNo != null" >
                REVOKE_TRANSACTION_NO = #{revokeTransactionNo,jdbcType=VARCHAR},
            </if>
            <if test="splitType != null" >
                split_type = #{splitType,jdbcType=VARCHAR},
            </if>
            <if test="isFirst != null" >
                IS_FIRST = #{isFirst,jdbcType=VARCHAR},
            </if>
            <if test="outSplitTradeNo != null" >
                OUT_SPLIT_TRADE_NO = #{outSplitTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="controlling != null" >
                CONTROLLING = #{controlling,jdbcType=VARCHAR},
            </if>
        </set>
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
              and STATE = '04'
    </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    update TXS_SPLIT_ORDER
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
      SPLIT_INFO_LIST = #{splitInfoList,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      SLAVE_AMOUNT_SUM = #{salveAmountSum,jdbcType=DECIMAL},
      REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
	  REFUNDING_FEE = #{refundingFee,jdbcType=DECIMAL},
	  PROCEDURE_CUSTOMER_CODE = #{procedureCustomerCode,jdbcType=VARCHAR},
	  split_model = #{splitModel,jdbcType=VARCHAR},
	  ori_business_code = #{oriBusinessCode,jdbcType=VARCHAR},
	  SPLIT_PROCEDURE_CUSTOMER_CODE = #{splitProcedureCustomerCode,jdbcType=VARCHAR},
	  SPLIT_MAIN = #{splitMain,jdbcType=VARCHAR},
	  cash_amount = #{cashAmount,jdbcType=DECIMAL},
	  coupon_amount = #{couponAmount,jdbcType=DECIMAL},
	  sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
      crossplat_customer_code = #{crossPlatCustomerCode,jdbcType=VARCHAR},
        procedure_type= #{procedureType,jdbcType=VARCHAR},
        REVOKE_TRANSACTION_NO = #{revokeTransactionNo,jdbcType=VARCHAR},
        split_type = #{splitType,jdbcType=VARCHAR},
        IS_FIRST = #{isFirst,jdbcType=VARCHAR},
        OUT_SPLIT_TRADE_NO = #{outSplitTradeNo,jdbcType=VARCHAR},
        CONTROLLING = #{controlling,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  <select id="selectByOutTradeNoAndCustomerCode"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>

    <select id="selectByThree"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from TXS_SPLIT_ORDER
        where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
        <if test="outSplitTradeNo != null" >
          and  OUT_SPLIT_TRADE_NO = #{outSplitTradeNo,jdbcType=VARCHAR}
        </if>
        <if test="outSplitTradeNo == null" >
            and  OUT_SPLIT_TRADE_NO = '-'
        </if>
    </select>


  <select id="selectByOutTradeNoAndCustomerCodes"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByTransactionNoAndCustomerCode"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} and
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
    <select id="selectByTransactionNo"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from TXS_SPLIT_ORDER
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    </select>
  <select id="selectByOutTradeNoToResultQuery"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} and
    STATE = #{state,jdbcType=VARCHAR}
  </select>
  
  <update id="updateAmountByTransactionNo">
    update TXS_SPLIT_ORDER
    set 
    REFUNDING_FEE = NVL(REFUNDING_FEE , 0) + #{currencyRefundFee,jdbcType=DECIMAL} 
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} and 
    AMOUNT >=  NVL(REFUND_FEE , 0) + NVL(REFUNDING_FEE , 0) + #{currencyRefundFee,jdbcType=DECIMAL}
  </update>
  
  <update id="updateReduceAmountByTransactionNo" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    update TXS_SPLIT_ORDER
    set 
    REFUNDING_FEE = NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} 
    and NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL} >= 0
  </update>
  
  <update id="updateAddAmountByTransactionNo" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitOrder">
    update TXS_SPLIT_ORDER
    set 
    REFUNDING_FEE = NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL},
    REFUND_FEE = NVL(REFUND_FEE , 0) + #{refundingFee,jdbcType=DECIMAL}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    and NVL(REFUNDING_FEE , 0) - #{refundingFee,jdbcType=DECIMAL} >= 0
  </update>
  
  <select id="selectByBusinessInstId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER
    where BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
  </select>
  
  <select id="selectByOutTradeNo" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER 
    where OUT_TRADE_NO = #{outTradeNo , jdbcType = VARCHAR}
  </select>
  
  <select id="selectByCustomerCodeAndStateAndEndTimeInDate" resultMap="BaseResultMap">
  	select
	<include refid="Base_Column_List" />
	from TXS_SPLIT_ORDER
	where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR} 
	and STATE = #{state , jdbcType = VARCHAR} 
	and to_char(END_TIME,'yyyyMMdd') = to_char(#{endTime , jdbcType = TIMESTAMP},'yyyyMMdd')
  </select>
  
  <select id="selectByOutTradeNoList" resultMap="BaseResultMap">
  	select
	<include refid="Base_Column_List" />
	from TXS_SPLIT_ORDER
	where OUT_TRADE_NO in 
	<foreach item="item" index="index" collection="outTradeNoList" open="(" separator="," close=")">  
        <if  test="(index % 999) == 998"> NULL) OR OUT_TRADE_NO IN (</if>#{item}  
    </foreach> 
  </select>
  
  <select id="selectAll" resultMap="BaseResultMap">
    	select 
    	<include refid="Base_Column_List" />
    	from TXS_SPLIT_ORDER where 
    	TRANSACTION_NO in (select t.transaction_no from txs_split_record t  group by t.transaction_no)
    	order by CREATE_TIME desc
  	</select>

  <select id="selectNoSucessOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TXS_SPLIT_ORDER where
    out_trade_no in (
      select out_trade_no from txs_pre_order  where TRANSACTION_TYPE = 'FZ' and pay_state = '1' and NVL(REFUNDING_FEE , 0) + NVL(REFUND_FEE , 0) = 0 and SETTLEMENT_STATE = '00' and create_time BETWEEN sysdate - 1 and sysdate - #{fzjyAuditNumber}/1440
    ) AND state != '00'
  </select>

    <select id="selectNoSucessSyncZhfz" resultType="java.lang.String" >
        select A.* from (
            select t.transaction_no from TXS_SPLIT_ORDER t
            where  t.transaction_type='ZHFZ' and t.state in('03')
              and t.create_time BETWEEN #{zhfzCheckBeginDay} and sysdate - #{zhfzCheckEndMinutes}/1440
            order by t.create_time desc
           ) A
        where rownum &lt;= #{zhfzCheckRowSize,jdbcType=DECIMAL}
    </select>

 <update id="updateState51To03" >
    update TXS_SPLIT_ORDER
    set STATE = '03',
      UPDATE_TIME = sysdate
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    	and (STATE = '51' or STATE = '03')
  </update>


    <insert id="insertIntoUq">
        insert into TXS_SPLIT_ORDER_UQ
        	  (uq)
        	values
        	  (#{uq,jdbcType=VARCHAR})
    </insert>

    <update id="updateStateNotSuccess" >
        update TXS_SPLIT_ORDER
          set
          END_TIME = #{endTime,jdbcType=TIMESTAMP},
          UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
          STATE = #{state,jdbcType=VARCHAR},
          ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
          AND STATE != '00'
    </update>


    <update id="updateAccountSplit00to04" >
        update TXS_SPLIT_ORDER
          set
          STATE = '04'
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
          AND SPLIT_TYPE = '1'
          AND STATE = '00'
    </update>

    <update id="updateSetControlling" >
        update TXS_SPLIT_ORDER
          set
                CONTROLLING = #{controlling,jdbcType=VARCHAR}
        where transaction_no = #{transactionNo,jdbcType=VARCHAR}
    </update>


</mapper>

