<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsRefundSplitRecordMapper">
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo"/>
        <result column="SOURCE_CUSTOMER_CODE" jdbcType="VARCHAR" property="sourceCustomerCode"/>
        <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode"/>
        <result column="STATE" jdbcType="CHAR" property="state"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="PROCEDUREFEE" jdbcType="DECIMAL" property="procedurefee"/>
        <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId"/>
        <result column="CUSTOMERNAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="REFUND_ALLAMOUNT" jdbcType="DECIMAL" property="refundAllAmount"/>
        <result column="BACKPAY_PROCEDUREFEE" jdbcType="DECIMAL" property="backPayProcedureFee"/>
        <result column="API_AMOUNT" jdbcType="DECIMAL" property="apiAmount"/>
        <result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR"/>
        <result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL"/>
        <result column="CASH_AMOUNT" property="cashAmount" jdbcType="DECIMAL"/>
        <result column="COUPON_AMOUNT" property="couponAmount" jdbcType="DECIMAL"/>
        <result column="REAL_REFUND_FEE" property="realRefundFee" jdbcType="DECIMAL"/>
        <result column="TRANSACTION_TYPE" property="transactionType" jdbcType="VARCHAR"/>
        <result column="OUT_REFUND_NO"  property="outRefundNo" jdbcType="VARCHAR"/>
        <result column="PAY_REAL_REFUND_FEE" property="payRealRefundFee" jdbcType="DECIMAL"/>
        <result column="SPLIT_ATTR" property="splitAttr" jdbcType="VARCHAR"/>
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, TRANSACTION_NO, SOURCE_CUSTOMER_CODE, CUSTOMER_CODE, STATE, AMOUNT, PROCEDUREFEE,
    ERROR_CODE, CREATE_TIME, UPDATE_TIME, BUSINESS_INST_ID, CUSTOMERNAME, REFUND_ALLAMOUNT,
    BACKPAY_PROCEDUREFEE, API_AMOUNT,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,
    CASH_AMOUNT,COUPON_AMOUNT, REAL_REFUND_FEE, TRANSACTION_TYPE,OUT_REFUND_NO,PAY_REAL_REFUND_FEE,SPLIT_ATTR,OUT_TRADE_NO
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_REFUND_SPLIT_RECORD
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_REFUND_SPLIT_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>

    <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord">
        insert into TXS_REFUND_SPLIT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="transactionNo != null">
                TRANSACTION_NO,
            </if>
            <if test="sourceCustomerCode != null">
                SOURCE_CUSTOMER_CODE,
            </if>
            <if test="customerCode != null">
                CUSTOMER_CODE,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="procedurefee != null">
                PROCEDUREFEE,
            </if>
            <if test="errorCode != null">
                ERROR_CODE,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="businessInstId != null">
                BUSINESS_INST_ID,
            </if>
            <if test="customerName != null">
                CUSTOMERNAME,
            </if>
            <if test="refundAllAmount != null">
                REFUND_ALLAMOUNT,
            </if>
            <if test="backPayProcedureFee != null">
                BACKPAY_PROCEDUREFEE,
            </if>
            <if test="apiAmount != null">
                API_AMOUNT,
            </if>
            <if test="businessMan != null">
                BUSINESS_MAN,
            </if>
            <if test="businessManId != null">
                BUSINESS_MAN_ID,
            </if>
            <if test="companyName != null">
                COMPANY_NAME,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="cashAmount != null">
                CASH_AMOUNT,
            </if>
            <if test="couponAmount != null">
                COUPON_AMOUNT,
            </if>
            <if test="realRefundFee != null">
                REAL_REFUND_FEE,
            </if>
            <if test="transactionType != null">
                TRANSACTION_TYPE,
            </if>
            <if test="splitAttr != null">
                SPLIT_ATTR,
            </if>
            <if test="outRefundNo != null">
                OUT_REFUND_NO,
            </if>
            <if test="payRealRefundFee != null">
                PAY_REAL_REFUND_FEE,
            </if>
            <if test="outTradeNo != null">
                OUT_TRADE_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=DECIMAL},
            </if>
            <if test="transactionNo != null">
                #{transactionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceCustomerCode != null">
                #{sourceCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null">
                #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=CHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="procedurefee != null">
                #{procedurefee,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessInstId != null">
                #{businessInstId,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="refundAllAmount != null">
                #{refundAllAmount,jdbcType=DECIMAL},
            </if>
            <if test="backPayProcedureFee != null">
                #{backPayProcedureFee,jdbcType=DECIMAL},
            </if>
            <if test="apiAmount != null">
                #{apiAmount,jdbcType=DECIMAL},
            </if>
            <if test="businessMan != null">
                #{businessMan,jdbcType=VARCHAR},
            </if>
            <if test="businessManId != null">
                #{businessManId,jdbcType=DECIMAL},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=DECIMAL},
            </if>

            <if test="cashAmount != null">
                #{cashAmount,jdbcType=DECIMAL},
            </if>
            <if test="couponAmount != null">
                #{couponAmount,jdbcType=DECIMAL},
            </if>
            <if test="realRefundFee != null">
                #{realRefundFee,jdbcType=DECIMAL},
            </if>
            <if test="transactionType != null">
                #{transactionType,jdbcType=VARCHAR},
            </if>
            <if test="splitAttr != null">
                #{splitAttr,jdbcType=VARCHAR},
            </if>
            <if test="outRefundNo != null">
                #{outRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="payRealRefundFee != null">
                #{payRealRefundFee,jdbcType=DECIMAL},
            </if>
            <if test="outTradeNo != null">
                #{outTradeNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord">
        update TXS_REFUND_SPLIT_RECORD
        <set>
            <if test="transactionNo != null">
                TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceCustomerCode != null">
                SOURCE_CUSTOMER_CODE = #{sourceCustomerCode,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null">
                CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=CHAR},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="procedurefee != null">
                PROCEDUREFEE = #{procedurefee,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessInstId != null">
                BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                CUSTOMERNAME = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="refundAllAmount != null">
                REFUND_ALLAMOUNT = #{refundAllAmount,jdbcType=DECIMAL},
            </if>
            <if test="backPayProcedureFee != null">
                BACKPAY_PROCEDUREFEE = #{backPayProcedureFee,jdbcType=DECIMAL},
            </if>
            <if test="apiAmount != null">
                API_AMOUNT = #{apiAmount,jdbcType=DECIMAL},
            </if>
            <if test="businessMan != null">
                BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
            </if>
            <if test="businessManId != null">
                BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
            </if>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=DECIMAL},
            </if>
            <if test="cashAmount != null">
                CASH_AMOUNT = #{cashAmount,jdbcType=DECIMAL},
            </if>
            <if test="couponAmount != null">
                COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
            </if>
            <if test="realRefundFee != null">
                REAL_REFUND_FEE = #{realRefundFee,jdbcType=DECIMAL},
            </if>
            <if test="transactionType != null">
                TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
            </if>
            <if test="splitAttr != null">
                SPLIT_ATTR = #{splitAttr,jdbcType=VARCHAR},
            </if>
            <if test="outRefundNo != null">
                OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="payRealRefundFee != null">
                PAY_REAL_REFUND_FEE = #{payRealRefundFee,jdbcType=DECIMAL},
            </if>
            <if test="outTradeNo != null">
                OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>
    <select id="selectByBusinessInstId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_REFUND_SPLIT_RECORD
        where BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
    </select>

    <select id="queryRefundSplitRecordByOutRefundNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_REFUND_SPLIT_RECORD
        where TRANSACTION_NO in (
        select TRANSACTION_NO from TXS_REFUND_PRE_ORDER where OUT_REFUND_NO = #{outRefundNo,jdbcType=VARCHAR}
        )
    </select>

    <select id="selectByTransactionNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_REFUND_SPLIT_RECORD
        where TRANSACTION_NO = #{transactionNo , jdbcType = VARCHAR}
    </select>

    <select id="selectByTransactionNoCustomerCodeAmt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TXS_REFUND_SPLIT_RECORD
        where TRANSACTION_NO = #{transactionNo , jdbcType = VARCHAR}
        and CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR}
        and API_AMOUNT = #{apiAmount , jdbcType = DECIMAL}
        order by id desc
    </select>
</mapper>