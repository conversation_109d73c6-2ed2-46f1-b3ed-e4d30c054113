<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsYmfQrCodeMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsYmfQrCode">
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="QRCODEKEY" jdbcType="CHAR" property="qrCodeKey" />
    <result column="CREATEDATETIME" jdbcType="TIMESTAMP" property="createDateTime" />
    <result column="QRCODE_UNIONPAY" jdbcType="VARCHAR" property="qrcodeUnionpay" />
  </resultMap>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsYmfQrCode">
    insert into TXS_YMFQRCODE (CUSTOMER_CODE, QRCODEKEY, CREATEDATETIME, QRCODE_UNIONPAY
      )
    values (
    #{customerCode,jdbcType=VARCHAR}, #{qrCodeKey,jdbcType=CHAR}, #{createDateTime,jdbcType=TIMESTAMP},
    #{qrcodeUnionpay,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsYmfQrCode">
    insert into TXS_YMFQRCODE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="qrCodeKey != null">
        QRCODEKEY,
      </if>
      <if test="createDateTime != null">
        CREATEDATETIME,
      </if>
      <if test="qrcodeUnionpay != null">
        QRCODE_UNIONPAY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeKey != null">
        #{qrCodeKey,jdbcType=CHAR},
      </if>
      <if test="createDateTime != null">
        #{createDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="qrcodeUnionpay != null">
        #{qrcodeUnionpay,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <select id="selectCustomerCodeByQrCodeKey" parameterType="java.lang.String" resultType="java.lang.String">
  	select CUSTOMER_CODE from TXS_YMFQRCODE where QRCODEKEY = #{qrCodeKey , jdbcType = VARCHAR}
  </select>
  <select id="selectCustomerCodeByQrCodeUnionPay" parameterType="java.lang.String" resultType="java.lang.String">
  	select CUSTOMER_CODE from TXS_YMFQRCODE where QRCODE_UNIONPAY = #{qrcodeUnionpay , jdbcType = VARCHAR}
  </select>
</mapper>