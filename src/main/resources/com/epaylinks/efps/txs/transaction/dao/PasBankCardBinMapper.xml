<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.PasBankCardBinMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.PasBankCardBin">
    <result column="RECORD_ID" jdbcType="VARCHAR" property="recordId" />
    <result column="CARD_NO_RANGE" jdbcType="VARCHAR" property="cardNoRange" />
    <result column="CARD_NO_RANGE_LEN" jdbcType="VARCHAR" property="cardNoRangeLen" />
    <result column="ISSUE_BANK_NO" jdbcType="VARCHAR" property="issueBankNo" />
    <result column="BANK_ICON" jdbcType="VARCHAR" property="bankIcon" />
    <result column="ISSUE_BANK_NAME" jdbcType="VARCHAR" property="issueBankName" />
    <result column="CARD_NAME" jdbcType="VARCHAR" property="cardName" />
    <result column="APPLY_RANGE" jdbcType="VARCHAR" property="applyRange" />
    <result column="CARD_NO_LEN" jdbcType="VARCHAR" property="cardNoLen" />
    <result column="CARD_TYPE" jdbcType="VARCHAR" property="cardType" />
    <result column="ISSUE_BANK_ACCOUNT" jdbcType="VARCHAR" property="issueBankAccount" />
    <result column="SELF_USE" jdbcType="VARCHAR" property="selfUse" />
  </resultMap>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.PasBankCardBin">
    insert into PAS_BANK_CARD_BIN (RECORD_ID, CARD_NO_RANGE, CARD_NO_RANGE_LEN, 
      ISSUE_BANK_NO, BANK_ICON, ISSUE_BANK_NAME, 
      CARD_NAME, APPLY_RANGE, CARD_NO_LEN, 
      CARD_TYPE, ISSUE_BANK_ACCOUNT, SELF_USE
      )
    values (#{recordId,jdbcType=VARCHAR}, #{cardNoRange,jdbcType=VARCHAR}, #{cardNoRangeLen,jdbcType=VARCHAR}, 
      #{issueBankNo,jdbcType=VARCHAR}, #{bankIcon,jdbcType=VARCHAR}, #{issueBankName,jdbcType=VARCHAR}, 
      #{cardName,jdbcType=VARCHAR}, #{applyRange,jdbcType=VARCHAR}, #{cardNoLen,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=VARCHAR}, #{issueBankAccount,jdbcType=VARCHAR}, #{selfUse,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.PasBankCardBin">
    insert into PAS_BANK_CARD_BIN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        RECORD_ID,
      </if>
      <if test="cardNoRange != null">
        CARD_NO_RANGE,
      </if>
      <if test="cardNoRangeLen != null">
        CARD_NO_RANGE_LEN,
      </if>
      <if test="issueBankNo != null">
        ISSUE_BANK_NO,
      </if>
      <if test="bankIcon != null">
        BANK_ICON,
      </if>
      <if test="issueBankName != null">
        ISSUE_BANK_NAME,
      </if>
      <if test="cardName != null">
        CARD_NAME,
      </if>
      <if test="applyRange != null">
        APPLY_RANGE,
      </if>
      <if test="cardNoLen != null">
        CARD_NO_LEN,
      </if>
      <if test="cardType != null">
        CARD_TYPE,
      </if>
      <if test="issueBankAccount != null">
        ISSUE_BANK_ACCOUNT,
      </if>
      <if test="selfUse != null">
        SELF_USE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="cardNoRange != null">
        #{cardNoRange,jdbcType=VARCHAR},
      </if>
      <if test="cardNoRangeLen != null">
        #{cardNoRangeLen,jdbcType=VARCHAR},
      </if>
      <if test="issueBankNo != null">
        #{issueBankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankIcon != null">
        #{bankIcon,jdbcType=VARCHAR},
      </if>
      <if test="issueBankName != null">
        #{issueBankName,jdbcType=VARCHAR},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="applyRange != null">
        #{applyRange,jdbcType=VARCHAR},
      </if>
      <if test="cardNoLen != null">
        #{cardNoLen,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="issueBankAccount != null">
        #{issueBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="selfUse != null">
        #{selfUse,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  
  <select id="select"  resultMap="BaseResultMap">
    select 
    * 
    from PAS_BANK_CARD_BIN
  </select>
</mapper>