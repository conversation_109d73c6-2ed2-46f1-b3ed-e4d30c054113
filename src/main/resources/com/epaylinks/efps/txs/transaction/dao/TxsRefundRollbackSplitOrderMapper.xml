<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsRefundRollbackSplitOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsRefundRollbackSplitOrder">
    <id column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="OUT_REFUND_TRADE_NO" jdbcType="VARCHAR" property="outRefundTradeNo" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="REAL_AMOUNT" jdbcType="DECIMAL" property="realAmount" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ATTACH_DATA" jdbcType="VARCHAR" property="attachData" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="SLAVE_AMOUNT_SUM" jdbcType="DECIMAL" property="slaveAmountSum" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSACTION_NO, OUT_REFUND_TRADE_NO, CUSTOMER_CODE, STATE, AMOUNT, PROCEDURE_FEE, 
    REAL_AMOUNT, END_TIME, ERROR_CODE, CREATE_TIME, UPDATE_TIME, ATTACH_DATA, BUSINESS_INST_ID, 
    SLAVE_AMOUNT_SUM, BUSINESS_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_REFUNDROLLBACK_SPLIT_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from TXS_REFUNDROLLBACK_SPLIT_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundRollbackSplitOrder">
    insert into TXS_REFUNDROLLBACK_SPLIT_ORDER (TRANSACTION_NO, OUT_REFUND_TRADE_NO, 
      CUSTOMER_CODE, STATE, AMOUNT, 
      PROCEDURE_FEE, REAL_AMOUNT, END_TIME, 
      ERROR_CODE, CREATE_TIME, UPDATE_TIME, 
      ATTACH_DATA, BUSINESS_INST_ID, SLAVE_AMOUNT_SUM, 
      BUSINESS_CODE)
    values (#{transactionNo,jdbcType=VARCHAR}, #{outRefundTradeNo,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{procedureFee,jdbcType=DECIMAL}, #{realAmount,jdbcType=DECIMAL}, #{endTime,jdbcType=TIMESTAMP}, 
      #{errorCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{attachData,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR}, #{slaveAmountSum,jdbcType=DECIMAL}, 
      #{businessCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundRollbackSplitOrder">
    insert into TXS_REFUNDROLLBACK_SPLIT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="outRefundTradeNo != null">
        OUT_REFUND_TRADE_NO,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="realAmount != null">
        REAL_AMOUNT,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="attachData != null">
        ATTACH_DATA,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="slaveAmountSum != null">
        SLAVE_AMOUNT_SUM,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outRefundTradeNo != null">
        #{outRefundTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attachData != null">
        #{attachData,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="slaveAmountSum != null">
        #{slaveAmountSum,jdbcType=DECIMAL},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundRollbackSplitOrder">
    update TXS_REFUNDROLLBACK_SPLIT_ORDER
    <set>
      <if test="outRefundTradeNo != null">
        OUT_REFUND_TRADE_NO = #{outRefundTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attachData != null">
        ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="slaveAmountSum != null">
        SLAVE_AMOUNT_SUM = #{slaveAmountSum,jdbcType=DECIMAL},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRefundRollbackSplitOrder">
    update TXS_REFUNDROLLBACK_SPLIT_ORDER
    set OUT_REFUND_TRADE_NO = #{outRefundTradeNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      SLAVE_AMOUNT_SUM = #{slaveAmountSum,jdbcType=DECIMAL},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
</mapper>