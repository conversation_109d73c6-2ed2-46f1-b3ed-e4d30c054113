<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsMemberInsideTransOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideTransOrder">
    <id column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="SRC_CUSTOMER_CODE" jdbcType="VARCHAR" property="srcCustomerCode" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="SRC_BUSINESS_INST_ID" jdbcType="VARCHAR" property="srcBusinessInstId" />
    <result column="TARGET_CUSTOMER_CODE" jdbcType="VARCHAR" property="targetCustomerCode" />
    <result column="TARGET_BUSINESS_INST_ID" jdbcType="VARCHAR" property="targetBusinessInstId" />
    <result column="OUT_PAY_TRADE_NO" jdbcType="VARCHAR" property="outPayTradeNo" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="SRC_BUSINESS_CODE" jdbcType="VARCHAR" property="srcBusinessCode" />
    <result column="TARGET_BUSINESS_CODE" jdbcType="VARCHAR" property="targetBusinessCode" />
    <result column="SHARED_INFO_LIST" jdbcType="VARCHAR" property="sharedInfoList" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSACTION_NO, OUT_TRADE_NO, SRC_CUSTOMER_CODE, STATE, AMOUNT, PROCEDURE_FEE, END_TIME, 
    ERROR_CODE, CREATE_TIME, UPDATE_TIME, SRC_BUSINESS_INST_ID, TARGET_CUSTOMER_CODE, 
    TARGET_BUSINESS_INST_ID, OUT_PAY_TRADE_NO, REMARK, SRC_BUSINESS_CODE, TARGET_BUSINESS_CODE, 
    SHARED_INFO_LIST, CUSTOMER_CODE, CURRENCY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_INSIDE_TRANS_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from TXS_MEMBER_INSIDE_TRANS_ORDER
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideTransOrder">
    insert into TXS_MEMBER_INSIDE_TRANS_ORDER (TRANSACTION_NO, OUT_TRADE_NO, SRC_CUSTOMER_CODE, 
      STATE, AMOUNT, PROCEDURE_FEE, 
      END_TIME, ERROR_CODE, CREATE_TIME, 
      UPDATE_TIME, SRC_BUSINESS_INST_ID, TARGET_CUSTOMER_CODE, 
      TARGET_BUSINESS_INST_ID, OUT_PAY_TRADE_NO, REMARK, 
      SRC_BUSINESS_CODE, TARGET_BUSINESS_CODE, SHARED_INFO_LIST, 
      CUSTOMER_CODE, CURRENCY)
    values (#{transactionNo,jdbcType=VARCHAR}, #{outTradeNo,jdbcType=VARCHAR}, #{srcCustomerCode,jdbcType=VARCHAR}, 
      #{state,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, #{procedureFee,jdbcType=DECIMAL}, 
      #{endTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{srcBusinessInstId,jdbcType=VARCHAR}, #{targetCustomerCode,jdbcType=VARCHAR}, 
      #{targetBusinessInstId,jdbcType=VARCHAR}, #{outPayTradeNo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{srcBusinessCode,jdbcType=VARCHAR}, #{targetBusinessCode,jdbcType=VARCHAR}, #{sharedInfoList,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideTransOrder">
    insert into TXS_MEMBER_INSIDE_TRANS_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO,
      </if>
      <if test="srcCustomerCode != null">
        SRC_CUSTOMER_CODE,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="srcBusinessInstId != null">
        SRC_BUSINESS_INST_ID,
      </if>
      <if test="targetCustomerCode != null">
        TARGET_CUSTOMER_CODE,
      </if>
      <if test="targetBusinessInstId != null">
        TARGET_BUSINESS_INST_ID,
      </if>
      <if test="outPayTradeNo != null">
        OUT_PAY_TRADE_NO,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="srcBusinessCode != null">
        SRC_BUSINESS_CODE,
      </if>
      <if test="targetBusinessCode != null">
        TARGET_BUSINESS_CODE,
      </if>
      <if test="sharedInfoList != null">
        SHARED_INFO_LIST,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="currency != null">
        CURRENCY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="srcCustomerCode != null">
        #{srcCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="srcBusinessInstId != null">
        #{srcBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="targetCustomerCode != null">
        #{targetCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessInstId != null">
        #{targetBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="outPayTradeNo != null">
        #{outPayTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="srcBusinessCode != null">
        #{srcBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessCode != null">
        #{targetBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="sharedInfoList != null">
        #{sharedInfoList,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideTransOrder">
    update TXS_MEMBER_INSIDE_TRANS_ORDER
    <set>
      <if test="outTradeNo != null">
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="srcCustomerCode != null">
        SRC_CUSTOMER_CODE = #{srcCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="srcBusinessInstId != null">
        SRC_BUSINESS_INST_ID = #{srcBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="targetCustomerCode != null">
        TARGET_CUSTOMER_CODE = #{targetCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessInstId != null">
        TARGET_BUSINESS_INST_ID = #{targetBusinessInstId,jdbcType=VARCHAR},
      </if>
      <if test="outPayTradeNo != null">
        OUT_PAY_TRADE_NO = #{outPayTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="srcBusinessCode != null">
        SRC_BUSINESS_CODE = #{srcBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="targetBusinessCode != null">
        TARGET_BUSINESS_CODE = #{targetBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="sharedInfoList != null">
        SHARED_INFO_LIST = #{sharedInfoList,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        CURRENCY = #{currency,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberInsideTransOrder">
    update TXS_MEMBER_INSIDE_TRANS_ORDER
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      SRC_CUSTOMER_CODE = #{srcCustomerCode,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      SRC_BUSINESS_INST_ID = #{srcBusinessInstId,jdbcType=VARCHAR},
      TARGET_CUSTOMER_CODE = #{targetCustomerCode,jdbcType=VARCHAR},
      TARGET_BUSINESS_INST_ID = #{targetBusinessInstId,jdbcType=VARCHAR},
      OUT_PAY_TRADE_NO = #{outPayTradeNo,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      SRC_BUSINESS_CODE = #{srcBusinessCode,jdbcType=VARCHAR},
      TARGET_BUSINESS_CODE = #{targetBusinessCode,jdbcType=VARCHAR},
      SHARED_INFO_LIST = #{sharedInfoList,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CURRENCY = #{currency,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  
  <select id="selectByOutPayTradeNoAndCustomerCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_INSIDE_TRANS_ORDER
    where OUT_PAY_TRADE_NO = #{outPayTradeNo,jdbcType=VARCHAR} and 
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByOutTradeNoAndCustomerCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_INSIDE_TRANS_ORDER
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and 
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByOutTradeNoAndOutPayTradeNoAndCustomerCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_INSIDE_TRANS_ORDER
    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} and 
    <if test="outPayTradeNo != null and outPayTradeNo !=''">
		OUT_PAY_TRADE_NO = #{outPayTradeNo,jdbcType=VARCHAR} and
	</if>
    CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByBusinessInstId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_INSIDE_TRANS_ORDER
    where TARGET_BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR} or
    SRC_BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
  </select>
</mapper>