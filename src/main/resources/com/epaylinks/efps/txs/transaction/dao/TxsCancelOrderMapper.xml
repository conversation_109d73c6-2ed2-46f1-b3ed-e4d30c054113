<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsCancelOrderMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsCancelOrder" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="OUT_CANCEL_NO" property="outCancelNo" jdbcType="VARCHAR" />
    <result column="CANCLE_NO" property="cancleNo" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="CURRENCY_TYPE" property="currencyType" jdbcType="VARCHAR" />
    <result column="ACQ_ORG_CODE" property="acqOrgCode" jdbcType="VARCHAR" />
    <result column="ACQ_SP_ID" property="acqSpId" jdbcType="VARCHAR" />
    <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
    <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
    <result column="CUSTOMERNAME" property="customername" jdbcType="VARCHAR" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="CANCEL_SOURCE" property="cancelSource" jdbcType="VARCHAR" />
    <result column="SEND_COUNT" property="sendCount" jdbcType="DECIMAL" />
    <result column="MAX_SEND_COUNT" property="maxSendCount" jdbcType="DECIMAL" />
    <result column="cancel_state" property="cancelState" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, OUT_CANCEL_NO, CANCLE_NO, TRANSACTION_NO, OUT_TRADE_NO, CREATE_TIME, 
    UPDATE_TIME, AMOUNT, CURRENCY_TYPE, ACQ_ORG_CODE, ACQ_SP_ID, RETURN_CODE, RETURN_MSG, 
    CUSTOMERNAME, PAY_METHOD, CANCEL_SOURCE, SEND_COUNT, MAX_SEND_COUNT, CANCEL_STATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TXS_CANCEL_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_CANCEL_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsCancelOrder" >
    insert into TXS_CANCEL_ORDER (ID, CUSTOMER_CODE, OUT_CANCEL_NO, 
      CANCLE_NO, TRANSACTION_NO, OUT_TRADE_NO, 
      CREATE_TIME, UPDATE_TIME, AMOUNT, 
      CURRENCY_TYPE, ACQ_ORG_CODE, ACQ_SP_ID, 
      RETURN_CODE, RETURN_MSG, CUSTOMERNAME, 
      PAY_METHOD, CANCEL_SOURCE, SEND_COUNT, 
      MAX_SEND_COUNT, CANCEL_STATE)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{outCancelNo,jdbcType=VARCHAR}, 
      #{cancleNo,jdbcType=VARCHAR}, #{transactionNo,jdbcType=VARCHAR}, #{outTradeNo,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{amount,jdbcType=DECIMAL}, 
      #{currencyType,jdbcType=VARCHAR}, #{acqOrgCode,jdbcType=VARCHAR}, #{acqSpId,jdbcType=VARCHAR}, 
      #{returnCode,jdbcType=VARCHAR}, #{returnMsg,jdbcType=VARCHAR}, #{customername,jdbcType=VARCHAR}, 
      #{payMethod,jdbcType=VARCHAR}, #{cancelSource,jdbcType=VARCHAR}, #{sendCount,jdbcType=DECIMAL}, 
      #{maxSendCount,jdbcType=DECIMAL}, #{cancelState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsCancelOrder" >
    insert into TXS_CANCEL_ORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="outCancelNo != null" >
        OUT_CANCEL_NO,
      </if>
      <if test="cancleNo != null" >
        CANCLE_NO,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="currencyType != null" >
        CURRENCY_TYPE,
      </if>
      <if test="acqOrgCode != null" >
        ACQ_ORG_CODE,
      </if>
      <if test="acqSpId != null" >
        ACQ_SP_ID,
      </if>
      <if test="returnCode != null" >
        RETURN_CODE,
      </if>
      <if test="returnMsg != null" >
        RETURN_MSG,
      </if>
      <if test="customername != null" >
        CUSTOMERNAME,
      </if>
      <if test="payMethod != null" >
        PAY_METHOD,
      </if>
      <if test="cancelSource != null" >
        CANCEL_SOURCE,
      </if>
      <if test="sendCount != null" >
        SEND_COUNT,
      </if>
      <if test="maxSendCount != null" >
        MAX_SEND_COUNT,
      </if>
      <if test="cancelState != null" >
        cancel_state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outCancelNo != null" >
        #{outCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="cancleNo != null" >
        #{cancleNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null" >
        #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="acqOrgCode != null" >
        #{acqOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="acqSpId != null" >
        #{acqSpId,jdbcType=VARCHAR},
      </if>
      <if test="returnCode != null" >
        #{returnCode,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null" >
        #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="customername != null" >
        #{customername,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="cancelSource != null" >
        #{cancelSource,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null" >
        #{sendCount,jdbcType=DECIMAL},
      </if>
      <if test="maxSendCount != null" >
        #{maxSendCount,jdbcType=DECIMAL},
      </if>
      <if test="cancelState != null" >
        #{cancelState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsCancelOrder" >
    update TXS_CANCEL_ORDER
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outCancelNo != null" >
        OUT_CANCEL_NO = #{outCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="cancleNo != null" >
        CANCLE_NO = #{cancleNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null" >
        CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="acqOrgCode != null" >
        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="acqSpId != null" >
        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
      </if>
      <if test="returnCode != null" >
        RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null" >
        RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="customername != null" >
        CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null" >
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="cancelSource != null" >
        CANCEL_SOURCE = #{cancelSource,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null" >
        SEND_COUNT = #{sendCount,jdbcType=DECIMAL},
      </if>
      <if test="maxSendCount != null" >
        MAX_SEND_COUNT = #{maxSendCount,jdbcType=DECIMAL},
      </if>
      <if test="cancelState != null" >
        CANCEL_STATE = #{cancelState,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsCancelOrder" >
    update TXS_CANCEL_ORDER
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      OUT_CANCEL_NO = #{outCancelNo,jdbcType=VARCHAR},
      CANCLE_NO = #{cancleNo,jdbcType=VARCHAR},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
      ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
      RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
      RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
      CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      CANCEL_SOURCE = #{cancelSource,jdbcType=VARCHAR},
      SEND_COUNT = #{sendCount,jdbcType=DECIMAL},
      MAX_SEND_COUNT = #{maxSendCount,jdbcType=DECIMAL},
      CANCEL_STATE = #{cancelState,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectStateInDoing" resultMap="BaseResultMap" parameterType="java.util.Map">
  select
		<include refid="Base_Column_List" />
		from (
				select A.*, rownum RN
				from ( 
				select * from TXS_CANCEL_ORDER where CREATE_TIME &gt;= sysdate-1 and cancel_state = '03' and (send_count &lt; max_send_count or send_count is null)
		 			order by CREATE_TIME desc 
					) A
				where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
    </select>
    
   <select id="selectByOutCancelNoAndCustomerCode" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from TXS_CANCEL_ORDER
    where 
    	OUT_CANCEL_NO = #{outCancelNo,jdbcType=VARCHAR}
    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
</mapper>