<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsMemberWithdrawRecordMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsMemberWithdrawRecord">
    <id column="WITHDRAW_TRANSACTION_NO" jdbcType="VARCHAR" property="withdrawTransactionNo" />
    <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="MEMBER_CUSTOMER_CODE" jdbcType="VARCHAR" property="memberCustomerCode" />
    <result column="V_AMOUNT" jdbcType="DECIMAL" property="vAmount" />
    <result column="NOTIFY_URL" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="PAY_STATE" jdbcType="CHAR" property="payState" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="PARENT_CUSTOMER_CODE" jdbcType="VARCHAR" property="parentCustomerCode" />
    <result column="R_AMOUNT" jdbcType="DECIMAL" property="rAmount" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="MEM_TRNAS_TRANSATION_NO" jdbcType="VARCHAR" property="memTrnasTransationNo" />
  </resultMap>
  <sql id="Base_Column_List">
    WITHDRAW_TRANSACTION_NO, OUT_TRADE_NO, MEMBER_CUSTOMER_CODE, V_AMOUNT, NOTIFY_URL, 
    PAY_STATE, CREATE_TIME, END_TIME, REMARK, BUSINESS_INST_ID, PARENT_CUSTOMER_CODE, 
    R_AMOUNT, BUSINESS_CODE, MEM_TRNAS_TRANSATION_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_WITHDRAW_RECORD
    where WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from TXS_MEMBER_WITHDRAW_RECORD
    where WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberWithdrawRecord">
    insert into TXS_MEMBER_WITHDRAW_RECORD (WITHDRAW_TRANSACTION_NO, OUT_TRADE_NO, 
      MEMBER_CUSTOMER_CODE, V_AMOUNT, NOTIFY_URL, 
      PAY_STATE, CREATE_TIME, END_TIME, 
      REMARK, BUSINESS_INST_ID, PARENT_CUSTOMER_CODE, 
      R_AMOUNT, BUSINESS_CODE, MEM_TRNAS_TRANSATION_NO
      )
    values (#{withdrawTransactionNo,jdbcType=VARCHAR}, #{outTradeNo,jdbcType=VARCHAR}, 
      #{memberCustomerCode,jdbcType=VARCHAR}, #{vAmount,jdbcType=DECIMAL}, #{notifyUrl,jdbcType=VARCHAR}, 
      #{payState,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{businessInstId,jdbcType=VARCHAR}, #{parentCustomerCode,jdbcType=VARCHAR}, 
      #{rAmount,jdbcType=DECIMAL}, #{businessCode,jdbcType=VARCHAR}, #{memTrnasTransationNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberWithdrawRecord">
    insert into TXS_MEMBER_WITHDRAW_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="withdrawTransactionNo != null">
        WITHDRAW_TRANSACTION_NO,
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO,
      </if>
      <if test="memberCustomerCode != null">
        MEMBER_CUSTOMER_CODE,
      </if>
      <if test="vAmount != null">
        V_AMOUNT,
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL,
      </if>
      <if test="payState != null">
        PAY_STATE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="parentCustomerCode != null">
        PARENT_CUSTOMER_CODE,
      </if>
      <if test="rAmount != null">
        R_AMOUNT,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="memTrnasTransationNo != null">
        MEM_TRNAS_TRANSATION_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="withdrawTransactionNo != null">
        #{withdrawTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="memberCustomerCode != null">
        #{memberCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="vAmount != null">
        #{vAmount,jdbcType=DECIMAL},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="parentCustomerCode != null">
        #{parentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="rAmount != null">
        #{rAmount,jdbcType=DECIMAL},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="memTrnasTransationNo != null">
        #{memTrnasTransationNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberWithdrawRecord">
    update TXS_MEMBER_WITHDRAW_RECORD
    <set>
      <if test="outTradeNo != null">
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="memberCustomerCode != null">
        MEMBER_CUSTOMER_CODE = #{memberCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="vAmount != null">
        V_AMOUNT = #{vAmount,jdbcType=DECIMAL},
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        PAY_STATE = #{payState,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="parentCustomerCode != null">
        PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="rAmount != null">
        R_AMOUNT = #{rAmount,jdbcType=DECIMAL},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="memTrnasTransationNo != null">
        MEM_TRNAS_TRANSATION_NO = #{memTrnasTransationNo,jdbcType=VARCHAR},
      </if>
    </set>
    where WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMemberWithdrawRecord">
    update TXS_MEMBER_WITHDRAW_RECORD
    set OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      MEMBER_CUSTOMER_CODE = #{memberCustomerCode,jdbcType=VARCHAR},
      V_AMOUNT = #{vAmount,jdbcType=DECIMAL},
      NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      PAY_STATE = #{payState,jdbcType=CHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR},
      R_AMOUNT = #{rAmount,jdbcType=DECIMAL},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      MEM_TRNAS_TRANSATION_NO = #{memTrnasTransationNo,jdbcType=VARCHAR}
    where WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR}
  </update>
  <select id="selectByMemCustAndParCustAndOutTradeNo" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_MEMBER_WITHDRAW_RECORD
    where MEMBER_CUSTOMER_CODE = #{memberCustomerCode,jdbcType=VARCHAR} and 
    PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR} and 
    OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
  </select>
</mapper>