<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.AsynNotifyMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.AsynNotify">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ORDERID" jdbcType="VARCHAR" property="orderId" />
    <result column="ORDERTYPE" jdbcType="CHAR" property="orderType" />
    <result column="NOTIFYSTRATEGY" jdbcType="CHAR" property="notifyStrategy" />
    <result column="NOTIFYURL" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="NOTIFYCONTENT" jdbcType="VARCHAR" property="notifyContent" />
    <result column="CREATEDATETIME" jdbcType="TIMESTAMP" property="createDateTime" />
    <result column="MAXNOTIFYCOUNT" jdbcType="DECIMAL" property="maxNotifyCount" />
    <result column="NOTIFIEDCOUNT" jdbcType="DECIMAL" property="notifiedCount" />
    <result column="RESULT" jdbcType="CHAR" property="result" />
    <result column="LASTNOTIFYTIME" jdbcType="TIMESTAMP" property="lastNotifyTime" />
    <result column="NEXTNOTIFYTIME" jdbcType="TIMESTAMP" property="nextNotifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDERID, ORDERTYPE, NOTIFYSTRATEGY, NOTIFYURL, NOTIFYCONTENT, CREATEDATETIME, 
    MAXNOTIFYCOUNT, NOTIFIEDCOUNT, RESULT, LASTNOTIFYTIME, NEXTNOTIFYTIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_ASYNNOTIFY
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_ASYNNOTIFY
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.AsynNotify">
    insert into TXS_ASYNNOTIFY (ID, ORDERID, ORDERTYPE, 
      NOTIFYSTRATEGY, NOTIFYURL, NOTIFYCONTENT, 
      CREATEDATETIME, MAXNOTIFYCOUNT, NOTIFIEDCOUNT, 
      RESULT, LASTNOTIFYTIME, NEXTNOTIFYTIME
      )
    values (#{id,jdbcType=DECIMAL}, #{orderId,jdbcType=VARCHAR}, #{orderType,jdbcType=CHAR}, 
      #{notifyStrategy,jdbcType=CHAR}, #{notifyUrl,jdbcType=VARCHAR}, #{notifyContent,jdbcType=VARCHAR}, 
      #{createDateTime,jdbcType=TIMESTAMP}, #{maxNotifyCount,jdbcType=DECIMAL}, #{notifiedCount,jdbcType=DECIMAL}, 
      #{result,jdbcType=CHAR}, #{lastNotifyTime,jdbcType=TIMESTAMP}, #{nextNotifyTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.AsynNotify">
    insert into TXS_ASYNNOTIFY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="orderId != null">
        ORDERID,
      </if>
      <if test="orderType != null">
        ORDERTYPE,
      </if>
      <if test="notifyStrategy != null">
        NOTIFYSTRATEGY,
      </if>
      <if test="notifyUrl != null">
        NOTIFYURL,
      </if>
      <if test="notifyContent != null">
        NOTIFYCONTENT,
      </if>
      <if test="createDateTime != null">
        CREATEDATETIME,
      </if>
      <if test="maxNotifyCount != null">
        MAXNOTIFYCOUNT,
      </if>
      <if test="notifiedCount != null">
        NOTIFIEDCOUNT,
      </if>
      <if test="result != null">
        RESULT,
      </if>
      <if test="lastNotifyTime != null">
        LASTNOTIFYTIME,
      </if>
      <if test="nextNotifyTime != null">
        NEXTNOTIFYTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=CHAR},
      </if>
      <if test="notifyStrategy != null">
        #{notifyStrategy,jdbcType=CHAR},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="notifyContent != null">
        #{notifyContent,jdbcType=VARCHAR},
      </if>
      <if test="createDateTime != null">
        #{createDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="maxNotifyCount != null">
        #{maxNotifyCount,jdbcType=DECIMAL},
      </if>
      <if test="notifiedCount != null">
        #{notifiedCount,jdbcType=DECIMAL},
      </if>
      <if test="result != null">
        #{result,jdbcType=CHAR},
      </if>
      <if test="lastNotifyTime != null">
        #{lastNotifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextNotifyTime != null">
        #{nextNotifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.AsynNotify">
    update TXS_ASYNNOTIFY
    <set>
      <if test="orderId != null">
        ORDERID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDERTYPE = #{orderType,jdbcType=CHAR},
      </if>
      <if test="notifyStrategy != null">
        NOTIFYSTRATEGY = #{notifyStrategy,jdbcType=CHAR},
      </if>
      <if test="notifyUrl != null">
        NOTIFYURL = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="notifyContent != null">
        NOTIFYCONTENT = #{notifyContent,jdbcType=VARCHAR},
      </if>
      <if test="createDateTime != null">
        CREATEDATETIME = #{createDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="maxNotifyCount != null">
        MAXNOTIFYCOUNT = #{maxNotifyCount,jdbcType=DECIMAL},
      </if>
      <if test="notifiedCount != null">
        NOTIFIEDCOUNT = #{notifiedCount,jdbcType=DECIMAL},
      </if>
      <if test="result != null">
        RESULT = #{result,jdbcType=CHAR},
      </if>
      <if test="lastNotifyTime != null">
        LASTNOTIFYTIME = #{lastNotifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextNotifyTime != null">
        NEXTNOTIFYTIME = #{nextNotifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.AsynNotify">
    update TXS_ASYNNOTIFY
    set ORDERID = #{orderId,jdbcType=VARCHAR},
      ORDERTYPE = #{orderType,jdbcType=CHAR},
      NOTIFYSTRATEGY = #{notifyStrategy,jdbcType=CHAR},
      NOTIFYURL = #{notifyUrl,jdbcType=VARCHAR},
      NOTIFYCONTENT = #{notifyContent,jdbcType=VARCHAR},
      CREATEDATETIME = #{createDateTime,jdbcType=TIMESTAMP},
      MAXNOTIFYCOUNT = #{maxNotifyCount,jdbcType=DECIMAL},
      NOTIFIEDCOUNT = #{notifiedCount,jdbcType=DECIMAL},
      RESULT = #{result,jdbcType=CHAR},
      LASTNOTIFYTIME = #{lastNotifyTime,jdbcType=TIMESTAMP},
      NEXTNOTIFYTIME = #{nextNotifyTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <select id="selectByOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from TXS_ASYNNOTIFY
    where ORDERID = #{orderId,jdbcType=VARCHAR}
  </select>
  
  <update id="updateResult" parameterType="com.epaylinks.efps.txs.transaction.model.AsynNotify">
  	update TXS_ASYNNOTIFY set RESULT = #{result , jdbcType = VARCHAR} 
  	, NEXTNOTIFYTIME = #{nextNotifyTime , jdbcType = TIMESTAMP} 
  	, LASTNOTIFYTIME = #{lastNotifyTime , jdbcType = TIMESTAMP}
  	, NOTIFIEDCOUNT = #{notifiedCount , jdbcType = INTEGER}
  	where ID = #{id , jdbcType = DECIMAL}
  </update>

  
  <select id = "selectByNextNotifyTimeNotNull" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from TXS_ASYNNOTIFY
    where NEXTNOTIFYTIME is not null and NEXTNOTIFYTIME <![CDATA[ <= ]]> #{date,jdbcType=TIMESTAMP}
  </select>
  
</mapper>