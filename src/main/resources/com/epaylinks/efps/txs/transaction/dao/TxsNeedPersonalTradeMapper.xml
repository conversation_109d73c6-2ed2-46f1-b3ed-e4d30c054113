<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsNeedPersonalTradeMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsNeedPersonalTrade">
    <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="TABLE_NAME" jdbcType="VARCHAR" property="tableName" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
    <result column="PROCESS_REMARK" jdbcType="VARCHAR" property="processRemark" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSACTION_NO, CREATE_TIME, UPDATE_TIME, UPDATE_TIME, REMARK, TABLE_NAME, UPDATE_USER_ID,
    UPDATE_USER_NAME, PROCESS_REMARK, STATE
  </sql>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsNeedPersonalTrade">
    insert into TXS_NEED_PERSONAL_TRADE (TRANSACTION_NO, CREATE_TIME, UPDATE_TIME, 
      REMARK, TABLE_NAME, UPDATE_USER_ID, 
      UPDATE_USER_NAME, PROCESS_REMARK, STATE
      )
    values (#{transactionNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{tableName,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, 
      #{updateUserName,jdbcType=VARCHAR}, #{processRemark,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsNeedPersonalTrade">
    insert into TXS_NEED_PERSONAL_TRADE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="tableName != null">
        TABLE_NAME,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
      <if test="updateUserName != null">
        UPDATE_USER_NAME,
      </if>
      <if test="processRemark != null">
        PROCESS_REMARK,
      </if>
      <if test="state != null">
        STATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="processRemark != null">
        #{processRemark,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  
  <select id="selectByPage" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
				select A.*, rownum RN
				from ( 
				select * from TXS_NEED_PERSONAL_TRADE
					<where>
						<if test="transactionNo != null and transactionNo !=''">
							AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}  
						</if>
						<if test="payState != null and payState !=''">
							AND STATE = #{payState,jdbcType=CHAR} 
						</if>
						<if test="beginTime!=null">
			   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
						</if>
						<if test="endTime!=null">
			   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP} 
						</if> 			 
					</where>
		 			order by CREATE_TIME desc 
					) A
				where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
		
	</select>
	
	<select id="selectCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		select count(*)
		from TXS_NEED_PERSONAL_TRADE
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="payState != null and payState !=''">
				AND STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="beginTime!=null">
				AND CREATE_TIME <![CDATA[ >= ]]>
				#{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null">
				AND CREATE_TIME <![CDATA[ <= ]]>
				#{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>
	
	<select id="selectByNotPage" resultMap="BaseResultMap"
	parameterType="java.util.Map">
	select
	<include refid="Base_Column_List" />
	from TXS_NEED_PERSONAL_TRADE
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="payState != null and payState !=''">
				AND STATE = #{payState,jdbcType=CHAR}
			</if>
			<if test="beginTime!=null">
				AND CREATE_TIME <![CDATA[ >= ]]> #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null">
				AND CREATE_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>
</mapper>