<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsSplitRelationMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsSplitRelation" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="IS_PROCEDURE_CUSTOMER" property="isProcedureCustomer" jdbcType="DECIMAL" />
    <result column="RATIO" property="ratio" jdbcType="DECIMAL" />
    <result column="SPLIT_RELATION_ID" property="splitRelationId" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="BELONG_CUSTOMER_CODE" property="belongCustomerCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, IS_PROCEDURE_CUSTOMER, RATIO, SPLIT_RELATION_ID, CREATE_TIME, 
    UPDATE_TIME, BELONG_CUSTOMER_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_RELATION
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_SPLIT_RELATION
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <delete id="deleteByRelationId" >
    delete from TXS_SPLIT_RELATION
    where SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR}
    and BELONG_CUSTOMER_CODE = #{belongCustomerCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitRelation" >
    insert into TXS_SPLIT_RELATION (ID, CUSTOMER_CODE, IS_PROCEDURE_CUSTOMER, 
      RATIO, SPLIT_RELATION_ID, CREATE_TIME, 
      UPDATE_TIME, BELONG_CUSTOMER_CODE)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{isProcedureCustomer,jdbcType=DECIMAL}, 
      #{ratio,jdbcType=DECIMAL}, #{splitRelationId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{belongCustomerCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitRelation" >
    insert into TXS_SPLIT_RELATION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="isProcedureCustomer != null" >
        IS_PROCEDURE_CUSTOMER,
      </if>
      <if test="ratio != null" >
        RATIO,
      </if>
      <if test="splitRelationId != null" >
        SPLIT_RELATION_ID,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="belongCustomerCode != null" >
        BELONG_CUSTOMER_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isProcedureCustomer != null" >
        #{isProcedureCustomer,jdbcType=DECIMAL},
      </if>
      <if test="ratio != null" >
        #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="splitRelationId != null" >
        #{splitRelationId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongCustomerCode != null" >
        #{belongCustomerCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitRelation" >
    update TXS_SPLIT_RELATION
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isProcedureCustomer != null" >
        IS_PROCEDURE_CUSTOMER = #{isProcedureCustomer,jdbcType=DECIMAL},
      </if>
      <if test="ratio != null" >
        RATIO = #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="splitRelationId != null" >
        SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongCustomerCode != null" >
        BELONG_CUSTOMER_CODE = #{belongCustomerCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsSplitRelation" >
    update TXS_SPLIT_RELATION
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      IS_PROCEDURE_CUSTOMER = #{isProcedureCustomer,jdbcType=DECIMAL},
      RATIO = #{ratio,jdbcType=DECIMAL},
      SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      BELONG_CUSTOMER_CODE = #{belongCustomerCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByRelationId" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from TXS_SPLIT_RELATION
    where SPLIT_RELATION_ID = #{splitRelationId,jdbcType=VARCHAR}
  </select>
  
</mapper>