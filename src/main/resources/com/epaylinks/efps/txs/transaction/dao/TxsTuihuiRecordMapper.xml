<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsTuihuiRecordMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsTuihuiRecord">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="ORGI_TXN_NO" jdbcType="VARCHAR" property="orgiTxnNo" />
    <result column="ORGI_TOTAL_FEE" jdbcType="DECIMAL" property="orgiTotalFee" />
    <result column="ORGI_PROCEDURE_FEE" jdbcType="DECIMAL" property="orgiProcedureFee" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="CUSTOMERCODE" jdbcType="VARCHAR" property="customercode" />
    <result column="CUSTOMERNAME" jdbcType="VARCHAR" property="customername" />
    <result column="BANK_ACCOUNT_NO" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="BANK_ACCOUNT_NAME" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="ACCOUNTVOUCHERNO" jdbcType="VARCHAR" property="accountVoucherNo" />
    <result column="MER_NAME" jdbcType="VARCHAR" property="merName" />
    <result column="ORIG_TXNDATE" jdbcType="TIMESTAMP" property="origTxnDate" />
    <result column="TUIHUI_NO" jdbcType="VARCHAR" property="tuihuiNo" />
    <result column="ORGI_SERVICE_FEE" jdbcType="DECIMAL" property="orgiServiceFee" />
    <result column="ORGI_ACTUAL_FEE" jdbcType="DECIMAL" property="orgiActualFee" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, TRANSACTION_NO, ORGI_TXN_NO, ORGI_TOTAL_FEE, ORGI_PROCEDURE_FEE, STATE, ERROR_CODE, 
    CREATE_TIME, UPDATE_TIME, REASON, CUSTOMERCODE, CUSTOMERNAME, BANK_ACCOUNT_NO, BANK_ACCOUNT_NAME,
    ACCOUNTVOUCHERNO, MER_NAME, ORIG_TXNDATE, TUIHUI_NO,ORGI_SERVICE_FEE,ORGI_ACTUAL_FEE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_TUIHUI_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_TUIHUI_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsTuihuiRecord">
    insert into TXS_TUIHUI_RECORD (ID, TRANSACTION_NO, ORGI_TXN_NO, 
      ORGI_TOTAL_FEE, ORGI_PROCEDURE_FEE, STATE, 
      ERROR_CODE, CREATE_TIME, UPDATE_TIME, 
      REASON, CUSTOMERCODE, CUSTOMERNAME, 
      BANK_ACCOUNT_NO, BANK_ACCOUNT_NAME, ACCOUNTVOUCHERNO,
      MER_NAME, ORIG_TXNDATE, TUIHUI_NO,ORGI_SERVICE_FEE,ORGI_ACTUAL_FEE)
    values (#{id,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, #{orgiTxnNo,jdbcType=VARCHAR}, 
      #{orgiTotalFee,jdbcType=DECIMAL}, #{orgiProcedureFee,jdbcType=DECIMAL}, #{state,jdbcType=VARCHAR}, 
      #{errorCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{reason,jdbcType=VARCHAR}, #{customercode,jdbcType=VARCHAR}, #{customername,jdbcType=VARCHAR}, 
      #{bankAccountNo,jdbcType=VARCHAR}, #{bankAccountName,jdbcType=VARCHAR}, #{accountVoucherNo,jdbcType=VARCHAR},
      #{merName,jdbcType=VARCHAR}, #{origTxnDate,jdbcType=TIMESTAMP}, #{tuihuiNo,jdbcType=VARCHAR},
      #{orgiServiceFee,jdbcType=DECIMAL}, #{orgiActualFee,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsTuihuiRecord">
    insert into TXS_TUIHUI_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="orgiTxnNo != null">
        ORGI_TXN_NO,
      </if>
      <if test="orgiTotalFee != null">
        ORGI_TOTAL_FEE,
      </if>
      <if test="orgiProcedureFee != null">
        ORGI_PROCEDURE_FEE,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="customercode != null">
        CUSTOMERCODE,
      </if>
      <if test="customername != null">
        CUSTOMERNAME,
      </if>
      <if test="bankAccountNo != null">
        BANK_ACCOUNT_NO,
      </if>
      <if test="bankAccountName != null">
        BANK_ACCOUNT_NAME,
      </if>
      <if test="accountVoucherNo != null">
        ACCOUNTVOUCHERNO,
      </if>
      <if test="merName != null">
        MER_NAME,
      </if>
      <if test="origTxnDate != null">
        ORIG_TXNDATE,
      </if>
      <if test="tuihuiNo != null">
        TUIHUI_NO,
      </if>
      <if test="orgiServiceFee != null">
        ORGI_SERVICE_FEE,
      </if>
      <if test="orgiActualFee != null">
        ORGI_ACTUAL_FEE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="orgiTxnNo != null">
        #{orgiTxnNo,jdbcType=VARCHAR},
      </if>
      <if test="orgiTotalFee != null">
        #{orgiTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="orgiProcedureFee != null">
        #{orgiProcedureFee,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="customercode != null">
        #{customercode,jdbcType=VARCHAR},
      </if>
      <if test="customername != null">
        #{customername,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="accountVoucherNo != null">
        #{accountVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="merName != null">
        #{merName,jdbcType=VARCHAR},
      </if>
      <if test="origTxnDate != null">
        #{origTxnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tuihuiNo != null">
        #{tuihuiNo,jdbcType=VARCHAR},
      </if>
      <if test="orgiServiceFee != null">
        #{orgiServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="orgiActualFee != null">
        #{orgiActualFee,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsTuihuiRecord">
    update TXS_TUIHUI_RECORD
    <set>
      <if test="transactionNo != null">
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="orgiTxnNo != null">
        ORGI_TXN_NO = #{orgiTxnNo,jdbcType=VARCHAR},
      </if>
      <if test="orgiTotalFee != null">
        ORGI_TOTAL_FEE = #{orgiTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="orgiProcedureFee != null">
        ORGI_PROCEDURE_FEE = #{orgiProcedureFee,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="customercode != null">
        CUSTOMERCODE = #{customercode,jdbcType=VARCHAR},
      </if>
      <if test="customername != null">
        CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        BANK_ACCOUNT_NO = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        BANK_ACCOUNT_NAME = #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="accountVoucherNo != null">
        ACCOUNTVOUCHERNO = #{accountVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="merName != null">
        MER_NAME = #{merName,jdbcType=VARCHAR},
      </if>
      <if test="origTxnDate != null">
        ORIG_TXNDATE = #{origTxnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tuihuiNo != null">
        TUIHUI_NO = #{tuihuiNo,jdbcType=VARCHAR},
      </if>
      <if test="orgiServiceFee != null">
        ORGI_SERVICE_FEE = #{orgiServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="orgiActualFee != null">
        ORGI_ACTUAL_FEE = #{orgiActualFee,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsTuihuiRecord">
    update TXS_TUIHUI_RECORD
    set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      ORGI_TXN_NO = #{orgiTxnNo,jdbcType=VARCHAR},
      ORGI_TOTAL_FEE = #{orgiTotalFee,jdbcType=DECIMAL},
      ORGI_PROCEDURE_FEE = #{orgiProcedureFee,jdbcType=DECIMAL},
      STATE = #{state,jdbcType=VARCHAR},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REASON = #{reason,jdbcType=VARCHAR},
      CUSTOMERCODE = #{customercode,jdbcType=VARCHAR},
      CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      BANK_ACCOUNT_NO = #{bankAccountNo,jdbcType=VARCHAR},
      BANK_ACCOUNT_NAME = #{bankAccountName,jdbcType=VARCHAR},
      ACCOUNTVOUCHERNO = #{accountVoucherNo,jdbcType=VARCHAR},
      MER_NAME = #{merName,jdbcType=VARCHAR},
      ORIG_TXNDATE = #{origTxnDate,jdbcType=TIMESTAMP},
      TUIHUI_NO = #{tuihuiNo,jdbcType=VARCHAR},
      ORGI_SERVICE_FEE = #{orgiServiceFee,jdbcType=DECIMAL},
      ORGI_ACTUAL_FEE = #{orgiActualFee,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByTransaction" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from TXS_TUIHUI_RECORD
		where ORGI_TXN_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select *
    from TXS_TUIHUI_RECORD
    where 1=1
    <if test="timeBegin != null">
      and CREATE_TIME &gt;= #{timeBegin,jdbcType=TIMESTAMP}
    </if>
    <if test="timeEnd != null">
      <![CDATA[
      and CREATE_TIME-1 <= #{timeEnd,jdbcType=TIMESTAMP}
      ]]>
    </if>
    <if test="orgTxnTimeBegin != null">
      and ORIG_TXNDATE &gt;= #{orgTxnTimeBegin,jdbcType=TIMESTAMP}
    </if>
    <if test="orgTxnTimeEnd != null">
      <![CDATA[
      and ORIG_TXNDATE-1 <= #{orgTxnTimeEnd,jdbcType=TIMESTAMP}
      ]]>
    </if>
    <if test="origTxnNo != null and origTxnNo !=''">
      AND ORGI_TXN_NO = #{origTxnNo,jdbcType=VARCHAR}
    </if>
    <if test="txnNo != null and txnNo !=''">
      AND TUIHUI_NO like '%'||#{txnNo,jdbcType=VARCHAR}||'%'
    </if>
    <if test="merName != null and merName !=''">
      AND CUSTOMERNAME like '%'||#{merName,jdbcType=VARCHAR}||'%'
    </if>
    <if test="merId != null and merId !=''">
      AND CUSTOMERCODE like '%'||#{merId,jdbcType=VARCHAR}||'%'
    </if>
    <if test="refundState != null and refundState !=''">
      AND STATE = #{refundState,jdbcType=CHAR}
    </if>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <select id="count" resultType="java.lang.Integer">
    select count(*)
    from TXS_TUIHUI_RECORD
    where 1=1
    <if test="timeBegin != null">
      and CREATE_TIME &gt;= #{timeBegin,jdbcType=TIMESTAMP}
    </if>
    <if test="timeEnd != null">
      <![CDATA[
      and CREATE_TIME-1 <= #{timeEnd,jdbcType=TIMESTAMP}
      ]]>
    </if>
    <if test="orgTxnTimeBegin != null">
      and ORIG_TXNDATE &gt;= #{orgTxnTimeBegin,jdbcType=TIMESTAMP}
    </if>
    <if test="orgTxnTimeEnd != null">
      <![CDATA[
      and ORIG_TXNDATE-1 <= #{orgTxnTimeEnd,jdbcType=TIMESTAMP}
      ]]>
    </if>
    <if test="origTxnNo != null and origTxnNo !=''">
      AND ORGI_TXN_NO = #{origTxnNo,jdbcType=VARCHAR}
    </if>
    <if test="txnNo != null and txnNo !=''">
      AND TUIHUI_NO like '%'||#{txnNo,jdbcType=VARCHAR}||'%'
    </if>
    <if test="merName != null and merName !=''">
      AND CUSTOMERNAME like '%'||#{merName,jdbcType=VARCHAR}||'%'
    </if>
    <if test="merId != null and merId !=''">
      AND CUSTOMERCODE like '%'||#{merId,jdbcType=VARCHAR}||'%'
    </if>
    <if test="refundState != null and refundState !=''">
      AND STATE = #{refundState,jdbcType=CHAR}
    </if>
  </select>
  <select id="notPageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TXS_TUIHUI_RECORD
    where 1=1
    <if test="timeBegin != null">
      and CREATE_TIME &gt;= #{timeBegin,jdbcType=TIMESTAMP}
    </if>
    <if test="timeEnd != null">
      <![CDATA[
      and CREATE_TIME-1 <= #{timeEnd,jdbcType=TIMESTAMP}
      ]]>
    </if>
    <if test="orgTxnTimeBegin != null">
      and ORIG_TXNDATE &gt;= #{orgTxnTimeBegin,jdbcType=TIMESTAMP}
    </if>
    <if test="orgTxnTimeEnd != null">
      <![CDATA[
      and ORIG_TXNDATE-1 <= #{orgTxnTimeEnd,jdbcType=TIMESTAMP}
      ]]>
    </if>
    <if test="origTxnNo != null and origTxnNo !=''">
      AND ORGI_TXN_NO = #{origTxnNo,jdbcType=VARCHAR}
    </if>
    <if test="txnNo != null and txnNo !=''">
      AND TUIHUI_NO like '%'||#{txnNo,jdbcType=VARCHAR}||'%'
    </if>
    <if test="merName != null and merName !=''">
      AND CUSTOMERNAME like '%'||#{merName,jdbcType=VARCHAR}||'%'
    </if>
    <if test="merId != null and merId !=''">
      AND CUSTOMERCODE like '%'||#{merId,jdbcType=VARCHAR}||'%'
    </if>
    <if test="refundState != null and refundState !=''">
      AND STATE = #{refundState,jdbcType=CHAR}
    </if>
    order by CREATE_TIME desc
  </select>
</mapper>