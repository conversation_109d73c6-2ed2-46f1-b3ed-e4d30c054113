<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="com.epaylinks.efps.txs.transaction.dao.TxsRechargeTradeOrderMapper">
	<resultMap id="BaseResultMap"
		type="com.epaylinks.efps.txs.transaction.model.TxsRechargeTradeOrder">
		<id column="ID" property="id" jdbcType="DECIMAL" />
		<result column="TRANSACTION_NO" property="transactionNo"
			jdbcType="VARCHAR" />
		<result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
		<result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
		<result column="TERMINAL_NO" property="terminalNo" jdbcType="VARCHAR" />
		<result column="TRADE_NAME" property="tradeName" jdbcType="VARCHAR" />
		<result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
		<result column="PAY_AMOUNT" property="payAmount" jdbcType="DECIMAL" />
		<result column="PAY_CURRENCY" property="payCurrency" jdbcType="VARCHAR" />
		<result column="CHANNEL_TYPE" property="channelType" jdbcType="CHAR" />
		<result column="FEE" property="fee" jdbcType="DECIMAL" />
		<result column="PAY_INFO" property="payInfo" jdbcType="VARCHAR" />
		<result column="MCH_USER_ID" property="mchUserId" jdbcType="VARCHAR" />
		<result column="APP_ID" property="appId" jdbcType="VARCHAR" />
		<result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
		<result column="REDIRECT_URL" property="redirectUrl" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="PAY_STATE" property="payState" jdbcType="CHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List">
		ID, TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE, TERMINAL_NO, TRADE_NAME, PAY_METHOD,
		PAY_AMOUNT, PAY_CURRENCY,
		CHANNEL_TYPE, FEE, PAY_INFO, MCH_USER_ID, APP_ID, NOTIFY_URL, REDIRECT_URL,
		CREATE_TIME,
		PAY_STATE, UPDATE_TIME
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from TXS_RECHARGE_TRADE_ORDER
		where ID = #{id,jdbcType=DECIMAL}
	</select>

	<select id="selectBySelective" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_RECHARGE_TRADE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} 
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR} 
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>
	
	<select id="selectByPage" resultMap="BaseResultMap"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRechargeTradeOrder">
		select 
		<include refid="Base_Column_List" />
		from TXS_RECHARGE_TRADE_ORDER 
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}  
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="payState != null and payState !=''">
				AND PAY_STATE = #{payState,jdbcType=CHAR} 
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}  
			</if>
		</where>
		order by CREATE_TIME desc  
	</select>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from TXS_RECHARGE_TRADE_ORDER
		where ID = #{id,jdbcType=DECIMAL}
	</delete>
	<insert id="insert"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRechargeTradeOrder">
		insert into TXS_RECHARGE_TRADE_ORDER (ID, TRANSACTION_NO,
		OUT_TRADE_NO,
		CUSTOMER_CODE, TERMINAL_NO, TRADE_NAME , PAY_METHOD,
		PAY_AMOUNT, PAY_CURRENCY, CHANNEL_TYPE,
		FEE, PAY_INFO, MCH_USER_ID,
		APP_ID, NOTIFY_URL, REDIRECT_URL,
		CREATE_TIME, PAY_STATE, UPDATE_TIME
		)
		values (#{id,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR},
		#{outTradeNo,jdbcType=VARCHAR},
		#{customerCode,jdbcType=VARCHAR}, #{terminalNo,jdbcType=VARCHAR}, #{tradeName,jdbcType=VARCHAR},#{payMethod,jdbcType=VARCHAR},
		#{payAmount,jdbcType=DECIMAL}, #{payCurrency,jdbcType=VARCHAR},
		#{channelType,jdbcType=CHAR},
		#{fee,jdbcType=DECIMAL}, #{payInfo,jdbcType=VARCHAR}, #{mchUserId,jdbcType=VARCHAR},
		#{appId,jdbcType=VARCHAR}, #{notifyUrl,jdbcType=VARCHAR},
		#{redirectUrl,jdbcType=VARCHAR},
		#{createTime,jdbcType=TIMESTAMP}, #{payState,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}
		)
	</insert>
	<insert id="insertSelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRechargeTradeOrder">
		insert into TXS_RECHARGE_TRADE_ORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				ID,
			</if>
			<if test="transactionNo != null">
				TRANSACTION_NO,
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO,
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE,
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO,
			</if>
			<if test="tradeName != null">
				TRADE_NAME,
			</if>
			<if test="payMethod != null">
				PAY_METHOD,
			</if>
			<if test="payAmount != null">
				PAY_AMOUNT,
			</if>
			<if test="payCurrency != null">
				PAY_CURRENCY,
			</if>
			<if test="channelType != null">
				CHANNEL_TYPE,
			</if>
			<if test="fee != null">
				FEE,
			</if>
			<if test="payInfo != null">
				PAY_INFO,
			</if>
			<if test="mchUserId != null">
				MCH_USER_ID,
			</if>
			<if test="appId != null">
				APP_ID,
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL,
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL,
			</if>
			<if test="createTime != null">
				CREATE_TIME,
			</if>
			<if test="payState != null">
				PAY_STATE,
			</if>
			<if test="updateTime != null">
				UPDATE_TIME,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=DECIMAL},
			</if>
			<if test="transactionNo != null">
				#{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				#{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				#{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				#{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="tradeName != null">
				#{tradeName,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				#{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="payAmount != null">
				#{payAmount,jdbcType=DECIMAL},
			</if>
			<if test="payCurrency != null">
				#{payCurrency,jdbcType=VARCHAR},
			</if>
			<if test="channelType != null">
				#{channelType,jdbcType=CHAR},
			</if>
			<if test="fee != null">
				#{fee,jdbcType=DECIMAL},
			</if>
			<if test="payInfo != null">
				#{payInfo,jdbcType=VARCHAR},
			</if>
			<if test="mchUserId != null">
				#{mchUserId,jdbcType=VARCHAR},
			</if>
			<if test="appId != null">
				#{appId,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				#{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				#{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				#{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRechargeTradeOrder">
		update TXS_RECHARGE_TRADE_ORDER
		<set>
			<if test="transactionNo != null">
				TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
			</if>
			<if test="outTradeNo != null">
				OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="terminalNo != null">
				TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
			</if>
			<if test="tradeName != null">
				TRADE_NAME = #{tradeName,jdbcType=VARCHAR},
			</if>
			<if test="payMethod != null">
				PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
			</if>
			<if test="payAmount != null">
				PAY_AMOUNT = #{payAmount,jdbcType=DECIMAL},
			</if>
			<if test="payCurrency != null">
				PAY_CURRENCY = #{payCurrency,jdbcType=VARCHAR},
			</if>
			<if test="channelType != null">
				CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
			</if>
			<if test="fee != null">
				FEE = #{fee,jdbcType=DECIMAL},
			</if>
			<if test="payInfo != null">
				PAY_INFO = #{payInfo,jdbcType=VARCHAR},
			</if>
			<if test="mchUserId != null">
				MCH_USER_ID = #{mchUserId,jdbcType=VARCHAR},
			</if>
			<if test="appId != null">
				APP_ID = #{appId,jdbcType=VARCHAR},
			</if>
			<if test="notifyUrl != null">
				NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
			</if>
			<if test="redirectUrl != null">
				REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payState != null">
				PAY_STATE = #{payState,jdbcType=CHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where ID = #{id,jdbcType=DECIMAL}
	</update>
	<update id="updateByPrimaryKey"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsRechargeTradeOrder">
		update TXS_RECHARGE_TRADE_ORDER
		set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
		OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
		CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
		TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
		TRADE_NAME = #{tradeName,jdbcType=VARCHAR},
		PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
		PAY_AMOUNT = #{payAmount,jdbcType=DECIMAL},
		PAY_CURRENCY = #{payCurrency,jdbcType=VARCHAR},
		CHANNEL_TYPE = #{channelType,jdbcType=CHAR},
		FEE = #{fee,jdbcType=DECIMAL},
		PAY_INFO = #{payInfo,jdbcType=VARCHAR},
		MCH_USER_ID = #{mchUserId,jdbcType=VARCHAR},
		APP_ID = #{appId,jdbcType=VARCHAR},
		NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
		REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
		CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
		PAY_STATE = #{payState,jdbcType=CHAR},
		UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		where ID = #{id,jdbcType=DECIMAL}
	</update>
</mapper>