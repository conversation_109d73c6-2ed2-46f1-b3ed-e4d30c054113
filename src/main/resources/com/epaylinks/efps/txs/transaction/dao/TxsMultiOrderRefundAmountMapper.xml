<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsMultiOrderRefundAmountMapper" >
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount" >
        <id column="ID" property="id" jdbcType="DECIMAL" />
        <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
        <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
        <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
        <result column="REFUNDING_AMOUNT" property="refundingAmount" jdbcType="DECIMAL" />
        <result column="REFUNDED_AMOUNT" property="refundedAmount" jdbcType="DECIMAL" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="IS_TRADE_CUSTOMER_CODE" property="isTradeCustomerCode" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
    ID, TRANSACTION_NO, CUSTOMER_CODE, TOTAL_AMOUNT, REFUNDING_AMOUNT, REFUNDED_AMOUNT,
    UPDATE_TIME, IS_TRADE_CUSTOMER_CODE
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from TXS_MULTI_ORDER_REFUND_AMOUNT
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_MULTI_ORDER_REFUND_AMOUNT
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount" >
    insert into TXS_MULTI_ORDER_REFUND_AMOUNT (ID, TRANSACTION_NO, CUSTOMER_CODE,
      TOTAL_AMOUNT, REFUNDING_AMOUNT, REFUNDED_AMOUNT,
      UPDATE_TIME, IS_TRADE_CUSTOMER_CODE)
    values (#{id,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR},
      #{totalAmount,jdbcType=DECIMAL}, #{refundingAmount,jdbcType=DECIMAL}, #{refundedAmount,jdbcType=DECIMAL},
      #{updateTime,jdbcType=TIMESTAMP}, #{isTradeCustomerCode,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount" >
        insert into TXS_MULTI_ORDER_REFUND_AMOUNT
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID,
            </if>
            <if test="transactionNo != null" >
                TRANSACTION_NO,
            </if>
            <if test="customerCode != null" >
                CUSTOMER_CODE,
            </if>
            <if test="totalAmount != null" >
                TOTAL_AMOUNT,
            </if>
            <if test="refundingAmount != null" >
                REFUNDING_AMOUNT,
            </if>
            <if test="refundedAmount != null" >
                REFUNDED_AMOUNT,
            </if>
            <if test="updateTime != null" >
                UPDATE_TIME,
            </if>
            <if test="isTradeCustomerCode != null" >
                IS_TRADE_CUSTOMER_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=DECIMAL},
            </if>
            <if test="transactionNo != null" >
                #{transactionNo,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null" >
                #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null" >
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundingAmount != null" >
                #{refundingAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundedAmount != null" >
                #{refundedAmount,jdbcType=DECIMAL},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isTradeCustomerCode != null" >
                #{isTradeCustomerCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount" >
        update TXS_MULTI_ORDER_REFUND_AMOUNT
        <set >
            <if test="transactionNo != null" >
                TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null" >
                CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null" >
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundingAmount != null" >
                REFUNDING_AMOUNT = #{refundingAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundedAmount != null" >
                REFUNDED_AMOUNT = #{refundedAmount,jdbcType=DECIMAL},
            </if>
            <if test="updateTime != null" >
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isTradeCustomerCode != null" >
                IS_TRADE_CUSTOMER_CODE = #{isTradeCustomerCode,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount" >
    update TXS_MULTI_ORDER_REFUND_AMOUNT
    set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      REFUNDING_AMOUNT = #{refundingAmount,jdbcType=DECIMAL},
      REFUNDED_AMOUNT = #{refundedAmount,jdbcType=DECIMAL},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      IS_TRADE_CUSTOMER_CODE = #{isTradeCustomerCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

    <select id="countByTransactionNoAndCustomer" >
    select count(*) from
      TXS_MULTI_ORDER_REFUND_AMOUNT
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>

    <update id="updateAddRefunding" >
    update
      TXS_MULTI_ORDER_REFUND_AMOUNT
    set
      REFUNDING_AMOUNT = REFUNDING_AMOUNT + #{amount,jdbcType=DECIMAL},
      UPDATE_TIME = sysdate
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      and TOTAL_AMOUNT> = (nvl(REFUNDING_AMOUNT,0)+nvl(REFUNDED_AMOUNT, 0)+#{amount,jdbcType=DECIMAL})
  </update>

    <select id="selectByTransactionNo" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from TXS_MULTI_ORDER_REFUND_AMOUNT
        where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    </select>

    <update id="updateRefundIngToDoneForSuccess" >
    update
      TXS_MULTI_ORDER_REFUND_AMOUNT
    set
      REFUNDING_AMOUNT = REFUNDING_AMOUNT - #{amount,jdbcType=DECIMAL},
      REFUNDED_AMOUNT = REFUNDED_AMOUNT + #{amount,jdbcType=DECIMAL},
      UPDATE_TIME = sysdate
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      and NVL(REFUNDING_AMOUNT , 0) - #{amount,jdbcType=DECIMAL} >= 0
  </update>

    <update id="updateReduceRefunding" >
    update
      TXS_MULTI_ORDER_REFUND_AMOUNT
    set
      REFUNDING_AMOUNT = REFUNDING_AMOUNT - #{amount,jdbcType=DECIMAL},
      UPDATE_TIME = sysdate
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      and NVL(REFUNDING_AMOUNT , 0) - #{amount,jdbcType=DECIMAL} >= 0
  </update>

</mapper>