<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.AuthInfoMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.cust.model.AuthInfo" >
    <id column="AUTH_ID" property="authId" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="AUTH_CUSTOMER_CODE" property="authCustomerCode" jdbcType="VARCHAR" />
    <result column="AUTH_TYPE" property="authType" jdbcType="VARCHAR" />
    <result column="URI" property="uri" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    AUTH_ID, CUSTOMER_CODE, AUTH_CUSTOMER_CODE, AUTH_TYPE, URI, STATUS, CREATE_TIME, 
    UPDATE_TIME, USER_ID, REMARKS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from CUST_AUTH_INFO
    where AUTH_ID = #{authId,jdbcType=DECIMAL}
  </select>
  <select id="selectByCustCodeAndType" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from CUST_AUTH_INFO
    where CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
    and AUTH_CUSTOMER_CODE = #{authCustomerCode,jdbcType=VARCHAR} 
    and AUTH_TYPE = #{authType,jdbcType=VARCHAR}
    <if test="uri != null" >
        and URI = #{uri,jdbcType=VARCHAR}
    </if>
    and STATUS = 1
  </select>
  <select id="countByCustCodeAndType" resultType="java.lang.Integer" >
    select 
    count(1)
    from CUST_AUTH_INFO
    where CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
    and AUTH_CUSTOMER_CODE = #{authCustomerCode,jdbcType=VARCHAR} 
    and AUTH_TYPE = #{authType,jdbcType=VARCHAR}
    <if test="uri != null" >
        and URI = #{uri,jdbcType=VARCHAR}
    </if>
    and STATUS = 1
  </select>

  <select id="selectAuthCustomerCode" resultType="java.lang.String" >
    select
      CUSTOMER_CODE
    from CUST_AUTH_INFO
    where
      AUTH_CUSTOMER_CODE = #{authCustomerCode,jdbcType=VARCHAR}
      and AUTH_TYPE = #{authType,jdbcType=VARCHAR}
      and STATUS = 1
  </select>
  
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from CUST_AUTH_INFO
    where AUTH_ID = #{authId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.cust.model.AuthInfo" >
    insert into CUST_AUTH_INFO (AUTH_ID, CUSTOMER_CODE, AUTH_CUSTOMER_CODE, 
      AUTH_TYPE, URI, STATUS, 
      CREATE_TIME, UPDATE_TIME, USER_ID, 
      REMARKS)
    values (#{authId,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{authCustomerCode,jdbcType=VARCHAR}, 
      #{authType,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=DECIMAL}, 
      #{remarks,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.cust.model.AuthInfo" >
    insert into CUST_AUTH_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="authId != null" >
        AUTH_ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="authCustomerCode != null" >
        AUTH_CUSTOMER_CODE,
      </if>
      <if test="authType != null" >
        AUTH_TYPE,
      </if>
      <if test="uri != null" >
        URI,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="remarks != null" >
        REMARKS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="authId != null" >
        #{authId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="authCustomerCode != null" >
        #{authCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="authType != null" >
        #{authType,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.cust.model.AuthInfo" >
    update CUST_AUTH_INFO
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="authCustomerCode != null" >
        AUTH_CUSTOMER_CODE = #{authCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="authType != null" >
        AUTH_TYPE = #{authType,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where AUTH_ID = #{authId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.cust.model.AuthInfo" >
    update CUST_AUTH_INFO
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      AUTH_CUSTOMER_CODE = #{authCustomerCode,jdbcType=VARCHAR},
      AUTH_TYPE = #{authType,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      USER_ID = #{userId,jdbcType=DECIMAL},
      REMARKS = #{remarks,jdbcType=VARCHAR}
    where AUTH_ID = #{authId,jdbcType=DECIMAL}
  </update>
</mapper>