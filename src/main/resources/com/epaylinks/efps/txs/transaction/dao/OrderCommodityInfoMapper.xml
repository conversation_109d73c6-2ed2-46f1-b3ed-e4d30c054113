<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.OrderCommodityInfoMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
    <result column="COMMODITY_ORDER_NO" property="commodityOrderNo" jdbcType="VARCHAR" />
    <result column="COMMODITY_ORDER_AMOUNT" property="commodityOrderAmount" jdbcType="DECIMAL" />
    <result column="COMMODITY_ORDER_TIME" property="commodityOrderTime" jdbcType="VARCHAR" />
    <result column="COMMODITY_DESCRIPTION" property="commodityDescription" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, OUT_TRADE_NO, COMMODITY_ORDER_NO, COMMODITY_ORDER_AMOUNT, COMMODITY_ORDER_TIME, 
    COMMODITY_DESCRIPTION
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TXS_ORDER_COMMODITY_INFO
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TXS_ORDER_COMMODITY_INFO
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo" >
    insert into TXS_ORDER_COMMODITY_INFO (ID, CUSTOMER_CODE, OUT_TRADE_NO, 
      COMMODITY_ORDER_NO, COMMODITY_ORDER_AMOUNT, 
      COMMODITY_ORDER_TIME, COMMODITY_DESCRIPTION
      )
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{outTradeNo,jdbcType=VARCHAR}, 
      #{commodityOrderNo,jdbcType=VARCHAR}, #{commodityOrderAmount,jdbcType=DECIMAL}, 
      #{commodityOrderTime,jdbcType=VARCHAR}, #{commodityDescription,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo" >
    insert into TXS_ORDER_COMMODITY_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO,
      </if>
      <if test="commodityOrderNo != null" >
        COMMODITY_ORDER_NO,
      </if>
      <if test="commodityOrderAmount != null" >
        COMMODITY_ORDER_AMOUNT,
      </if>
      <if test="commodityOrderTime != null" >
        COMMODITY_ORDER_TIME,
      </if>
      <if test="commodityDescription != null" >
        COMMODITY_DESCRIPTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="commodityOrderNo != null" >
        #{commodityOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="commodityOrderAmount != null" >
        #{commodityOrderAmount,jdbcType=DECIMAL},
      </if>
      <if test="commodityOrderTime != null" >
        #{commodityOrderTime,jdbcType=VARCHAR},
      </if>
      <if test="commodityDescription != null" >
        #{commodityDescription,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo" >
    update TXS_ORDER_COMMODITY_INFO
    <set >
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null" >
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="commodityOrderNo != null" >
        COMMODITY_ORDER_NO = #{commodityOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="commodityOrderAmount != null" >
        COMMODITY_ORDER_AMOUNT = #{commodityOrderAmount,jdbcType=DECIMAL},
      </if>
      <if test="commodityOrderTime != null" >
        COMMODITY_ORDER_TIME = #{commodityOrderTime,jdbcType=VARCHAR},
      </if>
      <if test="commodityDescription != null" >
        COMMODITY_DESCRIPTION = #{commodityDescription,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo" >
    update TXS_ORDER_COMMODITY_INFO
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      COMMODITY_ORDER_NO = #{commodityOrderNo,jdbcType=VARCHAR},
      COMMODITY_ORDER_AMOUNT = #{commodityOrderAmount,jdbcType=DECIMAL},
      COMMODITY_ORDER_TIME = #{commodityOrderTime,jdbcType=VARCHAR},
      COMMODITY_DESCRIPTION = #{commodityDescription,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByCustomerCodeAndCommodityTradeNo" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from TXS_ORDER_COMMODITY_INFO t
    where CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    AND  COMMODITY_ORDER_NO = #{commodityOrderNo,jdbcType=VARCHAR}
    and exists (
	select *  from txs_split_order sp where sp.out_trade_no=t.out_trade_no and sp.customer_code=t.customer_code and sp.state in('03','00')
	)
  </select>
</mapper>