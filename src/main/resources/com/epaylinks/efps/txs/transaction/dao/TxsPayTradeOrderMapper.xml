<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsPayTradeOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
    <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="COMMISSIONED_CUSTOMER_CODE" jdbcType="VARCHAR" property="commissionedCustomerCode" />
    <result column="PAY_METHOD" jdbcType="VARCHAR" property="payMethod" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="CURRENCY_TYPE" jdbcType="CHAR" property="currencyType" />
   	<result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="PAYER_TYPE" jdbcType="CHAR" property="payerType" />
    <result column="PAYER_ID" jdbcType="VARCHAR" property="payerId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="STATE" jdbcType="CHAR" property="state" />
    <result column="REPAY_STATE" jdbcType="CHAR" property="repayState" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType" />
    
    <result column="PAYER" jdbcType="VARCHAR" property="payer" />
    <result column="TERMINAL_NO" jdbcType="VARCHAR" property="terminalNo" />
    <result column="CLIENT_IP" jdbcType="VARCHAR" property="clientIp" />
    <result column="CHANNEL_ORDER" jdbcType="VARCHAR" property="channelOrder" />
    <result column="SRC_CHANNEL_TYPE" jdbcType="VARCHAR" property="srcChannelType" />
    <result column="ORDER_INFO" jdbcType="VARCHAR" property="orderInfo" />    
    <result column="NOTIFY_URL" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="REDIRECT_URL" jdbcType="VARCHAR" property="redirectUrl" />
    <result column="ATTACH_DATA" jdbcType="VARCHAR" property="attachData" />
    <result column="TRANSACTION_START_TIME" jdbcType="TIMESTAMP" property="transactionStartTime" />
    <result column="TRANSACTION_END_TIME" jdbcType="TIMESTAMP" property="transactionEndTime" />   
    <result column="SETTLEMENT_STATE" jdbcType="VARCHAR" property="settlementState" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="SETT_CYCLE_START_TIME" jdbcType="TIMESTAMP" property="settCycleStartTime" />
    <result column="SETT_CYCLE_END_TIME" jdbcType="TIMESTAMP" property="settCycleEndTime" />    
    <result column="WITHDRAW_TRANSACTION_NO" jdbcType="VARCHAR" property="withdrawTransactionNo" /> 
    <result column="AGENT_CUSTOMERCODE" jdbcType="VARCHAR" property="agentCustomerCode" />              
    <result column="PAY_PASSWAY" jdbcType="VARCHAR" property="payPassWay" />     
    <result column="AGENT_CUSTOMERNAME" jdbcType="VARCHAR" property="agentCustomerName" />         
    <result column="REDIRECT_FAILURL" jdbcType="VARCHAR" property="redirectFailUrl" />         
    <result column="QUICKPAY_CARDNO" jdbcType="VARCHAR" property="quickpayCardno" />  
    <result column="NO_CREDITCARDS" jdbcType="VARCHAR" property="noCreditCards" />
    <result column="ACQ_ORG_CODE" property="acqOrgCode" jdbcType="VARCHAR" />
    <result column="ACQ_SP_ID" property="acqSpId" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="TERM_INFO" property="termInfo" jdbcType="VARCHAR" />
    <result column="AREA_INFO" property="areaInfo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <result column="SUB_APP_ID" property="subAppId" jdbcType="VARCHAR" />
    <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR" />
    <result column="PLATFORM_CUSTOMER_CODE" property="platformCustomerCode" jdbcType="VARCHAR" />
    <result column="STORE_ID" property="storeId" jdbcType="VARCHAR" />
    <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR" />
    <result column="REQ_RESERVED" property="reqReserved" jdbcType="VARCHAR" />
    <result column="CUPS_REQ_RESERVED" property="cupsReqReserved" jdbcType="VARCHAR" />
    <result column="PAYER_APP_NAME" property="payerAppName" jdbcType="VARCHAR" />
    <result column="ACTUAL_PAY_AMOUNT" property="actualPayAmount" jdbcType="DECIMAL" />
    <result column="DISCOUNTABLE_AMOUNT" property="discountableAmount" jdbcType="DECIMAL" />
    <result column="SETTLEMENT_AMOUNT" property="settlementAmount" jdbcType="DECIMAL" />
    <result column="SOURCE_CHANNEL" property="sourceChannel" jdbcType="VARCHAR" /> 
    <result column="QR_CODE_ISSUER" property="qrCodeIssuer" jdbcType="VARCHAR" /> 
    <result column="PROCEDURE_RATE" property="procedureRate" jdbcType="VARCHAR" />
    <result column="RATE_MODE" property="rateMode" jdbcType="DECIMAL" />
    <result column="CUSTOMERNAME" property="customername" jdbcType="VARCHAR" />
    <result column="CARD_NO_MOSAIC" property="cardNoMosaic" jdbcType="VARCHAR" />
    <result column="CARD_NO_ENC" property="cardNoEnc" jdbcType="VARCHAR" />
    <result column="CARD_TYPE" property="cardType" jdbcType="VARCHAR" />  
    
   	    <result column="CANCEL_STATE" property="cancelState" jdbcType="VARCHAR" />
   	    <result column="CANCLE_NO" property="cancelNo" jdbcType="VARCHAR" />
   	    <result column="CANCEL_RETURN_MSG" property="cancelReturnMsg" jdbcType="VARCHAR" />
   	    <result column="CANCEL_UPDATE_TIME" property="cancelUpdateTime" jdbcType="TIMESTAMP" />
   	    
   	<result column="CARD_OWNER" property="cardOwner" jdbcType="VARCHAR" />
    <result column="CARD_OWNER_MOSAIC" property="cardOwnerMosaic" jdbcType="VARCHAR" />
    <result column="CERT_NO" property="certNo" jdbcType="VARCHAR" />
    <result column="CERT_NO_MOSAIC" property="certNoMosaic" jdbcType="VARCHAR" />
    <result column="CARD_OWNER_HASH" property="cardOwnerHash" jdbcType="VARCHAR" />
    <result column="CERT_NO_HASH" property="certNoHash" jdbcType="VARCHAR" />
    <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />  
    <result column="CHANNEL_RESP_CODE" property="channelRespCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_RESP_MSG" property="channelRespMsg" jdbcType="VARCHAR" />
    <result column="TERMINAL_NAME" jdbcType="VARCHAR" property="terminalName" />
    <result column="MEMBER_ID" jdbcType="VARCHAR" property="memberId" />
    <result column="SPLIT_MODEL" jdbcType="VARCHAR" property="splitModel" />
    <result column="SUB_CUSTOMER_CODE" jdbcType="VARCHAR" property="subCustomerCode" />
    <result column="SPLIT_PROCEDURE_FEE" jdbcType="DECIMAL" property="splitProcedureFee" />
    <result column="SPLIT_PROCEDURE_RATE" jdbcType="VARCHAR" property="splitProcedureRate"/>
	<result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
	<result column="BUYER_LOGON_ID" property="buyerLogonId" jdbcType="VARCHAR" />      
	<result column="BUYER_USER_ID" property="buyerUserId" jdbcType="VARCHAR" /> 
	<result column="API_VERSION" property="apiVersion" jdbcType="VARCHAR" />   
	<result column="sett_cycle_rule_code" property="settCycleRuleCode" jdbcType="VARCHAR" />
    <result column="SCENE" property="scene" jdbcType="VARCHAR" />
    <result column="auth_customer_code" property="authCustomerCode" jdbcType="VARCHAR" />
    <result column="trade_source" property="tradeSource" jdbcType="VARCHAR" />
    <result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR" />
    <result column="BUSINESS_MAN_ID" property="businessManId" jdbcType="DECIMAL" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
    <result column="max_fee" jdbcType="DECIMAL" property="maxFee" />
    <result column="min_fee" jdbcType="DECIMAL" property="minFee" />
    <result column="NFC_TAG_ID" property="nfcTagId" jdbcType="VARCHAR" />
    <result column="FEE_PER" jdbcType="DECIMAL" property="feePer" />
	<result column="D1_HOLIDAY_CHARGE_RATE" jdbcType="VARCHAR" property="D1HolidayChargeRate" />
	<result column="D1_HOLIDAY_CHARGE_PER" jdbcType="DECIMAL" property="D1HolidayChargePer" />
	<result column="D1_HOIDAY_CHARGED" jdbcType="VARCHAR" property="D1HolidayCharged" />    
	<result column="CHARGED_BY_BANK_CODE_AREA" jdbcType="VARCHAR" property="chargedByBankCodeArea" />   	
	<result column="fund_channel" jdbcType="VARCHAR" property="fundChannel" />
	<result column="cash_amount" property="cashAmount" jdbcType="DECIMAL" />
	<result column="coupon_amount" property="couponAmount" jdbcType="DECIMAL" />

	<result column="industry_code_type" property="industryCodeType" jdbcType="VARCHAR" />
	<result column="industry_name" property="industryName" jdbcType="VARCHAR" />
	<result column="machine_code" property="machineCode" jdbcType="VARCHAR" />

	    <result column="BUSINESS_CODE_FST" property="businessCodeFst" jdbcType="VARCHAR" />
	    <result column="BUSINESS_INST_ID_FST" property="businessInstIdFst" jdbcType="VARCHAR" />
	    <result column="SECOND_CHARGED" property="secondCharged" jdbcType="VARCHAR" />		
	    <result column="terminal_type" property="terminalType" jdbcType="VARCHAR" />
	    <result column="is_sett_with_cash_amount" property="isSettWithCashAmount" jdbcType="VARCHAR" />
    <result column="server_ip" property="serverIp" jdbcType="VARCHAR" />
    <result column="PROCEDURE_CUSTOMERCODE" property="procedureCustomercode" jdbcType="VARCHAR" />
    <result column="CONTROLLED" property="controlled" jdbcType="VARCHAR" />


  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDER_ID, TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE, PAY_METHOD, AMOUNT, CURRENCY_TYPE, 
    PROCEDURE_FEE, PAYER_TYPE, PAYER_ID, CREATE_TIME, STATE, REPAY_STATE, UPDATE_TIME, 
    ERROR_CODE, END_TIME, REMARK, TRANSACTION_TYPE, PAYER, TERMINAL_NO, CLIENT_IP, SRC_CHANNEL_TYPE, 
    ORDER_INFO, NOTIFY_URL, REDIRECT_URL, ATTACH_DATA, TRANSACTION_START_TIME, TRANSACTION_END_TIME, 
    SETTLEMENT_STATE, BUSINESS_INST_ID, BUSINESS_CODE, SETT_CYCLE_START_TIME, SETT_CYCLE_END_TIME, 
    WITHDRAW_TRANSACTION_NO, AGENT_CUSTOMERCODE, PAY_PASSWAY, AGENT_CUSTOMERNAME, REDIRECT_FAILURL, 
    QUICKPAY_CARDNO, NO_CREDITCARDS, COMMISSIONED_CUSTOMER_CODE, CHANNEL_ORDER, ACQ_ORG_CODE, 
    ACQ_SP_ID, ORDER_TYPE, TERM_INFO, AREA_INFO, USER_ID, SUB_APP_ID, ERROR_MSG, PLATFORM_CUSTOMER_CODE, 
    STORE_ID, OPERATOR_ID, REQ_RESERVED, CUPS_REQ_RESERVED, PAYER_APP_NAME, ACTUAL_PAY_AMOUNT, 
    DISCOUNTABLE_AMOUNT, SETTLEMENT_AMOUNT, SOURCE_CHANNEL, QR_CODE_ISSUER, PROCEDURE_RATE, 
    RATE_MODE, CUSTOMERNAME, CARD_NO_MOSAIC, CARD_NO_ENC, CARD_TYPE, CANCEL_STATE, CANCLE_NO, 
    CANCEL_RETURN_MSG, CANCEL_UPDATE_TIME, CARD_OWNER, CARD_OWNER_MOSAIC, CERT_NO, CERT_NO_MOSAIC, 
    CARD_OWNER_HASH, CERT_NO_HASH,BANK_CODE, 
    CHANNEL_RESP_CODE, CHANNEL_RESP_MSG,TERMINAL_NAME,MEMBER_ID,SPLIT_MODEL,SUB_CUSTOMER_CODE,trade_customer_code,
    SPLIT_PROCEDURE_FEE,SPLIT_PROCEDURE_RATE,
    OPEN_ID, BUYER_LOGON_ID, BUYER_USER_ID, API_VERSION,sett_cycle_rule_code,SCENE,auth_customer_code,trade_source,BUSINESS_MAN,BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,
    max_fee,min_fee, NFC_TAG_ID, FEE_PER, 
    D1_HOLIDAY_CHARGE_RATE, D1_HOLIDAY_CHARGE_PER, D1_HOIDAY_CHARGED, CHARGED_BY_BANK_CODE_AREA,fund_channel,cash_amount,coupon_amount,
    industry_code_type,industry_name,machine_code,

    BUSINESS_CODE_FST, BUSINESS_INST_ID_FST, SECOND_CHARGED,terminal_type,is_sett_with_cash_amount,server_ip,
    PROCEDURE_CUSTOMERCODE, CONTROLLED
  </sql>
  
  <select id="selectByMap" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_PAY_TRADE_ORDER
		where 1 = 1
		<if test="outTradeNo != null and outTradeNo !=''">
			and OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
		</if> 
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_PAY_TRADE_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_PAY_TRADE_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder">
    insert into TXS_PAY_TRADE_ORDER (ID, ORDER_ID, TRANSACTION_NO, 
    OUT_TRADE_NO, CUSTOMER_CODE,COMMISSIONED_CUSTOMER_CODE, PAY_METHOD, 
    AMOUNT, CURRENCY_TYPE, PROCEDURE_FEE, 
    PAYER_TYPE, PAYER_ID, CREATE_TIME, 
    STATE, REPAY_STATE, UPDATE_TIME, 
    ERROR_CODE, END_TIME, REMARK, TRANSACTION_TYPE,
    PAYER,TERMINAL_NO,CLIENT_IP,SRC_CHANNEL_TYPE,CHANNEL_ORDER, ORDER_INFO,NOTIFY_URL,
    REDIRECT_URL,ATTACH_DATA,TRANSACTION_START_TIME,TRANSACTION_END_TIME,SETTLEMENT_STATE,BUSINESS_INST_ID,
    BUSINESS_CODE,SETT_CYCLE_START_TIME,SETT_CYCLE_END_TIME, WITHDRAW_TRANSACTION_NO, AGENT_CUSTOMERCODE,
    PAY_PASSWAY, AGENT_CUSTOMERNAME, REDIRECT_FAILURL, QUICKPAY_CARDNO, NO_CREDITCARDS, ACQ_ORG_CODE, ACQ_SP_ID, ORDER_TYPE, 
      TERM_INFO, AREA_INFO, USER_ID, 
      SUB_APP_ID, ERROR_MSG, PLATFORM_CUSTOMER_CODE, 
      STORE_ID, OPERATOR_ID, REQ_RESERVED, 
      CUPS_REQ_RESERVED, PAYER_APP_NAME, ACTUAL_PAY_AMOUNT, 
      DISCOUNTABLE_AMOUNT, SETTLEMENT_AMOUNT, 
      SOURCE_CHANNEL,QR_CODE_ISSUER, PROCEDURE_RATE, 
      RATE_MODE, CUSTOMERNAME, CARD_NO_MOSAIC, CARD_NO_ENC, CARD_TYPE,
      CANCEL_STATE, CANCLE_NO, CANCEL_RETURN_MSG, CANCEL_UPDATE_TIME, 
      CARD_OWNER, CARD_OWNER_MOSAIC, CERT_NO, 
      CERT_NO_MOSAIC, CARD_OWNER_HASH, CERT_NO_HASH,BANK_CODE, 
      CHANNEL_RESP_CODE, CHANNEL_RESP_MSG,TERMINAL_NAME,MEMBER_ID,SPLIT_MODEL,SUB_CUSTOMER_CODE,trade_customer_code,
      SPLIT_PROCEDURE_FEE,SPLIT_PROCEDURE_RATE, 
      OPEN_ID, BUYER_LOGON_ID, BUYER_USER_ID, API_VERSION, sett_cycle_rule_code,SCENE,auth_customer_code,trade_source,BUSINESS_MAN,
      BUSINESS_MAN_ID,COMPANY_NAME,COMPANY_ID,max_fee,min_fee, NFC_TAG_ID, FEE_PER, 
      D1_HOLIDAY_CHARGE_RATE, D1_HOLIDAY_CHARGE_PER, D1_HOIDAY_CHARGED, CHARGED_BY_BANK_CODE_AREA,fund_channel,cash_amount,coupon_amount,
      industry_code_type,industry_name,machine_code,
      BUSINESS_CODE_FST, BUSINESS_INST_ID_FST, SECOND_CHARGED,terminal_type,is_sett_with_cash_amount,server_ip,
      PROCEDURE_CUSTOMERCODE)
    values (#{id,jdbcType=DECIMAL}, #{orderId,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, 
    #{outTradeNo,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR},#{commissionedCustomerCode,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, 
    #{amount,jdbcType=DECIMAL}, #{currencyType,jdbcType=CHAR}, #{procedureFee,jdbcType=DECIMAL}, 
    #{payerType,jdbcType=CHAR}, #{payerId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
    #{state,jdbcType=CHAR}, #{repayState,jdbcType=CHAR}, 
    #{updateTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR}, 
    #{endTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{transactionType,jdbcType=VARCHAR},
    #{payer,jdbcType=VARCHAR},#{terminalNo,jdbcType=VARCHAR},#{clientIp,jdbcType=VARCHAR},
    #{srcChannelType,jdbcType=VARCHAR},#{channelOrder,jdbcType=VARCHAR},#{orderInfo,jdbcType=VARCHAR},#{notifyUrl,jdbcType=VARCHAR},
    #{redirectUrl,jdbcType=VARCHAR},#{attachData,jdbcType=VARCHAR},#{transactionStartTime,jdbcType=TIMESTAMP},
    #{transactionEndTime,jdbcType=TIMESTAMP},#{settlementState,jdbcType=VARCHAR},#{businessInstId,jdbcType=VARCHAR},
    #{businessCode,jdbcType=VARCHAR},#{settCycleStartTime,jdbcType=TIMESTAMP},#{settCycleEndTime,jdbcType=TIMESTAMP},
    #{withdrawTransactionNo,jdbcType=VARCHAR},#{agentCustomerCode,jdbcType=VARCHAR},#{payPassWay,jdbcType=VARCHAR},
    #{agentCustomerName,jdbcType=VARCHAR}, #{redirectFailUrl,jdbcType=VARCHAR}, #{quickpayCardno,jdbcType=VARCHAR},
    #{noCreditCards,jdbcType=VARCHAR},#{acqOrgCode,jdbcType=VARCHAR}, #{acqSpId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{termInfo,jdbcType=VARCHAR}, #{areaInfo,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{subAppId,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR}, #{platformCustomerCode,jdbcType=VARCHAR}, 
      #{storeId,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, #{reqReserved,jdbcType=VARCHAR}, 
      #{cupsReqReserved,jdbcType=VARCHAR}, #{payerAppName,jdbcType=VARCHAR}, #{actualPayAmount,jdbcType=DECIMAL}, 
      #{discountableAmount,jdbcType=DECIMAL}, #{settlementAmount,jdbcType=DECIMAL},
      #{sourceChannel,jdbcType=VARCHAR},#{qrCodeIssuer,jdbcType=VARCHAR}, #{procedureRate,jdbcType=VARCHAR}, 
      #{rateMode,jdbcType=DECIMAL}, #{customername,jdbcType=VARCHAR}, 
      #{cardNoMosaic,jdbcType=VARCHAR}, #{cardNoEnc,jdbcType=VARCHAR}, #{cardType,jdbcType=VARCHAR},
      #{cancelState,jdbcType=VARCHAR},#{cancelNo,jdbcType=VARCHAR},#{cancelReturnMsg,jdbcType=VARCHAR},#{cancelUpdateTime,jdbcType=TIMESTAMP}, 
      #{cardOwner,jdbcType=VARCHAR}, #{cardOwnerMosaic,jdbcType=VARCHAR}, #{certNo,jdbcType=VARCHAR}, 
      #{certNoMosaic,jdbcType=VARCHAR}, #{cardOwnerHash,jdbcType=VARCHAR}, #{certNoHash,jdbcType=VARCHAR},
      #{bankCode,jdbcType=VARCHAR}, 
      #{channelRespCode,jdbcType=VARCHAR}, #{channelRespMsg,jdbcType=VARCHAR},
      #{terminalName,jdbcType=VARCHAR},#{memberId,jdbcType=VARCHAR},#{splitModel,jdbcType=VARCHAR},#{subCustomerCode,jdbcType=VARCHAR},
      #{tradeCustomerCode,jdbcType=VARCHAR},#{splitProcedureFee,jdbcType=DECIMAL},#{splitProcedureRate,jdbcType=VARCHAR},
      #{openId,jdbcType=VARCHAR}, #{buyerLogonId,jdbcType=VARCHAR},  #{buyerUserId,jdbcType=VARCHAR}, #{apiVersion,jdbcType=VARCHAR},
      #{settCycleRuleCode,jdbcType=VARCHAR},#{scene,jdbcType=VARCHAR},#{authCustomerCode,jdbcType=VARCHAR},#{tradeSource,jdbcType=VARCHAR},#{businessMan,jdbcType=VARCHAR},#{businessManId,jdbcType=DECIMAL},#{companyName,jdbcType=VARCHAR},#{companyId,jdbcType=DECIMAL},
      #{maxFee,jdbcType=DECIMAL},#{minFee,jdbcType=DECIMAL}, #{nfcTagId,jdbcType=VARCHAR}, #{feePer,jdbcType=DECIMAL}, 
      #{D1HolidayChargeRate,jdbcType=VARCHAR},  #{D1HolidayChargePer,jdbcType=DECIMAL}, #{D1HolidayCharged,jdbcType=VARCHAR}, 
      #{chargedByBankCodeArea,jdbcType=VARCHAR},#{fundChannel,jdbcType=VARCHAR},#{cashAmount,jdbcType=DECIMAL},#{couponAmount,jdbcType=DECIMAL},
      #{industryCodeType,jdbcType=VARCHAR},#{industryName,jdbcType=VARCHAR},#{machineCode,jdbcType=VARCHAR},
      #{businessCodeFst,jdbcType=VARCHAR}, #{businessInstIdFst,jdbcType=VARCHAR}, #{secondCharged,jdbcType=VARCHAR},
      #{terminalType,jdbcType=VARCHAR},#{isSettWithCashAmount,jdbcType=VARCHAR},#{serverIp,jdbcType=VARCHAR},
      #{procedureCustomercode,jdbcType=VARCHAR})

  </insert>
  
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder">
    insert into TXS_PAY_TRADE_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="commissionedCustomerCode != null">
        COMMISSIONED_CUSTOMER_CODE,
      </if>
      <if test="payMethod != null">
        PAY_METHOD,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="currencyType != null">
        CURRENCY_TYPE,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="payerType != null">
        PAYER_TYPE,
      </if>
      <if test="payerId != null">
        PAYER_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="repayState != null">
        REPAY_STATE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE,
      </if>
      <if test="payer != null">
        PAYER,
      </if>
      <if test="terminalNo != null">
        TERMINAL_NO,
      </if>
      <if test="clientIp != null">
        CLIENT_IP,
      </if>
      <if test="srcChannelType != null">
        SRC_CHANNEL_TYPE,
      </if>
      <if test="channelOrder != null">
        CHANNEL_ORDER,
      </if>
      <if test="orderInfo != null">
        ORDER_INFO,
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL,
      </if>
      <if test="redirectUrl != null">
        REDIRECT_URL,
      </if>
      <if test="attachData != null">
        ATTACH_DATA,
      </if>
      <if test="transactionStartTime != null">
        TRANSACTION_START_TIME,
      </if>
      <if test="transactionEndTime != null">
        TRANSACTION_END_TIME,
      </if>
      <if test="settlementState != null">
        SETTLEMENT_STATE,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="settCycleStartTime != null">
        SETT_CYCLE_START_TIME,
      </if>
      <if test="settCycleEndTime != null">
        SETT_CYCLE_END_TIME,
      </if>
      <if test="withdrawTransactionNo != null">
        WITHDRAW_TRANSACTION_NO,
      </if>
      <if test="agentCustomerCode != null">
        AGENT_CUSTOMERCODE,
      </if>
      <if test="payPassWay != null">
        PAY_PASSWAY,
      </if>
      <if test="agentCustomerName != null">
        AGENT_CUSTOMERNAME,
      </if>
      <if test="redirectFailUrl != null">
       	REDIRECT_FAILURL,
      </if>
      <if test="quickpayCardno != null">
       	QUICKPAY_CARDNO,
      </if>
      <if test="noCreditCards != null">
       	NO_CREDITCARDS,
      </if>
      <if test="acqOrgCode != null" >
        ACQ_ORG_CODE,
      </if>
      <if test="acqSpId != null" >
        ACQ_SP_ID,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="termInfo != null" >
        TERM_INFO,
      </if>
      <if test="areaInfo != null" >
        AREA_INFO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="subAppId != null" >
        SUB_APP_ID,
      </if>
      <if test="errorMsg != null" >
        ERROR_MSG,
      </if>
      <if test="platformCustomerCode != null" >
        PLATFORM_CUSTOMER_CODE,
      </if>
      <if test="storeId != null" >
        STORE_ID,
      </if>
      <if test="operatorId != null" >
        OPERATOR_ID,
      </if>
      <if test="reqReserved != null" >
        REQ_RESERVED,
      </if>
      <if test="cupsReqReserved != null" >
        CUPS_REQ_RESERVED,
      </if>
      <if test="payerAppName != null" >
        PAYER_APP_NAME,
      </if>
      <if test="actualPayAmount != null" >
        ACTUAL_PAY_AMOUNT,
      </if>
      <if test="discountableAmount != null" >
        DISCOUNTABLE_AMOUNT,
      </if>
      <if test="settlementAmount != null" >
        SETTLEMENT_AMOUNT,
      </if>
      <if test="sourceChannel != null" >
        SOURCE_CHANNEL,
      </if>
      <if test="qrCodeIssuer != null" >
        QR_CODE_ISSUER,
      </if>
      <if test="procedureRate != null" >
        PROCEDURE_RATE,
      </if>
      <if test="rateMode != null" >
        RATE_MODE,
      </if>
      <if test="customername != null" >
        CUSTOMERNAME,
      </if>
      <if test="cardNoMosaic != null" >
        CARD_NO_MOSAIC,
      </if>  
      <if test="cardNoEnc != null" >
        CARD_NO_ENC,
      </if>  
      <if test="cardType != null" >
        CARD_TYPE,
      </if>     
      <if test="cancelState != null" >
        CANCEL_STATE,
      </if> 
      <if test="cancelNo != null" >
        CANCLE_NO,
      </if> 
      <if test="cancelReturnMsg != null" >
        CANCEL_RETURN_MSG,
      </if> 
      <if test="cancelUpdateTime != null" >
        CANCEL_UPDATE_TIME,
      </if>  
      <if test="cardOwner != null" >
        CARD_OWNER,
      </if>
      <if test="cardOwnerMosaic != null" >
        CARD_OWNER_MOSAIC,
      </if>
      <if test="certNo != null" >
        CERT_NO,
      </if>
      <if test="certNoMosaic != null" >
        CERT_NO_MOSAIC,
      </if>
      <if test="cardOwnerHash != null" >
        CARD_OWNER_HASH,
      </if>
      <if test="certNoHash != null" >
        CERT_NO_HASH,
      </if>
      <if test="bankCode != null" >
        BANK_CODE,
      </if>   
      <if test="channelRespCode != null" >
        CHANNEL_RESP_CODE,
      </if>
      <if test="channelRespMsg != null" >
        CHANNEL_RESP_MSG,
      </if>
      <if test="terminalName != null">
      	TERMINAL_NAME,
      </if>
      <if test="memberId != null">
        MEMBER_ID,
      </if>
      <if test="splitModel != null">
        SPLIT_MODEL,
      </if>
      <if test="subCustomerCode != null">
        SUB_CUSTOMER_CODE,
      </if>
      <if test="tradeCustomerCode != null">
        trade_customer_code,
      </if>
      <if test="splitProcedureFee != null">
        SPLIT_PROCEDURE_FEE,
      </if>
      <if test="splitProcedureRate != null">
        SPLIT_PROCEDURE_RATE,
      </if>
      <if test="openId != null" >
        OPEN_ID,
      </if>  
      <if test="buyerLogonId != null" >
        BUYER_LOGON_ID,
      </if>  
      <if test="buyerUserId != null" >
        BUYER_USER_ID,
      </if>   
      <if test="apiVersion != null" >
        API_VERSION,
      </if> 
      <if test="settCycleRuleCode != null" >
	        sett_cycle_rule_code,
	  </if>
      <if test="scene != null" >
        SCENE,
      </if>
      <if test="authCustomerCode != null" >
        auth_customer_code,
      </if>
      <if test="tradeSource != null" >
        trade_source,
      </if>
      <if test="businessMan != null" >
            BUSINESS_MAN,
      </if>
      <if test="businessManId != null" >
            BUSINESS_MAN_ID,
      </if>
      <if test="companyName != null" >
            COMPANY_NAME,
      </if>
      <if test="companyId != null" >
            COMPANY_ID,
      </if>
       <if test="maxFee != null" >
            max_fee,
      </if>
      <if test="minFee != null" >
            min_fee,
      </if>
      <if test="nfcTagId != null" >
            NFC_TAG_ID,
      </if>
      <if test="feePer != null" >
        FEE_PER,
      </if>
		<if test="D1HolidayChargeRate != null" >
			D1_HOLIDAY_CHARGE_RATE,
		</if>
		<if test="D1HolidayChargePer != null" >
			D1_HOLIDAY_CHARGE_PER,
		</if>
		<if test="D1HolidayCharged != null" >
			D1_HOIDAY_CHARGED,
		</if>   
		<if test="chargedByBankCodeArea != null" >
			CHARGED_BY_BANK_CODE_AREA,
		</if>  	
		<if test="fundChannel != null" >
			fund_channel,
		</if>	
		<if test="cashAmount != null" >
        cash_amount,
      </if> 
      <if test="couponAmount != null" >
        coupon_amount,
      </if>  
      <if test="industryCodeType != null" >
        industry_code_type,
      </if>
      <if test="industryName != null" >
        industry_name,
      </if>
      <if test="machineCode != null" >
        machine_code,
      </if>	   
      <if test="businessCodeFst != null" >
        BUSINESS_CODE_FST,
      </if> 
      <if test="businessInstIdFst != null" >
        BUSINESS_INST_ID_FST,
      </if> 
      <if test="secondCharged != null" >
        SECOND_CHARGED,
      </if>   
      <if test="terminalType != null" >
        terminal_type,
      </if>     
       <if test="isSettWithCashAmount != null" >
	       is_sett_with_cash_amount,
	    </if>
      <if test="serverIp != null">
        server_ip,
      </if>
      <if test="procedureCustomercode != null">
        PROCEDURE_CUSTOMERCODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="commissionedCustomerCode != null">
        #{commissionedCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null">
        #{currencyType,jdbcType=CHAR},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="payerType != null">
        #{payerType,jdbcType=CHAR},
      </if>
      <if test="payerId != null">
        #{payerId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        #{state,jdbcType=CHAR},
      </if>
      <if test="repayState != null">
        #{repayState,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=VARCHAR},
      </if>   
      <if test="payer != null">
        #{payer,jdbcType=VARCHAR},
      </if>
      <if test="terminalNo != null">
        #{terminalNo,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="srcChannelType != null">
        #{srcChannelType,jdbcType=VARCHAR},
      </if>
      <if test="channelOrder != null">
         #{channelOrder,jdbcType=VARCHAR},
      </if>
      <if test="orderInfo != null">
        #{orderInfo,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="attachData != null">
        #{attachData,jdbcType=VARCHAR},
      </if>
      <if test="transactionStartTime != null">
        #{transactionStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transactionEndTime != null">
        #{transactionEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementState != null">
        #{settlementState,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="settCycleStartTime != null">
        #{settCycleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settCycleEndTime != null">
        #{settCycleEndTime,jdbcType=TIMESTAMP} 
      </if>
      <if test="withdrawTransactionNo != null">
        #{withdrawTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerCode != null">
        #{agentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="payPassWay != null">
        #{payPassWay,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerName != null">
        #{agentCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="redirectFailUrl != null">
		#{redirectFailUrl,jdbcType=VARCHAR},
	  </if>
      <if test="quickpayCardno != null">
		#{quickpayCardno,jdbcType=VARCHAR},
	  </if>
      <if test="noCreditCards != null">
		#{noCreditCards,jdbcType=VARCHAR},
	  </if>
	  <if test="acqOrgCode != null" >
        #{acqOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="acqSpId != null" >
        #{acqSpId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="termInfo != null" >
        #{termInfo,jdbcType=VARCHAR},
      </if>
      <if test="areaInfo != null" >
        #{areaInfo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="subAppId != null" >
        #{subAppId,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null" >
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="platformCustomerCode != null" >
        #{platformCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null" >
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="reqReserved != null" >
        #{reqReserved,jdbcType=VARCHAR},
      </if>
      <if test="cupsReqReserved != null" >
        #{cupsReqReserved,jdbcType=VARCHAR},
      </if>
      <if test="payerAppName != null" >
        #{payerAppName,jdbcType=VARCHAR},
      </if>
      <if test="actualPayAmount != null" >
        #{actualPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountableAmount != null" >
        #{discountableAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null" >
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="sourceChannel != null" >
        #{sourceChannel,jdbcType=VARCHAR},
      </if>
       <if test="qrCodeIssuer != null" >
        #{qrCodeIssuer,jdbcType=VARCHAR},
      </if>
      <if test="procedureRate != null" >
        #{procedureRate,jdbcType=VARCHAR},
      </if>
      <if test="rateMode != null" >
        #{rateMode,jdbcType=DECIMAL},
      </if>
      <if test="customername != null" >
        #{customername,jdbcType=VARCHAR},
      </if>
	     <if test="cardNoMosaic != null" >
	      	#{cardNoMosaic,jdbcType=VARCHAR},
	    </if>
	    <if test="cardNoEnc != null" >
	      	#{cardNoEnc,jdbcType=VARCHAR},
	    </if>
	    <if test="cardType != null" >
	      	#{cardType,jdbcType=VARCHAR},
	    </if>   
			<if test="cancelState != null" >
	       		#{cancelState,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelNo != null" >
	       		#{cancelNo,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelReturnMsg != null" >
	       		#{cancelReturnMsg,jdbcType=VARCHAR},
	      	</if> 
		    <if test="cancelUpdateTime != null" >
	       		#{cancelUpdateTime,jdbcType=TIMESTAMP},
	      	</if>
	        <if test="cardOwner != null" >
	          #{cardOwner,jdbcType=VARCHAR},
	        </if>
	        <if test="cardOwnerMosaic != null" >
	          #{cardOwnerMosaic,jdbcType=VARCHAR},
	        </if>
	        <if test="certNo != null" >
	          #{certNo,jdbcType=VARCHAR},
	        </if>
	        <if test="certNoMosaic != null" >
	          #{certNoMosaic,jdbcType=VARCHAR},
	        </if>
	        <if test="cardOwnerHash != null" >
	          #{cardOwnerHash,jdbcType=VARCHAR},
	        </if>
	      <if test="certNoHash != null" >
	        #{certNoHash,jdbcType=VARCHAR},
	      </if>
	      <if test="bankCode != null" >
	          #{bankCode,jdbcType=VARCHAR},
	        </if> 	
	      <if test="channelRespCode != null" >
	          #{channelRespCode,jdbcType=VARCHAR},
	        </if>
	      <if test="channelRespMsg != null" >
	          #{channelRespMsg,jdbcType=VARCHAR},
	       </if>
	       <if test="terminalName != null">
	      	#{terminalName,jdbcType=VARCHAR},
	   	   </if>
      <if test="memberId != null">
        #{memberId,jdbcType=VARCHAR},
      </if>
      <if test="splitModel != null">
        #{splitModel,jdbcType=VARCHAR},
      </if>
      <if test="subCustomerCode != null">
        #{subCustomerCode,jdbcType=VARCHAR},
      </if>
       <if test="tradeCustomerCode != null">
        #{tradeCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="splitProcedureFee != null">
        #{splitProcedureFee,jdbcType=DECIMAL},
      </if>
      <if test="splitProcedureRate != null">
        #{splitProcedureRate,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        #{openId,jdbcType=VARCHAR},
      </if>  
      <if test="buyerLogonId != null" >
        #{buyerLogonId,jdbcType=VARCHAR},
      </if>   
      <if test="buyerUserId != null" >
        #{buyerUserId,jdbcType=VARCHAR},
      </if>   
      <if test="apiVersion != null" >
        #{apiVersion,jdbcType=VARCHAR},
      </if>   
      <if test="settCycleRuleCode != null" >
	        #{settCycleRuleCode,jdbcType=VARCHAR},
	  </if>
      <if test="scene != null" >
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="authCustomerCode != null" >
        #{authCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeSource != null" >
        #{tradeSource,jdbcType=VARCHAR},
      </if>
        <if test="businessMan != null" >
            #{businessMan,jdbcType=VARCHAR},
        </if>
        <if test="businessManId != null" >
            #{businessManId,jdbcType=DECIMAL},
        </if>
        <if test="companyName != null" >
            #{companyName,jdbcType=VARCHAR},
        </if>
        <if test="companyId != null" >
            #{companyId,jdbcType=DECIMAL},
        </if>
         <if test="maxFee != null" >
            #{maxFee,jdbcType=DECIMAL},
      </if>
      <if test="minFee != null" >
            #{minFee,jdbcType=DECIMAL},
      </if>
      <if test="nfcTagId != null" >
            #{nfcTagId,jdbcType=VARCHAR},
      </if>
      <if test="feePer != null">
        #{feePer,jdbcType=DECIMAL},
      </if>
		<if test="D1HolidayChargeRate != null">
			#{D1HolidayChargeRate,jdbcType=VARCHAR},
		</if>
		<if test="D1HolidayChargePer != null">
			#{D1HolidayChargePer,jdbcType=DECIMAL},
		</if>
		<if test="D1HolidayCharged != null">
			#{D1HolidayCharged,jdbcType=VARCHAR},
		</if> 
		<if test="chargedByBankCodeArea != null">
			#{chargedByBankCodeArea,jdbcType=VARCHAR},
		</if> 	
		<if test="fundChannel != null" >
			#{fundChannel,jdbcType=VARCHAR},
		</if>	
		<if test="cashAmount != null" >
        #{cashAmount,jdbcType=DECIMAL},
      </if> 
      <if test="couponAmount != null" >
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="industryCodeType != null" >
	        #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        #{machineCode,jdbcType=VARCHAR},
	      </if>	   	   
			<if test="businessCodeFst != null" >
				#{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				#{businessInstIdFst,jdbcType=VARCHAR},
			</if>
			<if test="secondCharged != null" >
				#{secondCharged,jdbcType=VARCHAR},
			</if>	
			<if test="terminalType != null" >
	        #{terminalType,jdbcType=VARCHAR},
	      </if>   
	      <if test="isSettWithCashAmount != null" >
	        #{isSettWithCashAmount,jdbcType=VARCHAR},
	      </if>
        <if test="serverIp != null">
          #{serverIp,jdbcType=VARCHAR},
        </if>
      <if test="procedureCustomercode != null">
        #{procedureCustomercode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder">
    update TXS_PAY_TRADE_ORDER
    <set>
   	  <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="commissionedCustomerCode != null">
        COMMISSIONED_CUSTOMER_CODE =  #{commissionedCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null">
        CURRENCY_TYPE = #{currencyType,jdbcType=CHAR},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="payerType != null">
        PAYER_TYPE = #{payerType,jdbcType=CHAR},
      </if>
      <if test="payerId != null">
        PAYER_ID = #{payerId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="repayState != null">
        REPAY_STATE = #{repayState,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="payer != null">
        PAYER = #{payer,jdbcType=VARCHAR},
      </if>
      <if test="terminalNo != null">
        TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="srcChannelType != null">
        SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR},
      </if>
      <if test="channelOrder != null">
        CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR},
      </if>
      <if test="orderInfo != null">
        ORDER_INFO = #{orderInfo,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="attachData != null">
        ATTACH_DATA = #{attachData,jdbcType=VARCHAR},
      </if>
      <if test="transactionStartTime != null">
        TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transactionEndTime != null">
        TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementState != null">
        SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="settCycleStartTime != null">
        SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settCycleEndTime != null">
        SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP}, 
      </if>
      <if test="withdrawTransactionNo != null">
        WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerCode != null">
        AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="payPassWay != null">
        PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerName != null">
        AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="redirectFailUrl != null">
		REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR},
	  </if>
      <if test="quickpayCardno != null">
		QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR},
	  </if>
      <if test="noCreditCards != null">
		NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR},
	  </if>
	  <if test="acqOrgCode != null" >
        ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="acqSpId != null" >
        ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="termInfo != null" >
        TERM_INFO = #{termInfo,jdbcType=VARCHAR},
      </if>
      <if test="areaInfo != null" >
        AREA_INFO = #{areaInfo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="subAppId != null" >
        SUB_APP_ID = #{subAppId,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null" >
        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="platformCustomerCode != null" >
        PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null" >
        STORE_ID = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null" >
        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="reqReserved != null" >
        REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR},
      </if>
      <if test="cupsReqReserved != null" >
        CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR},
      </if>
      <if test="payerAppName != null" >
        PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR},
      </if>
      <if test="actualPayAmount != null" >
        ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountableAmount != null" >
        DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null" >
        SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="sourceChannel != null" >
        SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
      </if>
       <if test="qrCodeIssuer != null" >
        QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR},
      </if>
        <if test="procedureRate != null" >
        PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR},
      </if>
      <if test="rateMode != null" >
        RATE_MODE = #{rateMode,jdbcType=DECIMAL},
      </if>
      <if test="customername != null" >
        CUSTOMERNAME = #{customername,jdbcType=VARCHAR},
      </if>
     <if test="cardNoMosaic != null" >
      	CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR},
    </if>
    <if test="cardNoEnc != null" >
      	CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR},
    </if>
    <if test="cardType != null" >
      	CARD_TYPE = #{cardType,jdbcType=VARCHAR},
    </if> 
	    <if test="cancelState != null" >
       		CANCEL_STATE = #{cancelState,jdbcType=VARCHAR},
      	</if> 
	    <if test="cancelNo != null" >
       		CANCLE_NO = #{cancelNo,jdbcType=VARCHAR},
      	</if> 
	    <if test="cancelReturnMsg != null" >
       		CANCEL_RETURN_MSG = #{cancelReturnMsg,jdbcType=VARCHAR},
      	</if> 
	    <if test="cancelUpdateTime != null" >
       		CANCEL_UPDATE_TIME = #{cancelUpdateTime,jdbcType=TIMESTAMP},
      	</if>
      	<if test="cardOwner != null" >
        CARD_OWNER = #{cardOwner,jdbcType=VARCHAR},
      </if>
      <if test="cardOwnerMosaic != null" >
        CARD_OWNER_MOSAIC = #{cardOwnerMosaic,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null" >
        CERT_NO = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certNoMosaic != null" >
        CERT_NO_MOSAIC = #{certNoMosaic,jdbcType=VARCHAR},
      </if>
      <if test="cardOwnerHash != null" >
        CARD_OWNER_HASH = #{cardOwnerHash,jdbcType=VARCHAR},
      </if>
      <if test="certNoHash != null" >
        CERT_NO_HASH = #{certNoHash,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>        
      <if test="channelRespCode != null" >
        CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR},
      </if> 
      <if test="channelRespMsg != null" >
        CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR},
      </if>
      <if test="terminalName != null">
      	TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
   	   </if>
      <if test="memberId != null">
        MEMBER_ID = #{memberId,jdbcType=VARCHAR},
      </if>
      <if test="splitModel != null">
       SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR},
      </if>
      <if test="subCustomerCode != null">
       SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR},
      </if>
       <if test="tradeCustomerCode != null">
        trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="splitProcedureFee != null">
        SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL},
      </if>
      <if test="splitProcedureRate != null">
        SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if> 
      <if test="buyerLogonId != null" >
        BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR},
      </if>  
      <if test="buyerUserId != null" >
        BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR},
      </if>  
      <if test="apiVersion != null" >
        API_VERSION = #{apiVersion,jdbcType=VARCHAR},
      </if> 
      <if test="settCycleRuleCode != null" >
	     sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR},
	  </if>
      <if test="scene != null" >
        SCENE = #{scene,jdbcType=VARCHAR},
      </if>
       <if test="authCustomerCode != null" >
        auth_customer_code = #{authCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeSource != null" >
        trade_source = #{tradeSource,jdbcType=VARCHAR},
      </if>
        <if test="businessMan != null" >
            BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
        </if>
        <if test="businessManId != null" >
            BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL},
        </if>
        <if test="companyName != null" >
            COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
        </if>
        <if test="companyId != null" >
            COMPANY_ID = #{companyId,jdbcType=DECIMAL},
        </if>
        <if test="maxFee != null" >
            max_fee = #{maxFee,jdbcType=DECIMAL},
      </if>
      <if test="minFee != null" >
            min_fee = #{minFee,jdbcType=DECIMAL},
      </if>
      <if test="nfcTagId != null">
        NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR},
      </if>
      <if test="feePer != null">
        FEE_PER = #{feePer,jdbcType=DECIMAL},
      </if>
		<if test="D1HolidayChargeRate != null">
			D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR},
		</if>
		<if test="D1HolidayChargePer != null">
			D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL},
		</if>
		<if test="D1HolidayCharged != null">
			D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR},
		</if>	     
		<if test="chargedByBankCodeArea != null">
			CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR},
		</if>	
		<if test="fundChannel != null" >
			fund_channel = #{fundChannel,jdbcType=VARCHAR},
		</if>		
	  <if test="cashAmount != null" >
        cash_amount = #{cashAmount,jdbcType=DECIMAL},
      </if> 
      <if test="couponAmount != null" >
        coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      </if> 

      <if test="industryCodeType != null" >
	        industry_code_type = #{industryCodeType,jdbcType=VARCHAR},
	      </if>
	      <if test="industryName != null" >
	        industry_name = #{industryName,jdbcType=VARCHAR},
	      </if>
	      <if test="machineCode != null" >
	        machine_code = #{machineCode,jdbcType=VARCHAR},
	      </if>

			<if test="businessCodeFst != null" >
				BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR},
			</if>
			<if test="businessInstIdFst != null" >
				BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR},
			</if>
			<if test="secondCharged != null" >
				SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR},
			</if>	
			<if test="terminalType != null" >
	        terminal_type = #{terminalType,jdbcType=VARCHAR},
	      </if> 
	      <if test="isSettWithCashAmount != null" >
	       is_sett_with_cash_amount = #{isSettWithCashAmount,jdbcType=VARCHAR},
	    </if>
      <if test="serverIp != null">
        server_ip = #{serverIp,jdbcType=VARCHAR},
      </if>
      <if test="procedureCustomercode != null">
        PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR},
      </if>

    </set>
    where ID = #{id,jdbcType=DECIMAL} 
  </update>
  
  
  <select id="selectBySelective" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from TXS_PAY_TRADE_ORDER
		<where>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR} 
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="state != null and state !=''">
				AND STATE = #{state,jdbcType=CHAR} 
			</if>
			<if test="beginTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null ">
   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
			</if> 	
		    <if test="openId != null" >
		      and OPEN_ID = #{openId,jdbcType=VARCHAR}
		    </if>  
		    <if test="buyerLogonId != null" >
		      and BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR}
		    </if>

		</where>
	</select>
	
	<select id="selectByPage" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
				select A.*, rownum RN
				from ( 
				select * from TXS_PAY_TRADE_ORDER
					<where>
						<if test="transactionType != null and transactionType !=''">
							AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}  
						</if>
						<if test="transactionNo != null and transactionNo !=''">
							AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}  
						</if>
						<if test="payMethod != null and payMethod !=''">
							AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}  
						</if>
						<if test="outTradeNo != null and outTradeNo !=''">
							AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}  
						</if>
						<if test="customerCode != null and customerCode !=''">
							AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
						</if>
						<if test="state != null and state !=''">
							AND STATE = #{state,jdbcType=CHAR} 
						</if>
						<if test="beginTime!=null">
			   			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
						</if>
						<if test="endTime!=null">
			   			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP} 
						</if> 			 
					</where>
		 			order by CREATE_TIME desc 
					) A
				where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
		
	</select>
	
	
	<select id="selectByNotPage" resultMap="BaseResultMap"
	parameterType="java.util.Map">
	select
	<include refid="Base_Column_List" />
	from TXS_PAY_TRADE_ORDER
		<where>
            <if test="transactionType != null and transactionType !=''">
                AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
            </if>
			<if test="transactionNo != null and transactionNo !=''">
				AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</if>
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
			</if>
			<if test="outTradeNo != null and outTradeNo !=''">
				AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
			</if>
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
			</if>
			<if test="state != null and state !=''">
				AND STATE = #{state,jdbcType=CHAR}
			</if>
			<if test="beginTime!=null">
				AND CREATE_TIME <![CDATA[ >= ]]> #{beginTime,jdbcType=TIMESTAMP}
			</if>
			<if test="endTime!=null">
				AND CREATE_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>
	
	<select id="selectByOutTradeNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_PAY_TRADE_ORDER
	    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
    </select>
    
    <select id="selectByTransactionNo" resultMap="BaseResultMap" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_PAY_TRADE_ORDER
	    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    </select>
    
    <select id="selectByTransactionNoAndCustomerCode" resultMap="BaseResultMap" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_PAY_TRADE_ORDER
	    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
	    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    </select>
    
    <select id="selectCustomerCode" resultType="String" parameterType="java.util.Map">
	    select distinct	CUSTOMER_CODE
	    from TXS_PAY_TRADE_ORDER
	    <where>
	    	TRANSACTION_TYPE != #{transactionType,jdbcType=VARCHAR}
			<if test="beginTime!=null ">
	   		 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
	   			AND CREATE_TIME <![CDATA[ < ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if> 			 
		</where>
    </select>
    
    <select id="selectTxsTradePayOrderByCycleTime" resultMap="BaseResultMap" parameterType="java.util.Map">
	  	select 
		<include refid="Base_Column_List" />
		from TXS_PAY_TRADE_ORDER 
		<where>
			<if test="beginTime!=null ">
	   		 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
	   			AND CREATE_TIME <![CDATA[ < ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if> 			 
		</where>
    </select>	
    
    <select id="selectPayMethod" resultType="String" parameterType="java.util.Map">
	    select distinct	PAY_METHOD
	    from TXS_PAY_TRADE_ORDER
	    <where>
	    	TRANSACTION_TYPE != #{transactionType,jdbcType=VARCHAR}
	    	<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="beginTime!=null ">
	   		 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
	   			AND CREATE_TIME <![CDATA[ < ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if> 	
			<if test="state != null and state !=''">
				AND STATE = #{state,jdbcType=VARCHAR} 
			</if>
		</where>
   </select>
   
   <select id="selectTxsTradePayOrder" resultMap="BaseResultMap" parameterType="java.util.Map">
	  	select 
		<include refid="Base_Column_List" />
		from TXS_PAY_TRADE_ORDER 
		<where>
			TRANSACTION_TYPE != #{transactionType,jdbcType=VARCHAR}
			<if test="beginTime!=null ">
	   		 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
	   			AND CREATE_TIME <![CDATA[ < ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if> 	
			<if test="customerCode != null and customerCode !=''">
				AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
			</if>
			<if test="payMethod != null and payMethod !=''">
				AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR} 
			</if>
			<if test="payMethod == null or payMethod ==''">
				AND PAY_METHOD is null 
			</if>			 
		</where>
		order by CREATE_TIME
    </select>	
    
    <select id="selectByOutTradeNoToResultQuery" resultMap="BaseResultMap">
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_PAY_TRADE_ORDER
	    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
	    and STATE = #{state, jdbcType = CHAR} and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
	    order by CREATE_TIME desc
    </select>
    
    <select id="selectByOutTradeAndCustCode" resultMap="BaseResultMap">
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_PAY_TRADE_ORDER
	    where OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR} 
	    and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
	    order by CREATE_TIME desc
    </select>
    
    <select id="selectByCondition" parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder" 
    resultMap="BaseResultMap">
    	select 
    	<include refid="Base_Column_List" />
	    from TXS_PAY_TRADE_ORDER 
	    where 1=1 
	    <if test="id != null">
			AND ID = #{id,jdbcType=DECIMAL}
		</if>
		<if test="orderId != null">
			AND ORDER_ID = #{orderId,jdbcType=DECIMAL}
		</if>
		<if test="transactionNo != null">
			AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
		</if>
		<if test="outTradeNo != null">
			AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
		</if>
		<if test="customerCode != null">
			AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
		</if>
		<if test="payMethod != null">
			AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
		</if>
		<if test="amount != null">
			AND AMOUNT = #{amount,jdbcType=DECIMAL}
		</if>
		<if test="currencyType != null">
			AND CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR}
		</if>
		<if test="procedureFee != null">
			AND PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL}
		</if>
		<if test="procedureFee != null">
			AND PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL}
		</if>
		<if test="payerType != null">
			AND PAYER_TYPE = #{payerType,jdbcType=VARCHAR}
		</if>
		<if test="payerId != null">
			AND PAYER_ID = #{payerId,jdbcType=VARCHAR}
		</if>
		<if test="createTime != null">
			AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="state != null">
			AND STATE = #{state,jdbcType=VARCHAR}
		</if>
		<if test="repayState != null">
			AND REPAY_STATE = #{repayState,jdbcType=VARCHAR}
		</if>
		<if test="updateTime != null">
			AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="errorCode != null">
			AND ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
		</if>
		<if test="endTime != null">
			AND END_TIME = #{endTime,jdbcType=TIMESTAMP}
		</if>
		<if test="remark != null">
			AND REMARK = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="transactionType != null">
			AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
		</if>
		<if test="payer != null">
			AND PAYER = #{payer,jdbcType=VARCHAR}
		</if>
		<if test="terminalNo != null">
			AND TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR}
		</if>
		<if test="clientIp != null">
			AND CLIENT_IP = #{clientIp,jdbcType=VARCHAR}
		</if>
		<if test="srcChannelType != null">
			AND SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR}
		</if>
		<if test="orderInfo != null">
			AND ORDER_INFO = #{orderInfo,jdbcType=VARCHAR}
		</if>
		<if test="notifyUrl != null">
			AND NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR}
		</if>
		<if test="redirectUrl != null">
			AND REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR}
		</if>
		<if test="attachData != null">
			AND ATTACH_DATA = #{attachData,jdbcType=VARCHAR}
		</if>
		<if test="transactionStartTime != null">
			AND TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP}
		</if>
		<if test="transactionEndTime != null">
			AND TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP}
		</if>
		<if test="settlementState != null">
			AND SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR}
		</if>
		<if test="businessInstId != null">
			AND BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
		</if>
		<if test="businessCode != null">
			AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
		</if>
		<if test="settCycleStartTime != null">
			AND SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP}
		</if>
		<if test="settCycleEndTime != null">
			AND SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP}
		</if>
		<if test="withdrawTransactionNo != null">
			AND WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR}
		</if>
		<if test="agentCustomerCode != null">
			AND AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR}
		</if>
		<if test="agentCustomerName != null">
			AND AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR}
		</if>
		<if test="redirectFailUrl != null">
			AND REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR}
		</if>
      <if test="procedureCustomercode != null">
        AND PROCEDURE_CUSTOMERCODE = #{procedureCustomercode,jdbcType=VARCHAR}
      </if>
    </select>
    
    <select id="selectByOrderId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_PAY_TRADE_ORDER
    where ORDER_ID = #{orderId,jdbcType=DECIMAL}
  </select>

	<update id="updateByPrimaryKeyForRepeatUpdate"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder">
		update TXS_PAY_TRADE_ORDER
		<set>
			<if test="state != null">
				STATE = #{state,jdbcType=VARCHAR},
			</if>
		</set>
		where STATE != #{state,jdbcType=VARCHAR}
		<choose>
			<when test="id != null">
				and ID = #{id,jdbcType=DECIMAL}
			</when>
			<otherwise>
				and TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</otherwise>
		</choose>
	</update>
	
	<update id="updateByPrimaryKeyForCancelInfo"
		parameterType="com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder">
		update TXS_PAY_TRADE_ORDER
		<set>
			<if test="cancelUpdateTime != null">
				CANCEL_UPDATE_TIME = #{cancelUpdateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="cancelReturnMsg != null">
				CANCEL_RETURN_MSG = #{cancelReturnMsg,jdbcType=VARCHAR},
			</if>
			<if test="cancelNo != null">
				CANCLE_NO = #{cancelNo,jdbcType=VARCHAR},
			</if>
			<if test="cancelState != null">
				CANCEL_STATE = #{cancelState,jdbcType=VARCHAR},
			</if>
		</set>
		where (CANCEL_STATE is null or CANCEL_STATE != '00')
		<choose>
			<when test="id != null">
				and ID = #{id,jdbcType=DECIMAL}
			</when>
			<otherwise>
				and TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
			</otherwise>
		</choose>
	</update>
	
	<select id="selectByTransactionNos" resultMap="BaseResultMap">
		select 
		<include refid="Base_Column_List" />
    	from TXS_PAY_TRADE_ORDER 
    	where TRANSACTION_NO in 
    	<foreach collection="transactionNos" separator="," open="(" close=")" item="transactionNo">
    		#{transactionNo , jdbcType = VARCHAR}
    	</foreach>
	</select>

  <select id="selectByOutTradeNoAndCustomerCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TXS_PAY_TRADE_ORDER
    WHERE CUSTOMER_CODE = #{customerCode,jdbcType = VARCHAR}
    AND OUT_TRADE_NO = #{outTradeNo,jdbcType = VARCHAR}
    AND STATE = #{state,jdbcType = VARCHAR}
  </select>

  <select id="selectAuditList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from TXS_PAY_TRADE_ORDER
    WHERE create_time &gt;= #{startTime}
    AND create_time  &lt;= #{endTime}
    and state = '03'
    and  transaction_type != 'ZHFZ'
    and  transaction_type != 'SF'
    and pay_method != '59'
    and pay_method != '60'
  </select>
  
   <select id="selectByChannelOrder" resultType="java.lang.Integer">
		select
		count(*)
		from TXS_PAY_TRADE_ORDER
		where CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR}
	</select>
	
    <select id="selectNfcCustomerCode" resultType="String" parameterType="java.util.Map">
	    select distinct	CUSTOMER_CODE
	    from TXS_PAY_TRADE_ORDER
	    <where>
	    	PAY_METHOD = '52'
	    	AND CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR}
			<if test="beginTime!=null ">
	   		 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
	   			AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if> 			 
		</where>
    </select>	
    
    <select id="selectNfcStaticsCode" resultType="java.util.Map" parameterType="java.util.Map">
	    select count(*) as COUNT, sum(amount) as SUM
	    from TXS_PAY_TRADE_ORDER
	    <where>
	    	PAY_METHOD = '52'
  	  <if test="orderId != null">
        AND ORDER_ID = #{orderId,jdbcType=DECIMAL}
      </if>
      <if test="transactionNo != null">
        AND   TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      </if>
      <if test="outTradeNo != null">
         AND  OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
      </if>
      <if test="customerCode != null">
        AND   CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      </if>
      <if test="commissionedCustomerCode != null">
         AND  COMMISSIONED_CUSTOMER_CODE =  #{commissionedCustomerCode,jdbcType=VARCHAR}
      </if>
      <if test="payMethod != null">
         AND  PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
      </if>
      <if test="amount != null">
         AND  AMOUNT = #{amount,jdbcType=DECIMAL}
      </if>
      <if test="currencyType != null">
          AND CURRENCY_TYPE = #{currencyType,jdbcType=CHAR}
      </if>
      <if test="procedureFee != null">
          AND PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL}
      </if>
      <if test="payerType != null">
          AND PAYER_TYPE = #{payerType,jdbcType=CHAR}
      </if>
      <if test="payerId != null">
          AND PAYER_ID = #{payerId,jdbcType=VARCHAR}
      </if>
      <if test="state != null">
          AND STATE = #{state,jdbcType=CHAR}
      </if>
      <if test="repayState != null">
          AND REPAY_STATE = #{repayState,jdbcType=CHAR}
      </if>
      <if test="updateTime != null">
          AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="errorCode != null">
          AND ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
      </if>
      <if test="remark != null">
          AND REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="transactionType != null">
          AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
      </if>
      <if test="payer != null">
          AND PAYER = #{payer,jdbcType=VARCHAR}
      </if>
      <if test="terminalNo != null">
         AND  TERMINAL_NO = #{terminalNo,jdbcType=VARCHAR}
      </if>
      <if test="clientIp != null">
          AND CLIENT_IP = #{clientIp,jdbcType=VARCHAR}
      </if>
      <if test="srcChannelType != null">
          AND SRC_CHANNEL_TYPE = #{srcChannelType,jdbcType=VARCHAR}
      </if>
      <if test="channelOrder != null">
          AND CHANNEL_ORDER = #{channelOrder,jdbcType=VARCHAR}
      </if>
      <if test="orderInfo != null">
          AND ORDER_INFO = #{orderInfo,jdbcType=VARCHAR}
      </if>
      <if test="notifyUrl != null">
          AND NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR}
      </if>
      <if test="redirectUrl != null">
          AND REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR}
      </if>
      <if test="attachData != null">
          AND ATTACH_DATA = #{attachData,jdbcType=VARCHAR}
      </if>
      <if test="transactionStartTime != null">
          AND TRANSACTION_START_TIME = #{transactionStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test="transactionEndTime != null">
          AND TRANSACTION_END_TIME = #{transactionEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="settlementState != null">
          AND SETTLEMENT_STATE = #{settlementState,jdbcType=VARCHAR}
      </if>
      <if test="businessInstId != null">
          AND BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR}
      </if>
      <if test="businessCode != null">
          AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      </if>
      <if test="settCycleStartTime != null">
          AND SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test="settCycleEndTime != null">
          AND SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="withdrawTransactionNo != null">
          AND WITHDRAW_TRANSACTION_NO = #{withdrawTransactionNo,jdbcType=VARCHAR}
      </if>
      <if test="agentCustomerCode != null">
          AND AGENT_CUSTOMERCODE = #{agentCustomerCode,jdbcType=VARCHAR}
      </if>
      <if test="payPassWay != null">
         AND PAY_PASSWAY = #{payPassWay,jdbcType=VARCHAR}
      </if>
      <if test="agentCustomerName != null">
         AND AGENT_CUSTOMERNAME = #{agentCustomerName,jdbcType=VARCHAR}
      </if>
      <if test="redirectFailUrl != null">
		 AND REDIRECT_FAILURL = #{redirectFailUrl,jdbcType=VARCHAR}
	  </if>
      <if test="quickpayCardno != null">
		 AND QUICKPAY_CARDNO = #{quickpayCardno,jdbcType=VARCHAR}
	  </if>
      <if test="noCreditCards != null">
		 AND NO_CREDITCARDS = #{noCreditCards,jdbcType=VARCHAR}
	  </if>
	  <if test="acqOrgCode != null" >
         AND ACQ_ORG_CODE = #{acqOrgCode,jdbcType=VARCHAR}
      </if>
      <if test="acqSpId != null" >
         AND ACQ_SP_ID = #{acqSpId,jdbcType=VARCHAR}
      </if>
      <if test="orderType != null" >
         AND ORDER_TYPE = #{orderType,jdbcType=VARCHAR}
      </if>
      <if test="termInfo != null" >
         AND TERM_INFO = #{termInfo,jdbcType=VARCHAR}
      </if>
      <if test="areaInfo != null" >
         AND AREA_INFO = #{areaInfo,jdbcType=VARCHAR}
      </if>
      <if test="userId != null" >
         AND USER_ID = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="subAppId != null" >
         AND SUB_APP_ID = #{subAppId,jdbcType=VARCHAR}
      </if>
      <if test="errorMsg != null" >
         AND ERROR_MSG = #{errorMsg,jdbcType=VARCHAR}
      </if>
      <if test="platformCustomerCode != null" >
         AND PLATFORM_CUSTOMER_CODE = #{platformCustomerCode,jdbcType=VARCHAR}
      </if>
      <if test="storeId != null" >
         AND STORE_ID = #{storeId,jdbcType=VARCHAR}
      </if>
      <if test="operatorId != null" >
         AND OPERATOR_ID = #{operatorId,jdbcType=VARCHAR}
      </if>
      <if test="reqReserved != null" >
         AND REQ_RESERVED = #{reqReserved,jdbcType=VARCHAR}
      </if>
      <if test="cupsReqReserved != null" >
         AND CUPS_REQ_RESERVED = #{cupsReqReserved,jdbcType=VARCHAR}
      </if>
      <if test="payerAppName != null" >
         AND PAYER_APP_NAME = #{payerAppName,jdbcType=VARCHAR}
      </if>
      <if test="actualPayAmount != null" >
         AND ACTUAL_PAY_AMOUNT = #{actualPayAmount,jdbcType=DECIMAL}
      </if>
      <if test="discountableAmount != null" >
         AND DISCOUNTABLE_AMOUNT = #{discountableAmount,jdbcType=DECIMAL}
      </if>
      <if test="settlementAmount != null" >
         AND SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL}
      </if>
      <if test="sourceChannel != null" >
         AND SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR}
      </if>
       <if test="qrCodeIssuer != null" >
         AND QR_CODE_ISSUER = #{qrCodeIssuer,jdbcType=VARCHAR}
      </if>
        <if test="procedureRate != null" >
         AND PROCEDURE_RATE = #{procedureRate,jdbcType=VARCHAR}
      </if>
      <if test="rateMode != null" >
         AND RATE_MODE = #{rateMode,jdbcType=DECIMAL}
      </if>
      <if test="customername != null" >
         AND CUSTOMERNAME = #{customername,jdbcType=VARCHAR}
      </if>
     <if test="cardNoMosaic != null" >
      	 AND CARD_NO_MOSAIC = #{cardNoMosaic,jdbcType=VARCHAR}
    </if>
    <if test="cardNoEnc != null" >
      	 AND CARD_NO_ENC = #{cardNoEnc,jdbcType=VARCHAR}
    </if>
    <if test="cardType != null" >
      	 AND CARD_TYPE = #{cardType,jdbcType=VARCHAR}
    </if> 
	    <if test="cancelState != null" >
       		 AND CANCEL_STATE = #{cancelState,jdbcType=VARCHAR}
      	</if> 
	    <if test="cancelNo != null" >
       		 AND CANCLE_NO = #{cancelNo,jdbcType=VARCHAR}
      	</if> 
	    <if test="cancelReturnMsg != null" >
       		 AND CANCEL_RETURN_MSG = #{cancelReturnMsg,jdbcType=VARCHAR}
      	</if> 
	    <if test="cancelUpdateTime != null" >
       		 AND CANCEL_UPDATE_TIME = #{cancelUpdateTime,jdbcType=TIMESTAMP}
      	</if>
      	<if test="cardOwner != null" >
         AND CARD_OWNER = #{cardOwner,jdbcType=VARCHAR}
      </if>
      <if test="cardOwnerMosaic != null" >
         AND CARD_OWNER_MOSAIC = #{cardOwnerMosaic,jdbcType=VARCHAR}
      </if>
      <if test="certNo != null" >
         AND CERT_NO = #{certNo,jdbcType=VARCHAR}
      </if>
      <if test="certNoMosaic != null" >
         AND CERT_NO_MOSAIC = #{certNoMosaic,jdbcType=VARCHAR}
      </if>
      <if test="cardOwnerHash != null" >
         AND CARD_OWNER_HASH = #{cardOwnerHash,jdbcType=VARCHAR}
      </if>
      <if test="certNoHash != null" >
         AND CERT_NO_HASH = #{certNoHash,jdbcType=VARCHAR}
      </if>
      <if test="bankCode != null" >
         AND BANK_CODE = #{bankCode,jdbcType=VARCHAR}
      </if>        
      <if test="channelRespCode != null" >
         AND CHANNEL_RESP_CODE = #{channelRespCode,jdbcType=VARCHAR}
      </if> 
      <if test="channelRespMsg != null" >
         AND CHANNEL_RESP_MSG = #{channelRespMsg,jdbcType=VARCHAR}
      </if>
      <if test="terminalName != null">
      	 AND TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR}
   	   </if>
      <if test="memberId != null">
         AND MEMBER_ID = #{memberId,jdbcType=VARCHAR}
      </if>
      <if test="splitModel != null">
        AND SPLIT_MODEL = #{splitModel,jdbcType=VARCHAR}
      </if>
      <if test="subCustomerCode != null">
        AND SUB_CUSTOMER_CODE = #{subCustomerCode,jdbcType=VARCHAR}
      </if>
       <if test="tradeCustomerCode != null">
         AND trade_customer_code = #{tradeCustomerCode,jdbcType=VARCHAR}
      </if>
      <if test="splitProcedureFee != null">
         AND SPLIT_PROCEDURE_FEE = #{splitProcedureFee,jdbcType=DECIMAL}
      </if>
      <if test="splitProcedureRate != null">
         AND SPLIT_PROCEDURE_RATE = #{splitProcedureRate,jdbcType=VARCHAR}
      </if>
      <if test="openId != null" >
         AND OPEN_ID = #{openId,jdbcType=VARCHAR}
      </if> 
      <if test="buyerLogonId != null" >
         AND BUYER_LOGON_ID = #{buyerLogonId,jdbcType=VARCHAR}
      </if>  
      <if test="buyerUserId != null" >
         AND BUYER_USER_ID = #{buyerUserId,jdbcType=VARCHAR}
      </if>  
      <if test="apiVersion != null" >
         AND API_VERSION = #{apiVersion,jdbcType=VARCHAR}
      </if> 
      <if test="settCycleRuleCode != null" >
	      AND sett_cycle_rule_code = #{settCycleRuleCode,jdbcType=VARCHAR}
	  </if>
      <if test="scene != null" >
         AND SCENE = #{scene,jdbcType=VARCHAR}
      </if>
       <if test="authCustomerCode != null" >
         AND auth_customer_code = #{authCustomerCode,jdbcType=VARCHAR}
      </if>
      <if test="tradeSource != null" >
         AND trade_source = #{tradeSource,jdbcType=VARCHAR}
      </if>
        <if test="businessMan != null" >
           AND BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR}
        </if>
        <if test="businessManId != null" >
             AND BUSINESS_MAN_ID = #{businessManId,jdbcType=DECIMAL}
        </if>
        <if test="companyName != null" >
             AND COMPANY_NAME = #{companyName,jdbcType=VARCHAR}
        </if>
        <if test="companyId != null" >
             AND COMPANY_ID = #{companyId,jdbcType=DECIMAL}
        </if>
        <if test="maxFee != null" >
             AND max_fee = #{maxFee,jdbcType=DECIMAL}
      </if>
      <if test="minFee != null" >
             AND min_fee = #{minFee,jdbcType=DECIMAL}
      </if>
      <if test="nfcTagId != null">
         AND NFC_TAG_ID = #{nfcTagId,jdbcType=VARCHAR}
      </if>
      <if test="feePer != null">
         AND FEE_PER = #{feePer,jdbcType=DECIMAL}
      </if>
		<if test="D1HolidayChargeRate != null">
			 AND D1_HOLIDAY_CHARGE_RATE = #{D1HolidayChargeRate,jdbcType=VARCHAR}
		</if>
		<if test="D1HolidayChargePer != null">
			 AND D1_HOLIDAY_CHARGE_PER = #{D1HolidayChargePer,jdbcType=DECIMAL}
		</if>
		<if test="D1HolidayCharged != null">
			 AND D1_HOIDAY_CHARGED = #{D1HolidayCharged,jdbcType=VARCHAR}
		</if>	     
		<if test="chargedByBankCodeArea != null">
			 AND CHARGED_BY_BANK_CODE_AREA = #{chargedByBankCodeArea,jdbcType=VARCHAR}
		</if>	
		<if test="fundChannel != null" >
			 AND fund_channel = #{fundChannel,jdbcType=VARCHAR}
		</if>		
	  <if test="cashAmount != null" >
         AND cash_amount = #{cashAmount,jdbcType=DECIMAL}
      </if> 
      <if test="couponAmount != null" >
         AND coupon_amount = #{couponAmount,jdbcType=DECIMAL}
      </if> 

      <if test="industryCodeType != null" >
	         AND industry_code_type = #{industryCodeType,jdbcType=VARCHAR}
	      </if>
	      <if test="industryName != null" >
	         AND industry_name = #{industryName,jdbcType=VARCHAR}
	      </if>
	      <if test="machineCode != null" >
	         AND machine_code = #{machineCode,jdbcType=VARCHAR}
	      </if>
			<if test="businessCodeFst != null" >
				 AND BUSINESS_CODE_FST = #{businessCodeFst,jdbcType=VARCHAR}
			</if>
			<if test="businessInstIdFst != null" >
				 AND BUSINESS_INST_ID_FST = #{businessInstIdFst,jdbcType=VARCHAR}
			</if>
			<if test="secondCharged != null" >
				 AND SECOND_CHARGED = #{secondCharged,jdbcType=VARCHAR}
			</if>	
			<if test="terminalType != null" >
	         AND terminal_type = #{terminalType,jdbcType=VARCHAR}
	      </if> 
	      <if test="isSettWithCashAmount != null" >
	        AND is_sett_with_cash_amount = #{isSettWithCashAmount,jdbcType=VARCHAR}
	    </if>        		
			<if test="beginTime!=null ">
	   		 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
			</if>
			<if test="endTime!=null ">
	   			AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP} 
			</if> 			 
		</where>
    </select>    

</mapper>