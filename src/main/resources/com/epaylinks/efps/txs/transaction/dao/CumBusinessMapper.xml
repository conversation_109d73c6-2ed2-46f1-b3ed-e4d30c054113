<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.CumBusinessMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.CumBusiness">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="TYPE" jdbcType="CHAR" property="type" />
    <result column="RATIO_MODE" jdbcType="CHAR" property="ratioMode" />
    <result column="RATIO" jdbcType="VARCHAR" property="ratio" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="STATE" jdbcType="CHAR" property="state" />
    <result column="BUSINESS_CATEGORY" property="businessCategory" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, NAME, CODE, TYPE, RATIO_MODE, RATIO, REMARK, STATE, BUSINESS_CATEGORY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Short" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CUM_BUSINESS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
    <select id="selectByBusinessCode" resultMap="BaseResultMap">
	    select 
	    <include refid="Base_Column_List" />
	    from CUM_BUSINESS
	    where CODE = #{businessCode,jdbcType=VARCHAR}
  	</select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short">
    delete from CUM_BUSINESS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.CumBusiness">
    insert into CUM_BUSINESS (ID, NAME, CODE, 
      TYPE, RATIO_MODE, RATIO, REMARK, 
      STATE, BUSINESS_CATEGORY)
    values (#{id,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{type,jdbcType=CHAR}, #{ratioMode,jdbcType=CHAR}, #{ratio,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{state,jdbcType=CHAR}, #{businessCategory,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.CumBusiness">
    insert into CUM_BUSINESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="code != null">
        CODE,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="ratioMode != null">
        RATIO_MODE,
      </if>
      <if test="ratio != null">
        RATIO,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        BUSINESS_CATEGORY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=CHAR},
      </if>
      <if test="ratioMode != null">
        #{ratioMode,jdbcType=CHAR},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=CHAR},
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        #{businessCategory,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.CumBusiness">
    update CUM_BUSINESS
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=CHAR},
      </if>
      <if test="ratioMode != null">
        RATIO_MODE = #{ratioMode,jdbcType=CHAR},
      </if>
      <if test="ratio != null">
        RATIO = #{ratio,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        BUSINESS_CATEGORY = #{businessCategory,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.CumBusiness">
    update CUM_BUSINESS
    set NAME = #{name,jdbcType=VARCHAR},
      CODE = #{code,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=CHAR},
      RATIO_MODE = #{ratioMode,jdbcType=CHAR},
      RATIO = #{ratio,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=CHAR},
      BUSINESS_CATEGORY = #{businessCategory,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <delete id="deleteAll">
  	delete from CUM_BUSINESS
  </delete>
  
  <select id="selectByBusinessCategory" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from CUM_BUSINESS
    where BUSINESS_CATEGORY = #{businessCategory,jdbcType=VARCHAR}
  </select>

  <select id="selectByBusinessCodeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CUM_BUSINESS
    where CODE in
    <foreach item="item" collection="businessCodeList" open="(" separator="," close=")">#{item}</foreach>
  </select>

</mapper>