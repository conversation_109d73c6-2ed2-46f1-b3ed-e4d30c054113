<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.txs.transaction.dao.TxsRepayTradeOrderMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
    <result column="TRANSACTION_NO" jdbcType="VARCHAR" property="transactionNo" />
    <result column="OUT_TRADE_NO" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="PAY_METHOD" jdbcType="VARCHAR" property="payMethod" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="CURRENCY_TYPE" jdbcType="VARCHAR" property="currencyType" />
    <result column="PROCEDURE_FEE" jdbcType="DECIMAL" property="procedureFee" />
    <result column="PAYER_TYPE" jdbcType="VARCHAR" property="payerType" />
    <result column="PAYER_ID" jdbcType="VARCHAR" property="payerId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="STATE" jdbcType="CHAR" property="state" />
    <result column="REPAY_STATE" jdbcType="CHAR" property="repayState" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="TRANSACTION_TYPE" jdbcType="VARCHAR" property="transactionType" />
    <result column="BUSINESS_INST_ID" jdbcType="VARCHAR" property="businessInstId" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="SETT_CYCLE_START_TIME" jdbcType="TIMESTAMP" property="settCycleStartTime" />
    <result column="SETT_CYCLE_END_TIME" jdbcType="TIMESTAMP" property="settCycleEndTime" />
    <result column="REFUND_STATE" jdbcType="CHAR" property="refundState" /> 
    <result column="REFUND_TIME" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="REFUND_TRANSACTIONNO" jdbcType="VARCHAR" property="refundTransactionNo" />
    <result column="opr_state" jdbcType="VARCHAR" property="oprState" />
    <result column="opr_time" jdbcType="VARCHAR" property="oprTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDER_ID, TRANSACTION_NO, OUT_TRADE_NO, CUSTOMER_CODE, PAY_METHOD, AMOUNT, CURRENCY_TYPE, 
    PROCEDURE_FEE, PAYER_TYPE, PAYER_ID, CREATE_TIME, STATE, REPAY_STATE, UPDATE_TIME, 
    ERROR_CODE, END_TIME, REMARK, TRANSACTION_TYPE, BUSINESS_INST_ID,
    BUSINESS_CODE,SETT_CYCLE_START_TIME,SETT_CYCLE_END_TIME, REFUND_STATE, REFUND_TIME, REFUND_TRANSACTIONNO, opr_state, opr_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TXS_REPAY_TRADE_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TXS_REPAY_TRADE_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder">
    insert into TXS_REPAY_TRADE_ORDER (ID, ORDER_ID, TRANSACTION_NO, 
      OUT_TRADE_NO, CUSTOMER_CODE, PAY_METHOD, 
      AMOUNT, CURRENCY_TYPE, PROCEDURE_FEE, 
      PAYER_TYPE, PAYER_ID, CREATE_TIME, 
      STATE, REPAY_STATE, UPDATE_TIME, 
      ERROR_CODE, END_TIME, REMARK, 
      TRANSACTION_TYPE,BUSINESS_INST_ID,
      BUSINESS_CODE,SETT_CYCLE_START_TIME,
      SETT_CYCLE_END_TIME,REFUND_STATE, 
      REFUND_TIME, REFUND_TRANSACTIONNO)
    values (#{id,jdbcType=DECIMAL}, #{orderId,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, 
      #{outTradeNo,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, 
      #{amount,jdbcType=DECIMAL}, #{currencyType,jdbcType=VARCHAR}, #{procedureFee,jdbcType=DECIMAL}, 
      #{payerType,jdbcType=VARCHAR}, #{payerId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{state,jdbcType=CHAR}, #{repayState,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{errorCode,jdbcType=VARCHAR}, #{endTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{transactionType,jdbcType=VARCHAR},#{businessInstId,jdbcType=VARCHAR},
      #{businessCode,jdbcType=VARCHAR},#{settCycleStartTime,jdbcType=TIMESTAMP},
      #{settCycleEndTime,jdbcType=TIMESTAMP},#{refundState,jdbcType=CHAR},
      #{refundTime,jdbcType=TIMESTAMP},#{refundTransactionNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder">
    insert into TXS_REPAY_TRADE_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO,
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="payMethod != null">
        PAY_METHOD,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="currencyType != null">
        CURRENCY_TYPE,
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE,
      </if>
      <if test="payerType != null">
        PAYER_TYPE,
      </if>
      <if test="payerId != null">
        PAYER_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="repayState != null">
        REPAY_STATE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE,
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="settCycleStartTime != null">
        SETT_CYCLE_START_TIME,
      </if>
      <if test="settCycleEndTime != null">
        SETT_CYCLE_END_TIME,
      </if>
      <if test="refundState != null">
        REFUND_STATE,
      </if>
      <if test="refundTime != null">
        REFUND_TIME,
      </if>
      <if test="refundTransactionNo != null">
        REFUND_TRANSACTIONNO,
      </if>
      <if test="oprState != null">
        opr_state,
      </if>
      <if test="oprTime != null">
        opr_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null">
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null">
        #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="procedureFee != null">
        #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="payerType != null">
        #{payerType,jdbcType=VARCHAR},
      </if>
      <if test="payerId != null">
        #{payerId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        #{state,jdbcType=CHAR},
      </if>
      <if test="repayState != null">
        #{repayState,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="settCycleStartTime != null">
        #{settCycleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settCycleEndTime != null">
        #{settCycleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundState != null">
        #{refundState,jdbcType=CHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP}, 
      </if>
      <if test="refundTransactionNo != null">
        #{refundTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="oprState != null">
        #{oprState,jdbcType=VARCHAR},
      </if>
      <if test="oprTime != null">
        #{oprTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder">
    update TXS_REPAY_TRADE_ORDER
    <set>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null">
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null">
        CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="procedureFee != null">
        PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      </if>
      <if test="payerType != null">
        PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
      </if>
      <if test="payerId != null">
        PAYER_ID = #{payerId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="repayState != null">
        REPAY_STATE = #{repayState,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="businessInstId != null">
        BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="settCycleStartTime != null">
        SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settCycleEndTime != null">
        SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundState != null">
        REFUND_STATE = #{refundState,jdbcType=CHAR},
      </if>
      <if test="refundTime != null">
        REFUND_TIME = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTransactionNo != null">
        REFUND_TRANSACTIONNO = #{refundTransactionNo,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <update id="updateByPrimaryKeyForRepeatUpdate" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder">
    update TXS_REPAY_TRADE_ORDER
    <set>
      <if test="oprState != null">
        opr_state = #{oprState,jdbcType=VARCHAR},
      </if>
      <if test="oprTime != null">
        opr_time = #{oprTime,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
    and opr_state != #{oprState,jdbcType=VARCHAR}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder">
    update TXS_REPAY_TRADE_ORDER
    set ORDER_ID = #{orderId,jdbcType=DECIMAL},
      TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      CURRENCY_TYPE = #{currencyType,jdbcType=VARCHAR},
      PROCEDURE_FEE = #{procedureFee,jdbcType=DECIMAL},
      PAYER_TYPE = #{payerType,jdbcType=VARCHAR},
      PAYER_ID = #{payerId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      STATE = #{state,jdbcType=CHAR},
      REPAY_STATE = #{repayState,jdbcType=CHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR},
      BUSINESS_INST_ID = #{businessInstId,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      SETT_CYCLE_START_TIME = #{settCycleStartTime,jdbcType=TIMESTAMP},
      SETT_CYCLE_END_TIME = #{settCycleEndTime,jdbcType=TIMESTAMP},
      REFUND_STATE = #{refundState,jdbcType=CHAR},
      REFUND_TIME = #{refundTime,jdbcType=TIMESTAMP}, 
      REFUND_TRANSACTIONNO = #{refundTransactionNo,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectByNotPage" resultMap="BaseResultMap"
	parameterType="java.util.Map">
	select
	<include refid="Base_Column_List" />
	from TXS_REPAY_TRADE_ORDER
		<where>
	    	 <include refid="repay_common_query_condition"/>
	    </where>
	</select>
	
	
	<select id="selectByPage" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
				select A.*, rownum RN
				from ( 
				select * from TXS_REPAY_TRADE_ORDER
					<where>
				    	 <include refid="repay_common_query_condition"/>
				    </where>
		 			order by CREATE_TIME desc 
					) A
				where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
		
	</select>
	
	<select id="selectCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		select count(ORDER_ID)
		from TXS_REPAY_TRADE_ORDER
		<where>
	    	 <include refid="repay_common_query_condition"/>
	    </where>
	</select>
	
	<select id="selectSumProcedureFee" resultType="java.lang.Long"
		parameterType="java.util.Map">
		select COALESCE(SUM(PROCEDURE_FEE),0)
		from TXS_REPAY_TRADE_ORDER
		<where>
	    	 <include refid="repay_common_query_condition"/>
	    </where>
	</select>
	
	<select id="selectSumAmount" resultType="java.lang.Long"
		parameterType="java.util.Map">
		select COALESCE(SUM(AMOUNT),0)
		from TXS_REPAY_TRADE_ORDER
		<where>
	    	 <include refid="repay_common_query_condition"/>
	    </where>
	</select>
	
	<select id="selectByTransactionNo" resultMap="BaseResultMap" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_REPAY_TRADE_ORDER
	    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    </select>
	<select id="selectByRefundTransactionNo" resultMap="BaseResultMap" >
	    select 
	    <include refid="Base_Column_List" />
	    from TXS_REPAY_TRADE_ORDER
	    where REFUND_TRANSACTIONNO = #{refundTransactionNo,jdbcType=VARCHAR}
    </select>
    
    <sql id="repay_common_query_condition">
        <if test="transactionType != null and transactionType !=''">
			AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}  
		</if>
		<if test="transactionNo != null and transactionNo !=''">
			AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}  
		</if>
		<if test="payMethod != null and payMethod !=''">
			AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}  
		</if>
		<if test="outTradeNo != null and outTradeNo !=''">
			AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}  
		</if>
		<if test="customerCode != null and customerCode !=''">
			AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} 
		</if>
		<if test="businessCode != null and businessCode !=''">
			AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
		</if>
		<if test="customerName != null and customerName !=''">
			AND CUSTOMERNAME = #{customerName,jdbcType=VARCHAR}
		</if>
		<if test="state != null and state !=''">
			AND STATE = #{state,jdbcType=CHAR} 
		</if>
		<if test="refundState != null and refundState !=''">
			AND REFUND_STATE = #{refundState,jdbcType=CHAR}
		</if>
		<if test="beginTime!=null">
			 	AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP} 
		</if>
		<if test="endTime!=null">
			 	AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP} 
		</if>
		<if test="oprState!=null">
			 	AND opr_state = #{oprState,jdbcType=VARCHAR} 
		</if>
	</sql>
</mapper>