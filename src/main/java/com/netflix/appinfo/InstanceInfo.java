package com.netflix.appinfo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.netflix.appinfo.*;
import com.netflix.appinfo.AmazonInfo.MetaDataKey;
import com.netflix.appinfo.DataCenterInfo.Name;
import com.netflix.appinfo.providers.Archaius1VipAddressResolver;
import com.netflix.appinfo.providers.EurekaConfigBasedInstanceInfoProvider;
import com.netflix.appinfo.providers.VipAddressResolver;
import com.netflix.discovery.converters.Auto;
import com.netflix.discovery.provider.Serializer;
import com.netflix.discovery.util.StringCache;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Serializer("com.netflix.discovery.converters.EntityBodyConverter")
@XStreamAlias("instance")
@JsonRootName("instance")
public class InstanceInfo {
    private static final Logger logger = LoggerFactory.getLogger(InstanceInfo.class);
    public static final int DEFAULT_PORT = 7001;
    public static final int DEFAULT_SECURE_PORT = 7002;
    public static final int DEFAULT_COUNTRY_ID = 1;
    private volatile String instanceId;
    private volatile String appName;
    @Auto
    private volatile String appGroupName;
    private volatile String ipAddr;
    private static final String SID_DEFAULT = "na";
    /** @deprecated */
    @Deprecated
    private volatile String sid;
    private volatile int port;
    private volatile int securePort;
    @Auto
    private volatile String homePageUrl;
    @Auto
    private volatile String statusPageUrl;
    @Auto
    private volatile String healthCheckUrl;
    @Auto
    private volatile String secureHealthCheckUrl;
    @Auto
    private volatile String vipAddress;
    @Auto
    private volatile String secureVipAddress;
    @XStreamOmitField
    private String statusPageRelativeUrl;
    @XStreamOmitField
    private String statusPageExplicitUrl;
    @XStreamOmitField
    private String healthCheckRelativeUrl;
    @XStreamOmitField
    private String healthCheckSecureExplicitUrl;
    @XStreamOmitField
    private String vipAddressUnresolved;
    @XStreamOmitField
    private String secureVipAddressUnresolved;
    @XStreamOmitField
    private String healthCheckExplicitUrl;
    /** @deprecated */
    @Deprecated
    private volatile int countryId;
    private volatile boolean isSecurePortEnabled;
    private volatile boolean isUnsecurePortEnabled;
    private volatile DataCenterInfo dataCenterInfo;
    private volatile String hostName;
    private volatile InstanceStatus status;
    private volatile InstanceStatus overriddenstatus;
    @XStreamOmitField
    private volatile boolean isInstanceInfoDirty;
    private volatile LeaseInfo leaseInfo;
    @Auto
    private volatile Boolean isCoordinatingDiscoveryServer;
    @XStreamAlias("metadata")
    private volatile Map<String, String> metadata;
    @Auto
    private volatile Long lastUpdatedTimestamp;
    @Auto
    private volatile Long lastDirtyTimestamp;
    @Auto
    private volatile ActionType actionType;
    @Auto
    private volatile String asgName;
    private String version;

    private InstanceInfo() {
        this.sid = "na";
        this.port = 7001;
        this.securePort = 7002;
        this.countryId = 1;
        this.isSecurePortEnabled = false;
        this.isUnsecurePortEnabled = true;
        this.status = InstanceStatus.UP;
        this.overriddenstatus = InstanceStatus.UNKNOWN;
        this.isInstanceInfoDirty = false;
        this.isCoordinatingDiscoveryServer = Boolean.FALSE;
        this.metadata = new ConcurrentHashMap();
        this.lastUpdatedTimestamp = System.currentTimeMillis();
        this.lastDirtyTimestamp = System.currentTimeMillis();
        this.version = "unknown";
    }

    @JsonCreator
    public InstanceInfo(@JsonProperty("instanceId") String instanceId, @JsonProperty("app") String appName, @JsonProperty("appGroupName") String appGroupName, @JsonProperty("ipAddr") String ipAddr, @JsonProperty("sid") String sid, @JsonProperty("port") PortWrapper port, @JsonProperty("securePort") PortWrapper securePort, @JsonProperty("homePageUrl") String homePageUrl, @JsonProperty("statusPageUrl") String statusPageUrl, @JsonProperty("healthCheckUrl") String healthCheckUrl, @JsonProperty("secureHealthCheckUrl") String secureHealthCheckUrl, @JsonProperty("vipAddress") String vipAddress, @JsonProperty("secureVipAddress") String secureVipAddress, @JsonProperty("countryId") int countryId, @JsonProperty("dataCenterInfo") DataCenterInfo dataCenterInfo, @JsonProperty("hostName") String hostName, @JsonProperty("status") InstanceStatus status, @JsonProperty("overriddenstatus") InstanceStatus overriddenstatus, @JsonProperty("leaseInfo") LeaseInfo leaseInfo, @JsonProperty("isCoordinatingDiscoveryServer") Boolean isCoordinatingDiscoveryServer, @JsonProperty("metadata") HashMap<String, String> metadata, @JsonProperty("lastUpdatedTimestamp") Long lastUpdatedTimestamp, @JsonProperty("lastDirtyTimestamp") Long lastDirtyTimestamp, @JsonProperty("actionType") ActionType actionType, @JsonProperty("asgName") String asgName) {
        this.sid = "na";
        this.port = 7001;
        this.securePort = 7002;
        this.countryId = 1;
        this.isSecurePortEnabled = false;
        this.isUnsecurePortEnabled = true;
        this.status = InstanceStatus.UP;
        this.overriddenstatus = InstanceStatus.UNKNOWN;
        this.isInstanceInfoDirty = false;
        this.isCoordinatingDiscoveryServer = Boolean.FALSE;
        this.metadata = new ConcurrentHashMap();
        this.lastUpdatedTimestamp = System.currentTimeMillis();
        this.lastDirtyTimestamp = System.currentTimeMillis();
        this.version = "unknown";
        this.instanceId = instanceId;
        this.sid = sid;
        this.appName = StringCache.intern(appName);
        this.appGroupName = StringCache.intern(appGroupName);
        this.ipAddr = ipAddr;
        this.port = port == null ? 0 : port.getPort();
        this.isUnsecurePortEnabled = port != null && port.isEnabled();
        this.securePort = securePort == null ? 0 : securePort.getPort();
        this.isSecurePortEnabled = securePort != null && securePort.isEnabled();
        this.homePageUrl = homePageUrl;
        this.statusPageUrl = statusPageUrl;
        this.healthCheckUrl = healthCheckUrl;
        this.secureHealthCheckUrl = secureHealthCheckUrl;
        this.vipAddress = StringCache.intern(vipAddress);
        this.secureVipAddress = StringCache.intern(secureVipAddress);
        this.countryId = countryId;
        this.dataCenterInfo = dataCenterInfo;
        this.hostName = hostName;
        this.status = status;
        this.overriddenstatus = overriddenstatus;
        this.leaseInfo = leaseInfo;
        this.isCoordinatingDiscoveryServer = isCoordinatingDiscoveryServer;
        this.lastUpdatedTimestamp = lastUpdatedTimestamp;
        this.lastDirtyTimestamp = lastDirtyTimestamp;
        this.actionType = actionType;
        this.asgName = StringCache.intern(asgName);
        if (metadata == null) {
            this.metadata = Collections.emptyMap();
        } else if (metadata.size() == 1) {
            this.metadata = this.removeMetadataMapLegacyValues(metadata);
        } else {
            this.metadata = metadata;
        }

        if (sid == null) {
            this.sid = "na";
        }
    }

    private Map<String, String> removeMetadataMapLegacyValues(Map<String, String> metadata) {
        if ("java.util.Collections$EmptyMap".equals(metadata.get("@class"))) {
            metadata.remove("@class");
        } else if ("java.util.Collections$EmptyMap".equals(metadata.get("class"))) {
            metadata.remove("class");
        }

        return metadata;
    }

    public InstanceInfo(InstanceInfo ii) {
        this.sid = "na";
        this.port = 7001;
        this.securePort = 7002;
        this.countryId = 1;
        this.isSecurePortEnabled = false;
        this.isUnsecurePortEnabled = true;
        this.status = InstanceStatus.UP;
        this.overriddenstatus = InstanceStatus.UNKNOWN;
        this.isInstanceInfoDirty = false;
        this.isCoordinatingDiscoveryServer = Boolean.FALSE;
        this.metadata = new ConcurrentHashMap();
        this.lastUpdatedTimestamp = System.currentTimeMillis();
        this.lastDirtyTimestamp = System.currentTimeMillis();
        this.version = "unknown";
        this.instanceId = ii.instanceId;
        this.appName = ii.appName;
        this.appGroupName = ii.appGroupName;
        this.ipAddr = ii.ipAddr;
        this.sid = ii.sid;
        this.port = ii.port;
        this.securePort = ii.securePort;
        this.homePageUrl = ii.homePageUrl;
        this.statusPageUrl = ii.statusPageUrl;
        this.healthCheckUrl = ii.healthCheckUrl;
        this.secureHealthCheckUrl = ii.secureHealthCheckUrl;
        this.vipAddress = ii.vipAddress;
        this.secureVipAddress = ii.secureVipAddress;
        this.statusPageRelativeUrl = ii.statusPageRelativeUrl;
        this.statusPageExplicitUrl = ii.statusPageExplicitUrl;
        this.healthCheckRelativeUrl = ii.healthCheckRelativeUrl;
        this.healthCheckSecureExplicitUrl = ii.healthCheckSecureExplicitUrl;
        this.vipAddressUnresolved = ii.vipAddressUnresolved;
        this.secureVipAddressUnresolved = ii.secureVipAddressUnresolved;
        this.healthCheckExplicitUrl = ii.healthCheckExplicitUrl;
        this.countryId = ii.countryId;
        this.isSecurePortEnabled = ii.isSecurePortEnabled;
        this.isUnsecurePortEnabled = ii.isUnsecurePortEnabled;
        this.dataCenterInfo = ii.dataCenterInfo;
        this.hostName = ii.hostName;
        this.status = ii.status;
        this.overriddenstatus = ii.overriddenstatus;
        this.isInstanceInfoDirty = ii.isInstanceInfoDirty;
        this.leaseInfo = ii.leaseInfo;
        this.isCoordinatingDiscoveryServer = ii.isCoordinatingDiscoveryServer;
        this.metadata = ii.metadata;
        this.lastUpdatedTimestamp = ii.lastUpdatedTimestamp;
        this.lastDirtyTimestamp = ii.lastDirtyTimestamp;
        this.actionType = ii.actionType;
        this.asgName = ii.asgName;
        this.version = ii.version;
    }

    public int hashCode() {
        int prime = 1;
        int result = 31 * prime + (this.getId() == null ? 0 : this.getId().hashCode());
        return result;
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        } else if (obj == null) {
            return false;
        } else if (this.getClass() != obj.getClass()) {
            return false;
        } else {
            InstanceInfo other = (InstanceInfo)obj;
            if (this.getId() == null) {
                if (other.getId() != null) {
                    return false;
                }
            } else if (!this.getId().equals(other.getId())) {
                return false;
            }

            return true;
        }
    }

    public String getInstanceId() {
        return this.instanceId;
    }

    @JsonProperty("app")
    public String getAppName() {
        return this.appName;
    }

    public String getAppGroupName() {
        return this.appGroupName;
    }

    private final static void debug(String msg) {
        final String dm = System.getenv("DEBUG_MODE");
        if("true".equalsIgnoreCase(dm)) {
            System.out.println(">>>>"+ msg);
        }
    }

    public String getHostName() {
        final String rm = System.getenv("ROUTE_MODE");
        if("Host_Name".equalsIgnoreCase(rm)) {
            debug("["+this.getAppName()+"]getHostName as HOST:"+this.hostName);
            return this.hostName;
        } else if("SERVICE_NAME".equalsIgnoreCase(rm)) {
            debug("["+this.getAppName()+"]getHostName as SERVICE:"+this.appName);
            return this.appName;
        } else {
            debug("["+this.getAppName()+"]getHostName as IP:"+this.ipAddr);
            return this.ipAddr;
        }
    }

    /** @deprecated */
    @Deprecated
    public void setSID(String sid) {
        this.sid = sid;
        this.setIsDirty();
    }

    /** @deprecated */
    @JsonProperty("sid")
    @Deprecated
    public String getSID() {
        return this.sid;
    }

    @JsonIgnore
    public String getId() {
        if (this.instanceId != null && !this.instanceId.isEmpty()) {
            return this.instanceId;
        } else {
            if (this.dataCenterInfo instanceof UniqueIdentifier) {
                String uniqueId = ((UniqueIdentifier)this.dataCenterInfo).getId();
                if (uniqueId != null && !uniqueId.isEmpty()) {
                    return uniqueId;
                }
            }

            return this.hostName;
        }
    }

    @JsonProperty("ipAddr")
    public String getIPAddr() {
        return this.ipAddr;
    }

    @JsonIgnore
    public int getPort() {
        return this.port;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public InstanceStatus getOverriddenStatus() {
        return this.overriddenstatus;
    }

    public DataCenterInfo getDataCenterInfo() {
        return this.dataCenterInfo;
    }

    public LeaseInfo getLeaseInfo() {
        return this.leaseInfo;
    }

    public void setLeaseInfo(LeaseInfo info) {
        this.leaseInfo = info;
    }

    public Map<String, String> getMetadata() {
        return this.metadata;
    }

    /** @deprecated */
    @Deprecated
    public int getCountryId() {
        return this.countryId;
    }

    @JsonIgnore
    public int getSecurePort() {
        return this.securePort;
    }

    @JsonIgnore
    public boolean isPortEnabled(PortType type) {
        return type == PortType.SECURE ? this.isSecurePortEnabled : this.isUnsecurePortEnabled;
    }

    public long getLastUpdatedTimestamp() {
        return this.lastUpdatedTimestamp;
    }

    public void setLastUpdatedTimestamp() {
        this.lastUpdatedTimestamp = System.currentTimeMillis();
    }

    public String getHomePageUrl() {
        System.out.println(">>>>getHomePageUrl:"+this.homePageUrl.replace(this.hostName, this.ipAddr));
        return this.homePageUrl.replace(this.hostName, this.ipAddr);
    }

    public String getStatusPageUrl() {
        System.out.println(">>>>getStatusPageUrl:"+this.statusPageUrl.replace(this.hostName, this.ipAddr));
        return this.statusPageUrl.replace(this.hostName, this.ipAddr);
    }

    @JsonIgnore
    public Set<String> getHealthCheckUrls() {
        Set<String> healthCheckUrlSet = new LinkedHashSet();
        if (this.isUnsecurePortEnabled && this.healthCheckUrl != null && !this.healthCheckUrl.isEmpty()) {
            healthCheckUrlSet.add(this.healthCheckUrl);
        }

        if (this.isSecurePortEnabled && this.secureHealthCheckUrl != null && !this.secureHealthCheckUrl.isEmpty()) {
            healthCheckUrlSet.add(this.secureHealthCheckUrl);
        }

        return healthCheckUrlSet;
    }

    public String getHealthCheckUrl() {
        System.out.println(">>>>getHealthCheckUrl:"+this.healthCheckUrl.replace(this.hostName, this.ipAddr));
        return this.healthCheckUrl.replace(this.hostName, this.ipAddr);
    }

    public String getSecureHealthCheckUrl() {
        System.out.println(">>>>getSecureHealthCheckUrl:"+this.secureHealthCheckUrl.replace(this.hostName, this.ipAddr));
        return this.secureHealthCheckUrl.replace(this.hostName, this.ipAddr);
    }

    @JsonProperty("vipAddress")
    public String getVIPAddress() {
        return this.vipAddress;
    }

    public String getSecureVipAddress() {
        return this.secureVipAddress;
    }

    public Long getLastDirtyTimestamp() {
        return this.lastDirtyTimestamp;
    }

    public void setLastDirtyTimestamp(Long lastDirtyTimestamp) {
        this.lastDirtyTimestamp = lastDirtyTimestamp;
    }

    public synchronized InstanceStatus setStatus(InstanceStatus status) {
        if (this.status != status) {
            InstanceStatus prev = this.status;
            this.status = status;
            this.setIsDirty();
            return prev;
        } else {
            return null;
        }
    }

    public synchronized void setStatusWithoutDirty(InstanceStatus status) {
        if (this.status != status) {
            this.status = status;
        }

    }

    public synchronized void setOverriddenStatus(InstanceStatus status) {
        if (this.overriddenstatus != status) {
            this.overriddenstatus = status;
        }

    }

    @JsonIgnore
    public boolean isDirty() {
        return this.isInstanceInfoDirty;
    }

    public synchronized Long isDirtyWithTime() {
        return this.isInstanceInfoDirty ? this.lastDirtyTimestamp : null;
    }

    /** @deprecated */
    @Deprecated
    public synchronized void setIsDirty(boolean isDirty) {
        if (isDirty) {
            this.setIsDirty();
        } else {
            this.isInstanceInfoDirty = false;
        }

    }

    public synchronized void setIsDirty() {
        this.isInstanceInfoDirty = true;
        this.lastDirtyTimestamp = System.currentTimeMillis();
    }

    public synchronized void unsetIsDirty(long unsetDirtyTimestamp) {
        if (this.lastDirtyTimestamp <= unsetDirtyTimestamp) {
            this.isInstanceInfoDirty = false;
        }

    }

    public void setIsCoordinatingDiscoveryServer() {
        String instanceId = this.getId();
        if (instanceId != null && instanceId.equals(ApplicationInfoManager.getInstance().getInfo().getId())) {
            this.isCoordinatingDiscoveryServer = Boolean.TRUE;
        } else {
            this.isCoordinatingDiscoveryServer = Boolean.FALSE;
        }

    }

    @JsonProperty("isCoordinatingDiscoveryServer")
    public Boolean isCoordinatingDiscoveryServer() {
        return this.isCoordinatingDiscoveryServer;
    }

    public ActionType getActionType() {
        return this.actionType;
    }

    public void setActionType(ActionType actionType) {
        this.actionType = actionType;
    }

    @JsonProperty("asgName")
    public String getASGName() {
        return this.asgName;
    }

    /** @deprecated */
    @Deprecated
    @JsonIgnore
    public String getVersion() {
        return this.version;
    }

    synchronized void registerRuntimeMetadata(Map<String, String> runtimeMetadata) {
        this.metadata.putAll(runtimeMetadata);
        this.setIsDirty();
    }

    public static String getZone(String[] availZones, InstanceInfo myInfo) {
        String instanceZone = availZones != null && availZones.length != 0 ? availZones[0] : "default";
        if (myInfo != null && myInfo.getDataCenterInfo().getName() == Name.Amazon) {
            String awsInstanceZone = ((AmazonInfo)myInfo.getDataCenterInfo()).get(MetaDataKey.availabilityZone);
            if (awsInstanceZone != null) {
                instanceZone = awsInstanceZone;
            }
        }

        return instanceZone;
    }

    public static enum ActionType {
        ADDED,
        MODIFIED,
        DELETED;

        private ActionType() {
        }
    }

    public static final class Builder {
        private static final String COLON = ":";
        private static final String HTTPS_PROTOCOL = "https://";
        private static final String HTTP_PROTOCOL = "http://";
        @XStreamOmitField
        private InstanceInfo result;
        @XStreamOmitField
        private final VipAddressResolver vipAddressResolver;
        private String namespace;

        private Builder(InstanceInfo result, VipAddressResolver vipAddressResolver) {
            this.vipAddressResolver = vipAddressResolver;
            this.result = result;
        }

        public Builder(InstanceInfo instanceInfo) {
            this(instanceInfo, LazyHolder.DEFAULT_VIP_ADDRESS_RESOLVER);
        }

        public static Builder newBuilder() {
            return new Builder(new InstanceInfo(), LazyHolder.DEFAULT_VIP_ADDRESS_RESOLVER);
        }

        public static Builder newBuilder(VipAddressResolver vipAddressResolver) {
            return new Builder(new InstanceInfo(), vipAddressResolver);
        }

        public Builder setInstanceId(String instanceId) {
            this.result.instanceId = instanceId;
            return this;
        }

        public Builder setAppName(String appName) {
            this.result.appName = StringCache.intern(appName.toUpperCase(Locale.ROOT));
            return this;
        }

        public Builder setAppGroupName(String appGroupName) {
            if (appGroupName != null) {
                this.result.appGroupName = appGroupName.toUpperCase(Locale.ROOT);
            } else {
                this.result.appGroupName = null;
            }

            return this;
        }

        public Builder setHostName(String hostName) {
            if (hostName != null && !hostName.isEmpty()) {
                String existingHostName = this.result.hostName;
                this.result.hostName = hostName;
                if (existingHostName != null && !hostName.equals(existingHostName)) {
                    this.refreshStatusPageUrl().refreshHealthCheckUrl().refreshVIPAddress().refreshSecureVIPAddress();
                }

                return this;
            } else {
                InstanceInfo.logger.warn("Passed in hostname is blank, not setting it");
                return this;
            }
        }

        public Builder setStatus(InstanceStatus status) {
            this.result.status = status;
            return this;
        }

        public Builder setOverriddenStatus(InstanceStatus status) {
            this.result.overriddenstatus = status;
            return this;
        }

        public Builder setIPAddr(String ip) {
            this.result.ipAddr = ip;
            return this;
        }

        /** @deprecated */
        @Deprecated
        public Builder setSID(String sid) {
            this.result.sid = sid;
            return this;
        }

        public Builder setPort(int port) {
            this.result.port = port;
            return this;
        }

        public Builder setSecurePort(int port) {
            this.result.securePort = port;
            return this;
        }

        public Builder enablePort(PortType type, boolean isEnabled) {
            if (type == PortType.SECURE) {
                this.result.isSecurePortEnabled = isEnabled;
            } else {
                this.result.isUnsecurePortEnabled = isEnabled;
            }

            return this;
        }

        /** @deprecated */
        @Deprecated
        public Builder setCountryId(int id) {
            this.result.countryId = id;
            return this;
        }

        public Builder setHomePageUrl(String relativeUrl, String explicitUrl) {
            String hostNameInterpolationExpression = "${" + this.namespace + "hostname}";
            if (explicitUrl != null) {
                this.result.homePageUrl = explicitUrl.replace(hostNameInterpolationExpression, this.result.hostName);
            } else if (relativeUrl != null) {
                this.result.homePageUrl = "http://" + this.result.hostName + ":" + this.result.port + relativeUrl;
            }

            return this;
        }

        public Builder setHomePageUrlForDeser(String homePageUrl) {
            this.result.homePageUrl = homePageUrl;
            return this;
        }

        public Builder setStatusPageUrl(String relativeUrl, String explicitUrl) {
            String hostNameInterpolationExpression = "${" + this.namespace + "hostname}";
            this.result.statusPageRelativeUrl = relativeUrl;
            this.result.statusPageExplicitUrl = explicitUrl;
            if (explicitUrl != null) {
                this.result.statusPageUrl = explicitUrl.replace(hostNameInterpolationExpression, this.result.hostName);
            } else if (relativeUrl != null) {
                this.result.statusPageUrl = "http://" + this.result.hostName + ":" + this.result.port + relativeUrl;
            }

            return this;
        }

        public Builder setStatusPageUrlForDeser(String statusPageUrl) {
            this.result.statusPageUrl = statusPageUrl;
            return this;
        }

        public Builder setHealthCheckUrls(String relativeUrl, String explicitUrl, String secureExplicitUrl) {
            String hostNameInterpolationExpression = "${" + this.namespace + "hostname}";
            this.result.healthCheckRelativeUrl = relativeUrl;
            this.result.healthCheckExplicitUrl = explicitUrl;
            this.result.healthCheckSecureExplicitUrl = secureExplicitUrl;
            if (explicitUrl != null) {
                this.result.healthCheckUrl = explicitUrl.replace(hostNameInterpolationExpression, this.result.hostName);
            } else if (this.result.isUnsecurePortEnabled) {
                this.result.healthCheckUrl = "http://" + this.result.hostName + ":" + this.result.port + relativeUrl;
            }

            if (secureExplicitUrl != null) {
                this.result.secureHealthCheckUrl = secureExplicitUrl.replace(hostNameInterpolationExpression, this.result.hostName);
            } else if (this.result.isSecurePortEnabled) {
                this.result.secureHealthCheckUrl = "https://" + this.result.hostName + ":" + this.result.securePort + relativeUrl;
            }

            return this;
        }

        public Builder setHealthCheckUrlsForDeser(String healthCheckUrl, String secureHealthCheckUrl) {
            if (healthCheckUrl != null) {
                this.result.healthCheckUrl = healthCheckUrl;
            }

            if (secureHealthCheckUrl != null) {
                this.result.secureHealthCheckUrl = secureHealthCheckUrl;
            }

            return this;
        }

        public Builder setVIPAddress(String vipAddress) {
            this.result.vipAddressUnresolved = StringCache.intern(vipAddress);
            this.result.vipAddress = StringCache.intern(this.vipAddressResolver.resolveDeploymentContextBasedVipAddresses(vipAddress));
            return this;
        }

        public Builder setVIPAddressDeser(String vipAddress) {
            this.result.vipAddress = StringCache.intern(vipAddress);
            return this;
        }

        public Builder setSecureVIPAddress(String secureVIPAddress) {
            this.result.secureVipAddressUnresolved = StringCache.intern(secureVIPAddress);
            this.result.secureVipAddress = StringCache.intern(this.vipAddressResolver.resolveDeploymentContextBasedVipAddresses(secureVIPAddress));
            return this;
        }

        public Builder setSecureVIPAddressDeser(String secureVIPAddress) {
            this.result.secureVipAddress = StringCache.intern(secureVIPAddress);
            return this;
        }

        public Builder setDataCenterInfo(DataCenterInfo datacenter) {
            this.result.dataCenterInfo = datacenter;
            return this;
        }

        public Builder setLeaseInfo(LeaseInfo info) {
            this.result.leaseInfo = info;
            return this;
        }

        public Builder add(String key, String val) {
            this.result.metadata.put(key, val);
            return this;
        }

        public Builder setMetadata(Map<String, String> mt) {
            this.result.metadata = mt;
            return this;
        }

        public InstanceInfo getRawInstance() {
            return this.result;
        }

        public InstanceInfo build() {
            if (!this.isInitialized()) {
                throw new IllegalStateException("name is required!");
            } else {
                return this.result;
            }
        }

        public boolean isInitialized() {
            return this.result.appName != null;
        }

        public Builder setASGName(String asgName) {
            this.result.asgName = StringCache.intern(asgName);
            return this;
        }

        private Builder refreshStatusPageUrl() {
            this.setStatusPageUrl(this.result.statusPageRelativeUrl, this.result.statusPageExplicitUrl);
            return this;
        }

        private Builder refreshHealthCheckUrl() {
            this.setHealthCheckUrls(this.result.healthCheckRelativeUrl, this.result.healthCheckExplicitUrl, this.result.healthCheckSecureExplicitUrl);
            return this;
        }

        private Builder refreshSecureVIPAddress() {
            this.setSecureVIPAddress(this.result.secureVipAddressUnresolved);
            return this;
        }

        private Builder refreshVIPAddress() {
            this.setVIPAddress(this.result.vipAddressUnresolved);
            return this;
        }

        public Builder setIsCoordinatingDiscoveryServer(boolean isCoordinatingDiscoveryServer) {
            this.result.isCoordinatingDiscoveryServer = isCoordinatingDiscoveryServer;
            return this;
        }

        public Builder setLastUpdatedTimestamp(long lastUpdatedTimestamp) {
            this.result.lastUpdatedTimestamp = lastUpdatedTimestamp;
            return this;
        }

        public Builder setLastDirtyTimestamp(long lastDirtyTimestamp) {
            this.result.lastDirtyTimestamp = lastDirtyTimestamp;
            return this;
        }

        public Builder setActionType(ActionType actionType) {
            this.result.actionType = actionType;
            return this;
        }

        public Builder setNamespace(String namespace) {
            this.namespace = namespace.endsWith(".") ? namespace : namespace + ".";
            return this;
        }

        private static final class LazyHolder {
            private static final VipAddressResolver DEFAULT_VIP_ADDRESS_RESOLVER = new Archaius1VipAddressResolver();

            private LazyHolder() {
            }
        }
    }

    public static enum PortType {
        SECURE,
        UNSECURE;

        private PortType() {
        }
    }

    public static enum InstanceStatus {
        UP,
        DOWN,
        STARTING,
        OUT_OF_SERVICE,
        UNKNOWN;

        private InstanceStatus() {
        }

        public static InstanceStatus toEnum(String s) {
            InstanceStatus[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                InstanceStatus e = var1[var3];
                if (e.name().equalsIgnoreCase(s)) {
                    return e;
                }
            }

            return UNKNOWN;
        }
    }

    public static class PortWrapper {
        private final boolean enabled;
        private final int port;

        @JsonCreator
        public PortWrapper(@JsonProperty("@enabled") boolean enabled, @JsonProperty("$") int port) {
            this.enabled = enabled;
            this.port = port;
        }

        public boolean isEnabled() {
            return this.enabled;
        }

        public int getPort() {
            return this.port;
        }
    }
}
