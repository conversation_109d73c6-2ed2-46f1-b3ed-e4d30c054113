package com.epaylinks.efps.txs.util;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;
import org.slf4j.LoggerFactory;



public class HttpAsynClientUtil {
	private static CloseableHttpAsyncClient httpAsyncClient = null;
	org.slf4j.Logger log = LoggerFactory.getLogger(HttpAsynClientUtil.class);
	
	static {
		ConnectingIOReactor ioReactor = null;
		try {
			ioReactor = new DefaultConnectingIOReactor();
		} catch (IOReactorException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        PoolingNHttpClientConnectionManager cm = new PoolingNHttpClientConnectionManager(ioReactor);
        cm.setMaxTotal(100);
        cm.setDefaultMaxPerRoute(30);
        
        RequestConfig requestConfig = RequestConfig.custom()  
                .setConnectTimeout(1000)  
                .setSocketTimeout(1000).setConnectionRequestTimeout(1000).build();
        
        httpAsyncClient = HttpAsyncClients.custom().setConnectionManager(cm).setDefaultRequestConfig(requestConfig).build();
        httpAsyncClient.start();
//        Runtime.getRuntime().availableProcessors()
	}


	public static CloseableHttpAsyncClient getHttpAsyncClient() {
		return httpAsyncClient;
	}

}
