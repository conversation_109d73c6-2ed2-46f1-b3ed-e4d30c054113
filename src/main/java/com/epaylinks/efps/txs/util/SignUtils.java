package com.epaylinks.efps.txs.util;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import com.epaylinks.efps.common.util.MD5Utils;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;

/**
 * 签名工具
 *
 */
public class SignUtils {
	/**
     * 过滤参数
     * <AUTHOR> @param sArray
     * @return
     */
    public static Map<String, String> paraFilter(Map<String, String> sArray) {
        Map<String, String> result = new HashMap<String, String>(sArray.size());
        if (sArray == null || sArray.size() <= 0) {
            return result;
        }
        for (String key : sArray.keySet()) {
            String value = sArray.get(key);
            if (value == null || value.equals("") || key.equalsIgnoreCase("sign")) {
                continue;
            }
            result.put(key, value);
        }
        return result;
    }
    
    /**
     * <AUTHOR> @param payParams
     * @return
     */
    public static void buildPayParams(StringBuilder sb,Map<String, String> payParams,boolean encoding){
        List<String> keys = new ArrayList<String>(payParams.keySet());
        Collections.sort(keys);
        for(String key : keys){
            sb.append(key).append("=");
            if(encoding){
                sb.append(urlEncode(payParams.get(key)));
            }else{
                sb.append(payParams.get(key));
            }
            sb.append("&");
        }
        sb.setLength(sb.length() - 1);
    }
    
    public static String urlEncode(String str){
        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (Throwable e) {
            return str;
        } 
    }
    
    /**
     * 返回参数签名
     * @param outTradeNo
     * @param transactionNo
     * @param payState
     * @param signsNo
     * @return
     */
    public static String signData(String outTradeNo, String transactionNo, String payState, String signsNo) {
    	SortedMap<String, String> signMap = new TreeMap<String, String>();
		signMap.put("outTradeNo", outTradeNo);
		signMap.put("transactionNo", transactionNo);
		signMap.put("payState", payState);
		signMap.put("signsNo", signsNo);
		Map<String, String> params = paraFilter(signMap);// 过滤参数
		StringBuilder buf = new StringBuilder((params.size() + 1) * 10);
		SignUtils.buildPayParams(buf, params, false);
		String preStr = buf.toString();
		return preStr;
    }
    
    
    /**
     * MD5加密结果
     * <AUTHOR> @param payParams
     * @return
     */
    public static String md5Result(Map<String, Object> payParams){
        List<String> keys = new ArrayList<String>(payParams.keySet());
        StringBuilder sb = new StringBuilder();
        Collections.sort(keys);
        for(String key : keys){
            sb.append(payParams.get(key)).append("|");
        }
        return MD5Utils.getMD5(sb.toString());
    }
    
    public static void main(String[] args) {
		Map<String, Object> map = new HashMap<>();
		List<String> strings = new ArrayList<>();
		strings.add("a");
		map.put("b", "aa");
		map.put("a", true);
		map.put("c", strings);
		Map<String, Object> map1 = new HashMap<>();
		List<String> strings1 = new ArrayList<>();
		strings1.add("a");
		map1.put("b", "aa");
		map1.put("a", true);
		map1.put("c", strings1);
		System.out.println(SignUtils.md5Result(map).equals(SignUtils.md5Result(map1)));
	}
}
