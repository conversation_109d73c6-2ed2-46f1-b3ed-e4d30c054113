package com.epaylinks.efps.txs.util;



import java.io.IOException;
import java.text.ParseException;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;

public class OppIdGet {
	
	
	
	private static final Logger log = LoggerFactory.getLogger(OppIdGet.class);

	
	public final static String SNSAPI_BASE_GET_OPENID = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=$redirectUri&response_type=code&scope=snsapi_base&state=1#wechat_redirect";
    public final static String PAGE_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code";
    public final static String COMPONENT_PAGE_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/component/access_token?appid=APPID&code=CODE&grant_type=authorization_code&component_appid=COMPONENT_APPID&component_access_token=COMPONENT_ACCESS_TOKEN";
//https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/how_to_apply.html
    public static String getOpenId(String code, String appid, String secret) {
        if (code != null) {
            String url = PAGE_ACCESS_TOKEN_URL
                    .replace("APPID", appid)
                    .replace("SECRET", secret)
                    .replace("CODE", code);
            try {
                JSONObject jsonObject = doGetStr(url);
                log.info("jsonObject:"+jsonObject);
                String openId = jsonObject.getString("openid");
                return openId;
            } catch (ParseException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return "";
    }

    public static JSONObject doGetStr(String url) throws ParseException,
            IOException {
//        DefaultHttpClient httpClient = new DefaultHttpClient();
        HttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        JSONObject jsonObject = null;
        HttpResponse httpResponse = httpClient.execute(httpGet);
        HttpEntity httpEntity = httpResponse.getEntity();
        if (httpClient != null) {
            String result = EntityUtils.toString(httpEntity, "UTF-8");
            jsonObject = JSONObject.parseObject(result);
        }
        return jsonObject;
    }
}
