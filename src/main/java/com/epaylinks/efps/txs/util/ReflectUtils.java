package com.epaylinks.efps.txs.util;

import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

/**
 * 描述 ：反射工具类
 * Created by 陈奕丞 on 2017/9/18.
 */
public class ReflectUtils {

    /**
     * Copy the property values of the given source bean into the target bean. 
     * Note: The source and target classes do not have to match or even be derived from each other, 
     * as long as the properties match. Any bean properties that the source bean exposes but the target bean does not will silently be ignored. 
     *
     * @param dest 目标类
     * @param orig 来源
     */
    public static void copyProperties(Object dest, Object orig) {
        BeanUtils.copyProperties(orig,dest);
    }
    public static String[] getNullPropertyNames (Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for(java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 将orig中的非空属性复制到dest中的非空属性中去
     * @param dest
     * @param orig
     */
    public static void copyPropertiesIgnoreNull(Object dest, Object orig){
        BeanUtils.copyProperties(orig, dest, getNullPropertyNames(orig));
    }
}
