package com.epaylinks.efps.txs.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

public class SortUtils {
    // 删除ArrayList中重复元素，保持顺序
    public static List removeDuplicateWithOrder(List list) {
        Set set = new HashSet();
        List newList = new ArrayList();
        for (Iterator iter = list.iterator(); iter.hasNext();) {
            Object element = iter.next();
            if (set.add(element))
                newList.add(element);
        }
        list.clear();
        list.addAll(newList);
        return list;
    }
    
    /**
     * 保留相同的元素
     * @param aa
     * @param bb
     * @return
     */
    public static List saveEqual(List aa, List bb) {
    	Collection exists=new ArrayList<String>(bb);
    	Collection notexists=new ArrayList<String>(bb);
    	exists.removeAll(aa);
    	notexists.removeAll(exists);
    	return (List) notexists;
    }
    
    public static void main(String[] args) {
    	List<String> list = new ArrayList<String>();
        list.add("AAAA");
        list.add("BBBB");
        list.add("CCCC");
        List<String> list1 = new ArrayList<String>();
        list1.add("aaa");
        list1.add("bbb");
        list1.add("CCCC");
        list1.add("dddd");
        System.out.println(saveEqual(list, list1));
	}
}
