package com.epaylinks.efps.txs.constants;

import com.epaylinks.efps.common.systemcode.SystemCode;

@SystemCode
public enum FzCode {
	SUCCESS("0000", "请求成功"),
	FAIL("1801", "系统忙，请稍后再试"),
	AUTH_ERROR("1802", "交易权限尚未开通"),
	ORDER_NOT_SUPPORT("1803", "终态交易不允许该操作"),
	SPLIT_ALREADY_SETTLE("1804", "有分账记录已结算,不能修改结算周期"),
	;

    public final String code;
	public final String message;
	
	private FzCode(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
	public static String getComment(String code) {
		for (FzCode v : FzCode.values()) {
			if (v.code.equalsIgnoreCase(code))
				return v.message;
		}
		return null;
	}
	
	public static FzCode getTxsCode(String code) {
		for (FzCode v : FzCode.values()) {
			if (v.code.equalsIgnoreCase(code))
				return v;
		}
		return null;
	}

	public String getDespStr() {
		return code + ":" + message;
	}
}
