package com.epaylinks.efps.txs.constants;

import com.epaylinks.efps.common.systemcode.SystemCode;

@SystemCode
public enum TxsCode {
	SUCCESS("0000", "请求成功"),
	FAIL("0901", "系统忙，请稍后再试"),
    ERROR_REPORT_NOT_EXIT("0902", "报表不存在"),
    ERROR_PAYTOKEN_NOT_EXIT("0903", "payToken不存在"),
    ERROR_CACHE("0904","缓存失败"),
    ERROR_PAYEEINFO("0905","收款方客户信息异常"),
    ERROR_PAYERINFO("0906","付款方客户信息异常"),
    ERROR_CUSTINFO("0907","客户信息异常"),
    PAYMETHOD_NOTSUPPORT("0908","不支持支付方式"),
    ERROR_PAYPWD("0909","支付密码异常"),
    TRADEORDER_NOTFOUND("0910","订单不存在"),
    OUTTRADEORDER_DIFFER("0911","外部订单不一致"),
    ERROR_REFUNDFEE("0912","退款金额不正确"),
    CUSTOMERINFO_NOTEXIST("0913","用户信息不存在"),
    BANKNO_BANDCARDTYPE_MUSTEXIST("0914","银行卡必须传入"),
    TRADEORDER_NOTPAY("0915","该订单未支付"),
    ERROR_TOPAY("0916","支付失败"),
    ERROR_REFUND("0917","退款失败"),
    ERROR_FEIGN_SYSTEM("0918","调用子系统异常"),
    CHANNELTYPE_NOTSUPPORT("0919","支付渠道不支持"),
    APPID_MUSTEXIST("0920", "公众号ID必须传入"),
	OPENID_MUSTEXIST("0921", "公众号的用户ID必须传入"),
	OUTREFUNDNO_EXIST("0922", "退款单号已存在"),
	CUSTOMER_ATYPISM("0923", "客户编码不一致"),
	TRANSACTIONTYPE_NOTSUPPORT("0924", "不支持此交易类型"),
	METHOD_HAS_PAYER("0925", "指定了支付方式，付款方必填"),
	ERROR_PARSEDATE("0926", "日期解析错误"),
	START_TIME_OVERSIZE("0927", "交易开始时间偏差过大"),
	NOW_MORETHAN_ENDTIME("0928", "当前时间大于请求中的交易截止时间，不执行交易处理"),
	PAYER_ATTRIBUTE_NOTEXIST("0929", "付款人属性不存在"),
	TOKENPAYER_ORDERPAYER_NOTCONGRUITY("0930", "入参的payer必须与订单中的对应数据相同"),
	TOKENMETHOD_ORDERMETHOD_NOTCONGRUITY("0931", "入参的支付方式必须与订单中的对应数据相同"),
	ORDERMETHOD_ISNOTNULL("0932", "入参的支付方式不能为空"),
	TOKEN_ISNULL("0933", "跳转到收银台的token为空"),
	ORDERAMOUNT_ERROR("0934", "订单金额错误"),
	ERROR_CONTENTTYPE("0935", "	清算系统contentType不存在"),
	CONFIRMPAY_METHODISNULL("0936", "确认支付支付方式不能为空"),
	CONFIRMPAY_PAYERISNULL("0937", "确认支付用户信息不能为空"),
	CONFIRMPAY_AMOUNTISNULL("0938", "确认支付支付金额不能为空"),
	CONFIRMPAY_CURRENCYTYPEISNULL("0939", "金额类型不能为空"),
	TRANSACTIONTYPE_ISNULL("0940", "交易类型不能为空"),
	TRANSACTIONTYPE_ISNOTEXIST("0941", "交易类型不存在"),
	OUTTRADENO_TRANSACTIONNO_ISNULL("0942", "商户订单号和交易订单号必传一个"),
	NOTSUPPORT_ACCOUNTPAY("0943","充值不支持账号支付"),
	NOTIFYURL_MUSTHAS("0944", "异步通知地址必传"),
	OUTTRADENO_MUSTHAS("0945", "商户单号必传"),
	CUSTOMERCODE_MUSTHAS("0946", "客户编码必传"),
	TERMINALNO_MUSTHAS("0947", "终端号必传"),
	CLIENTIP_MUSTHAS("0948", "用户终端IP必传"),
	ORDERINFO_MUSTHAS("0949", "订单信息必传"),
	TRANSACTIONSTARTTIME_MUSTHAS("0950","订单信息开始时间必传"),
	AMOUNT_ERROR("0951", "订单金额必须大于0"),
	AMOUNT_TOLARGE("0952", "订单金额太大"),
	PARSE_JSON("0953", "json字符串转换异常"),
	PAYMENT_TIMEINTERVAL("0955", "确认支付时间间隔过短"),
	TRANSACTIONNO_OUTTRADENO_ALLNULL("0956", "交易单号和外部单号必传一个"),
	USER_NOT_EXIST ("0957", "用户不存在"),
	CUSTOMER_NOT_EXIST ("0958", "客户不存在"),
	LOGIN_FAIL ("0959","登录失败"),
	LOGIN_PWD_ERROR("0960","登录密码错误"),
	PAY_PWD_ERROR("0961","支付密码错误"),
	USER_STAUS_EXCEPTION("0962","用户状态异常"),
	CUMTOMER_STAUS_EXCEPTION("0963","客户状态异常"),
	PASSWORD_EXPIRED("0964","密码过期"),
	VALIDATECODE_ERROR("0965","验证码错误"),
	CUSTOMER_TYPE_ERROR("0966","客户类型错误"),
	USER_AUTH_ERROR("0967","用户权限错误"),
	ERROR_ADDACCOUNT("0968","记账异常"),
	ORDERNOTFOUND("0970","订单找不到，请检查查询单号或客户编码是否正确"),
	CUSTOMER_DIFFER("0971" ,"客户编码不一致"),
	SYSTEMCODE_MUSTHAS("0972" ,"系统编码必传"),
	TRADEPASS_MUSTHAS("0973" ,"提现密码必传"),
	CUSTTYPE_MUSTHAS("0974" ,"客户类型必传"),
	PAYCURRENCY_MUSTHAS("0975" ,"支付币种必传"),
	NOTSUPPORT_CURRENCY("0976", "暂不支持此支付币种"),
	NOTSUPPORT_ARRIVALTYPE("0977", "暂不支持此到账类型"),
	BANKACCNOID_MUSTHAS("0978", "银行账号Id必传"),
	CUSTOMERSETTLEINFO_NOTEXIST("0979", "客户结算信息不存在"),
	SETTLEINFO_CUSTOMERCODEDIFFER("0980", "客户结算信息客户编码不一致"),
	FEIGN_PAYEXCEPTION("0981", "PAY支付子系统异常"),
	NOTIFYURL_FAIL("0982", "异步通知失败"),
	REPEAT_SPLIT("0983", "重复分账"),
	AMOUNT_ATYPISM("0984", "订单金额不一致"),
	SPLITFAIL_SETTLEFAIL("0985", "对应支付订单结算失败导致分账失败"),
	SPLITFAIL_PAYFAIL("0986", "对应支付订单结算失败导致分账取消"),
	SPLITINFOLIST_MUSTHAS("0987", "分账信息必传"),
	NEEDSPLIT_TRUE("0988", "传入了分账信息参数,'是否分账'取值一定要为true"),
	MAINCUSTOMER_NOTFULLAMOUNT("0989", "扣取收单/分账手续费时余额不足"),
	SCENEINFO_MUSTHAS("0990", "微信H5支付场景信息必传"),
	NONCESTR_MUSTHAS("0991", "随机数必传"),
	REPEAT_ORDER("0992", "重复订单"),
	REPEAT_PREORDERSUCCESS("0993", "订单已支付成功，不需重复支付"),
	REPEATPARAM_ATYPISM("0994", "重复请求支付参数不一致"),
	REPEATENDTIME_ERROR("0995", "同一个商户订单号的后续请求的交易结束时间需大于之前请求的交易结束时间"),
	TRANSACTIONENDTIME_ERROR("0996", "小于等于当前时间+24小时且大于等于当前时间+5分钟"),
	APPID_OPENID_MUSTHAS("0997", "appId和openId必传"),
	AMOUNT_MORETHAN_PROCEDUREFEE("0998", "支付金额必须大于手续费"),
	WITHDRAWAL_AMOUNT_ERROR("0999", "提现金额太小"),
	TRADEORDER_NOTSUCCESSPAY("09100", "订单未成功支付"),
	SPLITORDER_NOTFOUND("09101", "分账订单不存在"),
	PAYMETHOD_MUSTHAS("09102", "支付方式必传"),
	ALIJSAPI_PARAMERROR("09103", "支付宝用户号和支付宝账号不能同时为空"),
	UNSUPPORT_RATE_MODE("09104","不支持的手续费类型"),
	UNSUPPORT_RATE_PARAM("09105","不支持的手续费参数"),
	CUSTOMER_MUST_OPEN_BUSINESS("09106","客户未开通该业务"),
	SPLIT_PAY_ORDER_REJECT_REFUND("09107","分账的支付订单不允许退款"),
	ORGI_PAY_ORDRE_UNSUPPORT_FZ("09108","原支付订单不支持分账操作"),
	REPEAT_WITHDRAW("09109","重复提现请求"),
	FILENAME_TYPE_MUSTHAS("09110","文件名和类型必传"),
	BUSINESSPARAM_MAXAMOUNT_ERROR("09111","支付金额大于该业务所支持的单笔最大金额"),
	BUSINESSPARAM_MINAMOUNT_ERROR("09112","支付金额小于该业务所支持的单笔最小金额"),
	QUERY_CONDITION_ISNULL("09113", "查询条件为空"),
	NOTSUPPORT_USERTYPE("09114", "不支持此用户类型"),
	EXPANDNAME_NOTFOUND_EXPANDCUSTOMERCODE("09115", "根据客户名称找不到对应的客户编码"),
	CUSTOMERNAME_CUSTOMERCODE_ATYPISM("09116", "客户名称与客户编码不一致"),
	SUB_CUSTOMERCODE_MUSTHAS("09117", "分账子商户编码不能为空"),
	SUB_CUSTOMERCODE_MUST_OPEN_MATCHED_BFZ("09118","子商户必须开通基于父客户的被分账业务"),
	REMARK_TO_LONG("09119","备注信息过长，最大200字节"),
	ORDER_NOTSUCCESS("09120", "订单未支付成功，不能执行退款"),
	SPLITRECORD_NOTFOUND("09121", "找不到分账记录"),
	REFUND_SUMSPLITAMOUNT_NOT_EQUAL_REFUND("09122","申请退款金额与分账信息退款金额之和不相等"),
	FZORDERREFUND_HAS_SPLITINFO("09123", "分账订单的退款必须后传入分账信息"),
	SPLIT_INFO_MUST_HAVE_ONE_PROCEDURE_CUS("09124","分账信息中必须指定一个手续费扣款方"),
	SHOULD_NOT_INCLUDE_SPLITINTO("09125","原支付单无分账信息，退款中不可包含分账信息"),
	BUSINESS_NOTFOUND("09126", "业务不存在"),
	NEEDSPLIT_RECHARGEMEMCUSTCODE_NOTSAMEEXIST("09127", "分账和会员充值不能同时存在"),
	INSERTORDER_EXCEPTION("09128", "订单插入异常"),
	AMOUNT_MUSTHAS("09129","订单金额必传"),
	SHAREAMOUNT_LARGE_ORDERAMOUNT("09130", "分成总金额大于订单金额"),
	PROCEDURE_LARGE_SHAREAMOUNT("09131", "分成金额的手续费大于或等于分成金额"),
	MEMBERWITHDRAWORDER_EXIST("09132", "会员提现记录已存在"),
	MEMBERINSIDEORDER_EXIST("09133", "会员内转订单已存在"),
	MEMBER_NOTBELONG_MERCHANT("09134", "会员不属于交易商户"),
	SELLING_CANNOT_SHARED("09135", "平仓无法分成"),
	NOTSUPPORT_TERMINAL("09136", "不支持该终端"),
	RECHARGECUSTOMER_NOTEXIST("09137", "不是会员充值业务暂不支持快捷支付"),
	BANKCARDNO_INVALID("09138", "卡号无效"),
	ENCRYPT_DECRYPT_ERROR("09139", "数据解析繁忙"),
	PAY_FAIL("09140", "支付失败"),
	NOT_QUICKPAY("09141", "不是快捷支付"),
	SENDMESSAGE_FAIL("09142", "发送短信失败"),
	QUICKPAYSIGN_FAIL("09143", "快捷签约失败"),
	SENDVALIDATECODE_ERROR("09144", "发送验证码失败"),
	ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL("09145","原支付订单的手续费为空，无法执行分账"),
	SETTMSG_FASTERTHAN_PAYMSG("09146", "结算消息比支付消息先到"),
	BILL_ISNULL("09147", "找不到对账单信息"),
	FILE_UPLOAD_EXCEPTION("09148" , "文件上传失败"),
	BILL_DATE_EXCEPTION("09149" , "生成账单的日期异常"),
	BILL_ISEXIST("09150" , "账单已存在"),
	D0BUSINESS_NOTEXIST("09151", "未开通D0业务"),
	CERTID_ATYPISM("09152","身份证号不一致"),
	ORDER_OVERTIME("09153", "订单不存在或超时"),
	CHECK_BANKCARD_INFO("09154", "请检查银行卡四要素信息是否为空"),
	QUICKPAYAMOUT_EXCEED_DAYLIMIT("09155", "快捷支付超过日交易限额"),
	COMMITPAYPARAM_ERROR("09156", "提交快捷支付参数不能为空"),
	ORDERREPEATANDSUCCESS("09157", "该订单已存在并支付成功"),
	ELEMENT_CHECKERROR("09158", "四要素校验不通过"),
	TUIHUI_RECORD_NOTFOUND("09159", "找不到退汇记录"),
	CALL_ACCTUIHUI_FAIL("09160", "调用账务系统退汇失败"),
	ORDER_TUIHUI_SUCCESS("09161", "订单退汇已退汇成功"),
	BANK_NAME_EMPTY("09162","银行名称不能为空"),
	BANK_USERNAME_EMPTY("09163","开户人姓名不能为空"),
	BANK_ACCOUNTTYPE_EMPTY("09164","银行账户类型不能为空"),
	PARAM_ERROR("09165", "参数错误"),
	NOTSUPPORT_CARDTYPE("09166", "不支持的卡类型"),
	ORDERSTATE_COINCIDENT("09167", "订单状态一致，不需要重复更新"),
	TRANSACTIONNO_MUSTHAS("09168", "交易单号必传"),
	SPLITNOTSUCCESS_NOTREFUND("09169", "未分账成功不能进行退款"),
	ORDER_HAS_PROCESS("09170", "订单已处理，请勿重复操作"),
	FEIGN_RCEXCEPTION("09171", "RC风控子系统异常"),
	CARDTYPE_ERROR("09172", "卡类型不一致"),
	PROTOCOL_NOT_EXIST("09173", "协议号不存在"),
	PROTOCOL_NOT_MATCH_MEMBERID("09174", "协议号和会员编号不匹配"),
	PROTOCOL_NOT_MATCH_CUSTOMER("09175", "协议号和客户编号不匹配"),
	PROTOCOL_INVALID("09176", "协议号无效"),
	CARD_BIN_NOT_EXIST("09177", "卡bin信息未找到"),
    NOT_MATCH_MEMBERID("09178", "分账子商户编码和memberId不一致，或分账子商户信息不存在"),
    EXCEED_MAX_PROFIT("09179", "分账主体的分账比例不符"),
    NOT_MATCH_REAL_NAME("09180", "签约资料信息与银行卡信息不一致"),
    CUSTOMER_NOT_MARRY_PARENT("09181", "客户编号和父编号不匹配"),
    SERVICE_CUSTOMER_ERROR("09182", "服务商号不正确"),
	FAIL_REQUEST_CUST("09183", "查询报备情况失败"),
	BANK_CARD_NOT_REG("09184", "银行卡未报备"),
	EXCEPTION_BANKCARD_QUERY("09185", "银行卡报备查询异常"),
	ORDER_CLOSE("09186", "订单已关闭"),
	ORDER_CANCEL("09187", "订单已撤销"),
	ORDER_REFUND("09188", "订单已退款"),
	ORDER_SUCCESS("09189", "订单已支付"),
	RC_NOTPASS("09190", "交易受限"),
	MERCHANTCODE_NOTEXIST("09191", "子商户上游机构号不存在"),
	MERCHANT_BUSINESS_CODE_EXPIRED("09192", "该商户的业务在有效期外"),
	ONLY_CHARGE_BY_PER("09193", "仅支持按单笔收费"),
	NOT_ALLOW_REFUND("09194", "该订单对应的业务不允许退款"),
	NOT_SUPPORT_TRADETYPE("09195", "不支持此交易类型"),
	LACK_BUSINESS_PARAM("09196", "缺少业务参数"),
	SUBMERCHANTCODE_NOT_MATCH("09197", "上游机构子商户不匹配"),
	PROTOCOL_CANNOT_PAY("09198", "提现协议号不允许交易"),
	WITHDRAW_PASS_CHECKFAIL("09199", "校验提现密码失败"),
	FULLWITHDRAW_ONLY_SUPPORT_DEBITCARD("09200","全额提现只支持借记卡"),
	FULLWITHDRAW_ONLY_ERROR_PROCEDURE("09201","全额提现手续费处理出错"),
	OUTCANCELNO_MUST_HAS("09202","客户撤销单号必传"),
	SPLIT_NOT_MATCH_MEMBERID("09203", "被分账会员与交易会员不一致"),
	SPLIT_NOT_MATCH_CUSTOMER("09204", "被分账商户不合法"),
	OUT_SPLIT_SIZE("09205", "超出单次最大分账数量"),
	AVAILABLE_BALANCE_ZERO("09206", "账户余额为0"),
	WITHDRAW_AMT_ZERO("09207", "可代付金额为0"),
	MAX_PROFIT_EMPTY("09208", "最高分润比例未设置"),
	NOT_ALLOW_CANCEL("09209", "原交易不允许撤销,请查询原交易状态"),
	CUSTOMER_NOT_PLAT("09210", "当前商户非平台商户"),
	SPLIT_RATIO_ERROR("09211", "分账比例之和必须为100%"),
	SPLIT_RELATION_BELONG_ERROR("09212", "分账关系号不属于当前商户"),
	SPLIT_RELATION_ID_EMPTY("09213", "分账关系号不能为空"),
	SPLIT_RELATION_ID_NOT_SAME("09214", "分账关系号不一致"),
	SPLIT_RELATION_NOT_EXIST("09215", "分账关系不存在"),
	SERVICE_FEE_REQUIRED_ERROR("09216", "主商户不允许收取服务费"),
	SERVICE_FEE_MAX_ERROR("09217", "服务费超出最大限制"),
	SERVICE_FEE_MIN_ERROR("09218", "服务费不能小于0"),
	EXCEED_MAX_PROFIT_AMOUNT("09219", "超过最大分润金额"),
	AVAILABLE_BALANCE_NOTENOUGH("09220", "账户余额不足"),
	BANK_NO_EMPTY("09221", "联行号不能为空"),
	PARAM_LACK("09222", "缺少参数"),
	ORDER_ALREADY_REFUND("09223", "原交易已退款成功或失败"),
	FAIL_GET_PAS_MOBILE("09224", "获取手机号失败"),
	NOT_SUPPOR_ONLINE_REFUND_CONFIRM("09225", "不支持线上交易手工置退款成功"),
	NOT_ALLOW_RETURN("09226", "原交易非正向交易不允许退货"),
	REFUND_AMT_LARGEER("09227", "退货金额大于原交易金额"),
	TRADEORDER_NOT_INCOME_ACC("09228", "订单未入账"),
	ORIG_TRAN_CANCELED("09229", "原交易已由pos撤销"),
	ORIG_ORDER_DONE("09230", "原交易已完成,请重新下单"),
	FZ_MUST_FULL_AMT_REFUND("09231", "分账失败的交易必须全额退货"),
	FZ_PROCESSING_NOT_ALLOW_REFUND("09232", "分账进行中不允许退货,请稍后再发起退货"),
	SPLIT_MAIN_ERROR("09233", "分账主体商户不符"),
	SPLIT_MAIN_EMPTY("09234", "分账主体商户不能为空"),
	MEMBER_ID_EMPTY("09235", "会员编号不能为空"),
	SPLIT_MAIN_UNSUPPORT("09236", "分账主体商户不合法"),
	PLAT_CUSTOMERCODE_EMPTY("09237", "平台商户编号为空"),
    NOT_OPEN_TRANS_FZ_BUSINESS("09238", "尚未开通交易分账业务"),
	NOT_OPEN_FZ_MODEL("09239","暂未开通该分账模式"),
	SPLIT_PROCEDURE_CUSTOMER_EMPTY("09240","分账手续费扣除商户有且只能有一个"),
	SPLIT_REAL_REFUND_AMT_BELOW_ZERO("09241","有分账商户应退金额为负数,必须全额退款!"),
	SPLIT_ANMOUT_GREATER_THAN_COMMODITY_AMOUNT("09242", "分账总金额大于商品总金额"),
	TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE("09243", "交易账户可用余额不足"),
	FAIL_GET_USERID("09244", "银联JS获取授权ID失败"),
	GET_USER_ID_CHANNEL_NOT_SET("09245", "平台未配置好参数"),
	NOT_FACE_DISCERN("09246", "提现失败，需交易人脸确认"),
	QRCODE_CUST_NOT_EXIST ("09247", "码牌商户不存在"),
	QRCODE_TERM_NOT_EXIST ("09248", "码牌终端不存在"),
	TERM_NOT_VALID ("09249", "终端未启用"),
	QRCODE_NOT_EXIST ("09250", "码牌不存在"),
	COMMODITY_ORDERNO_REPEAT("09251", "商品订单号重复"),
	TERM_NEED_APPROVAL("09252", "终端待审核"),
	NOT_SUPPORT_DZ("09253", "不支持垫资"),
	BANK_CARD_NOT_SETCARD("09254", "非结算卡"),
	CHECK_FLOAT_CHARGE_ERROR("09255", "检查附加费用设置出错"),
	SERVICE_FEE_TRANSACTION_ERROR("09256", "服务费订单不存在"),
	LOCATION_EMPTY("09257", "位置信息为空"),
	LOCATION_UNSUPPORT("09258", "当前位置存在交易风险，详情请咨询商家"),
	LOCATION_NOT_MATCH("09259", "位置信息与订单不符"),
	LOCATION_NOT_AUTH("09260", "尚未完成授权"),
	BROWSER_NOT_SURPPORT("09261", "不支持该方式，请使用其他app扫码"),
	USERID_NULL("09262", "获取支付授权信息失败，请重新扫码"),
	BUSINESS_AND_CARDTYPE_MUSTHAS("09263", "二次计费业务及卡种类必传"),
	SECOND_CHARGE_NONEED("09264", "无须二次计费"),
	CARD_TYPE_ERR("09265", "卡类型错误"),	
	CUSTOMER_NOT_SUPPORT_FUKUAN("09266", "该商户不支持付款"),
	NOT_SUPPORT_RECHARGE_PRODUCE("09267", "交易成功订单不允许重新计算手续费"),
	COUPON_MUST_FULL_AMT_REFUND("09268", "有优惠金额必须全额退货"),
	WITHRAW_TYPE_NOT_DEFINED("09269", "代付接口类型未定义"),
	ORI_ORDER_TIME_OUT_CANCEL("09270","原订单超出可撤销时间"),
	ENTRUST_START_TIME_ERROR("09271","交易时间小于代收起始时间"),
	ENTRUST_TIME_EXPIRE("09272","代收生效时间已过期"),
	ENTRUST_FREQUENCY_LIMIT("09273","代收扣款次数已达上限"),
	STATE_NOT_ALLOW_SPLIT("09274","当前状态不允许执行分账"),
	PRODUCE_CUSTOMER_ERROR("09800","手续费扣除商户权限不足"),//改到这里的时候发现txs与FZ的code重了.谨慎起见先不改.先起一个大一点的值
	MULTI_SPLIT_NOT_FIRST("09801","拆单分账非第一次分账,无需再指定手续费扣除方"),
	AMOUNT_EXCEED("09802", "分账金额超额"),
	SPLIT_SETTLE_CYCLE_ERROR("09803", "分账列表结算周期错误"),
	MULTI_SPLIT_MUST_NEW("09804", "拆单分账请按新模式传参"),
	MUST_NOT_SPLIT_NO("09805", "非拆单分账请勿传商户拆单单号"),
	MUST_SPLIT_NO("09806", "拆单分账必须填商户拆单单号"),
	SPLIT_RETURN_SAME_TIME("09807","退货与分账不能同时进行,请稍后再操作"),
	ZHFZ_CONFIRTM_SPLIT_INFO_DIFF("09808","分账列表与原来不一致!"),
	ALREADY_ZHFZ_CONFIRMED("09809","已有确认分账记账记录!不能执行此操作!"),
	//后面的,请FZ加CODE
	
	;

    public final String code;
	public final String message;
	
	private TxsCode(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
	public static String getComment(String code) {
		for (TxsCode v : TxsCode.values()) {
			if (v.code.equalsIgnoreCase(code))
				return v.message;
		}
		return null;
	}
	
	public static TxsCode getTxsCode(String code) {
		for (TxsCode v : TxsCode.values()) {
			if (v.code.equalsIgnoreCase(code))
				return v;
		}
		return null;
	}

	public String getDespStr() {
		return code + ":" + message;
	}
}
