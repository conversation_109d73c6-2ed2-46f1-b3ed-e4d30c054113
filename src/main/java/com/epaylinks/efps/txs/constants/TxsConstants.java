package com.epaylinks.efps.txs.constants;

import java.util.HashMap;
import java.util.Map;

import com.epaylinks.efps.common.util.Constants;
/**
 * 
 * <AUTHOR>
 */
public interface TxsConstants extends Constants {
    
    enum TxsRechargeReturnCode {
		
		DETAIL_SUCCESS ("0000", "处理成功"),
		DETAIL_FAIL ("0001",   "处理失败");
		
		public final String code;
		public final String comment;
		TxsRechargeReturnCode(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
    
    enum FenZhangReturnCode {
		
		DETAIL_SUCCESS ("0000", "处理成功"),
		DETAIL_FAIL ("0001",   "处理失败");
		
		public final String code;
		public final String comment;
		FenZhangReturnCode(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
    
    /**
     * 收银台 Token校验
     */
    enum CashierVerify {
		
		OVER_TIME ("0", "超时"),
		TOKEN_INVALID ("1",   "token校验失败"),
    	UNSUPPORT_USER_AGENT("2","您的终端设备太独特,我们无法识别"),
    	UNSUPPORT_SCENE("3","不走寻常路的您走到了我们未能预见的程序路径,请马上跟我们联系,大侠");
		
		public final String code;
		public final String comment;
		CashierVerify(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		public String getDescStr()
		{
			return code+":"+comment;
		}
	}
    
    
    
    /**
     * 重复支付状态
     */
    enum RepayState {

		NOT_REPAY_ORDER("0", "非重复支付订单"),
		REPAY_ORDER("1", "重复支付订单"),
		REPAY_ORDER_IN_PROCESS("2", "重复支付处理中"),
		REPAY_ORDER_FINISH("3", "重复支付处理完成");

		public final String code;
		public final String message;
		RepayState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 预下单状态
     */
    enum PreOrderState {

    	IN_PROCESS("0", "处理中"),
		TRANSACTION_SUCCESS("1", "交易成功"),
		TRANSACTION_FAIL("2", "交易失败"),//非终态
		OVERTIME_FAIL("3", "订单超时失败"),
		WAIT_TOPAY("4", "待支付"),
		CANCEL("5", "已撤销"),
		CLOSE("6", "已关闭");

		public final String code;
		public final String message;
		PreOrderState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    /**
     * 支付状态
     */
    enum PayState {

		SUCCESS("00", "支付成功"),
		FAIL("01", "支付失败"),
		NOT_PROCESS("02", "未处理"),
    	IN_PROCESS("03", "处理中"),
    	CANCEL("05", "已撤销"), 
    	CLOSE("06", "已关闭");

		public final String code;
		public final String message;
		PayState(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		private static final Map<String, PayState> CODE_MAP = new HashMap<String, PayState>();
		
		static {
	        for (PayState typeEnum : PayState.values()) {
	            CODE_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    
	    public static PayState getEnum(String typeName) {
	        return CODE_MAP.get(typeName);
	    }
	}
    
//    /**
//     * 支付系统发送的支付订单结果消息类型
//     * <AUTHOR>
//     *
//     */
//    enum PayResultMsgType {
//
//    	GATEWAY_PAYMENT_RESULT_MSG("ZF", "网关支付结果消息"),
//    	FZ_GATEWAY_PAYMENT_RESULT_MSG("FZ", "分账网关支付结果消息"),
//    	REHARGE_RESULT_MSG("CZ", "充值结果消息"),
//    	WITHDRAWALS_RESULT_MSG("TX", "提现结果消息"),
//    	REFUND_RESULT_MSG("TK", "退款结果消息"),
//    	FFZJY_RESULT_MSG("FFZJY" , "反分账交易"),
//    	HYCZ_RESULT_MSG("HYCZ", "会员充值"),
//    	HYNZ_RESULT_MSG("HYNZ", "会员内转");
//
//		public final String code;
//		public final String message;
//		PayResultMsgType(String code, String message) {
//			this.code = code;
//			this.message = message;
//		}
//	}
    
    /**
     * 支付系统发送的支付订单结果消息类型
     * <AUTHOR>
     *
     */
    enum AsynNotifyResult {

    	SUCCESS("1", "通知成功"),
    	NOTIFYING("0", "通知中"),
    	FAILED("2", "通知失败");
		public final String code;
		public final String message;
		AsynNotifyResult(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 结算状态
     */
    enum SettleMentState {

		SUCCESS("00", "结算成功"),
		FAIL("01", "结算失败"),
		NOT_PROCESS("02", "未结算");

		public final String code;
		public final String message;
		SettleMentState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 退款状态
     */
    enum RefundState {

		SUCCESS("00", "退款成功"),
		FAIL("01", "退款失败"),
		PENDING("02", "未退款"),
		IN_PROCESS("03", "处理中（网关支付待网银上游通知）");

		public final String code;
		public final String message;
		RefundState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 提现状态
     */
    enum WithdrawState {

    	SUCCESS("00", "成功"), 
    	FAIL("01", "失败"), 
    	PENDING("02", "未支付"), 
    	IN_PROCESS("03", "处理中");

		public final String code;
		public final String message;
		WithdrawState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 分账订单状态
     */
/*	解决初插时允许退货:
	fz:
	初插入数据的时候,一律使用初始化.
	进行处理的时候,将初始化51更新为未处理03,更新条数为1再往下执行. 
	
	txs:
	退货的时候,成功,失败,或者初始化都可以退货.如果是初始化(初始化时,必定是全额退货),则先更改为已撤销再进行退货。*///2021-09-08
    enum SplitOrderState {
    	INIT("51", "初始化"),	//
    	SUCCESS("00", "成功"),
    	FAIL("01", "失败"),
    	//CANCEL("02", "取消，对应支付交易支付失败会导致分账订单变成取消状态"),	//没有使用 的,生产上也没有,注释掉.免得混洧视听
    	UNEXECUTED("03", "未执行"),
		PRE_SUCCESS("04", "预分账成功"),
		REVOKE("05","已撤销");
    	//ROLLBACK("04", "已回滚");//退款分账订单会存在回滚状态，即反分账交易成功，退款失败,; 没有使用 的,生产上也没有,注释掉.免得混洧视听

		public final String code;
		public final String message;
		SplitOrderState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 分账记录状态
     */
    enum SplitRecordState {
    	UNEXECUTED("1", "未执行"),
    	FAIL("2", "分账失败"),
    	SUCCESS("3", "分账成功"),
		REVOKE("4","已撤销");

		public final String code;
		public final String message;
		SplitRecordState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
	enum VoucherStatus {
		SUCCESS(1, "成功"),
		ROLBACK(2, "已回滚"),
		REVOKE(3, "已撤销");

		public final Integer code;
		public final String message;
		VoucherStatus(Integer code, String message) {
			this.code = code;
			this.message = message;
		}
	}



	enum SplitType {
		common_split("0", "普通分账"),
		multy_split("1", "分步分账");

		public final String code;
		public final String message;
		SplitType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    
    /**
     * 会员内转状态，会员分成状态
     */
    enum InsideState {
    	SUCCESS("00", "成功"),
    	FAIL("01", "失败"),
    	CANCEL("02", "取消，对应交易支付失败会导致会员内转订单变为取消状态"),
    	UNEXECUTED("03", "未执行");

		public final String code;
		public final String message;
		InsideState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 提现退汇状态
     */
    enum WithDrawTuihuiState{
    	
    	NOTEXECUTED("0", "未退汇"),
    	SUCCESS("1", "退汇成功"),
    	FAIL("2", "退汇失败");
    	
    	public final String code;
		public final String message;
		WithDrawTuihuiState(String code, String message) {
			this.code = code;
			this.message = message;
		}
    }
    
    /**
     * 退汇状态
     */
    enum TuihuiState{
    	
    	SUCCESS("00", "退汇成功"),
    	FAIL("01", "退汇失败"),
    	INIT("02", "退汇初始化");
    	
    	public final String code;
		public final String message;
		TuihuiState(String code, String message) {
			this.code = code;
			this.message = message;
		}
    }
    
    /**
     * 兑换状态
     */
    enum ExchangeState {
    	SUCCESS("00", "成功"),
    	FAIL("01", "失败"),
    	NOTPAY("02", "未支付"),
    	IN_PROCESS("03", "处理中"),
    	ROLLBACK("04", "已回滚"),
    	ROLLBACKFAIL("05", "回滚失败");

		public final String code;
		public final String message;
		ExchangeState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 兑换状态
     */
    enum NeedPersonalTradeState {
    	PENDING("0", "待处理"),
    	PROCESSED("1", "已处理");

		public final String code;
		public final String message;
		NeedPersonalTradeState(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    
    /**
     * 分账记录状态
     */
    enum PayType {
    	INSIDEPAY("1", "内转"),
    	RECHARGE("2", "充值"),
    	WITHDRAWAL("3", "提现"),
    	REFUND("4", "退款");

		public final String code;
		public final String message;
		PayType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 渠道类型
     */
    enum WithdrawSourceType {

    	AUTOWITHDRAW("AutoWithdraw", "自动提现"),
    	PERSONALWITHDRAW("PersonalWithdraw", "手动提现"),
    	MEMBERWITHDRAW("MemberWithdraw", "会员卡提现触发"),
    	D0AUTO("D0Auto", "D0代付-自动业务触发");

		public final String code;
		public final String message;
		WithdrawSourceType(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		public static String getMessageByCode(String code) {
			WithdrawSourceType[] withdrawSourceTypes = WithdrawSourceType.values();
			for (WithdrawSourceType withdrawSourceType : withdrawSourceTypes) {
				if (withdrawSourceType.code.equals(code)) {
					return withdrawSourceType.message;
				}
			}
			return null;
		}
	}
    
    /**
     * 渠道类型
     */
    enum ChannelType {

		INTERNET("01", "互联网"),
		MOBILE_TERMINAL("02", "移动"),
		SERVICE("03", "服务端直接发起");

		public final String code;
		public final String message;
		ChannelType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}

    enum CardType {

    	/**
    	 * 借记卡
    	 */
    	DEBIT("debit", "借记卡"),
    	/**
    	 * 贷记卡
    	 */
    	CREDIT("credit", "贷记卡"),
    	/**
    	 * 存折
    	 */
    	BANKBOOK("bankBook", "存折");

		public final String code;
		public final String message;
		CardType(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		public static String getCardType(String code) {
			CardType[] cardTypes = CardType.values();
			for (CardType cardType : cardTypes) {
				if (cardType.code.equals(code)) {
					return cardType.message;
				}
			}
			return null;
		}
	}
    
    enum BankAccType {

    	/**
    	 * 借记卡
    	 */
    	personal_bank_card("1", "对私银行卡"),
    	/**
    	 * 存折
    	 */
    	bank_book("2", "存折"),
    	/**
    	 * 对公卡
    	 */
    	commpany_bank_card("3", "对公卡");

		public final String code;
		public final String message;
		BankAccType(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		public static String getCardType(String code) {
			CardType[] cardTypes = CardType.values();
			for (CardType cardType : cardTypes) {
				if (cardType.code.equals(code)) {
					return cardType.message;
				}
			}
			return null;
		}
	}
    
    
    enum TransactionType {

		/*GATEWAY("001", "网关支付"),
		CONTACT("002", "协议支付"),
		ACCOUNT("014", "账号支付"),
		AGENTPAY("003", "代付"),
		WXPAY("008", "微信支付");*/
    	HYCZ("HYCZ" , "会员充值"),
    	CZ("CZ" , "充值"),
    	ZF("ZF" , "支付"),
    	FZ("FZ" , "分账"),
		SF("SF","服务费"),
		KJ("KJ","跨境资金划拨"),
		JQ("JQ" , "鉴权"),
		OCR("OCR" , "OCR");

		public final String code;
		public final String message;
		TransactionType(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		public static String getTransactionTypeName(String code) {
			TransactionType[] transactionTypes = TransactionType.values();
			for (TransactionType transactionType : transactionTypes) {
				if (transactionType.code.equals(code)) {
					return transactionType.message;
				}
			}
			return null;
		}
	}
    
    
    /**
     * 支付方式类型
     */
//    enum MethodType {
//    	ACCOUNT_PAY("0", "账号支付"),
//    	WECHAT_PUBLIC ("1", "微信公众号支付"),
//    	PERSON_GATEWAY_LOAN("2", "个人网银_贷记卡"),
//		PERSON_GATEWAY_DEBIT("3", "个人网银_借记卡"),
//		ALIPAY_LIFE("4", "支付宝生活号支付"),
//		AGENTPAY("5", "代付"),
//		WXSWEEPCODE("6", "微信扫码支付"),
//		ALISWEEPCODE("7", "支付宝扫码支付"),
//		QUICK_PAY("8", "快捷支付"),
//		WXAPP_PAY("9", "微信app支付"),
//		WXH5_PAY("10", "微信H5支付"),
//		//SPLIT_PAY("11", "分账"),
//		//SPLITED_PAY("12", "被分账"),
//		WECHAT_SCANINGPAY("13", "微信被扫"),
//		ALI_SCANINGPAY("14", "支付宝被扫"),
//		UNION_PAY("20", "手机银联支付");
//
//		public final String code;
//		public final String message;
//		MethodType(String code, String message) {
//			this.code = code;
//			this.message = message;
//		}
//		
//		/**
//		 * 返回收单支付方式
//		 * @return
//		 */
//		public static List<String> getMethod(){
//			List<String> methods = new ArrayList<>();
//			methods.add(MethodType.ACCOUNT_PAY.code);
//			methods.add(MethodType.WECHAT_PUBLIC.code);
//			methods.add(MethodType.PERSON_GATEWAY_LOAN.code);
//			methods.add(MethodType.PERSON_GATEWAY_DEBIT.code);
//			methods.add(MethodType.ALIPAY_LIFE.code);
//			methods.add(MethodType.AGENTPAY.code);
//			methods.add(MethodType.WXSWEEPCODE.code);
//			methods.add(MethodType.ALISWEEPCODE.code);
//			methods.add(MethodType.QUICK_PAY.code);
//			methods.add(MethodType.WXAPP_PAY.code);
//			methods.add(MethodType.WXH5_PAY.code);
//			methods.add(MethodType.UNION_PAY.code);
//			return methods;
//		}
//	}
    
    /**
     * 付款方类型
     */
    enum PayerType {
    	QUICK_PAY("quickPay", "快捷支付"),
    	EPAYLINKS_PAY("epaylinksPay", "易票联支付，内转"),
    	GATEWAY_PAY("gatewayPay", "网银支付"),
    	ALILIFE_PAY("alipayLifePay", "支付宝生活号支付"),
    	ALIMAINSACN_PAY("aliMainScanPay", "支付宝主扫"),
    	WXMAINSACN_PAY("wxMainScanPay", "微信主扫"),
    	WXSUBSCRIPTIONTRADER_PAY("wxSubScriptionPay", "微信公众号支付"),
    	WXH5_PAY("wxh5Pay", "微信h5支付"),
		WXAPP_PAY("wxappPay", "微信app支付");
		public final String code;
		public final String message;
		PayerType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    enum CurrencyType {

		CNY("CNY", "人民币"),
		VGG("VGG", "广州贵金属虚拟币");

		public final String code;
		public final String message;
		CurrencyType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    enum ContentType {

		FORM("HTML", "form表单"),
		QRCODE("QRCODE","二维码"),
		WECHAT_PAY("JSAPI", "微信公众号"),
		QUICKPAY("QUICKPAY", "快捷支付"),
    	ALIJSAPI("ALIJSAPI" , "支付宝jsapi支付"),
    	REDIRECTURL("REDIRECT_URL", "微信H5支付");

		public final String code;
		public final String message;
		ContentType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
	enum UserType {
		BUSINESS_USER("0", "运营用户"), 
		CUSTOMER_USER("1", "客户用户"),
		SERVICE_USER("3", "服务端发起");

		public final String code;
		public final String comment;

		UserType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	enum CustomerType {
		PAYEE("0", "收款方"), 
		PAYER("1", "付款方");

		public final String code;
		public final String comment;

		CustomerType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
		
	/**
	 * 到账类型
	 */
	enum ArrivalType {
		REALTIME ("0", "实时到账"), 
		T1("1", "T+1到账"),
		D0("2", "D0到账");
		
		public final String code;
		public final String comment;
		
		ArrivalType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		public static String getCommentByCode(String code) {
			ArrivalType[] arrivalTypes = ArrivalType.values();
			for (ArrivalType arrivalType : arrivalTypes) {
				if (arrivalType.code.equals(code)) {
					return arrivalType.comment;
				}
			}
			return null;
		}
	}
	
	/**
	 * 发送信息类型
	 */
	enum MessageType {
		PHONE("phone","手机号"),
		EMAIL("email","邮箱"),
		TxsQuickPay("TXS-QuickPay", "交易系统：快捷支付"),
		CumQuickPaySign("CUM:QuickPaySign", "客户系统：快捷签约");
		
		public final String code;
		public final String comment;
		
		MessageType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	enum asynNotifyOrderType{
		ZF("01" , "支付"),
		TK("02" , "退款"),
		TX("03" , "提现"),
		FZ("04" , "分账"),
		CHANGE_CHECK("05","修改审核");
		public final String code;
		public final String comment;
		
		asynNotifyOrderType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	
	/**
	 * 客户信息状态
	 */
	enum CumInfoStatus {
		Abnormal("0", "无效"),
	    Normal("1", "有效");
		
		public final String code;
		public final String comment;
		
		CumInfoStatus(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	/**
	 * 客户费率模式 
	 * @date 2018年2月23日 上午11:22:48
	 */
	enum CumRatioMode {

        Single("1", "单笔固定费率"),
        Rate("2", "按交易金额比例");

        public final String code;
        public final String comment;

        CumRatioMode(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
	
	/**
	 * 被扫场景信息 
	 * @date 2018年3月6日 上午8:56:39
	 */
	enum ScaningScene {
		barCode("bar_code", "条码支付"),
		waveCode("wave_code", "声波支付"),
		faceCode("face_code", "人脸识别支付");
		
		public final String code;
        public final String comment;
        
        ScaningScene(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
	}
	
	/**
	 * 退款来源类型
	 */
	enum RefundSourceType {
		RepayRefund("RepayRefund", "重复支付退款"),
		MerchantApply("MerchantApply", "商户申请退款");
		
		public final String code;
        public final String comment;
        
        RefundSourceType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
	}
	
	enum PayBusiness {
		YMF("YMF", "一码付"), 
		AlIJSAPI("AliJSAPI", "支付宝生活号"),
		AlIMICRO("AliMicro", "支付宝被扫"),
		WXMICRO("WxMicro", "微信被扫"),
		AlINATIVE("AliNative", "支付宝主扫"),
		QUICKPAY("QuickPay", "快捷支付"),
		WXMWEB("WxMWEB", "微信H5支付"),
		WXJSAPI("WxJSAPI", "微信公众号支付"),
		WXNATIVE("WxNatvie", "微信主扫"),
		SAVINGCARDPAY("SavingCardPay", "网银支付")	,
		UNIONSWEEP("UnionSweep", "银联收单二维码主扫"),
		UNIONQRCODE("UnionQrcode", "银联收单二维码被扫"),
		FZ_SavingCardPay("FZ-SavingCardPay", "分账网银支付"),
		FZ_ALIJSAPI("FZ-AliJSAPI", "分账-支付宝生活号"),
		FZ_ALIMICRO("FZ-AliMicro" , "分账-支付宝被扫"),
		FZ_WXMICRO("FZ-WxMicro", "分账-微信被扫"),
		FZ_ALINATIVE("FZ-AliNative", "分账-支付宝主扫"),
		FZ_QUICKPAY("FZ-QuickPay", "分账-快捷支付"),
		FZ_WXMWEB("FZ-WxMWEB", "分账-微信H5支付"),
		FZ_WXJSAPI("FZ-WxJSAPI", "分账-微信公众号支付"),
		FZ_WXNATVIE("FZ-WxNatvie", "分账-微信主扫"),
		FZ_UNIONSWEEP("FZ-UnionSweep", "分账-银联收单二维码主扫"),
		FZ_UNIONQRCODE("FZ-UnionQrcode", "分账-银联收单二维码被扫"),
		RECHARGE("Recharge", "充值"),
		MEMBER_RECHARGE_ALIJSAPI("MemberRecharge-AliJSAPI", "会员充值-支付宝生活号"),
		MEMBER_RECHARGE_ALIMICRO("MemberRecharge-AliMicro", "会员充值-支付宝被扫"),
		MEMBER_RECHARGE_WXMICRO("MemberRecharge-WxMicro", "会员充值-微信被扫"),
		MEMBER_RECHARGE_ALINATIVE("MemberRecharge-AliNative", "会员充值-支付宝主扫"),
		MEMBER_RECHARGE_QUICKPAY("MemberRecharge-QuickPay", "会员充值-快捷支付"),
		MEMBER_RECHARGE_WXMWEB("MemberRecharge-WxMWEB", "会员充值-微信H5支付"),
		MEMBER_RECHARGE_WXJSAPI("MemberRecharge-WxJSAPI", "会员充值-企业网银"),
		MEMBER_RECHARGE_WXNATIVE("MemberRecharge-WxNatvie" , "会员充值-微信主扫"),
		MEMBER_RECHARGE_UNION("MemberRecharge-Union" , "会员充值-企业网银"),
		MEMBER_RECHARGEUNIONSWEEP("MemberRecharge-UnionSweep", "会员充值-银联收单二维码主扫"),
		MEMBER_RECHARGEUNIONQRCODE("MemberRecharge-UnionQrcode", "会员充值-银联收单二维码被扫");
		public final String code;
		public final String comment;
		PayBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, PayBusiness> BUSINESS_MAP = new HashMap<String, PayBusiness>();

	    static {
	        for (PayBusiness typeEnum : PayBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static PayBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum FZTKBusiness {
		FZ_REFUND_USING_FLOAT("FzRefundUsingFloat", "分账_在途金额退款"),
		FZ_REFUND_USING_AVAILABLE("FzRefundUsingAvailable", "分账_可用金额退款");
		public final String code;
		public final String comment;
		FZTKBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, FZTKBusiness> BUSINESS_MAP = new HashMap<String, FZTKBusiness>();

	    static {
	        for (FZTKBusiness typeEnum : FZTKBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static FZTKBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum BFZTKBusiness {
		BFZ_REFUND_USING_FLOAT("BfzRefundUsingFloat", "被分账_在途金额退款"),
		BFZ_REFUND_USING_AVAILABLE("BfzRefundUsingAvailable", "被分账_可用金额退款");
		public final String code;
		public final String comment;
		BFZTKBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, BFZTKBusiness> BUSINESS_MAP = new HashMap<String, BFZTKBusiness>();

	    static {
	        for (BFZTKBusiness typeEnum : BFZTKBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static BFZTKBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum TKBusiness {
		REFUND_USING_FLOAT("RefundUsingFloat", "支付_在途金额退款"),
		REFUND_USING_AVAILABLE("RefundUsingAvailable", "支付_可用金额退款"),
		FZ_PAY_REFUND_USING_AVAILABLE("FzPayRefundUsingAvailable", "分账支付_可用金额退款");
		public final String code;
		public final String comment;
		TKBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, TKBusiness> BUSINESS_MAP = new HashMap<String, TKBusiness>();

	    static {
	        for (TKBusiness typeEnum : TKBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static TKBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum WithDrawBusiness {
		WITHDRAW("Withdraw", "代付");
		public final String code;
		public final String comment;
		WithDrawBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, WithDrawBusiness> BUSINESS_MAP = new HashMap<String, WithDrawBusiness>();

	    static {
	        for (WithDrawBusiness typeEnum : WithDrawBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static WithDrawBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum FZBusiness {
		FZ("FZ", "分账");
		public final String code;
		public final String comment;
		FZBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, FZBusiness> BUSINESS_MAP = new HashMap<String, FZBusiness>();

	    static {
	        for (FZBusiness typeEnum : FZBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static FZBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum BFZBusiness {
		BFZ("BFZ", "被分账");
		public final String code;
		public final String comment;
		BFZBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, BFZBusiness> BUSINESS_MAP = new HashMap<String, BFZBusiness>();

	    static {
	        for (BFZBusiness typeEnum : BFZBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static BFZBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum DLBusiness {
		DL("DL", "代理");
		public final String code;
		public final String comment;
		DLBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, DLBusiness> BUSINESS_MAP = new HashMap<String, DLBusiness>();

	    static {
	        for (DLBusiness typeEnum : DLBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static DLBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum MemberInsideBusiness {
		MEMBER_INSIDE_PAY("MemberInsidePay" , "会员内转");
		public final String code;
		public final String comment;
		MemberInsideBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, MemberInsideBusiness> BUSINESS_MAP = new HashMap<String, MemberInsideBusiness>();

	    static {
	        for (MemberInsideBusiness typeEnum : MemberInsideBusiness.values()) {
	        	BUSINESS_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    public static MemberInsideBusiness getEnum(String code) {
	        return BUSINESS_MAP.get(code);
	    }
	}
	
	enum ExchangeBusiness {
		MEMBER_EXCHANGE_IN("MemberExchangeIn" , "会员兑换转入业务"),
		MEMBER_EXCHANGE_OUT("MemberExchangeOut" , "会员兑换转出业务"),
		MEMBER_OPPOSITE_EXCHANGE_IN("MemberOppositeExchangeIn" , "会员反兑换转入业务"),
		MEMBER_OPPOSITE_EXCHANGE_OUT("MemberOppositeExchangeOut" , "会员反兑换转出业务");
		public final String code;
		public final String comment;
		ExchangeBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, ExchangeBusiness> BUSINESS_MAP = new HashMap<String, ExchangeBusiness>();
		
		static {
			for (ExchangeBusiness typeEnum : ExchangeBusiness.values()) {
				BUSINESS_MAP.put(typeEnum.code, typeEnum);
			}
		}
		public static ExchangeBusiness getEnum(String code) {
			return BUSINESS_MAP.get(code);
		}
	}
	
	
	enum InsidePayCentBusiness {
		MEMBER_INSIDE_PAY_CENT("MemberInsidePayCent" , "会员内转分成业务");
		public final String code;
		public final String comment;
		InsidePayCentBusiness(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
		
		private static final Map<String, InsidePayCentBusiness> BUSINESS_MAP = new HashMap<String, InsidePayCentBusiness>();
		
		static {
			for (InsidePayCentBusiness typeEnum : InsidePayCentBusiness.values()) {
				BUSINESS_MAP.put(typeEnum.code, typeEnum);
			}
		}
		public static InsidePayCentBusiness getEnum(String code) {
			return BUSINESS_MAP.get(code);
		}
	}
	
	 /**
     * 退款处理状态
     */
    enum OprState {

		SUCCESS("00", "已退款"),
		NOT_PROCESS("01", "未处理"),
		FAIL("02", "处理失败"),
		IN_PROCESS("03", "处理中");

		public final String code;
		public final String message;
		OprState(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		private static final Map<String, OprState> CODE_MAP = new HashMap<String, OprState>();
		
		static {
	        for (OprState typeEnum : OprState.values()) {
	            CODE_MAP.put(typeEnum.code, typeEnum);
	        }
	    }
	    
	    public static OprState getEnum(String typeName) {
	        return CODE_MAP.get(typeName);
	    }
	}

    /**
	 * 风控指标
	 *
	 */
	enum RcIndex {
		AMOUNT("amount", "金额指标"), TRADE_NUM("tradeNum", "交易笔数指标");

		public final String code;
		public final String comment;

		RcIndex(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	enum BusinessTagerType{
    	IDENTITY_CARD("001" , "身份证"),
    	CELL_PHONE_NUMBER("002" , "手机号"),
    	BUSINESS_LICENSE("003" , "社会统一信用代码"),
    	BANK_CARD("004" , "银行卡"),
    	CUSTOMER_CODE("005" , "商户编号");
		
    	public final String code;
    	public final String message;
		private BusinessTagerType(String code, String message) {
			this.code = code;
			this.message = message;
		}
		public static boolean contains(String code) {
			BusinessTagerType[] businessTagerTypes = BusinessTagerType.values();
			for (BusinessTagerType businessTagerType : businessTagerTypes) {
				if (businessTagerType.code.equals(code)) {
					return true;
				}
			}
			return false;
		}
    }
	
	/**
	 * 是否禁止使用信用卡
	 * <AUTHOR>
	 */
	enum NoCreditCards {
		TRUE("0", "禁止使用信用卡"), 
		FALSE("1", "不禁止使用信用卡");

		public final String code;
		public final String comment;

		NoCreditCards(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	
	
	  /**
     * 鉴权结果
     */
    enum AuthResult {

		SUCCESS("00", "鉴权成功"),
		FAIL("01", "鉴权失败"),
		INIT("02", "初始化,未进行鉴权");

		public final String code;
		public final String message;
		AuthResult(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}

	/**
	 * 鉴权结果
	 */
	enum OCRResult {

		SUCCESS("00", "OCR成功"),
		FAIL("01", "OCR失败"),
		INIT("02", "初始化,未进行OCR");

		public final String code;
		public final String message;
		OCRResult(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}

	/**
	 * 企业详情核查结果
	 */
	enum EnterpriseDetailResult {

		SUCCESS("00", "查询成功"),
		FAIL("01", "查询失败"),
		INIT("02", "未查询");

		public final String code;
		public final String message;
		EnterpriseDetailResult(String code, String message) {
			this.code = code;
			this.message = message;
		}


	}
    
	  /**
     * 鉴权结果
     */
    enum ElementConsistState {

		YES("0", "要素一致"),
		NO("1", "要素不一致");

		public final String code;
		public final String message;
		ElementConsistState(String code, String message) {
			this.code = code;
			this.message = message;
		}
		

	}	
    
    /**
     * 是否允许退款
     */
    enum isAllowRefund {

		NO("0", "不允许退款"),
		YES("1", "允许退款");

		public final String code;
		public final String message;
		isAllowRefund(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}	
    
    /**
     * 是否允许退回收单手续费
     */
    enum isAllowRefundProcedure {

		NO("0", "不退回收单手续费"),
		YES("1", "退回收单手续费");

		public final String code;
		public final String message;
		isAllowRefundProcedure(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}	
    
    /**
     * 银联交易类型
     */
    enum unionTraderType {

    	PERSONALBANKING("personal_bank", "个人网银支付"),//暂定个人网银
		UNION_ONLINE("union_online", "银联在线支付"),
		B2B("b2b", "企业网银");

		public final String code;
		public final String message;
		unionTraderType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}	
    
    /**
     * 银联交易是否返回易票联中转地址
     */
    enum returnChannelPayurl {

		NO("0", "不返回"),
		YES("1", "返回");

		public final String code;
		public final String message;
		returnChannelPayurl(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}	
    
    /**
     * 银联开放平台订单状态
     */
    enum OpenUnionRefundStatus {
    	SUCCESS("00", "退款成功"),
    	FAIL("01", "退款失败"),
    	PROCESSING("02", "退款处理中");

		public final String code;
		public final String message;
		OpenUnionRefundStatus(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 交易来源1:EPSP平台,3:云闪付开放平台，4:旧系统
     */
    enum SourceChannel {
    	EPSP("1", "EPSP平台"),
    	OPENUNION("3", "云闪付开放平台"),
    	OLDSYSTEM("4", "旧系统");

		public final String code;
		public final String message;
		SourceChannel(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 撤销来源，1-商户发起，2-系统发起
     */
    enum CancelSource {
    	customer_send("1", "商户发起"),
    	system_send("2", "系统发起");

		public final String code;
		public final String message;
		CancelSource(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    /**
     * 提现(代付)类型
     */
    enum WithDrawToCardIsFullAmount {
    	YES(1, "是"),
    	NO(0, "否");	

		public final Integer code;
		public final String message;
		WithDrawToCardIsFullAmount(Integer code, String message) {
			this.code = code;
			this.message = message;
		}
	}


	enum ServiceFeeStatus {
		SUCCESS(1, "收取成功"),
		FAILED(0, "收取失败"),
		REFUND_SUCCESS(2, "退款成功"),
		REFUND_FAIL(3, "退款失败");

		public final Integer code;
		public final String comment;

		ServiceFeeStatus(Integer code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
    enum OrderCardType {

    	/**
    	 * 借记卡
    	 */
    	DEBIT("D", "借记卡"),
    	/**
    	 * 贷记卡
    	 */
    	CREDIT("C", "贷记卡");

		public final String code;
		public final String message;
		OrderCardType(String code, String message) {
			this.code = code;
			this.message = message;
		}
		
		public static String getCardType(String code) {
			CardType[] cardTypes = CardType.values();
			for (CardType cardType : cardTypes) {
				if (cardType.code.equals(code)) {
					return cardType.message;
				}
			}
			return null;
		}
	}

	enum SplitModel {
		/**
		 * 全额分账
		 */
		FULL_AMOUNT("0", "全额分账"),
		/**
		 * 普通分账
		 */
		COMMON("1", "普通分账"),
		/**
		 * 其他分账
		 */
		ORTHER("2", "其他分账"),


		/**
		 * 费差分账
		 */
		FEE_DIFF("3", "费差分账"),
		/**
		 * 拆单分账
		 */
		MULTI_ORDER("4", "拆单分账"),
		/**
		 * 快捷分账
		 */
		MEMBER("5", "快捷分账");

		public final String code;
		public final String message;
		SplitModel(String code, String message) {
			this.code = code;
			this.message = message;
		}

		public static String getSplitModel(String code) {
			SplitModel[] splitModels = SplitModel.values();
			for (SplitModel splitModel : splitModels) {
				if (splitModel.code.equals(code)) {
					return splitModel.message;
				}
			}
			return null;
		}
	}
	
	 /**
     * 订单请求来源,目前只分为裸接口与收银台,后续扩展
     */
    enum RequestSrc {

		BAKEAPI("0", "裸接口调用"),
		CASH("1", "收银台"),
		NOTIFY("2", "上游异步通知"),
		INNER("3", "内部调用");

		public final String code;
		public final String message;
		RequestSrc(String code, String message) {
			this.code = code;
			this.message = message;
		}
		

	}
    
    /**
     * 提现(代付)类型. roller层的调用时,必须指明如下类型的一种. 这个api类型限制了业务类型的
     */
    enum WithDrawTransferType {
    	TRANSFER("0", "门户转账,批量转账.传接口是不走信用卡的.但是业务判断时也加了信用卡判断"),
    	WITHDRAW("1", "门户提现.最终决出业务后,是提现到借记卡或提现到贷记卡"),
    	APIREQ("2", "接口请求.如果是结算卡,则会转换为提现业务");	

		public final String code;
		public final String message;
		WithDrawTransferType(String code, String message) {
			this.code = code;
			this.message = message;
		}
	}
    
    public enum FuKuanState {
        SUCCESS("00", "成功"),
        FAIL("01", "失败"),
        WAITFORNOTIFY("02", "交易完等待查询或异步通知"),
        INIT("03", "初始化");
        public final String code;
        public final String comment;

        FuKuanState(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
    
    
    public enum FuKuanAccStatus {
        SUCCESS("00", "记账成功"),
        FAILED("01", "记账失败"),
        RETURNED("10", "记账已回退"),
        RETURNFAILED("11", "回退失败");
        public final String code;
        public final String comment;

        FuKuanAccStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
    
    public enum NfcJudgeType {
        BYCNT("1", "按笔数风控"),
        BYAMOUNT("2", "按金额风控");
        public final String code;
        public final String comment;

        NfcJudgeType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }    
    
    public enum bandType {
        BYCARD("1", "按卡当日交易过的商户号来禁"),
        BYCUSTOMER("2", "按商户号来禁");
        public final String code;
        public final String comment;

        bandType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

	public enum ProcedureTypeEnum {
		payer_procedure("0", "付款方扣"),
		receiver_procedure("1", "收款方扣");
		public final String code;
		public final String comment;

		ProcedureTypeEnum(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}


    
    public enum NfcPayOrBiz {
        PAY("1", "止付"),
        BIZ("2", "禁业务");
        public final String code;
        public final String comment;

        NfcPayOrBiz(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

	public enum ComfirmType {
		confirm_split("1", "确认分账"),
		confirm_rollback("0", "确认退回");
		public final String code;
		public final String comment;

		ComfirmType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}

	/**
	 * 分账对象属性
	 */
	enum SplitAttr{
		/**
		 * 分账主体商户
		 */
		SPLIT_MAIN("0", "分账主体商户"),
		/**
		 * 常规分账对象
		 */
		SPLIT_CUSTOMER_CODE("1", "常规分账对象"),

		PROCEDURE_CUSTOMER_CODE("2","收单手续费扣除对象"),

		SPLIT_PROCEDURE_CUSTOMER_CODE("3","分账手续费扣除对象"),

		PAY_AND_SPLIT_PROCEDURE_CUSTOMER_CODE("4","收单和分账手续费扣除对象");
		public final String code;
		public final String message;
		SplitAttr(String code, String message) {
			this.code = code;
			this.message = message;
		}

		public static String getSplitAttr(String code) {
			SplitAttr[] splitAttrs = SplitAttr.values();
			for (SplitAttr splitAttr : splitAttrs) {
				if (splitAttr.code.equals(code)) {
					return splitAttr.message;
				}
			}
			return null;
		}
	}

	/**
	 * txsPayTradeOrder表的controlled字段
	 */
	public enum ControlledState {
		FREEZE("1", "管控中"),
		UNFREEZE("0", "已解管控");
		public final String code;
		public final String comment;

		ControlledState(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
}
