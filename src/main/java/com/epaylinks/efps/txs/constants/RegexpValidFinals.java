package com.epaylinks.efps.txs.constants;

public class RegexpValidFinals {
    public static final String URL = "^[Hh][Tt][Tt][Pp][Ss]?:\\/\\/[^\\s]+$";
    public static final String CURRENCY = "CNY";
    public static final String BANK_CARD_TYPE = "debit|credit";
    public static final String PAY_METHOD_MS = "6|7|24|49"; //主扫支付方式
    public static final String ComfirmType = "1|0"; //确认提交类型
    public static final String TRADE_TYPE = "personal_bank|union_online|b2b"; //网银支付交易类型

    public static final class Message {
        public static final String URL = "无效的HTTP地址";
        public static final String CURRENCY = "不在有效取值范围[" + RegexpValidFinals.CURRENCY + "]";
        public static final String BANK_CARD_TYPE = "不在有效取值范围[" + RegexpValidFinals.BANK_CARD_TYPE + "]";
        public static final String PAY_METHOD_MS = "不在有效取值范围[" + RegexpValidFinals.PAY_METHOD_MS + "]";
        public static final String ComfirmType = "不在有效取值范围[" + RegexpValidFinals.ComfirmType + "]";
        public static final String TRADE_TYPE = "不在有效取值范围[" + RegexpValidFinals.TRADE_TYPE + "]";
    }
}
