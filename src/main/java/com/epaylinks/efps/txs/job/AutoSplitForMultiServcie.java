package com.epaylinks.efps.txs.job;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.txs.transaction.service.AutoSplitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component("autoSplitForMultiServcie")
public class AutoSplitForMultiServcie  implements TaskJob {


    @Autowired
    private AutoSplitService autoSplitService;

    @Override
    @Logable(businessTag = "AutoSplitForMultiServcie")
    public void execute(TaskRequest request) throws Exception {
        autoSplitService.processAuto();
    }


}
