package com.epaylinks.efps.txs.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.util.DateTimeUtil;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.service.AccountSplitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component("syncZhfzCheck")
public class SyncZhfzCheckJob implements TaskJob {

	@Autowired
	private AccountSplitService accountSplitService;

    @Autowired
    private TxsSplitOrderMapper txsSplitOrderMapper;

    @Value("${zhfzCheckEndMinutes:5}")
    private int zhfzCheckEndMinutes;

    @Value("${zhfzCheckBeginDay:3}")
    private int zhfzCheckBeginDay;

    @Value("${zhfzCheckRowSize:2000}")
    private int zhfzCheckRowSize;

    @Override
    public void execute(TaskRequest taskRequest) throws Exception {

        Date dateBefore = DateTimeUtil.addDate(new Date(), -zhfzCheckBeginDay);//提前zhfzCheckBeginDay天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateBefore);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);

        Date zhfzCheckBeginDate = calendar.getTime();

        List<String> transactionNoList =  txsSplitOrderMapper.selectNoSucessSyncZhfz(zhfzCheckEndMinutes, zhfzCheckBeginDate, zhfzCheckRowSize);
    	//实时代收
        accountSplitService.reviewSplitState(transactionNoList);
    }

}
