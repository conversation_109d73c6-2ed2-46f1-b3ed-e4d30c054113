package com.epaylinks.efps.txs.service.dto.cust;

import java.io.Serializable;

/**
 * 商户位置总控配置
 * <AUTHOR>
 * @date 2020-03-20
 *
 */
public class CustomerLocationConfig  implements Serializable{
    
    private static final long serialVersionUID = -6732813196545948851L;

    private String checkPayLocation = "0"; // 是否需要验证交易位置：0：否，1：是
    
    private Long payLocationRange; // 交易限制范围（米）
    
    /**
     * constructor
     */
    public CustomerLocationConfig() {
        
    }
    
    public CustomerLocationConfig(String checkPayLocation, Long payLocationRange) {
        this.checkPayLocation = checkPayLocation;
        this.payLocationRange = payLocationRange;
    }
    
    public String getCheckPayLocation() {
        return checkPayLocation;
    }

    public void setCheckPayLocation(String checkPayLocation) {
        this.checkPayLocation = checkPayLocation;
    }

    public Long getPayLocationRange() {
        return payLocationRange;
    }

    public void setPayLocationRange(Long payLocationRange) {
        this.payLocationRange = payLocationRange;
    }

    
}
