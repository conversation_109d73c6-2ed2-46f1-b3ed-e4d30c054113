package com.epaylinks.efps.txs.service;

import com.epaylinks.efps.common.business.cum.BankInfo;
import com.epaylinks.efps.common.business.cum.PayChannel;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.txs.service.dto.cum.Business;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class CacheService {
	@Autowired
    private CumCacheServiceImpl cumCacheService;

    private static final Map<String, BankInfo> bankInfoMap = new ConcurrentHashMap<>();

    @Scheduled(cron = "0 0/20 * * * ?")
    public void clearMap(){
//        businessMap.clear();
        bankInfoMap.clear();
//        payChannelMap.clear();
    }

/*    public PayChannel queryPayChannel(Long payChannelId){
        PayChannel payChannel = payChannelMap.get(payChannelId);
        if(payChannel == null){
            payChannel = cumCacheService.queryPayChannel(payChannelId);
            if(payChannel == null){
                payChannel = new PayChannel();
            }
            payChannelMap.put(payChannelId,payChannel);
        }
        return payChannel;
    }*/

    public BankInfo getBankInfo(String bankCode) {
        if(StringUtils.isBlank(bankCode)){
            return null;
        }
        BankInfo bankInfo = bankInfoMap.get(bankCode);
        if(bankInfo == null){
            bankInfo = cumCacheService.queryBankInfo(bankCode);
            if(bankInfo == null){
                bankInfo = new BankInfo();
            }
            bankInfoMap.put(bankCode,bankInfo);
        }
        return bankInfo;
    }

/*    public Business getBusiness(String businessCode){
        Business business = businessMap.get(businessCode);
        if(business == null){
            business = getApplyBusinessByCode(businessCode);
            if(business == null){
                business = new Business();
            }
            businessMap.put(businessCode,business);
        }
        return business;
    }*/

    /**
     * 缓存业务信息
     */
/*    @Cacheable(value = "cacheApplyBusiness", key = "#businessCode")
    public Business getApplyBusinessByCode(String businessCode) {
        if(StringUtils.isEmpty(businessCode)) {
            return null;
        }
        List<Business> businessList = pasService.getApplyBusinessByCode(businessCode);
        Business resultBusiness = null;
        for(Business business: businessList) {
            if(businessCode.equals(business.getCode())) {
                resultBusiness = business;
                break;
            }
        }
        return resultBusiness;
    }*/
}
