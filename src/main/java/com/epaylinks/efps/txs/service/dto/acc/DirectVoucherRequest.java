package com.epaylinks.efps.txs.service.dto.acc;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 15:46
 * <p>
 * 直接传入记账信息进行记账
 */
public class DirectVoucherRequest {
    /**
     * 易票联订单编号，唯一索引
     */
    @NotBlank
    private String transactionNo;
    /**
     * 交易类型
     */
    @NotBlank
    private String transactionType;
    /**
     * 支付指令类型：1：内转 2：充值 3：提现 4：退款
     */
    @NotNull
    @Min(1)
    @Max(4)
    private Integer payType;
    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 0：未支付成功 1：支付成功 记账凭证对应的交易订单是否已支付成功
     */
    private Integer paySuccess = 1;

    private List<Detail> details;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(PayType payType) {
        this.payType = payType.code;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getPaySuccess() {
        return paySuccess;
    }

    public void setPaySuccess(Integer paySuccess) {
        this.paySuccess = paySuccess;
    }

    public List<Detail> getDetails() {
        return details;
    }

    public void addDetail(String customerCode, Long amount, AccountType accountType, BalanceType balanceType) {
        if (this.details == null) {
            this.details = new ArrayList<>();
        }
        Detail detail = new Detail(customerCode, accountType.code, amount, balanceType.code);
        this.details.add(detail);
    }

    public enum PayType {
        INTURN(1, "内转"),
        RECHARGE(2, "充值"),
        WITHDRAWALS(3, "提现"),
        REFUND(4, "退款"),
        REVOKE(5, "撤销");

        public final Integer code;
        public final String message;

        PayType(Integer code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    public enum AccountType {
        JY("JY-A", "交易账户"),
        BJ("BJ-B", "簿记账户");

        public final String code;
        public final String message;

        AccountType(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    public enum BalanceType {
        USEFUL_AMOUNT(1, "可用金额"),
        WAY_AMOUNT(2, "在途金额"),
        FROZEN_AMOUNT(3, "冻结金额");

        public final Integer code;
        public final String message;

        BalanceType(Integer code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    public static class Detail {
        private final String customerCode;
        /**
         * JY-A:交易账户 BJ-B:簿记账户
         */
        private final String accountType;
        private final Long amount;
        /**
         * 1：可用金额 ， 2：在途金额 ， 3：冻结金额
         */
        private final Integer balanceType;

        private Detail(String customerCode, String accountType, Long amount, Integer balanceType) {
            this.customerCode = customerCode;
            this.accountType = accountType;
            this.amount = amount;
            this.balanceType = balanceType;
        }

        public String getCustomerCode() {
            return customerCode;
        }

        public String getAccountType() {
            return accountType;
        }

        public Long getAmount() {
            return amount;
        }

        public Integer getBalanceType() {
            return balanceType;
        }

    }


}
