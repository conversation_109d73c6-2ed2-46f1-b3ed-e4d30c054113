package com.epaylinks.efps.txs.service;



import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.cust.model.Customer;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.service.dto.cust.CustomerLocationConfig;


@FeignClient(value = "term" , configuration = DefaultFeignConfiguration.class)
public interface TermService {

	/**
	 * 查询行业码明细
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/industrycode/detail2Txs", method = RequestMethod.GET)
	CommonOuterResponse<Map<String, String>> industrycodeQuery(@RequestParam("qc") String qc);
}