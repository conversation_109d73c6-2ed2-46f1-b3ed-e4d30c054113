package com.epaylinks.efps.txs.service;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("REPORT")
public interface ReportService {

	@PostMapping("/getBillDownloadUrl")
	String getBillDownloadUrl(@RequestParam("acqOrgCode") String acqOrgCode, @RequestParam("acqSpId") String acqSpId, @RequestParam("billDate") String billDate);
}
