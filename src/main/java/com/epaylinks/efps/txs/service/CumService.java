package com.epaylinks.efps.txs.service;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.cum.ChannelMchtInfo;
import com.epaylinks.efps.common.business.cum.CumNfcTagRecord;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.PayChannel;
import com.epaylinks.efps.common.business.cum.PayChannelParam;
import com.epaylinks.efps.common.business.cum.SendSignQuickPayMessageRequest;
import com.epaylinks.efps.common.business.cum.SendSignQuickPayMessageResponse;
import com.epaylinks.efps.common.business.cum.customerBusiness.CumSplitBusiness;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;
import com.epaylinks.efps.common.business.domain.CumBusinessParamInst;
import com.epaylinks.efps.common.business.domain.CustomerBusinessInfo;
import com.epaylinks.efps.common.business.txs.BankInfo;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.service.dto.cum.Business;
import com.epaylinks.efps.txs.transaction.domain.CustomerSettleInfo;

@FeignClient(value = "CUM" , configuration = DefaultFeignConfiguration.class)
public interface CumService {

	/**
	 * 获取企业客户状态
	 * 
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/customerStatus", method = RequestMethod.GET)
	Map<String, Long> getCustomerSattus(@RequestParam("customerCode") String customerCode);

	/**
	 * 查询客户业务信息
	 * 
	 * @param customerCode
	 * @param userType
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/BusinessInfo", method = RequestMethod.GET)
	List<CustomerBusinessInfo> getCustomerBusinessInfo(@RequestParam("customerCode") String customerCode,
			@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader(value = "x-user-type") String userType);
	
	/**
	 * 查询客户所支持的支付方式
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/getCustomerPayMethods", method = RequestMethod.GET)
	public List<String> getCustomerPayMethods(@RequestParam("customerCode") String customerCode);

	/**
	 * 查询客户业务实例信息，返回该客户的所有业务实例，status:0-无效 1-有效
	 * @param customerCode
	 * @param status
	 * @param xCustomerCode
	 * @param userType
	 * @return
	 */
	@RequestMapping(value = "/Customer/BusinessInsts", method = RequestMethod.GET)
	List<CustomerBusinessInstance> getCustomerBusinessInsts(@RequestParam("customerCode") String customerCode,
			@RequestParam("status") Short status,
			@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader(value = "x-user-type") String userType);
	
	/**
	 * 检验支付密码
	 * 
	 * @param customerCode
	 * @param payPwd
	 * @return
	 */
	@RequestMapping(value = "/Password/payPwdCheck", method = RequestMethod.GET)
	String payPwdCheck(@RequestParam("customerCode") String customerCode, @RequestParam("payPwd") String payPwd);

	/**
	 * 查询客户基本
	 * 
	 * @param customerCode
	 * @param userType
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/CustomerInfo", method = RequestMethod.GET)
	CustomerInfo getCustomerInfo(@RequestParam("customerCode") String customerCode,
			@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader(value = "x-user-type") String userType);


	/**
	 * 根据id查看客户结算信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/Customer/CustomerSettleInfo/detail", method = RequestMethod.GET)
	CustomerSettleInfo customerSettleInfoById(@RequestParam("id") String id);

	/**
	 * 查询客户结算信息
	 * @param xCustomerCode
	 * @param userType
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/Settle", method = RequestMethod.GET)
	CustomerSettleInfo Settle(@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader("x-user-type") String userType, @RequestParam("customerCode") String customerCode);

	/**
	 * 根据结算目标查看客户结算信息
	 * @param target
	 * @return
	 */
	@RequestMapping(value = "/Customer/CustomerSettleInfo/queryByTarget", method = RequestMethod.GET)
	List<CustomerSettleInfo> queryAllCustomerSettleInfoByTarget(@RequestParam("target") Short target);

	/**
	 * 判断某组客户是否都开通了某个业务，并且业务实例的当前状态是有效的，有效期包含当前时间
	 * @param customerCodeList
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/Customer/Business/CustomerBusinessCheck", method = RequestMethod.POST)
	boolean isCustomersOpenBusiness(@RequestParam("customerCodeList") List<String> customerCodeList, @RequestParam("businessCode")String businessCode);
	
	/**
	 * 根据拓展商户找到对应代理商的业务参数实例
	 * @param customerCodeDL
	 * @param customerCode
	 * @param status
	 * @param payMethod
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/Customer/BusinessParamInst", method = RequestMethod.GET)
	List<CumBusinessParamInst> getCustomerBusinessParamInstByDL(
			@RequestParam("customerCodeDL") String customerCodeDL,
			@RequestParam("customerCode") String customerCode,
			@RequestParam("status") Integer status,
			@RequestParam(value = "payMethod", required = false) String payMethod,
			@RequestParam("code") String code);
	
	/**
	 * 根据业务实例获取业务参数实例
	 * @param businessExamId
	 * @return
	 */
	@RequestMapping(value = "/Customer/BusinessParamInstByBusinessExamId", method = RequestMethod.GET)
	List<CumBusinessParamInst> getCustomerBusinessParamInst(
			@RequestParam("businessExamId") String businessExamId);
	
	/**
	 * 根据代理商客户和拓展商户查询业务参数实例
	 * @param customerCodeDL
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/cumBusinessParamInsts", method = RequestMethod.GET)
	List<CumBusinessParamInst> cumBusinessParamInsts(@RequestParam("customerCodeDL") String customerCodeDL,
			@RequestParam("customerCode") String customerCode);
	
	/**
	 * 根据代理商客户和拓展商户查询业务参数实例
	 * @param customerCodeDL
	 * @param businessParamCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/queryCumBusinessParamInsts", method = RequestMethod.GET)
	List<CumBusinessParamInst> queryCumBusinessParamInsts(@RequestParam("customerCodeDL") String customerCodeDL,
			@RequestParam("businessParamCode") String businessParamCode);
	
	/**
	 * 根据客户名称查询客户编码
	 * @param customerName
	 * @return
	 */
	@RequestMapping(value = "/Customer/queryCustomerCode", method = RequestMethod.GET)
	String queryCustomerCodeByCustomerName(@RequestParam(value = "customerName" , required = true) String customerName);
	
	@RequestMapping(value = "/business/category" , method = RequestMethod.GET)
	public List<Business> getBusinessByCategory(@RequestParam(name = "businessCategory" , required = true)String businessCategory);

	/**
	 * 判断商户会员与商户之间的关系
	 * @param customerCode
	 * @param parentCustomerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/checkMemberCustomerCode", method = RequestMethod.GET)
	public boolean checkMemberCustomerCode(@RequestParam("customerCode") String customerCode,
			@RequestParam("parentCustomerCode") String parentCustomerCode);
	

	/**
	 * 发送快捷支付短信
	 * @param quickPayParam
	 * @return
	 */
	@PostMapping("/quickPayMessage/sign")
	public SendSignQuickPayMessageResponse sendSignQuickPayMessage(@RequestBody SendSignQuickPayMessageRequest sendSignQuickPayMessageRequest);
	
	
	/**
	 * 查询所有的客户信息
	 * @return
	 */
	@RequestMapping(value = "/Customer/queryAllCustomerInfo", method = RequestMethod.POST)
	public List<CustomerInfo> queryAllCustomerInfo();

	/**
	 * 查询所有的标准客户信息
	 * @return
	 */
	@RequestMapping(value = "/Customer/queryEpspCustomerInfo", method = RequestMethod.GET)
	public List<CustomerInfo> queryEpspCustomerInfo();


	@GetMapping("/Customer/queryThNotifyUrlByCustomerCode")
	public String queryThNotifyUrlByCustomerCode(@RequestParam("customerCode") String customerCode) ;
	
	
	/**
	 *协议支付解除绑卡
	 * @param signQuickPayRequest
	 * @return
	 */
	@PostMapping("/quickPay/unBindCard")
	public void unBindCard(@RequestParam("customerCode")String customerCode,  @RequestParam("memberId")String memberId,
			@RequestParam("protocol")String protocol); 
	
	@PostMapping(value = "getMerchantCode")
	public String getMerchantCode(@RequestParam("customerCode") String customerCode,
			@RequestParam("subCustomerCode") String subCustomerCode,
			@RequestParam("channelCategoryId") Long channelCategoryId,
			@RequestParam("isPreOrder") String isPreOrder,
			@RequestParam("amount") Long amount);
	
	//更新子商户额度
	@PostMapping(value = "updateSubCustomerLimit")
	public void updateSubCustomerLimit(
			@RequestParam("customerCode") String customerCode,
			@RequestParam("subCustomerCode") String subCustomerCode,
			@RequestParam("amount") Long amount,
			@RequestParam("channelCategoryId") Long channelCategoryId, @RequestParam("tradeDate") String tradeDate);//yyyy-MM-dd
	
	
	/**
	 * 根据开通的支付方式查询支持银行信息
	 * @param payMethodList
	 * @param resultType  查询结果类型 默认/0: 返回支持卡类型，借与记分开，1：返回支持的支付方式
	 * @return
	 */
	@RequestMapping(value= "/channelBankInfo/querySupportBankByPayMethod", method = RequestMethod.GET)
	List<BankInfo> querySupportBankByPayMethod(
			@RequestParam(value = "customerCode") String customerCode,
			@RequestParam("payMethodList")List<String> payMethodList,
			@RequestParam(value = "resultType", required = false) String resultType);
	
	
	/**
	 * 根据开通的支付方式查询支持银行信息
	 * @param payMethodList
	 * @param resultType  查询结果类型 默认/0: 返回支持卡类型，借与记分开，1：返回支持的支付方式
	 * @return
	 */
	@Logable(businessTag = "queryBusinessInfoByCustomerAndBusinessCodeAndState")
	@RequestMapping(value= "/Customer/queryBusinessInfoByCustomerAndBusinessCodeAndState", method = RequestMethod.GET)
	public CustomerBusinessInfo queryBusinessInfoByCustomerAndBusinessCodeAndState(
			@RequestParam(value = "customerCode") String customerCode,
			@RequestParam(value = "businessCode") String businessCode);
	
	/**
     * 路由商户交易渠道信息
     * 通过商户号和支付方式，路由商户渠道信息
     * @param customerCode
     * @param payMethod
     * @return
     */
    @PostMapping("/payChannel/payChannelRouteV2")
    PayChannelParam routeChannelMcht(
            @RequestParam(value = "customerCode") String customerCode,
            @RequestParam(value = "payMethod") String payMethod,
            @RequestParam(value = "subCustomerCode", required = false) String subCustomerCode,
            @RequestParam(value = "bankCode", required = false) String bankCode,
            @RequestParam(value = "amount") Long amount,
            @RequestParam(value = "transactionNo", required = false) String transactionNo,
            @RequestParam(value = "bankAccType", required = false) String bankAccType,
            @RequestParam(value = "appid", required = false) String appid,
            @RequestParam(value = "channelMchtNo", required = false) String channelMchtNo);

    /**
     * 路由商户交易渠道信息
     * @param institutionId 机构表主键
     * @param channelInstCode 机构号
     * @param channelMchtNo 渠道商户号
     * @return
     */
    @PostMapping("/payChannel/getChannelMcht")
    ChannelMchtInfo getChannelMcht(
            @RequestParam(value = "机构表主键", required = true) Long institutionId,
            @RequestParam(value = "机构号", required = true) String channelInstCode,
            @RequestParam(value = "渠道商户号", required = true) String channelMchtNo);
    
    /**
     * 查询卡信息
     * @param bankCode
     * @param bankName
     * @return
     */
    @PostMapping("/bankInfo/queryAllBankInfo")
    List<com.epaylinks.efps.common.business.cum.BankInfo> queryAllBankInfo(
    		@RequestParam("bankCode") String bankCode,
    		@RequestParam("bankName") String bankName);
    
    /**
     * 查询渠道小类，不传id则查询所有hui
     * @param channelId
     * @return
     */
    @GetMapping("/payChannel/queryPayChannel")
    PageResult<PayChannel> queryPayChannel(
    		@RequestParam("channelId") Long channelId,
    		@RequestParam("pageNum") int pageNum,
    		@RequestParam("pageSize") int pageSize);

	@RequestMapping(value = "/Customer/queryCustomerInfoByCustomerCode" , method = RequestMethod.GET)
	CustomerInfo queryCustomerInfoByCustomerCode(@RequestParam("customerCode") String customerCode);
	
	/**
	 * 判断customerCode是否parentCustomerCode的子商户
	 * @param customerCode
	 * @param parentCustomerCode
	 * @return
	 */
	@RequestMapping(value = "/Customer/checkIsParentCustomerCode", method = RequestMethod.GET)
	public boolean checkIsParentCustomerCode(@RequestParam("customerCode") String customerCode,
			@RequestParam("parentCustomerCode") String parentCustomerCode);

	@GetMapping(value = "/Customer/queryChildCustomerCodes")
	List<String> queryChildCustomerCodes(@RequestParam("customerCode")String customerCode,@RequestParam("expandCustomerCode")String expandCustomerCode,@RequestParam("expandCustomerName")String expandCustomerName);
	
	/**
	 * 批量查询父商户，返回map key为子商户，value为父商户
	 * @param customerCodeList
	 * @param customerCategory
	 * @return
	 */
	@GetMapping("/Customer/queryParentCustomerCode")
    Map<String, CustomerInfo> queryParentCustomerCode(@RequestParam("customerCodeList") List<String> customerCodeList,@RequestParam("customerCategory") String customerCategory);

    @GetMapping("/Customer/checkIsAncestorAndReturnParentCustomer")
	Map<String,Object> checkIsAncestorAndReturnParentCustomer(@RequestParam("customerCode") String customerCode, @RequestParam("ancestorCustomerCode") String ancestorCustomerCode);

	/**
	 * 获取分账业务信息
	 * @param customerCode
	 * @param businessCode
	 * @param splitModel
	 * @return
	 */
	@GetMapping("/Customer/getSplitBusiness")
	List<CumSplitBusiness> getSplitBusiness(@RequestParam("customerCode") String customerCode, @RequestParam(value = "businessCode",required = false) String businessCode, @RequestParam(value = "splitModel",required = false) Short splitModel);
	

    /**
     * 根据订单号取标签绑定信息
     * @param
     * @param
     * @param
     * @return
     */
    @PostMapping("/tag/getNfcTagRecordFromOrderNo")
    CumNfcTagRecord getNfcTagRecordByOrderNot(
            @RequestParam(value = "订单号", required = true) String orderNo);

	/**
	 * 更新CustomerInfo
	 * @param customerInfo
	 * @return
	 */
	@PostMapping("/Customer/updateCustomerInfo")
	Integer updateCustomerInfo(@RequestBody CustomerInfo customerInfo);
	
    @GetMapping("/business/getMapOfBusinessCodeAndName")
    Map<String,String> getMapOfBusinessCodeAndName(@RequestParam("businessCodeList") List<String> businessCodeList);
    
	/**
	 * 查询商户的所有上级父商户号列表
	 * @return
	 */
	@GetMapping("/Customer/queryParentCustomerCodeListByCustomerCode")
	List<String> queryParentCustomerCodeListByCustomerCode(@RequestParam("customerCode") String customerCode);
	
	@GetMapping("/Customer/checkIsAncestorCustomer")
	boolean checkIsAncestorCustomer(
			@RequestParam("customerCode") String customerCode,
			@RequestParam("ancestorCustomerCode") String ancestorCustomerCode);
	
	@PostMapping("/tagSync/updateNoUpdated")
	List<Long> updateNoUpdated(@RequestParam("updateTimeBegin") String updateTimeBegin,
			@RequestParam("updateTimeEnd") String updateTimeEnd, 
			@RequestParam("orderNo") String orderNo);
	
	@PostMapping("/tagSync/syncNotSynced")
	List<Long> syncNotSynced(@RequestParam("createTimeBegin") String createTimeBegin,
			@RequestParam("createTimeEnd") String createTimeEnd, 
			@RequestParam("orderNo") String orderNo);	
	
	
	
}

