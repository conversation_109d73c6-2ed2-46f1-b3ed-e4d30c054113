package com.epaylinks.efps.txs.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(value = "posp" , configuration = DefaultFeignConfiguration.class)
public interface PospService {

    @GetMapping("/realName/fzOrder")
    CommonOuterResponse queryIsRealName(@RequestParam(value = "sysRefNo") String sysRefNo,
                                        @RequestParam(value = "amount") Long amount,
                                        @RequestParam(value = "customerCode") String customerCode);
}