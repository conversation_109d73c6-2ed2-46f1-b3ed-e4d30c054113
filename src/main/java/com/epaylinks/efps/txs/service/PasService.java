package com.epaylinks.efps.txs.service;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.txs.service.dto.cum.Business;

@FeignClient("PAS")
public interface PasService {

    @GetMapping("/Business/selectBySelective")
    List<Business> queryBusiness(
    		@RequestParam("name") String name,
    		@RequestParam("code") String code, 
    		@RequestParam("type") String type, 
    		@RequestParam("ratioMode") String ratioMode, 
    		@RequestParam("state") String state //状态:1-正常.2-已删除
    		);
    @GetMapping("/Business/selectBySelective")
    List<Business> queryAllBusiness();
    
        
    //yyyyMMdd 实时交易不能过多远程调用. 待缓存机制有了以后,通过缓存来取
    @RequestMapping(value = "/holiday/checkHoliday", method = RequestMethod.GET)
    Boolean checkHoliday(@RequestParam("date")String date);
        


    
}