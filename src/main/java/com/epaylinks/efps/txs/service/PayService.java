package com.epaylinks.efps.txs.service;

import java.util.List;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.domain.InsidePayRequest;
import com.epaylinks.efps.common.business.domain.InsidePayResponse;
import com.epaylinks.efps.common.business.pay.request.PayRequest;
import com.epaylinks.efps.common.business.pay.response.PayResponse;
import com.epaylinks.efps.common.business.txs.Refund.RefundParam;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.service.dto.GatewayPay;
import com.epaylinks.efps.txs.service.dto.RefundResponse;

@FeignClient(value = "PAY" , configuration = DefaultFeignConfiguration.class)
public interface PayService {

	/**
	 * 网关支付
	 * 
	 * @param payRequest
	 * @return
	 */
	@PostMapping(value = "GatewayPay")
	public PayResponse handleGatewayPay(@RequestBody PayRequest payRequest);

	/**
	 * 网关下单
	 *
	 * @param fromSystemId
	 *            来源系统的编码
	 * @param transactionNo
	 *            交易订单号
	 * @param transactionType
	 *            交易类型
	 * @param payAmount
	 *            支付金额
	 * @param payCurrency
	 *            支付币种 CNY:人民币
	 * @param channelType
	 *            渠道类型 01：PC端 02：移动端
	 * @param payWay
	 *            支付方式
	 * @param settleCycle
	 *            结算周期 00表示实时结算 其他均为非实时结算
	 * @param payeeType
	 *            收款方用户类型 01：商家 02：普通会员
	 * @param payeeCustCode
	 *            收款方用户编号
	 * @param productDesc
	 *            商品描述
	 * @param bankCode
	 *            银行编号
	 * @param bankAccountType
	 *            银行账号类型
	 * @param bankCardType
	 *            银行卡类型
	 * @param openId
	 *            公众号code值,微信公众号需要填写，其他不用填写
	 * @param merchantNo
	 *            商家号
	 * @return
	 */
	@RequestMapping(value = "/GatewayPayment", method = RequestMethod.POST)
	GatewayPay gatewayPayment(@RequestParam(value = "fromSystemId") String fromSystemId,
			@RequestParam(value = "transactionNo") String transactionNo,
			@RequestParam(value = "transactionType") String transactionType,
			@RequestParam(value = "payAmount") Long payAmount, @RequestParam(value = "payCurrency") String payCurrency,
			@RequestParam(value = "channelType") String channelType,
			@RequestParam(value = "payMethod") String payMethod,
			@RequestParam(value = "settleCycle") String settleCycle,
			@RequestParam(value = "payeeType") String payeeType,
			@RequestParam(value = "payeeCustCode") String payeeCustCode,
			@RequestParam(value = "productDesc") String productDesc, @RequestParam(value = "bankCode") String bankCode,
			@RequestParam(value = "bankAccountType") String bankAccountType,
			@RequestParam(value = "bankCardType") String bankCardType,
			@RequestParam(value = "bankCardNo") String bankCardNo, @RequestParam(value = "openId") String openId,
			@RequestParam(value = "appId") String appId, @RequestParam(value = "merchantNo") String merchantNo,
			@RequestParam(value = "buyerLogonId") String buyerLogonId, @RequestParam(value = "buyerId") String buyerId,
			@RequestParam(value = "procedureFee") Long procedureFee,
			@RequestParam(value = "redirectURL") String redirectURL

	);

	/**
	 * 调用支付内转实现
	 * 
	 * @param payRequest
	 * @return
	 */
	@PostMapping(value = "/InsidePay")
	public InsidePayResponse handleInsidePay(@RequestBody InsidePayRequest payRequest);

	/**
	 * 分步分账
	 *
	 * @param payRequest
	 * @return
	 */
	@PostMapping(value = "/preInsidePay")
	public InsidePayResponse preInsidePay(@RequestBody InsidePayRequest payRequest);



	/**
	 * 内转（下单）
	 *
	 * @param fromSystemId
	 *            来源系统的编码
	 * @param transactionNo
	 *            交易订单号
	 * @param transactionType
	 *            交易类型
	 * @param payAmount
	 *            支付金额
	 * @param payCurrency
	 *            支付币种 CNY:人民币
	 * @param channelType
	 *            渠道类型
	 * @param payMethod
	 *            支付方式
	 * @param payerCustCode
	 *            付款方客户编号
	 * @param payerPassword
	 *            付款方支付密码
	 * @param payeeType
	 *            收款方客户类型
	 * @param payeeCustCode
	 *            收款方客户编号
	 * @return
	 */
	@RequestMapping(value = "/InsidePayment", method = RequestMethod.POST)
	GatewayPay insidePayment(@RequestParam(value = "fromSystemId") String fromSystemId,
			@RequestParam(value = "transactionNo") String transactionNo,
			@RequestParam(value = "transactionType") String transactionType,
			@RequestParam(value = "payAmount") Long payAmount, @RequestParam(value = "payCurrency") String payCurrency,
			@RequestParam(value = "channelType") String channelType,
			@RequestParam(value = "payMethod") String payMethod,
			@RequestParam(value = "payerCustCode") String payerCustCode,
			@RequestParam(value = "payerPassword") String payerPassword,
			@RequestParam(value = "payeeType") String payeeType,
			@RequestParam(value = "payeeCustCode") String payeeCustCode,
			@RequestParam(value = "procedureFee") Long procedureFee);

	/**
	 * 退款（下单）
	 *
	 * @param fromSystemId
	 *            来源系统的编码
	 * @param transactionNo
	 *            交易订单号
	 * @param origTransactionNo
	 *            原交易订单号
	 * @param transactionType
	 *            交易类型
	 * @param payAmount
	 *            支付金额
	 * @param payCurrency
	 *            支付币种 CNY:人民币
	 * @param channelType
	 *            渠道类型 01：PC端 02：移动端
	 * @param payWay
	 *            支付方式
	 * @param notifyUrl
	 *            回调地址
	 * @param settleCycle
	 *            结算周期 00表示实时结算 其他均为非实时结算
	 * @param payerCustCode
	 *            付款方用户编号
	 * @param payerPassword
	 *            付款方支付密码
	 * @param payerBankAccno
	 *            收款方银行账号
	 * @param payerBankCode
	 *            收款方银行机构编号
	 * @param payeeCustCode
	 *            收款方用户编号
	 * @param payeeCustName
	 *            收款方姓名
	 * @return
	 */
	@RequestMapping(value = "/Refund", method = RequestMethod.POST)
	public RefundResponse handleRefund(
			@RequestParam(value = "fromSystemId")  String fromSystemId,
			@RequestParam(value = "transactionNo")  String transactionNo,
			@RequestParam(value = "origTransactionNo")  String origTransactionNo,
			@RequestParam(value = "businessInstId")  String businessInstId,
			@RequestParam(value = "transactionType") String transactionType,
			@RequestParam(value = "payAmount")  Long payAmount,
			@RequestParam(value = "realAmount") Long realAmount,
			@RequestParam(value = "channelType")  String channelType,
			@RequestParam(value = "procedureFee")  Long procedureFee,
			@RequestParam(value = "fzjyTransactionNo") String accNeedNo,
			@RequestBody List<RefundParam> refundParams, 
			@RequestParam(value = "isFromBJ") String isFromBJ, 
			@RequestParam(value = "orderInfoObjStr")String orderInfoObjStr);

	
}
