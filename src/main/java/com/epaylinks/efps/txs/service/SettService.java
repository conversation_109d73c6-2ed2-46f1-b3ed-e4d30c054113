package com.epaylinks.efps.txs.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.service.dto.settUpdtSettCycleRequest;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(value = "sett", configuration = DefaultFeignConfiguration.class)
public interface SettService {
    @GetMapping("getZhySettDetail")
    CommonOuterResponse<SettClearSettlementDetailEntity> getZhySettDetail(@RequestParam("customerCode") String customerCode,
                                                                          @RequestParam("settlementDate") String settlementDate);

    class SettClearSettlementDetailEntity {
        private long procedureFee;
        private String txnDateBgn;
        private String txnDateEnd;

        public long getProcedureFee() {
            return procedureFee;
        }

        public void setProcedureFee(long procedureFee) {
            this.procedureFee = procedureFee;
        }

        public String getTxnDateBgn() {
            return txnDateBgn;
        }

        public void setTxnDateBgn(String txnDateBgn) {
            this.txnDateBgn = txnDateBgn;
        }

        public String getTxnDateEnd() {
            return txnDateEnd;
        }

        public void setTxnDateEnd(String txnDateEnd) {
            this.txnDateEnd = txnDateEnd;
        }
    }

    @PostMapping("/sett/updateCdSettCycle")
    CommonOuterResponse updateSettCycle(@RequestBody settUpdtSettCycleRequest request);


}
