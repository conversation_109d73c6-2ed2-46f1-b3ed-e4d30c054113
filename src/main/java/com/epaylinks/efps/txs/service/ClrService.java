package com.epaylinks.efps.txs.service;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.business.EnterpriseDetailResponse;
import com.epaylinks.efps.common.business.clr.ClrPayRecord;
import com.epaylinks.efps.common.business.clr.request.FuKuanByQrCodeRequest;
import com.epaylinks.efps.common.business.clr.request.UnionJsGerUserIdRequest;
import com.epaylinks.efps.common.business.clr.response.FuKuanByQrCodeResponse;
import com.epaylinks.efps.common.business.clr.response.UnionJsGerUserIdResponse;
import com.epaylinks.efps.common.business.txs.BankInfo;
import com.epaylinks.efps.common.business.txs.CommonElememtCheckResponse;
import com.epaylinks.efps.common.business.txs.FaceDiscernRequest;
import com.epaylinks.efps.common.business.txs.FaceDiscernResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.transaction.domain.query.TxsQueryResponse;

@FeignClient(value = "clr" , configuration = DefaultFeignConfiguration.class)
public interface ClrService {

	@RequestMapping(value = "/AnalysisRedirectUrl", method = RequestMethod.POST)
	public TxsQueryResponse analysisRedirectUrl(@RequestBody Map<String, Object> map);

	

	@Logable(businessTag = "elementCheck")
	@RequestMapping(value= "/ConsistCheck", method = RequestMethod.POST)
	public CommonElememtCheckResponse elementCheck(@RequestParam("cardId") String cardId,@RequestParam("certId") String certId,
			@RequestParam("certName") String certName,@RequestParam("mobileNo") String mobileNo,
			@RequestParam("transactionNo") String transactionNo,@RequestParam("consistCheckType") String consistCheckType);
	
	/**
	 * 根据开通的支付方式查询支持银行信息
	 * @param payMethodList
	 * @param resultType  查询结果类型 默认/0: 返回支持卡类型，借与记分开，1：返回支持的支付方式
	 * @return
	 */
	@RequestMapping(value= "/ChannelBank/querySupportBankByPayMethod", method = RequestMethod.GET)
	List<BankInfo> querySupportBankByPayMethod(@RequestParam("payMethodList")List<String> payMethodList,
			@RequestParam(value = "resultType", required = false) String resultType);
	
	/**
	 * 手工修改提现订单为终态成功或失败
	 * @param transactionNo  系统订单号
	 * @param state  将要手动修改的终状态,00或01
	 * @return  返回CommonResponse.result为0000时表示修改 成功
	 */
	@RequestMapping(value= "/updateWithdrawRecordFinalByMenual", method = RequestMethod.POST)
	public CommonResponse updateWithdrawRecordFinalByMenual(@RequestParam("transactionNo") String transactionNo, 
			@RequestParam("state") String state);


	@PostMapping(value = "auditOrders")
	void auditOrders(List<JSONObject> auditOrders);
	
	@RequestMapping(value= "/tradeQuery", method = RequestMethod.GET)
	public CommonOuterResponse<ClrPayRecord> tradeQuery(@RequestParam("transactionNo") String transactionNo);
	
	/**
	 * UNIONJS
	 * @param 
	 * @param 
	 * @return
	 */
	@RequestMapping(value= "/unionJsGerUserId", method = RequestMethod.POST)
	public UnionJsGerUserIdResponse unionJsGerUserId(@RequestBody UnionJsGerUserIdRequest unionJsGerUserIdRequest);
	
	@Logable(businessTag = "enterpriseDetail")
	@RequestMapping(value= "/enterpriseDetail", method = RequestMethod.POST)
	public EnterpriseDetailResponse enterpriseDetail(
			@RequestParam("customerCode") String customerCode,
			@RequestParam("transactionNo") String transactionNo,
			@RequestParam("enterpriseInfo") String enterpriseInfo,
			@RequestParam("enterpriseInfoType") String enterpriseInfoType);

	@Logable(businessTag = "faceDiscern")
	@RequestMapping(value= "/faceDiscern", method = RequestMethod.POST)
	public FaceDiscernResponse faceDiscern(@RequestBody FaceDiscernRequest faceDiscernRequest);
	
	/**
	 * 主扫收款方
	 * @param 
	 * @param 
	 * @return
	 */
	@RequestMapping(value= "/fukuan/qrCodeFukuan", method = RequestMethod.POST)
	public FuKuanByQrCodeResponse qrCodeFukuan(@RequestBody FuKuanByQrCodeRequest fuKuanByQrCodeRequest);
	
	@RequestMapping(value = "/pyp/reHandleNotifyRecords", method = RequestMethod.POST)
	public List<String> reHandleNotifyRecords(
			@RequestParam("timeBegin") String timeBegin,
			@RequestParam("timeEnd") String timeEnd, 
			@RequestParam("transactionNo") String transactionNo, 
			@RequestParam("voucherNum") String voucherNum, 
			@RequestParam("orderNo") String orderNo, 
			@RequestParam("settleDate") String settleDate,
			@RequestParam("handleSuccess") String handleSuccess);	
}
