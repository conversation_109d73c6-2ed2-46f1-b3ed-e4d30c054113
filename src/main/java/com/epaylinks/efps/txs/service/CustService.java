package com.epaylinks.efps.txs.service;



import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.cust.model.Customer;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.service.dto.cust.CustomerLocationConfig;


@FeignClient(value = "cust" , configuration = DefaultFeignConfiguration.class)
public interface CustService {

	/**
	 * 获取商户提现是否需要报备,以及该笔提现的交易是否有报备
	 * 
	 * @param customerNo
	 * @param cardNo
	 * @return
	 */
	@RequestMapping(value = "/bankCard/checkCustomerCard", method = RequestMethod.GET)
	CommonResponse checkCustomerCard(@RequestParam("customerNo") String customerNo, 
														@RequestParam("cardNo")String cardNo);
	
	/**
	 * 根据客户编号查询cust下游客户
	 * 
	 * @param customerNo
	 * @param cardNo
	 * @return
	 */
	@RequestMapping(value = "/customer/queryCustomerByCustomerNo", method = RequestMethod.GET)
	Customer queryCustomerByCustomerNo(@RequestParam("customerNo") String customerNo);
	/**
	 * 查询微信appid对应secret_key
	 * 
	 * @param customerNo
	 * @param cardNo
	 * @return
	 */
	@RequestMapping(value = "/querySecretKeyByAppid", method = RequestMethod.GET)
	String querySecretKeyByAppid(@RequestParam("subAppid") String subAppid);
	
	/**
	 * 
	 * @param customerCode
	 * @param payPassword
	 * @param captcha 短信验证码 可不填
	 * @return
	 */
	//code是"0000"校验成功
	//伟烨
	//其他编码看message提示
	@RequestMapping(value = "/validate/payPasswordAuthed", method = RequestMethod.POST)
	CommonResponse payPasswordAuthed(
			@RequestParam("username") String customerCode, 
			@RequestParam("payPassword") String payPassword,
			@RequestParam("captcha") String captcha
			);
	
	
	@RequestMapping(value = "/bankCard/querySettleInfo", method = RequestMethod.GET)
	CommonOuterResponse querySettleInfo (@RequestParam( "customerNo") String customerNo);
	
	@RequestMapping(value="/terminal/queryNameByCode",method = RequestMethod.GET)
	String queryNameByCode(@RequestParam("code") String code);
	
	/*arg: ******** name,channel_type或NAME,CHANNEL_TYPE
	 * {
  "returnCode": "0000",
  "returnMsg": "成功",
  "nonceStr": "a13df5f46fc249438dc45372724b4596",
  "data": {
    "CHANNEL_TYPE": "03",
    "NAME": "滦南城东加油站"
  }
}
	 */
	@RequestMapping(value="/terminal/queryTermParams",method = RequestMethod.GET)
	CommonOuterResponse<Map<String, String>> queryTermParams(@RequestParam("terminalCode")String terminalCode, @RequestParam("machineCode")String machineCode, @RequestParam("fields")String fields);
	
	/**
	 * 检查该卡是否结算卡
	 * 
	 * @param customerNo
	 * @param cardNo
	 * @return
	 */
	@RequestMapping(value = "/bankCard/checkCustomerSettCard", method = RequestMethod.GET)
	CommonResponse checkCustomerSettCard(@RequestParam("customerNo") String customerNo, 
														@RequestParam("cardNo")String cardNo);
	
	@RequestMapping(value="/customer/queryLocationConfig",method = RequestMethod.GET)
    CustomerLocationConfig queryLocationConfig(@RequestParam("customerCode") String customerCode);

	/**
	 * 获取客户调账异步地址
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/merchant/queryAdjustAccountNotifyUrl", method = RequestMethod.GET)
	String queryAdjustAccountNotifyUrl(@RequestParam("customerCode") String customerCode);
	
  @RequestMapping(value = "/business/changStatus", method = RequestMethod.POST)
  CommonOuterResponse<Object> changStatus(
        @RequestParam(value = "customerCode", required = true) String customerCode,	//"客户编码"
        @RequestParam(value = "businessCode", required = true) String businessCode,	//"业务编码"
        @RequestParam(value = "settCycle", required = false) String settCycle,	//"结算周期"
        @RequestParam(value = "status", required = true) String status	 //"更新状态：0：停用，1：启用"
    );
  
	/**
	 * 获取平台商异步通知地址
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/merchant/queryTransferAccountNotifyUrl", method = RequestMethod.GET)
	String queryTransferAccountNotifyUrl(@RequestParam("platformCustomerNo") String platformCustomerNo);  
}