package com.epaylinks.efps.txs.service;

import java.util.List;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.service.dto.rc.RcCalculateRequest;

@FeignClient(value = "rc" , configuration = DefaultFeignConfiguration.class)
public interface RcService {
	@PostMapping("/calculate")
	public boolean calculate(@RequestBody List<RcCalculateRequest> rcCalculateRequest);
	
	@PostMapping("/calculateResponse")
	public CommonOuterResponse calculateResponse(@RequestBody List<RcCalculateRequest> rcCalculateRequest);
	
    /**
     * 鏇存柊椋庢帶鐘舵��
     * @param customerCode
     * @param statusName
     * @param status
     * @param reason
     * @param userId
     * @return
     */
    @PostMapping("/rcArchive/updateCustomerRcStatus")
    public CommonOuterResponse<Object> updateCustomerRcStatus(
            @RequestParam("customerCode") String customerCode,
            @RequestParam("statusName") String statusName,  // 状态名称：rcStatus：风控状态；accountStatus：账户状态
            @RequestParam("status") String status,   // statusName为风控状态：0：正常;1：冻结; statusName为账户状态：0:正常;1:冻结;2:止付;3:禁止入金
            @RequestParam("reason") String reason,
            @RequestHeader("x-userid") Long userId);
	
}
