package com.epaylinks.efps.txs.service;

import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.Null;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.pay.response.SignMsg;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;

@FeignClient(value = "UAA" , configuration = DefaultFeignConfiguration.class)
public interface UaaService {
    /**
     * @param businessType
     * @param userType
     * @param remark
     * @param tokenType
     * @param effectiveMinutes
     * @param maxUseTimes
     * @param isAllowDelay
     * @param operator
     * @return
     */
    @RequestMapping(value = "/token/applyBusinessType/{businessType}/{userType}", method = RequestMethod.POST)
    String tokenType(@PathVariable(value = "businessType") String businessType,
                     @PathVariable(value = "userType") Long userType,
                     @RequestParam(value = "remark", required = false) String remark,
                     @RequestParam(value = "tokenType") Long tokenType,
                     @RequestParam(value = "effectiveMinutes") Long effectiveMinutes,
                     @RequestParam(value = "maxUseTimes") Long maxUseTimes,
                     @RequestParam(value = "isAllowDelay") Long isAllowDelay,
                     @RequestParam(value = "operator") String operator);


    /**
     * @param businessType 业务类型
     * @param userId       用户id
     * @param extra        扩展字段
     * @return
     */
    @RequestMapping(value = "/Token", method = RequestMethod.POST)
    String token(@RequestParam(value = "businessType") String businessType,
                 @RequestParam(value = "userId") String userId,
                 @RequestParam(value = "extra") String extra);
    
    /**
	 * 校验验证码接口
	 * @param uid
	 * @param authcode
	 * @param serviceCode
	 * @return
	 */
    @RequestMapping(value = "/VerificationController/VerificationCode",method = RequestMethod.POST)
	public String validateCode(@RequestParam("uid") String uid, @RequestParam("authCode") String authcode, @RequestParam("serviceCode") String serviceCode);

    /**
     * 验证码发送接口
     * @param uid
     * @param serviceCode
     * @return
     */
    @RequestMapping(value = "/VerificationController/ApplyVerificationCode", method = RequestMethod.POST)
    public String applyVerificationCode (@RequestParam("uid") String uid, @RequestParam("serviceCode") String serviceCode, @RequestParam("modelCode")String modelCode);

    /**
     * 数据签名
     * @param input
     * @return
     */
    @RequestMapping(value = "/Sign/signByPost", method = RequestMethod.POST)
    public SignMsg sign(@RequestParam("input") String input);
    
    /**
     * 云闪付数据签名
     * @param input
     * @param certType 普通传3：YSF传4
     * @return
     */
    @RequestMapping(value = "/Sign/ysfSignByPost", method = RequestMethod.POST)
    public SignMsg ysfSign(@RequestParam("input") String input, @RequestParam("certType") Integer certType);
    
    /**
     * 商户公钥加密接口
     * @param input
     * @return
     */
    @RequestMapping(value = "/encryptByMchtPublicKey", method = RequestMethod.POST)
    public SignMsg encryptByMchtPublicKey(@RequestBody Map<String, String> map, @RequestParam("signSN") String signSN);
    
    /**
     * EPSP私钥解密接口
     * @param input
     * @return
     */
    @RequestMapping(value = "/decryptByEpspPrivateKey", method = RequestMethod.POST)
    public Map<String, String> decryptByEpspPrivateKey(@RequestBody Map<String, String> map);
    
    
}
