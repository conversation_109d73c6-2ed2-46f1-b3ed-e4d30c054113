package com.epaylinks.efps.txs.service.dto;

import java.util.Date;
import java.util.List;

/**
 * 退款返回给TXS
 */
public class RefundResponse {

    private String returnCode;

    private String returnMsg;

    private String transactionNo;

    private String transactionType;
    
    private Long totalFee;

    private Date stateUpdateTime;// 退款状态变更时间
    
    private List<String> tkFailCustomerCodes;

	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	public String getReturnMsg() {
		return returnMsg;
	}

	public void setReturnMsg(String returnMsg) {
		this.returnMsg = returnMsg;
	}

	public String getTransactionNo() {
		return transactionNo;
	}

	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	
	public Long getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Long totalFee) {
		this.totalFee = totalFee;
	}

	public Date getStateUpdateTime() {
		return stateUpdateTime;
	}

	public void setStateUpdateTime(Date stateUpdateTime) {
		this.stateUpdateTime = stateUpdateTime;
	}

	public List<String> getTkFailCustomerCodes() {
		return tkFailCustomerCodes;
	}

	public void setTkFailCustomerCodes(List<String> tkFailCustomerCodes) {
		this.tkFailCustomerCodes = tkFailCustomerCodes;
	}
	
}
