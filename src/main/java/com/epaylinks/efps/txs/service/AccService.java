package com.epaylinks.efps.txs.service;

import com.epaylinks.efps.txs.service.dto.acc.DirectVoucherRequest;
import com.epaylinks.efps.txs.service.dto.acc.DirectVoucherResponse;
import com.epaylinks.efps.txs.transaction.domain.acc.AccountVoucher;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.AccountVoucherRes;
import com.epaylinks.efps.common.business.CustomerRequest;
import com.epaylinks.efps.common.business.acc.Account;
import com.epaylinks.efps.common.business.acc.AccountQueryResponse;
import com.epaylinks.efps.txs.config.DefaultFeignConfiguration;
import com.epaylinks.efps.txs.transaction.domain.acc.AccoundAndSubAccounts;

import javax.validation.Valid;

@FeignClient(value = "acc" , configuration = DefaultFeignConfiguration.class)
public interface AccService {
	/**
	 * 获取企业客户状态
	 * 
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/account-voucher/check", method = RequestMethod.GET)
	String getCheck(@RequestParam("transactionNo") String transactionNo);

	@RequestMapping(value = "/accountVoucherQuery", method = RequestMethod.GET)
	AccountVoucher accountVoucherQuery(@RequestParam("transactionNo") String transactionNo);

	
	/**
	 * 查询客户的主账户及其下的所有子账户
	 * @param usrType
	 * @param customerCodeByBus
	 * @return
	 */
	@RequestMapping(value = "/accountWithCustomerCode", method = RequestMethod.GET)
	AccoundAndSubAccounts accountWithCustomerCode(@RequestHeader("x-user-type") String usrType,
			@RequestParam("customerCodeByBus") String customerCodeByBus);
	
	/**
	 * 退汇处理接口
	 * @param transactionNo
	 * @param orgiTxNo
	 * @return
	 */
	@GetMapping(value = "/accountRemittance")
	String accountRemittance(@RequestParam(value = "transactionNo")String transactionNo , 
		      @RequestParam(value = "orgiTxNo")String orgiTxNo);
	
	/**
	 * 查询某个叶子账户
	 * @param usrType
	 * @param customerCodeByBus
	 * @return
	 */
	@RequestMapping(value = "/queryWithCustomerCodeAndAccountType", method = RequestMethod.POST)
	Account queryWithCustomerCodeAndAccountType(@RequestParam(value = "customerCode")String customerCode , 
		      @RequestParam(value = "accountType")String accountType);
	
	@PostMapping("account-voucher-add-check")
	public String accountVoucherCheck(
			@RequestParam(value = "transactionNo", required = true) String transactionNo,
			@RequestParam(value = "transactionType", required = true) String transactionType,
			@RequestBody(required = true) CustomerRequest customerRequest,
			@RequestParam(value = "payChannelCode", required = false) String payChannelCode,
			@RequestParam(value = "payType", required = true) String payType,
			@RequestParam(value = "withdrawAccountType", required = false) String withdrawAccountType,
			@RequestParam(value = "paySuccess", required = true) Integer paySuccess,
			@RequestParam(value = "channelCost", required = false) Long channelCost,
			@RequestParam(value = "summary", required = false) String summary,
			@RequestParam(value = "businessCode", required = false) String businessCode);


	@PostMapping("account-voucher")
	public AccountVoucherRes accountVoucher(
			@RequestParam(value = "transactionNo", required = true) String transactionNo,
			@RequestParam(value = "transactionType", required = true) String transactionType,
			@RequestBody(required = true) CustomerRequest customerRequest,
			@RequestParam(value = "payChannelCode", required = false) String payChannelCode,
			@RequestParam(value = "payType", required = true) String payType,
			@RequestParam(value = "withdrawAccountType", required = false) String withdrawAccountType,
			@RequestParam(value = "paySuccess", required = true) Integer paySuccess,
			@RequestParam(value = "channelCost", required = false) Long channelCost,
			@RequestParam(value = "summary", required = false) String summary,
			@RequestParam(value = "businessCode", required = false) String businessCode,
			@RequestParam(value = "orgiTransactionNo" , required = false) String orgiTransactionNo);
	
	
	@RequestMapping(value = "account-voucher/rollback", method = RequestMethod.POST)
	public String accountVoucherRollback(
		@RequestParam(value = "accountVoucherNo", required = true) String accountVoucherNo
	);
	
	/*
	 * 查询交易账户的余额
	 * */
	@PostMapping(value = "/accountQueryInner")
	AccountQueryResponse accountQuery(
		@RequestHeader("x-customer-code") String x_customer_code,
		@RequestParam("customerCode") String customerCode,
		@RequestParam("accountType") String accountType
	);

	/**
	 *
	 * @param cancelNo 易票联撤销订单号
	 * @param transactionNo 原交易订单号
	 * @return
	 */
	@RequestMapping(value = "account-voucher/revoke", method = RequestMethod.POST)
	public AccountVoucherRes accountVoucherRevoke(
			@RequestParam(value = "transactionNo", required = true) String cancelNo,
			@RequestParam(value = "oriTransactionNo", required = true) String transactionNo
	);

	@PostMapping("api/v1/voucher/direct")
	DirectVoucherResponse direct(@RequestBody @Valid DirectVoucherRequest request);
	
}
