package com.epaylinks.efps.txs.kafka.model;
/**
 * 支付发送的支付结果通知
 *
 * <AUTHOR>
 */

import com.alibaba.fastjson.annotation.JSONField;
import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.model.CustomerInMq;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.List;

public class PayResultMsg {

    TransactionType msgTransactionType;

    String transactionNo;
    Date endTime;
    TxsConstants.PayState payStateEnum;

    /**
     * 支付指令：Recharge：充值 Refund：退款 Withdraw：提现 InsidePay：内转
     */
    private String payInstruction;
    /**
     * 支付的源客户信息，仅包含类型为EFPS客户类型的付款方
     */
    private List<CustomerInMq> sourceCustomerList;
    /**
     * 支付的目标客户信息，仅包含类型为EFPS客户类型的收款方
     */
    private List<CustomerInMq> targetCustomerList;

    /**
     * 渠道返回的订单
     */
    private String channelOrder;

    /**
     * 渠道名字
     */
    private String channelName;

    private String payPassWay;

    /**
     * 渠道小类
     */
    private Long payChannelId;

    /**
     * 渠道大类
     */
    private Long channelCategoryId;

    //马赛克卡号
    private String cardNoMosaic; //CARD_NO_MOSAIC; 打马赛克的卡号

    //加密过的卡号
    private String cardNoEnc; //CARD_NO_ENC; 加密的卡号

    //卡种类
    private String cardType;    //CARD_TYPE;
    /**
     * 银行简码
     */
    private String bankCode;

    //不需要处理此消息的模块.目的在于一个kafka主题,可能多个模块会收到.但是某种情况下,其中的某个模块是不需要处理此消息的
    private List<String> noHandleModules;

    /**
     * 上游通道查询返回码
     */
    private String channelQueryCode;
    /**
     * 上游通道查询返回描述
     */
    private String channelQueryMsg;

    /**
     * 上游流水号
     */
    private String channelTradeNo;

    private String openId;
    private String buyerLogonId;
    private String buyerUserId;

    private String transactionType;
    /**
     * 资金渠道
     */
    private String fundChannel;
    /**
   	 * 现金支付金额，分
   	 */
   	private Long cashAmount;
   	/**
   	 * 代金券金额，分
   	 */
   	private Long couponAmount;
   	
	/*
 	 * 业务代码
 	 */
	private String businessCode;
	
	/**
	 * 计费模式
	 */
	private Short rateMode;
	
	/*
	 * 费率
	 */
	private String procedureRate;	
	
	
	/*
	 * 业务实例id
	 */
	private String businessExamId;
	
	/*
	 * 单笔收费
	 */
	private Long feePer;
	
	/*
	 * 最低收费
	 */
	private Long minFee;
	
	/*
	 * 最高收费
	 */
	private Long maxFee;
	
	/*
	 * 结算周期
	 */
	private String settCycleRuleCode;
	
	/*
	 * 是否执行了二次计费 (使用了新的业务来计费)
	 */
	private String secondCharged; 
	
	/*
	 * 手续费
	 */
	private Long procedureFee;
	
	/**
	 * D1交易节假日附加收费的费率万份比.比如值为200,表示万分之200,即百二
	 */
	private String D1HolidayChargeRate;

	/**
	 * D1交易节假日附加收费的单笔加收费用.单位为分. 比如3表示3分
	 */
	private Long D1HolidayChargePer;
	
	/**
	 * 是否实际应收D1交易节假日附加收费,1表是应收.0为不收(不收可能因为非D1,非节假日,非要收取的业务)
	 */
	private String D1HolidayCharged ;
    

    public String getCardNoMosaic() {
        return cardNoMosaic;
    }

    public void setCardNoMosaic(String cardNoMosaic) {
        this.cardNoMosaic = cardNoMosaic;
    }

    public String getCardNoEnc() {
        return cardNoEnc;
    }

    public void setCardNoEnc(String cardNoEnc) {
        this.cardNoEnc = cardNoEnc;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getChannelTradeNo() {
        return channelTradeNo;
    }

    public void setChannelTradeNo(String channelTradeNo) {
        this.channelTradeNo = channelTradeNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }


    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @JSONField(name = "payState")
    public void setPayState(String payState) {
        this.payStateEnum = TxsConstants.PayState.getEnum(payState);

    }

    @JSONField(name = "payState")
    public String getPayState() {
        return payStateEnum.code;
    }

    @JSONField(serialize = false)
    public TxsConstants.PayState getPayStateEnum() {
        return payStateEnum;
    }

    @JSONField(serialize = false)
    public void setPayStateEnum(TxsConstants.PayState payState) {
        this.payStateEnum = payState;
    }

    public String getPayInstruction() {
        return payInstruction;
    }

    public void setPayInstruction(String payInstruction) {
        this.payInstruction = payInstruction;
    }

    public List<CustomerInMq> getSourceCustomerList() {
        return sourceCustomerList;
    }

    public void setSourceCustomerList(List<CustomerInMq> sourceCustomerList) {
        this.sourceCustomerList = sourceCustomerList;
    }

    public List<CustomerInMq> getTargetCustomerList() {
        return targetCustomerList;
    }

    public void setTargetCustomerList(List<CustomerInMq> targetCustomerList) {
        this.targetCustomerList = targetCustomerList;
    }

    public String getChannelOrder() {
        return channelOrder;
    }

    public void setChannelOrder(String channelOrder) {
        this.channelOrder = channelOrder;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getPayPassWay() {
        return payPassWay;
    }

    public void setPayPassWay(String payPassWay) {
        this.payPassWay = payPassWay;
    }

    public Long getPayChannelId() {
        return payChannelId;
    }

    public void setPayChannelId(Long payChannelId) {
        this.payChannelId = payChannelId;
    }

    public Long getChannelCategoryId() {
        return channelCategoryId;
    }

    public void setChannelCategoryId(Long channelCategoryId) {
        this.channelCategoryId = channelCategoryId;
    }

    public List<String> getNoHandleModules() {
        return noHandleModules;
    }

    public void setNoHandleModules(List<String> noHandleModules) {
        this.noHandleModules = noHandleModules;
    }

    public String getChannelQueryCode() {
        return channelQueryCode;
    }

    public void setChannelQueryCode(String channelQueryCode) {
        this.channelQueryCode = channelQueryCode;
    }

    public String getChannelQueryMsg() {
        return channelQueryMsg;
    }

    public void setChannelQueryMsg(String channelQueryMsg) {
        this.channelQueryMsg = channelQueryMsg;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getBuyerLogonId() {
        return buyerLogonId;
    }

    public void setBuyerLogonId(String buyerLogonId) {
        this.buyerLogonId = buyerLogonId;
    }

    public String getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(String buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public TransactionType getMsgTransactionType() {
        if (msgTransactionType == null) {
            //保险起见，transactionType如果为空则通过transactionNo获取
            if (StringUtils.isNotBlank(transactionType)) {
                msgTransactionType = TransactionType.fromTypeCode(transactionType);
            } else {
                msgTransactionType = TransactionType.fromTransactionNo(transactionNo);
            }
        }
        return msgTransactionType;
    }
    

    public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getFundChannel() {
		return fundChannel;
	}

	public void setFundChannel(String fundChannel) {
		this.fundChannel = fundChannel;
	}
	
	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}
	
/////////////////	

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public Short getRateMode() {
		return rateMode;
	}

	public void setRateMode(Short rateMode) {
		this.rateMode = rateMode;
	}

	public String getProcedureRate() {
		return procedureRate;
	}

	public void setProcedureRate(String procedureRate) {
		this.procedureRate = procedureRate;
	}

	public String getBusinessExamId() {
		return businessExamId;
	}

	public void setBusinessExamId(String businessExamId) {
		this.businessExamId = businessExamId;
	}

	public Long getFeePer() {
		return feePer;
	}

	public void setFeePer(Long feePer) {
		this.feePer = feePer;
	}

	public Long getMinFee() {
		return minFee;
	}

	public void setMinFee(Long minFee) {
		this.minFee = minFee;
	}

	public Long getMaxFee() {
		return maxFee;
	}

	public void setMaxFee(Long maxFee) {
		this.maxFee = maxFee;
	}

	public String getSettCycleRuleCode() {
		return settCycleRuleCode;
	}

	public void setSettCycleRuleCode(String settCycleRuleCode) {
		this.settCycleRuleCode = settCycleRuleCode;
	}

	public String getSecondCharged() {
		return secondCharged;
	}

	public void setSecondCharged(String secondCharged) {
		this.secondCharged = secondCharged;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public String getD1HolidayChargeRate() {
		return D1HolidayChargeRate;
	}

	public void setD1HolidayChargeRate(String d1HolidayChargeRate) {
		D1HolidayChargeRate = d1HolidayChargeRate;
	}

	public Long getD1HolidayChargePer() {
		return D1HolidayChargePer;
	}

	public void setD1HolidayChargePer(Long d1HolidayChargePer) {
		D1HolidayChargePer = d1HolidayChargePer;
	}

	public String getD1HolidayCharged() {
		return D1HolidayCharged;
	}

	public void setD1HolidayCharged(String d1HolidayCharged) {
		D1HolidayCharged = d1HolidayCharged;
	}

	@Override
	public String toString() {
		return "PayResultMsg [msgTransactionType=" + msgTransactionType + ", transactionNo=" + transactionNo
				+ ", endTime=" + endTime + ", payStateEnum=" + payStateEnum + ", payInstruction=" + payInstruction
				+ ", sourceCustomerList=" + sourceCustomerList + ", targetCustomerList=" + targetCustomerList
				+ ", channelOrder=" + channelOrder + ", channelName=" + channelName + ", payPassWay=" + payPassWay
				+ ", payChannelId=" + payChannelId + ", channelCategoryId=" + channelCategoryId + ", cardNoMosaic="
				+ cardNoMosaic + ", cardNoEnc=" + cardNoEnc + ", cardType=" + cardType + ", bankCode=" + bankCode
				+ ", noHandleModules=" + noHandleModules + ", channelQueryCode=" + channelQueryCode
				+ ", channelQueryMsg=" + channelQueryMsg + ", channelTradeNo=" + channelTradeNo + ", openId=" + openId
				+ ", buyerLogonId=" + buyerLogonId + ", buyerUserId=" + buyerUserId + ", transactionType="
				+ transactionType + ", fundChannel=" + fundChannel + ", cashAmount=" + cashAmount + ", couponAmount="
				+ couponAmount + ", businessCode=" + businessCode + ", rateMode=" + rateMode + ", procedureRate="
				+ procedureRate + ", businessExamId=" + businessExamId + ", feePer=" + feePer + ", minFee=" + minFee
				+ ", maxFee=" + maxFee + ", settCycleRuleCode=" + settCycleRuleCode + ", secondCharged=" + secondCharged
				+ ", procedureFee=" + procedureFee + ", D1HolidayChargeRate=" + D1HolidayChargeRate
				+ ", D1HolidayChargePer=" + D1HolidayChargePer + ", D1HolidayCharged=" + D1HolidayCharged + "]";
	}
}
