package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsSplitOrder;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("FZBusiness")
public class FZBusinessImpl implements BusinessTransactionCheck {

	@Autowired
	private TxsSplitOrderMapper txsSplitOrderMapper;
	
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {

		List<TxsSplitOrder> txsSplitOrders = txsSplitOrderMapper.selectByBusinessInstId(businessExamId);
		if (txsSplitOrders != null && !txsSplitOrders.isEmpty()) {
			return true;
		}
		return false;
	}

}
