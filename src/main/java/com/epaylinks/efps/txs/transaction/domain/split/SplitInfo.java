package com.epaylinks.efps.txs.transaction.domain.split;

import com.alibaba.fastjson.JSON;

/**
 * 分账信息
 * <AUTHOR>
 *
 * @date 2018年1月24日 上午9:53:55
 */
public class SplitInfo {
	private String customerCode;
	private Long amount;
	private Integer isProcedureCustomer=0;//1表示该客户为扣除手续费的客户，默认值为0
	private Double splitRatio;
	/**
	 * 结算时间
	 * 拆单分账的结算周期
	 * 拆单分账时必须
	 * 最小值为商户交易业务结算周期时间，最大30天
	 */
	private Integer settleCycle;

	/**
	 * 退款退给谁.针对带券的. 退款时要传
	 */
	private String returnTo;
	
	public Double getSplitRatio() {
		return splitRatio;
	}
	public void setSplitRatio(Double splitRatio) {
		this.splitRatio = splitRatio;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public Integer getIsProcedureCustomer() {
		return isProcedureCustomer;
	}
	public void setIsProcedureCustomer(Integer isProcedureCustomer) {
		this.isProcedureCustomer = isProcedureCustomer;
	}

	public Integer getSettleCycle() {
		return settleCycle;
	}

	public void setSettleCycle(Integer settleCycle) {
		this.settleCycle = settleCycle;
	}

	public String getReturnTo() {
		return returnTo;
	}

	public void setReturnTo(String returnTo) {
		this.returnTo = returnTo;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
