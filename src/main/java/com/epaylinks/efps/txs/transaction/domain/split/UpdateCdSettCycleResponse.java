package com.epaylinks.efps.txs.transaction.domain.split;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public class UpdateCdSettCycleResponse<T> extends CommonOuterResponse {

    private String outTradeNo;

    private String splitTransactionNo;

    private String outSplitTradeNo;

    private List<SplitInfo> splitResultInfoList;

    private String nonceStr;

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getSplitTransactionNo() {
        return splitTransactionNo;
    }

    public void setSplitTransactionNo(String splitTransactionNo) {
        this.splitTransactionNo = splitTransactionNo;
    }

    public String getOutSplitTradeNo() {
        return outSplitTradeNo;
    }

    public void setOutSplitTradeNo(String outSplitTradeNo) {
        this.outSplitTradeNo = outSplitTradeNo;
    }

    public List<SplitInfo> getSplitResultInfoList() {
        return splitResultInfoList;
    }

    public void setSplitResultInfoList(List<SplitInfo> splitResultInfoList) {
        this.splitResultInfoList = splitResultInfoList;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
