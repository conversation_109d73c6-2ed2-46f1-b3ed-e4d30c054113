package com.epaylinks.efps.txs.transaction.domain.query;

/**
 * 分账结果查询请求参数
 * <AUTHOR>
 *
 * @date 2018年1月25日 下午5:07:00
 */
public class SplitResultQueryRequest {
	private String customerCode;
	private String outTradeNo;
	private String transactionNo;
	private String nonceStr;
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public String getNonceStr() {
		return nonceStr;
	}
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
}
