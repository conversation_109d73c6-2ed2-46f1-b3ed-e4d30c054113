package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.business.domain.CumBusinessParamInst;
import com.epaylinks.efps.txs.service.CumService;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("DLBusiness")
public class DLBusinessImpl implements BusinessTransactionCheck {
	@Autowired
	private CumService cumService;
	
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		String[] s = businessExamId.split("_");
		List<CumBusinessParamInst> cumBusinessParamInsts = cumService.cumBusinessParamInsts(s[1], null);
		if (cumBusinessParamInsts != null && !cumBusinessParamInsts.isEmpty()) {
			return true;
		}
		return false;
	}

}
