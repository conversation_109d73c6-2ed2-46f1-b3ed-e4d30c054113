package com.epaylinks.efps.txs.transaction.domain.query;

import java.util.List;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRecord;
/**
 * 分账记录响应
 * <AUTHOR>
 *
 */
public class TxsSplitRecordResponse extends CommonOuterResponse{

	private List<TxsSplitRecord> txsSplitRecords;

	public List<TxsSplitRecord> getTxsSplitRecords() {
		return txsSplitRecords;
	}

	public void setTxsSplitRecords(List<TxsSplitRecord> txsSplitRecords) {
		this.txsSplitRecords = txsSplitRecords;
	}
	

}
