package com.epaylinks.efps.txs.transaction.controller.maintain.dto;

import java.util.List;

import com.epaylinks.efps.common.business.CommonOuterResponse;

public class MainTainSplitResponse extends CommonOuterResponse {
	private List<String> detailedTransactionNos;
	private List<String> notDetailTransactionNos;
	public List<String> getDetailedTransactionNos() {
		return detailedTransactionNos;
	}
	public void setDetailedTransactionNos(List<String> detailedTransactionNos) {
		this.detailedTransactionNos = detailedTransactionNos;
	}
	public List<String> getNotDetailTransactionNos() {
		return notDetailTransactionNos;
	}
	public void setNotDetailTransactionNos(List<String> notDetailTransactionNos) {
		this.notDetailTransactionNos = notDetailTransactionNos;
	}
}
