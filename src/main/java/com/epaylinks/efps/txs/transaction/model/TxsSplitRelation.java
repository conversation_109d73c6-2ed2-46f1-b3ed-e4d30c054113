package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

public class TxsSplitRelation {
    /**
     * 主键
     */
    private Long id;

    /**
     * 分账商户号
     */
    private String customerCode;

    /**
     * 1表示该客户为扣除手续费的客户，默认值为0
     */
    private Integer isProcedureCustomer;

    /**
     * 分账比例，万分之n
     */
    private Integer ratio;

    /**
     * 分账关系序列号
     */
    private String splitRelationId;

    /**
     * 建立时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 所属商户号
     */
    private String belongCustomerCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Integer getIsProcedureCustomer() {
		return isProcedureCustomer;
	}

	public void setIsProcedureCustomer(Integer isProcedureCustomer) {
		this.isProcedureCustomer = isProcedureCustomer;
	}

	public Integer getRatio() {
        return ratio;
    }

    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }

    public String getSplitRelationId() {
        return splitRelationId;
    }

    public void setSplitRelationId(String splitRelationId) {
        this.splitRelationId = splitRelationId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBelongCustomerCode() {
        return belongCustomerCode;
    }

    public void setBelongCustomerCode(String belongCustomerCode) {
        this.belongCustomerCode = belongCustomerCode;
    }
}