package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.UnionJsTrader;
import com.epaylinks.efps.common.business.pay.request.trader.UnionReverseScanTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;

@Service("UnionJs")
public class UnionJsPayerImpl implements PayerService {

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		UnionJsTrader unionJsTrader = new  UnionJsTrader();
		unionJsTrader.setUserId(MapUtils.getString(map, "userId"));
		unionJsTrader.setOrderDesc(MapUtils.getString(map, "orderDesc"));
		unionJsTrader.setType(TraderType.UnionJs);
		return unionJsTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO Auto-generated method stub
		Payer payer = new Payer();
		payer.setPayerType(TraderType.UnionJs.name());
		payer.setPayerId(payerJson.getString("userId"));
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}

