package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.BankTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;
@Service("PersonalBanking")
public class PersonBankPayerServiceImpl implements PayerService{

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		BankTrader bankTrader = new BankTrader();
		bankTrader.setBankCode(MapUtils.getString(map, "bankCode"));
		bankTrader.setType(TraderType.PersonalBanking);
		return bankTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO Auto-generated method stub
		Payer payer = new Payer();
		payer.setPayerType(TraderType.PersonalBanking.name());
		payer.setPayerId(payerJson.getString("bankCode"));
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		if (!payer.containsKey("bankCode") || !payer.containsKey("bankCardType")) {
			return false;
		}
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
