package com.epaylinks.efps.txs.transaction.domain.query;

import java.util.List;

public class QueryParam {
	private String transactionNo;
	private String outTradeNo; 
	private String customerCode; 
	private String payState;
	private String beginTime; 
	private String endTime; 
	private String transactionType; 
	private String payMethod;
	List<String> expandCustomerCodes; 
	private String expandCustomerName;
	private String outRefundNo; 
	private String customerName; 
	private String businessCode; 
	private String channelName;
	private String payPassWay;
	private String agentCustomerCode;
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getPayState() {
		return payState;
	}
	public void setPayState(String payState) {
		this.payState = payState;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getTransactionType() {
		return transactionType;
	}
	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	public List<String> getExpandCustomerCodes() {
		return expandCustomerCodes;
	}
	public void setExpandCustomerCodes(List<String> expandCustomerCodes) {
		this.expandCustomerCodes = expandCustomerCodes;
	}
	public String getExpandCustomerName() {
		return expandCustomerName;
	}
	public void setExpandCustomerName(String expandCustomerName) {
		this.expandCustomerName = expandCustomerName;
	}
	public String getOutRefundNo() {
		return outRefundNo;
	}
	public void setOutRefundNo(String outRefundNo) {
		this.outRefundNo = outRefundNo;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public String getChannelName() {
		return channelName;
	}
	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
	public String getPayPassWay() {
		return payPassWay;
	}
	public void setPayPassWay(String payPassWay) {
		this.payPassWay = payPassWay;
	}
	public String getAgentCustomerCode() {
		return agentCustomerCode;
	}
	public void setAgentCustomerCode(String agentCustomerCode) {
		this.agentCustomerCode = agentCustomerCode;
	}
}
