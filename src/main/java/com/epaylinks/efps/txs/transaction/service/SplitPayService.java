package com.epaylinks.efps.txs.transaction.service;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import com.epaylinks.efps.txs.constants.FzCode;
import com.epaylinks.efps.txs.service.*;
import com.epaylinks.efps.txs.service.dto.settUpdtSettCycleRequest;
import com.epaylinks.efps.txs.transaction.domain.split.*;
import com.github.pagehelper.Constant;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.business.acc.AccountQueryResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.domain.CumBusinessParamInst;
import com.epaylinks.efps.common.business.domain.InsidePayCustomer;
import com.epaylinks.efps.common.business.domain.InsidePayRequest;
import com.epaylinks.efps.common.business.domain.InsidePayResponse;
import com.epaylinks.efps.common.business.pay.request.PayMethod;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.business.split.ExecuteSplitRequest;
import com.epaylinks.efps.common.business.split.SplitByRelationRequest;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.kafka.txs.TxsPayResultMsg;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.mykafka.SendToNts;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.Constants.BusinessCode;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.service.dto.rc.RcCalculateRequest;
import com.epaylinks.efps.txs.transaction.dao.AuthInfoMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsPayTradeOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsPreOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitRecordMapper;
import com.epaylinks.efps.txs.transaction.domain.SplitResultInfo;
import com.epaylinks.efps.txs.transaction.domain.notify.SplitResultNotify;
import com.epaylinks.efps.txs.transaction.model.ProcedureFeeResult;
import com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitOrder;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord;
import com.epaylinks.efps.txs.transaction.model.TxsSplitOrder;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRecord;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRelation;

/**
 * 分账服务
 *
 * <AUTHOR>
 *
 * @date 2018年1月24日 上午10:30:36
 */

@Service
public class SplitPayService {

	@Value("${FZTransaction}")
	private String FZTransaction;
	@Value("${FZJYTransaction}")
	private String FZJYTransaction;
	@Value("${fz.systemId}")
	private String systemId;

	@Autowired
	private SplitPayService self;


	@Autowired
	private TxsPreOrderMapper txsPreOrderMapper;
	@Autowired
	private TxsSplitOrderMapper txsSplitOrderMapper;
	@Autowired
	private TxsSplitRecordMapper txsSplitRecordMapper;
	@Autowired
	private TransactionService transactionService;
	@Autowired
	private SplitRelationService splitRelationService;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private PayService payService;
	@Autowired
	private CumService cumService;
	@Autowired
	private SendToNts sendToNts;
	@Autowired
	private RcService rcService;
	@Autowired
	private RiskCalcService riskCalcService;
	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;
	@Autowired
	private CommonService commonService;
	@Autowired
	private ReturnCodeUtil returnCodeUtil;

	private final String format = "yyyyMMddHHmmss";
	@Value("${cashAmountChargePaymethod:4,7,14}")	
	private String cashAmountChargePaymethod ;//减优惠券结算类
	@Value("${afterPayChargePaymethod:49,40,41,24,25}")	
	private String cardTypeChargePaymethod ;//借贷分离计费类

	@Autowired
	private CumCacheServiceImpl cumCacheService;

	@Autowired
    private TxsPayTradeOrderMapper txsPayTradeOrderMapper;
	
	@Autowired
	private AuthInfoMapper authInfoMapper;

	@Autowired
	private AccService accService;

	@Autowired
	private SettService settService;

	@Autowired
	private RedisLockService redisLockService;

	private static final String SettCycleRuleCode_X = "X";

	/**
	 * 分账服务
	 *
	 * @param customerHead
	 * @param fenZhangRequest
	 * @return
	 */
	@Logable(businessTag = "splitPay", outputArgs=false, outputResult=false)
	public FenZhangResponse splitPay(String customerCodeHead, FenZhangRequest fenZhangRequest) {
		// 分账客户编码与请求头不一致
//		if (!fenZhangRequest.getCustomerCode().equals(customerCodeHead)) {
//			throw new AppException(TxsCode.CUSTOMER_DIFFER.code,TxsCode.CUSTOMER_DIFFER.message);
//		}
		transactionService.checkCustomerCodeAndHead(customerCodeHead, fenZhangRequest.getCustomerCode());

		return self.splitCommon(fenZhangRequest.getOutTradeNo(), fenZhangRequest.getCustomerCode(),
				fenZhangRequest.getNotifyUrl(), fenZhangRequest.getSplitInfoList(), fenZhangRequest.getOutSplitTradeNo());
	}

	/**
	 * 分账接口共用
	 * 校验参数，插入分账订单和分账记录
	 * 如果PreOrder已经结算成功，调用支付服务的内转接口完成分账
	 * 调用支付服务的内转结果并不会影响订单的插入
	 * @param txsPreOrder
	 * @param outTradeNo
	 * @param customerCode
	 * @param notifyUrl
	 * @param splitInfoList
	 * @return
	 */
	@Logable(businessTag = "splitCommon")
	public FenZhangResponse splitCommon(String outTradeNo, String customerCode, String notifyUrl,
			List<SplitInfo> splitInfoListParam, String outSplitTradeNo) {
		FenZhangResponse fenZhangResponse = new FenZhangResponse();
		fenZhangResponse.setOutTrandNo(outTradeNo);
		fenZhangResponse.setNonceStr(UUIDUtils.uuid());
		TxsPreOrder txsPreOrder = queryOrigPayPreOrder(outTradeNo, customerCode);
		if(!txsPreOrder.getTransactionType().equalsIgnoreCase(FZTransaction))
			throw new AppException(TxsCode.ORGI_PAY_ORDRE_UNSUPPORT_FZ.code);

		if(StringUtils.isNotEmpty(outSplitTradeNo) && !TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			throw  new AppException(TxsCode.MUST_NOT_SPLIT_NO.code);
		}

		if(StringUtils.isEmpty(outSplitTradeNo) && TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			throw  new AppException(TxsCode.MUST_SPLIT_NO.code);
		}


		String isMultiOrderNonFirst = isTheNonFirstOfTheMulti(txsPreOrder);
		List<SplitInfo> splitInfoList = null;

		if(Constants.YesOrNoByNum.YES.code.equals(isMultiOrderNonFirst)){
			splitInfoList = self.checkSplitInfoParamMultiSplitNotFirst(splitInfoListParam, txsPreOrder.getSplitModel());
		}else{
			splitInfoList = self.checkSplitInfoParamIsNotNull(splitInfoListParam, txsPreOrder.getSplitModel());
		}


		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			//Long totalDivAmount = txsPreOrderMapper.selectTotalDivideAmount(txsPreOrder.getOrderId());
		}else{
			//注:********, jinyan确定分账类退货只能全额退.这里可以认为只要有退货,就是全额的.即有原交易发生过退货,即不允许分账
			if(txsPreOrderMapper.selectTotalRefundAmt(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode())>0) {
				throw new AppException(TxsCode.ORDER_REFUND.code);
			}
		}


		Long slaveAmountSum=self.checkSplitInfoAmountRight(txsPreOrder, splitInfoList);

		String memberId = txsPreOrder.getMemberId();
		String bankIcon = txsPreOrder.getBankCode();
		boolean canSplitCond2 = true;
		CommonOuterResponse checkResponse = checkSplitDetail(splitInfoListParam, customerCode, memberId, txsPreOrder.getAmount(), txsPreOrder.getBusinessCode(),txsPreOrder.getSplitModel());
		if(!checkResponse.isSuccess()) {
//			fenZhangResponse.setReturnCode(checkResponse.getReturnCode());
//			fenZhangResponse.setReturnMsg(checkResponse.getReturnMsg());
//			return fenZhangResponse;
			throw new AppException(checkResponse.getReturnCode(), checkResponse.getReturnMsg());
		}

		self.checkCustomersBusiness(customerCode,splitInfoList);

		// 查询数据库是否有分账订单数据
		TxsSplitOrder txsSplitOrderSql = null;
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			txsSplitOrderSql = txsSplitOrderMapper.selectByThree(outSplitTradeNo,
					customerCode, outSplitTradeNo);
		}else{
			txsSplitOrderSql = txsSplitOrderMapper.selectByThree(outTradeNo,
					customerCode, null);
		}

		if (txsSplitOrderSql != null) {// 构造重复请求响应返回
			throw new AppException(TxsCode.REPEAT_SPLIT.code,TxsCode.REPEAT_SPLIT.message);
		}

		List<TxsSplitRecord> txsSplitRecords = new ArrayList<>();
		Long procedureFee = txsPreOrder.getProcedureFee();
		ProcedureFeeResult procedureFeeResult = null;
		CustomerBusinessInstance businessInst = null;
		if (procedureFee == null) {// 支付尚未成功，因此没有手续费，计算可能的最高的手续费
			if (StringUtils.isBlank(txsPreOrder.getPayMethod())) {
/*				procedureFee = transactionService.calcMaxPayProcedure(customerCode, txsPreOrder.getAmount())
						.getProcedureFee();*///////后续的快捷类交易是按银行计费的,这种情况下,如果该businessExamId设置了按银行计价但又不传bankIcon,会抛异常!因此这里改为0.待后续收到sett的kafka后再重新计算
				procedureFee = 0L;
				canSplitCond2 = false;
			} else {
				procedureFeeResult = transactionService.calcProcedure(customerCode, txsPreOrder.getAmount(),
						txsPreOrder.getPayMethod(), null, FZTransaction, null, bankIcon, txsPreOrder.getSplitModel());
				procedureFee = procedureFeeResult.getProcedureFee();
				businessInst = procedureFeeResult.getBusinessInst();
				commonService.payLog("procedureFee:"+procedureFee);

			}
		}
//		else {
////			List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
////	                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);
////	        businessInst = transactionService.decideBusinessInst(customerInstanceList, null, txsPreOrder.getBusinessCode(), txsPreOrder.getTransactionType());			//手续费不为空，延迟分账
////			businessInst = transactionService.getBusinessInstNew(customerCode, null, txsPreOrder.getBusinessCode(), txsPreOrder.getTransactionType());
////			businessInst = null;
//		}

		Long splitProcedureFeeBaseAmount = txsPreOrder.getAmount();
		if(Constants.YesOrNoByNum.YES.code.equals(txsPreOrder.getIsTicket())){
			splitProcedureFeeBaseAmount = txsPreOrder.getPayerAmount();
		}
		String fzBusinessCode = Constants.BusinessCode.FZ.code;
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			fzBusinessCode = Constants.BusinessCode.CDFZ.code;
		}
		ProcedureFeeResult splitProcedureFeeResult = transactionService.calcProcedure(customerCode, splitProcedureFeeBaseAmount,
				null, fzBusinessCode, FZTransaction, null);

		if((procedureFeeResult != null && procedureFeeResult.getBusinessInst().getBusinessCode().startsWith(FZTransaction))
				|| (txsPreOrder.getBusinessCode() != null && txsPreOrder.getBusinessCode().startsWith(FZTransaction))) {
			splitProcedureFeeResult.setProcedureFee(0L);//历史分账类交易FZ-WXNATIVE 不算分账手续费
		}

		TxsSplitOrder txsSplitOrder = self.createSplitOrderAndRecords(txsPreOrder,procedureFee,customerCode,
				outTradeNo,notifyUrl,splitInfoList,slaveAmountSum,
				txsSplitRecords, businessInst, splitProcedureFeeResult, outSplitTradeNo);

		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			if(Constants.YesOrNoByNum.YES.code.equals(txsSplitOrder.getIsFirst())){
				self.fillSplitProcedureFeeAndCheckBurden(txsPreOrder, splitProcedureFeeResult, splitInfoList, procedureFee);
			}
			//即是:拆单非第一次不用管手续费
		}else{
			self.fillSplitProcedureFeeAndCheckBurden(txsPreOrder, splitProcedureFeeResult, splitInfoList, procedureFee);
		}

		// 数据持久化
		self.insertSplitOrderAndRecords(txsPreOrder,txsSplitOrder, txsSplitRecords);
		//不论达不达到分账条件,都更新一下分账手续费。以免有一种情况:当时不够分账,当够钱分账后,会用
		//txsPayTradeOrder的分账手续费更新到txsPreOrder,导致一直为0
		try{
			self.updateTxsPayTradeOrderSplitProcedureFee(txsPreOrder.getTransactionNo(), splitProcedureFeeResult);
		}catch (Exception e){
			commonService.logException(e);
		}

		if (self.canExecuteSplit(txsPreOrder) && canSplitCond2) {
			if(!redisLockService.setReturnSplitMutex(txsPreOrder.getTransactionNo())){
				fenZhangResponse.setReturnCode(TxsCode.SPLIT_RETURN_SAME_TIME.code);
				return fenZhangResponse;
			}
			self.checkAndUpdateStateReadyToExcute(txsSplitOrder.getTransactionNo());
			TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNo(txsPreOrder.getTransactionNo());
			//更新分账记录
//			self.updateTxsSplitRecord(txsPreOrder.getOrderId());
			self.updateTxsSplitRecord(txsPayTradeOrder, txsSplitOrder, txsSplitRecords);
			
			
			TxsPayTradeOrder txsPayForUpd = new TxsPayTradeOrder();
			txsPayForUpd.setId(txsPayTradeOrder.getId());
			txsPayForUpd.setSplitProcedureFee(splitProcedureFeeResult.getProcedureFee());
			txsPayForUpd.setSplitProcedureRate(splitProcedureFeeResult.getBusinessInst().getRateParam());
			txsPayTradeOrderMapper.updateByPrimaryKeySelective(txsPayForUpd);
			
//			reCalcProcedureAndPersistIfNeed(txsPayTradeOrder, txsSplitOrder, txsSplitRecords);
			InsidePayRequest insidePayRequest = self.createInsidePayRequest(txsSplitOrder, txsSplitRecords, txsPayTradeOrder.getControlled());
			self.executeSplit(insidePayRequest,
					new Date(), txsSplitOrder, txsSplitRecords);
			redisLockService.deleteReturnSplitMutex(txsPreOrder.getTransactionNo());
		}
		//只要上面不抛出异常，就可以返回成功响应

		fenZhangResponse.setReturnCode(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.code);
		return fenZhangResponse;
	}
	

	/**
	 * 根据分账关系号进行分账
	 * @param txsPreOrder
	 * @param outTradeNo
	 * @param customerCode
	 * @param notifyUrl
	 * @param splitRelationId
	 * @return
	 */
	@Logable(businessTag = "splitCommon")
	public FenZhangResponse splitByRelationId(String outTradeNo, String customerCode, String notifyUrl,
			Long amount, String splitRelationId) {
		List<TxsSplitRelation> txsSplitRelationList = splitRelationService.query(customerCode, splitRelationId);
		List<SplitInfo> splitInfoListParam = new ArrayList<>();
		BigDecimal bigAmount = new BigDecimal(amount);
		Long splitAmountSum = 0l;
		for(int i=0; i<txsSplitRelationList.size(); i++) {
			TxsSplitRelation item = txsSplitRelationList.get(i);

			SplitInfo splitInfo = new SplitInfo();
			splitInfo.setCustomerCode(item.getCustomerCode());
			splitInfo.setIsProcedureCustomer(item.getIsProcedureCustomer());
			BigDecimal splitRatio = new BigDecimal(item.getRatio()).divide(new BigDecimal(10000)).setScale(4, BigDecimal.ROUND_HALF_UP);
			splitInfo.setSplitRatio(splitRatio.doubleValue());

			if(i == txsSplitRelationList.size() -1) {//最后一个金额，不用比例计算，用总金额减去之前的。
				splitInfo.setAmount(amount - splitAmountSum);
			}
			else {
				BigDecimal splitAmount = bigAmount.multiply(new BigDecimal(item.getRatio())).
						divide(new BigDecimal(10000)).setScale(0, BigDecimal.ROUND_HALF_UP);

				splitInfo.setAmount(splitAmount.longValue());
				splitAmountSum += splitAmount.longValue();
			}
			splitInfoListParam.add(splitInfo);
		}

		return splitCommon(outTradeNo, customerCode, notifyUrl, splitInfoListParam, null);
	}
	/**
	 * 根据分账关系号进行分账
	 * @param splitByRelationRequest
	 * @return
	 */
	@Logable(businessTag = "splitByRelationId")
	public FenZhangResponse splitByRelationId(SplitByRelationRequest splitByRelationRequest) {
		
		String outTradeNo = splitByRelationRequest.getOutTradeNo();
		String customerCode = splitByRelationRequest.getCustomerCode();
		String notifyUrl = splitByRelationRequest.getNotifyUrl();
		Long amount = splitByRelationRequest.getAmount();
		String splitRelationId = splitByRelationRequest.getSplitRelationId();
		
		List<TxsSplitRelation> txsSplitRelationList = splitRelationService.query(customerCode, splitRelationId);
		List<SplitInfo> splitInfoListParam = new ArrayList<>();
		BigDecimal bigAmount = new BigDecimal(amount);
		Long splitAmountSum = 0l;
		for(int i=0; i<txsSplitRelationList.size(); i++) {
			TxsSplitRelation item = txsSplitRelationList.get(i);

			SplitInfo splitInfo = new SplitInfo();
			splitInfo.setCustomerCode(item.getCustomerCode());
			splitInfo.setIsProcedureCustomer(item.getIsProcedureCustomer());
			BigDecimal splitRatio = new BigDecimal(item.getRatio()).divide(new BigDecimal(10000)).setScale(4, BigDecimal.ROUND_HALF_UP);
			splitInfo.setSplitRatio(splitRatio.doubleValue());

			if(i == txsSplitRelationList.size() -1) {//最后一个金额，不用比例计算，用总金额减去之前的。
				splitInfo.setAmount(amount - splitAmountSum);
			}
			else {
				BigDecimal splitAmount = bigAmount.multiply(new BigDecimal(item.getRatio())).
						divide(new BigDecimal(10000)).setScale(0, BigDecimal.ROUND_HALF_UP);

				splitInfo.setAmount(splitAmount.longValue());
				splitAmountSum += splitAmount.longValue();
			}
			splitInfoListParam.add(splitInfo);
		}

		return splitCommon(outTradeNo, customerCode, notifyUrl, splitInfoListParam, null);
	}
	
	/**
	 * 校验分账信息
	 * 分账级别,以及分润比例.拆单位分账不校验比例
	 * @param splitInfoList
	 * @param customerCode
	 * @param memberId
	 * @param payAmount
	 * @param maxProfit
	 * @return
	 */
	public CommonOuterResponse checkSplitDetail(List<SplitInfo> splitInfoList, String customerCode, String memberId, Long payAmount, String businessCode,String spilitModel) {
		CommonOuterResponse response = new CommonOuterResponse();

		ArrayList<String> customerCodeList = new ArrayList<>();
		customerCodeList.add(customerCode);
		customerCodeList.add(memberId);
		long profitAmount = 0l;
		if(splitInfoList.size() > 100) {
			throw new AppException(TxsCode.OUT_SPLIT_SIZE.code);
		}
		if(splitInfoList != null && splitInfoList.size() != 0) {
			for (SplitInfo splitInfo:splitInfoList) {
				customerCodeList.add(splitInfo.getCustomerCode());
			}
		}

		Map<String, CustomerInfo> customerParentMap = cumService.queryParentCustomerCode(customerCodeList, null);
		CustomerInfo tradeCustomerInfo = customerParentMap.get(customerCode);//交易主体商户
		CustomerInfo memberCustomerInfo = customerParentMap.get(memberId);//主分账商户

//		self.checkSplitMain(customerCode, memberId, spilitModel);
//		if(StringUtils.isNotBlank(spilitModel)) {
//			if (StringUtils.isBlank(memberId)){
//				response.setReturnCode(TxsCode.SPLIT_MAIN_EMPTY.code);
//				response.setReturnMsg(TxsCode.SPLIT_MAIN_EMPTY.message);
//				return response;
//			}
//			//简易商户不允许被分账
//			if(Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(memberCustomerInfo.getCustomerCategory())) {
//				response.setReturnCode(TxsCode.SPLIT_MAIN_ERROR.code);
//				response.setReturnMsg(TxsCode.SPLIT_MAIN_ERROR.message);
//				return response;
//			}
//			
//			//普通分账
//			if(TxsConstants.SplitModel.COMMON.code.equals(spilitModel)) {
//				//普通分账，分账主体不是能是会员，只能是发起交易的平台商或者普通商户
//				if(Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(memberCustomerInfo.getCustomerCategory()) || 
//						!customerCode.equals(memberId)) {
//					response.setReturnCode(TxsCode.SPLIT_MAIN_ERROR.code);
//					response.setReturnMsg(TxsCode.SPLIT_MAIN_ERROR.message + ":" + memberId);
//					return response;
//				}
//			}
//			//分账模式为2时校验
//			if (TxsConstants.SplitModel.ORTHER.code.equals(spilitModel)){
//				//其他分账，分账主体只能是会员
//				if(!Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(memberCustomerInfo.getCustomerCategory())) {
//					response.setReturnCode(TxsCode.SPLIT_MAIN_ERROR.code);
//					response.setReturnMsg(TxsCode.SPLIT_MAIN_ERROR.message + ":" + memberId);
//					return response;
//				}
//				Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(memberId, customerCode);
//				boolean isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
//				//一个树形下，或者归属同一个平台商户
//				if (StringUtils.isNotBlank(memberCustomerInfo.getPlatCustomerCode())
//						&& memberCustomerInfo.getPlatCustomerCode().equals(tradeCustomerInfo.getPlatCustomerCode()) 
//						|| isAncestorCustomer) {
//					//合法主分账
//				}
//				else {
//					response.setReturnCode(TxsCode.SPLIT_MAIN_ERROR.code);
//					response.setReturnMsg(TxsCode.SPLIT_MAIN_ERROR.message);
//					return response;
//				}
//			}
//			
//		}

		boolean isMultiOrder = TxsConstants.SplitModel.MULTI_ORDER.code.equals(spilitModel);
		boolean isAuthTrade = false;
		if(splitInfoList != null && splitInfoList.size() != 0) {
			//交易商户的父商户
			String customerParent = tradeCustomerInfo.getParentCustomerCode();

			for (SplitInfo splitInfo:splitInfoList) {

				CustomerInfo splitCustomerInfo = customerParentMap.get(splitInfo.getCustomerCode());
				if(splitCustomerInfo == null) {
					response.setReturnCode(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.code);
					response.setReturnMsg(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.message + ":" + splitInfo.getCustomerCode());
					return response;
				}
				//简易商户不允许被分账
				if(Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(splitCustomerInfo.getCustomerCategory())) {
					response.setReturnCode(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.code);
					response.setReturnMsg(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.message + ":" + splitInfo.getCustomerCode());
					return response;
				}

				String splitParent = splitCustomerInfo.getParentCustomerCode();

				//商户下级会员商户校验
				if(Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(splitCustomerInfo.getCustomerCategory())) {
					if(splitParent != null && !splitParent.equals(customerCode) && !splitParent.equals(customerParent)
							&& !customerCode.equals(splitCustomerInfo.getPlatCustomerCode())) {
						response.setReturnCode(TxsCode.MEMBER_NOTBELONG_MERCHANT.code);
						response.setReturnMsg(TxsCode.MEMBER_NOTBELONG_MERCHANT.message + ":" + splitInfo.getCustomerCode());
						return response;
					}

					//20190604产品要去掉，是一个父子体系的就行，不校验会员商户必须是交易会员本身
//					if(businessCode != null && businessCode.startsWith(Constants.EfpsNocardService.FZ_NOCARD_PAY.code)) {
//						if(!splitInfo.getCustomerCode().equals(memberId)) {
//							response.setReturnCode(TxsCode.SPLIT_NOT_MATCH_MEMBERID.code);
//							response.setReturnMsg(TxsCode.SPLIT_NOT_MATCH_MEMBERID.message + ":" + splitInfo.getCustomerCode());
//							return response;
//						}
//					}
				}
				//商户校验
//				else if(Constants.customerCategory.EFPS_CUSTOMER.code.equals(splitCustomerInfo.getCustomerCategory())) {
				else {
					boolean flag = false;
					//本身
					if(customerCode.equals(splitInfo.getCustomerCode())) {
						flag = true;
					}
					//向上分账校验，分账商户是否等于交易商户的父商户
					if(customerParent != null && customerParent.equals(splitInfo.getCustomerCode())) {
						flag = true;
					}
					//平级分账校验，分账商户父商户是否为交易商户
					if(splitParent != null && splitParent.equals(customerParent)) {
						flag = true;
					}

					//向下分账，平台商户分给子商户
					if(splitParent != null && splitParent.equals(customerCode)) {
						flag = true;
					}

					if(!flag) {
						int authCount = authInfoMapper.countByCustCodeAndType(customerCode, splitInfo.getCustomerCode(), Constants.AuthType.bfz.code, null);

						if(authCount > 0) {
							isAuthTrade = true;
						}
						else {
							response.setReturnCode(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.code);
							response.setReturnMsg(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.message + ":" + splitInfo.getCustomerCode());
							return response;
						}
					}
				}
				
				int splitAuthCount = authInfoMapper.countByCustCodeAndType(memberId, splitInfo.getCustomerCode(), Constants.AuthType.splitMain.code, null);
				//非分账主体  且 未授权 累计分账金额
				if(!splitInfo.getCustomerCode().equals(memberId) && splitAuthCount <= 0 && splitInfo.getAmount() > 0) {//splitInfo.getAmount()小于0时，为出资商户，不计为分润
					//分润总金额
					profitAmount += splitInfo.getAmount();
				}
			}
			//无卡分账 或者 传了分账模式
			//|| StringUtils.isNotBlank(spilitModel)
			List<CumBusinessParamInst> bpis = null;
			if(businessCode != null && businessCode.startsWith(Constants.EfpsNocardService.FZ_NOCARD_PAY.code)) {

				CustomerBusinessInstance customerBusinessInstance = transactionService.getBusinessInstId(customerCode, businessCode);
				bpis = cumCacheService.getCustomerBusinessParamInst(customerBusinessInstance.getBusinessExamId());
				if(!isAuthTrade && !isMultiOrder) {
					doCheckSplitAmount(payAmount, profitAmount, bpis);
				}
			}
			else if(StringUtils.isNotBlank(spilitModel)) {

				String querySplitModel = judgeQuerySplitModel(spilitModel);
				String rateCustomer = customerCode;
				if(StringUtils.isNotBlank(tradeCustomerInfo.getPlatCustomerCode())) {
					//平台商不为空，取平台商的比例参数
					bpis = getCustomerBusinessParamInst(tradeCustomerInfo.getPlatCustomerCode(), querySplitModel);
				}

				if(bpis == null || bpis.size() == 0){
					bpis = getCustomerBusinessParamInst(rateCustomer, querySplitModel);
				}
				if(!isAuthTrade && !isMultiOrder) {
					doCheckSplitAmount(payAmount, profitAmount, bpis);
				}
			}


		}

		return response;
	}


	String judgeQuerySplitModel(String origSplitModel){
		if(StringUtils.isNotEmpty(origSplitModel)){
			if(TxsConstants.SplitModel.MEMBER.code.equals(origSplitModel)){
				return TxsConstants.SplitModel.ORTHER.code;
			}

			if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(origSplitModel)){
				return TxsConstants.SplitModel.COMMON.code;
			}
		}
		return origSplitModel;
	}
	/**
	 * 简易校验分账信息
	 * @param splitInfoList
	 * @return
	 */
	public CommonOuterResponse checkSplitDetailSimple(List<SplitInfo> splitInfoList) {
		CommonOuterResponse response = new CommonOuterResponse();

		ArrayList<String> customerCodeList = new ArrayList<>();

		long profitAmount = 0l;
		if(splitInfoList.size() > 100) {
			throw new AppException(TxsCode.OUT_SPLIT_SIZE.code);
		}
		if(splitInfoList != null && splitInfoList.size() != 0) {
			for (SplitInfo splitInfo:splitInfoList) {
				customerCodeList.add(splitInfo.getCustomerCode());
			}
		}

		Map<String, CustomerInfo> customerParentMap = cumService.queryParentCustomerCode(customerCodeList, null);


		boolean isAuthTrade = false;
		if(splitInfoList != null && splitInfoList.size() != 0) {


			for (SplitInfo splitInfo : splitInfoList) {

				CustomerInfo splitCustomerInfo = customerParentMap.get(splitInfo.getCustomerCode());
				if (splitCustomerInfo == null) {
					response.setReturnCode(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.code);
					response.setReturnMsg(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.message + ":" + splitInfo.getCustomerCode());
					return response;
				}
				//简易商户不允许被分账
				if (Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(splitCustomerInfo.getCustomerCategory())) {
					response.setReturnCode(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.code);
					response.setReturnMsg(TxsCode.SPLIT_NOT_MATCH_CUSTOMER.message + ":" + splitInfo.getCustomerCode());
					return response;
				}

				String splitParent = splitCustomerInfo.getParentCustomerCode();

			}
		}

		return response;
	}

	public void checkSplitMain(String customerCode, String memberId, String spilitModel) {

		if(StringUtils.isNotBlank(spilitModel)) {

			CustomerInfo memberCustomerInfo = cumCacheService.getCustomerInfo(memberId, memberId, "1");//主分账商户
			CustomerInfo tradeCustomerInfo = cumCacheService.getCustomerInfo(customerCode, customerCode, "1");

			if(customerCode == null) {
				throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code);
			}
			if(memberId == null) {
				throw new AppException(TxsCode.SPLIT_MAIN_UNSUPPORT.code);
			}

			if (StringUtils.isBlank(memberId)){
				throw new AppException(TxsCode.SPLIT_MAIN_EMPTY.code);
			}
			//简易商户不允许被分账
			if(Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(memberCustomerInfo.getCustomerCategory())) {
				throw new AppException(TxsCode.SPLIT_MAIN_ERROR.code);
			}

			//普通分账
			if(TxsConstants.SplitModel.COMMON.code.equals(spilitModel)) {
				//普通分账，分账主体不是能是会员，只能是发起交易的平台商或者普通商户
				if(Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(memberCustomerInfo.getCustomerCategory()) ||
						!customerCode.equals(memberId)) {
					throw new AppException(TxsCode.SPLIT_MAIN_ERROR.code);

				}
			}
			//分账模式为2时校验
			if (TxsConstants.SplitModel.ORTHER.code.equals(spilitModel)  || TxsConstants.SplitModel.MEMBER.code.equals(spilitModel)){
				//其他分账，分账主体只能是会员
				if(!Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(memberCustomerInfo.getCustomerCategory())) {
					throw new AppException(TxsCode.SPLIT_MAIN_ERROR.code);
				}
				Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(memberId, customerCode);
				boolean isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
				//一个树形下，或者归属同一个平台商户
				if (StringUtils.isNotBlank(memberCustomerInfo.getPlatCustomerCode())
						&& memberCustomerInfo.getPlatCustomerCode().equals(tradeCustomerInfo.getPlatCustomerCode())
						|| isAncestorCustomer) {
					//合法主分账
				}
				else {
					throw new AppException(TxsCode.SPLIT_MAIN_ERROR.code);
				}
			}

		}
	}
	private List<CumBusinessParamInst> getCustomerBusinessParamInst(String customerCode, String spilitModel){
		String businessExamId = "FZ_" + customerCode + "_" + spilitModel;//FZ_5651300000000952_2
		List<CumBusinessParamInst> bpis = cumCacheService.getCustomerBusinessParamInst(businessExamId);
		return bpis;
	}

	private void doCheckSplitAmount(Long payAmount,Long profitAmount, List<CumBusinessParamInst> bpis) {
		String maxProfitAmountStr = null;
		String maxProfit = null;

		if(bpis == null || bpis.size() ==0) {
			return ;
		}
		for (CumBusinessParamInst bpi : bpis) {
			//判断主商户最高分润比例
			if(Constants.BusinessParamCode.MAX_PROFIT_PROPORTION.code.equalsIgnoreCase(bpi.getCode())) {
				//先不判断是扣除谁的手续费
				maxProfit = bpi.getValue();
			}
			if(Constants.BusinessParamCode.MAX_PROFIT_AMOUNT.code.equalsIgnoreCase(bpi.getCode())) {
				maxProfitAmountStr = bpi.getValue();
			}
		}
		if(StringUtils.isBlank(maxProfit)) {
			throw new AppException(TxsCode.MAX_PROFIT_EMPTY.code);
		}
		Long maxProfitAmount = 0l;
		if(!StringUtils.isBlank(maxProfitAmountStr)) {
			maxProfitAmount = Long.valueOf(maxProfitAmountStr);
		}
		//先不判断是扣除谁的手续费
		long max = new BigDecimal(maxProfit).longValue() ;//100%百分比
		if(profitAmount > (0.01 * max * payAmount + maxProfitAmount)) {
			throw new AppException(TxsCode.EXCEED_MAX_PROFIT.code);
		}
	}

	//@Logable(businessTag="createSplitOrderAndRecords") //todo 上生产日志不打印入参
	public TxsSplitOrder createSplitOrderAndRecords(TxsPreOrder txsPreOrder, Long payProcedureFee, String customerCode, String outTradeNo,
			String notifyUrl, List<SplitInfo> splitInfoList,Long slaveAmountSum,List<TxsSplitRecord> txsSplitRecords, CustomerBusinessInstance businessInst,
			ProcedureFeeResult splitProcedureFeeResult, String outSplitTradeNo) {
		Date now = new Date();
		Long payAmount = txsPreOrder.getAmount();

		boolean bMultiAndFirst = false;
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			bMultiAndFirst = self.isMultiOrderAndFirst(txsPreOrder);
		}
		// 构造分账订单
		TxsSplitOrder txsSplitOrder = self.createTxsSplitOrder(payAmount, payProcedureFee, now, customerCode, outTradeNo,
				notifyUrl, splitInfoList, slaveAmountSum, splitProcedureFeeResult, txsPreOrder.getMemberId(), outSplitTradeNo, bMultiAndFirst, txsPreOrder.getSplitModel());
		txsSplitOrder.setSplitModel(txsPreOrder.getSplitModel());
		txsSplitOrder.setAttachData(txsPreOrder.getAttachData());//20190926 txsSplitOrder目前AttachData字段没正确赋值，权衡决定用txs_pre_order的
		if(businessInst != null) {
			txsSplitOrder.setOriBusinessCode(businessInst.getBusinessCode());
		}
		if(TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code.equals(txsPreOrder.getPayState())) {
			txsSplitOrder.setOriBusinessCode(txsPreOrder.getBusinessCode());
			txsSplitOrder.setSettCycleRuleCode(txsPreOrder.getSettCycleRuleCode());
		}
		
		//分账收单的客户名称
		String sourceCustomerName = cumCacheService.getCustomerInfo(txsSplitOrder.getCustomerCode(),
				txsSplitOrder.getCustomerCode(), "1").getName();
				//cumService.getCustomerInfo(txsSplitOrder.getCustomerCode(), txsSplitOrder.getCustomerCode(), "1").getName();
		// 所有分账记录
		for (SplitInfo splitInfo : splitInfoList) {
			TxsSplitRecord txsSplitRecord = self.createSplitRecord(businessInst, payProcedureFee, splitInfo,slaveAmountSum, txsSplitOrder, now, sourceCustomerName, txsPreOrder);
			txsSplitRecords.add(txsSplitRecord);
		}
		return txsSplitOrder;

	}

	@Logable(businessTag = "genTotalSplitAmount", outputArgs = false)
	Long genTotalSplitAmount(List<SplitInfo> splitInfoList){
		long totalAmount = 0L;
		for(SplitInfo splitInfo: splitInfoList){
			totalAmount += splitInfo.getAmount();
		}

		return totalAmount;
	}

	@Logable(businessTag="insertSplitOrderAndRecords",outputArgs = false)
	@Transactional(rollbackFor = Exception.class)
	public void insertSplitOrderAndRecords(TxsPreOrder txsPreOrder, TxsSplitOrder txsSplitOrder,
			List<TxsSplitRecord> txsSplitRecords) {

		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			//更新已拆单金额
			int i = txsPreOrderMapper.updateAddDividIngAmountById(txsSplitOrder.getAmount(), txsPreOrder.getOrderId());
			txsPreOrderMapper.updateSyncDivToPayTradeOrder(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode());
			if(i <= 0){
				throw new AppException(TxsCode.AMOUNT_EXCEED.code);
			}
		}

		txsSplitOrderMapper.insertSelective(txsSplitOrder);
		for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
			CustomerInfo customerInfo = cumCacheService.getCustomerInfo(txsSplitRecord.getCustomerCode(),txsSplitRecord.getCustomerCode(),UserType.PAS_USER.code);
			txsSplitRecord.setBusinessMan(customerInfo.getBusinessMan());
			txsSplitRecord.setBusinessManId(customerInfo.getBusinessManId());
			txsSplitRecord.setCompanyName(customerInfo.getCompanyName());
			txsSplitRecord.setCompanyId(customerInfo.getCompanyId());
			txsSplitRecordMapper.insertSelective(txsSplitRecord);
		}

		txsPreOrderMapper.updateByPrimaryKeySelective(txsPreOrder);

	}

	@Logable(businessTag="dealSplitRecords",outputArgs = false)
	@Transactional(rollbackFor = Exception.class)
	public void dealSplitRecords(TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords, String splitState, List<SplitInfo> splitInfoList) {

		txsSplitOrder.setState(splitState);

		Date now = new Date();
		TxsSplitOrder updateSplitOrder = new TxsSplitOrder();
		updateSplitOrder.setState(splitState);
		updateSplitOrder.setUpdateTime(now);
		updateSplitOrder.setTransactionNo(txsSplitOrder.getTransactionNo());
		if(null != splitInfoList){
			updateSplitOrder.setSplitInfoList(JSONObject.toJSONString(splitInfoList));
		}
		updateSplitOrder.setRevokeTransactionNo(txsSplitOrder.getRevokeTransactionNo());
		int result = txsSplitOrderMapper.updateConfirmByTransactionNo(updateSplitOrder);

		if(result < 1){
			throw new AppException(TxsCode.REPEAT_SPLIT.code, TxsCode.REPEAT_SPLIT.message);
		}

		if(TxsConstants.SplitOrderState.SUCCESS.code.equals(splitState)) {
			//产品积压工作项 11653:【PJ24】账户分账确认失败支持再次确认或取消
			if(txsSplitRecordMapper.fenBuFenZhangHasRecords(txsSplitOrder.getTransactionNo())){
				commonService.payLog(txsSplitOrder.getTransactionNo()+"txsSplitRecord有记录!不插");
			}else{
				for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
					txsSplitRecordMapper.insertSelective(txsSplitRecord);
				}
			}

		}

	}


//	@Logable(businessTag="checkCustomersBusiness")
	public void checkCustomersBusiness(String customerCode,List<SplitInfo> splitInfoList) {
		List<String> customerCodeList = new ArrayList<String>();
		customerCodeList.add(customerCode);
		if(false==cumService.isCustomersOpenBusiness(customerCodeList,BusinessCode.FZ.code))
			throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code);

//		customerCodeList.clear();
//		for(SplitInfo info: splitInfoList)
//		{
//			customerCodeList.add(info.getCustomerCode());
//		}
//		if(false==cumService.isCustomersOpenBusiness(customerCodeList,BusinessCode.BFZ.code))
//			throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code);
	}


	/**
	 * 校验输入请求中的金额是否正确
	 * 金额正确包括：分账金额之和等于原订单金额
	 * @param txsPreOrder
	 * @param splitInfoList
	 * @param fzCustomerCode
	 * @return
	 * to do for 折单分账
	 */
//	@Logable(businessTag = "checkSplitInfoAmountRight", outputArgs=false)
	public Long checkSplitInfoAmountRight(TxsPreOrder txsPreOrder,List<SplitInfo> splitInfoList) {
		// 分账接口传入的被分账总金额，用来判断是否与交易单的金额相同
		long interfaceInputAmount = 0L;
		// 除去付款客户的被分账的总金额
		long slaveAmountSum = 0L;
		for (SplitInfo splitInfo : splitInfoList) {
			transactionService.checkCumStatus(splitInfo.getCustomerCode(),TxsConstants.CustomerType.PAYEE.code);
			interfaceInputAmount += splitInfo.getAmount();
			if (splitInfo.getIsProcedureCustomer().intValue()!=1) {
				//前面的检查已经保证有且仅有一个splitInfo的标记为付款客户
				slaveAmountSum += splitInfo.getAmount();
			}
		}

		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			//拆单分账时总额不大于即可
			Long doneOrDoingAmount = txsPreOrderMapper.selectTotalDivideAmount(txsPreOrder.getOrderId());
			if ((interfaceInputAmount + doneOrDoingAmount.longValue()) > txsPreOrder.getAmount().longValue()) {
				throw new AppException(TxsCode.AMOUNT_EXCEED.code,
						TxsCode.AMOUNT_EXCEED.message);
			}
		}else{
			// 分账金额不等于原订单金额，异常
			if (interfaceInputAmount != txsPreOrder.getAmount()) {
				throw new AppException(TxsCode.AMOUNT_ATYPISM.code,
						TxsCode.AMOUNT_ATYPISM.message);
			}
		}

		return slaveAmountSum;

	}

	/**
	 * 查询outTradeNo指定的原支付交易的商户订单并执行该订单的状态检查
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	@Logable(businessTag="checkOrigPayPreOrder")
	public TxsPreOrder queryOrigPayPreOrder(String outTradeNo, String customerCode) {
		TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByParam(outTradeNo, customerCode, FZTransaction);
		if (txsPreOrder == null) {
			throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code,TxsCode.TRADEORDER_NOTFOUND.message);
		}
		return txsPreOrder;
	}

	/**
	 * 检查入参的各个金额是否合法
	 * 如果没有指定扣除手续费的客户，则报错
	 * @param splitInfoList
	 * @return
	 *
	 * to do 拆单分账
	 */
//	@Logable(businessTag="checkSplitInfoParamIsNotNull")
	public List<SplitInfo> checkSplitInfoParamIsNotNull(List<SplitInfo> splitInfoList, String splitModel) {

		checkNullLAndAmt(splitInfoList, splitModel);
		int procedureCustomerCount = 0;
		int splitProcedureCustomerCount = 0;

		for (SplitInfo splitInfo : splitInfoList) {
			if (splitInfo.getIsProcedureCustomer().intValue() == 1 ) {
				//0：不是；1：扣收单手续费；2：扣分账手续费；3：扣收单和分账手续费
				procedureCustomerCount++;
			}
			if(Constants.IsProcedureCustomerEnum.trade_split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				procedureCustomerCount++;
				splitProcedureCustomerCount++;//分账手续费
			}
			if(Constants.IsProcedureCustomerEnum.split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				splitProcedureCustomerCount++;//分账手续费
			}
		}
		if(StringUtils.isNotBlank(splitModel)) {
			if(splitProcedureCustomerCount != 1) {
				throw new AppException(TxsCode.SPLIT_PROCEDURE_CUSTOMER_EMPTY.code,
						TxsCode.SPLIT_PROCEDURE_CUSTOMER_EMPTY.message);
			}
		}
		if (procedureCustomerCount == 1) {
			checkDuplicate(splitInfoList);
			return splitInfoList;
		} else {
			throw new AppException(TxsCode.SPLIT_INFO_MUST_HAVE_ONE_PROCEDURE_CUS.code,
					TxsCode.SPLIT_INFO_MUST_HAVE_ONE_PROCEDURE_CUS.message);
		}

	}

	/**
	 * 拆单分账,非第一次拆单的检查. 为了不影响原来的逻辑,在checkSplitInfoParamIsNotNull的基础上新写了一个
	 * 非第一笔的校验方式
	 * 非第一笔不需要要有人承担手续费
	 * @param splitInfoList
	 * @param splitModel
	 * @return
	 */
	public List<SplitInfo> checkSplitInfoParamMultiSplitNotFirst(List<SplitInfo> splitInfoList, String splitModel) {

		if(!TxsConstants.SplitModel.MULTI_ORDER.code.equals(splitModel)){
			return splitInfoList;
		}

		checkNullLAndAmt(splitInfoList, splitModel);
		for (SplitInfo splitInfo : splitInfoList) {
			if (splitInfo.getIsProcedureCustomer().intValue() != 0 ) {
				throw new AppException(TxsCode.MULTI_SPLIT_NOT_FIRST.code);
			}
		}

		checkDuplicate(splitInfoList);
		return splitInfoList;

	}

	public void checkDuplicate(List<SplitInfo> splitInfoList){
		List<SplitInfo> distinctSplitInfoList = splitInfoList.stream().collect(
				collectingAndThen(
						toCollection(() -> new TreeSet<>(Comparator.comparing(SplitInfo::getCustomerCode))), ArrayList::new)
		);

		if (distinctSplitInfoList.size() != splitInfoList.size()) { //分账商户不能重复
			throw new AppException(TxsCode.REPEAT_SPLIT.code);
		}
	}

	public void checkNullLAndAmt(List<SplitInfo> splitInfoList, String splitModel){

		if (splitInfoList == null || splitInfoList.isEmpty()) {
			throw new AppException(TxsCode.SPLITINFOLIST_MUSTHAS.code);
		}

		for (SplitInfo splitInfo : splitInfoList) {
			if (splitInfo.getAmount() == null || splitInfo.getCustomerCode() == null)
				throw new AppException(TxsCode.SPLITINFOLIST_MUSTHAS.code);
			// 分账的金额要大于等于0
			if (splitInfo.getAmount() < 0)
				throw new AppException(TxsConstants.INVALID_PARAM);

			if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(splitModel)){
				if(null == splitInfo.getSettleCycle()){
					splitInfo.setSettleCycle(30);
				}
			}

			if(null != splitInfo.getSettleCycle() && (splitInfo.getSettleCycle().intValue() > 30 ||splitInfo.getSettleCycle().intValue() <0)){
				throw new AppException(TxsCode.SPLIT_SETTLE_CYCLE_ERROR.code);
			}
		}
	}

//	@Logable(businessTag="createInsidePayRequest")
	public InsidePayRequest createInsidePayRequest(TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords, String controledState) {

		boolean bTxsControlling = TxsConstants.ControlledState.FREEZE.code.equals(controledState);
		String oriBusinessCode = txsSplitOrder.getOriBusinessCode();
		InsidePayRequest insidePayRequest = new InsidePayRequest();
		insidePayRequest.setFromSystemId(systemId);
		insidePayRequest.setTransactionNo(txsSplitOrder.getTransactionNo());
		insidePayRequest.setTransactionType(FZJYTransaction);
		insidePayRequest.setPayMethod(PayMethod.ACCOUNT_PAY.code);
		insidePayRequest.setPayCurrency(TxsConstants.CurrencyType.CNY.code);
		insidePayRequest.setChannelType(TxsConstants.ChannelType.SERVICE.code);
		// 构造源客户
		InsidePayCustomer insidePayCustomerSource = new InsidePayCustomer();
		insidePayCustomerSource.setAmount(txsSplitOrder.getRealAmount());
		insidePayCustomerSource.setCustomerCode(txsSplitOrder.getCustomerCode());
		insidePayCustomerSource.setProcedureFee(txsSplitOrder.getProcedureFee());
		insidePayCustomerSource.setSettCycleRuleCode(txsSplitOrder.getSettCycleRuleCode());
		if(bTxsControlling){
			commonService.payLog(txsSplitOrder.getTransactionNo()+"是一笔管控中,源保持原来的");
			//insidePayCustomerSource.setSettCycleRuleCode(SettCycleRuleCode_X);
		}
		//此处手续费不能传到acc，否则acc记账会失败，此处分账手续费会被留在簿记账户
//		insidePayCustomerSource.setProcedureFee(0l);

		if(StringUtils.isNotBlank(oriBusinessCode) && !oriBusinessCode.startsWith("FZ")) {
			//不以FZ开头的业务  新版本,  不需单独开通每种业务的分账
			insidePayCustomerSource.setBusinessCode(oriBusinessCode);
		}

		insidePayCustomerSource.setBusinessInstanceId(txsSplitOrder.getBusinessInstId());
		List<InsidePayCustomer> insidePayCustomerSources = new ArrayList<>();
		insidePayCustomerSources.add(insidePayCustomerSource);
		insidePayRequest.setSourceCustomerList(insidePayCustomerSources);
		// 构造目标客户
		List<InsidePayCustomer> insidePayCustomerTargets = new ArrayList<>();
		for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
			InsidePayCustomer insidePayCustomerTarget = new InsidePayCustomer();
			insidePayCustomerTarget.setAmount(txsSplitRecord.getAmount());
			insidePayCustomerTarget.setCustomerCode(txsSplitRecord.getCustomerCode());
			insidePayCustomerTarget.setProcedureFee(txsSplitRecord.getProcedurefee());
			insidePayCustomerTarget.setBusinessInstanceId(txsSplitRecord.getBusinessInstId());
			if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsSplitOrder.getSplitModel())){
				insidePayCustomerTarget.setDelayDays(txsSplitRecord.getSettleCycle());
			}

			if(StringUtils.isNotBlank(oriBusinessCode) && !oriBusinessCode.startsWith("FZ")) {
				//不以FZ开头的业务  新版本,  不需单独开通每种业务的分账
				insidePayCustomerTarget.setBusinessCode(oriBusinessCode);
			}
			if(bTxsControlling){
				commonService.payLog(txsSplitOrder.getTransactionNo()+"目标是一笔管控中,传X");
				insidePayCustomerTarget.setSettCycleRuleCode(SettCycleRuleCode_X);
			}
			insidePayCustomerTargets.add(insidePayCustomerTarget);
		}
		insidePayRequest.setTargetCustomerList(insidePayCustomerTargets);

		if(StringUtils.isNotEmpty(controledState)){
			//因为分失败了可以重分,所以这个字段要实时更新
			int i = txsSplitOrderMapper.updateSetControlling(txsSplitOrder.getTransactionNo(), controledState);
			commonService.payLog(txsSplitOrder.getTransactionNo()+"更新order controlling为:"+controledState+",条数:"+i);
			i = txsSplitRecordMapper.updateSetControlling(txsSplitOrder.getTransactionNo(), controledState);
			commonService.payLog(txsSplitOrder.getTransactionNo()+"更新record controlling为:"+controledState+",条数:"+i);
		}
		return insidePayRequest;
	}

	/**
	 * 是否可执行分账
	 * @return
	 */
	@Logable(businessTag="canExecuteSplit", outputArgs=false)
	public boolean canExecuteSplit(TxsPreOrder txsPreOrder)
	{
		return StringUtils.equals(txsPreOrder.getPayState(), TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code)
		&& StringUtils.equals(txsPreOrder.getSettlementState(), TxsConstants.SettleMentState.SUCCESS.code);
	}

	/**
	 * 执行分账操作
	 * 调用支付系统的内转接口完成分账处理，根据结果更新数据库中的分账记录和分账订单，并触发异步消息的发送
	 * 不会抛出异常
	 * @param insidePayRequest
	 * @param transactionNo
	 * @param now
	 * @param txsPreOrder
	 * @param txsSplitOrder
	 * @param txsSplitRecords
	 */
	@Logable(businessTag="executeSplit")
	public void executeSplit(InsidePayRequest insidePayRequest,
			Date now, TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords) {
		InsidePayResponse insidePayResponse = null;
		
//		self.splitCalcWithRc(txsSplitOrder, txsSplitRecords);
		self.calcInturnRc(insidePayRequest, txsSplitOrder.getOutTradeNo(), txsSplitOrder, txsSplitRecords, null, null);
		
		try {
			// 调用支付子系统
			insidePayResponse = payService.handleInsidePay(insidePayRequest);
			String errorCode = insidePayResponse.getErrorCode();
			Date insideEndTime = insidePayResponse.getEndTime();
			self.updateDb(now, insideEndTime, txsSplitRecords, txsSplitOrder, errorCode,
					TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.SUCCESS);
			txsSplitOrder.setState(TxsConstants.SplitOrderState.SUCCESS.code);
			txsSplitOrder.setEndTime(insideEndTime);
			if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsSplitOrder.getSplitModel())){
				self.updateDividedDividingAmount(txsSplitOrder);
			}
			
		}catch (Exception e) {
			commonService.logException(e);
			
			String code = returnCodeUtil.translateExceptionToCode(e, null);
			if(StringUtils.isNotBlank(code) && !"0101".equals(code)) {
				self.updateDb(now, now, txsSplitRecords, txsSplitOrder, code,
						TxsConstants.SplitRecordState.FAIL, TxsConstants.SplitOrderState.FAIL);
				txsSplitOrder.setState(TxsConstants.SplitOrderState.FAIL.code);
				txsSplitOrder.setEndTime(now);
				if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsSplitOrder.getSplitModel())){
					self.updateDividedDividingAmount(txsSplitOrder);
				}
			}
			else {
				self.updateDb(now, now, txsSplitRecords, txsSplitOrder, code,
						TxsConstants.SplitRecordState.UNEXECUTED, TxsConstants.SplitOrderState.UNEXECUTED);
			}
			
			if (StringUtils.isNotBlank(txsSplitOrder.getNotifyUrl())) {
				self.notifyMerchant(txsSplitOrder);
			}

			List<TxsPayResultMsg> txsPayResultMsgs = self.createInturnTxsPayResultMsg(insidePayRequest, txsSplitOrder.getOutTradeNo(), txsSplitOrder.getSplitModel(), false);
			kafkaTemplate.send("TXS_RC_RESULT", txsSplitOrder.getTransactionNo(), txsPayResultMsgs.toString());

			throw e;
		}
		
		try {
			//通知商户和更新RC不应该影响主流程
			if (StringUtils.isNotBlank(txsSplitOrder.getNotifyUrl())) {
				self.notifyMerchant(txsSplitOrder);
			}
			//进入此过程一定是内转成功
			List<TxsPayResultMsg> txsPayResultMsgs = self.createInturnTxsPayResultMsg(insidePayRequest, txsSplitOrder.getOutTradeNo(), txsSplitOrder.getSplitModel(), true);
			kafkaTemplate.send("TXS_RC_RESULT", txsSplitOrder.getTransactionNo(), txsPayResultMsgs.toString());
		}
		catch(Exception e) {
			commonService.payLog(txsSplitOrder.getTransactionNo() + "通知商户或rc异常" + e.getMessage());
			commonService.logException(e);
		}
		
	}


	/**
	 * 执行分账操作
	 * 调用支付系统的内转接口完成分账处理，根据结果更新数据库中的分账记录和分账订单，并触发异步消息的发送
	 * 不会抛出异常
	 * @param insidePayRequest
	 * @param now
	 * @param txsSplitOrder
	 * @param
	 */
	@Logable(businessTag="executePreSplit")
	public void executePreSplit(InsidePayRequest insidePayRequest,
							 Date now, TxsSplitOrder txsSplitOrder) {
		InsidePayResponse insidePayResponse = null;

		try {
			// 调用支付子系统
			insidePayResponse = payService.preInsidePay(insidePayRequest);
			String errorCode = insidePayResponse.getErrorCode();
			Date insideEndTime = insidePayResponse.getEndTime();
			self.updateDb(now, insideEndTime, null, txsSplitOrder, errorCode,
					null, TxsConstants.SplitOrderState.PRE_SUCCESS);
			txsSplitOrder.setState(TxsConstants.SplitOrderState.PRE_SUCCESS.code);
			txsSplitOrder.setEndTime(insideEndTime);

		}catch (Exception e) {
			commonService.logException(e);

			String code = returnCodeUtil.translateExceptionToCode(e, null);
			if(StringUtils.isNotBlank(code) && !"0101".equals(code)) {
				self.updateDb(now, now, null, txsSplitOrder, code,
						TxsConstants.SplitRecordState.FAIL, TxsConstants.SplitOrderState.FAIL);
				txsSplitOrder.setState(TxsConstants.SplitOrderState.FAIL.code);
				txsSplitOrder.setEndTime(now);
			}
			else {
				self.updateDb(now, now, null, txsSplitOrder, code,
						TxsConstants.SplitRecordState.UNEXECUTED, TxsConstants.SplitOrderState.UNEXECUTED);
			}

			throw e;
		}

	}

	/**
	 * 确认分账操作
	 *
	 * 不会抛出异常
	 * @param insidePayRequest
	 * @param now
	 * @param txsSplitOrder
	 * @param
	 */
	@Logable(businessTag="executeConfirmSplit")
	public void executeConfirmSplit(InsidePayRequest insidePayRequest,
									Date now, TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords) {
		InsidePayResponse insidePayResponse = null;

//		self.splitCalcWithRc(txsSplitOrder, txsSplitRecords);
		self.calcInturnRc(insidePayRequest, txsSplitOrder.getOutTradeNo(), txsSplitOrder, txsSplitRecords, null, null);

		try {
			// 调用支付子系统
			insidePayResponse = payService.preInsidePay(insidePayRequest);
			String errorCode = insidePayResponse.getErrorCode();
			Date insideEndTime = insidePayResponse.getEndTime();
			self.updateDb(now, insideEndTime, txsSplitRecords, txsSplitOrder, errorCode,
					TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.SUCCESS);
			txsSplitOrder.setState(TxsConstants.SplitOrderState.SUCCESS.code);
			txsSplitOrder.setEndTime(insideEndTime);

		}catch (Exception e) {
			commonService.logException(e);

			String code = returnCodeUtil.translateExceptionToCode(e, null);
			if(StringUtils.isNotBlank(code) && !"0101".equals(code)) {
				self.updateDb(now, now, txsSplitRecords, txsSplitOrder, code,
						TxsConstants.SplitRecordState.UNEXECUTED, TxsConstants.SplitOrderState.UNEXECUTED);
				txsSplitOrder.setState(TxsConstants.SplitOrderState.UNEXECUTED.code);
				txsSplitOrder.setEndTime(now);
			}
			else {
				self.updateDb(now, now, txsSplitRecords, txsSplitOrder, code,
						TxsConstants.SplitRecordState.UNEXECUTED, TxsConstants.SplitOrderState.UNEXECUTED);
			}

			if (StringUtils.isNotBlank(txsSplitOrder.getNotifyUrl())) {
				try {
					self.notifyMerchant(txsSplitOrder);
				}
				catch(Exception ne) {
					commonService.logException(ne);
				}

			}
			List<TxsPayResultMsg> txsPayResultMsgs = self.createInturnTxsPayResultMsg(insidePayRequest, txsSplitOrder.getOutTradeNo(), txsSplitOrder.getSplitModel(), false);
			kafkaTemplate.send("TXS_RC_RESULT", txsSplitOrder.getTransactionNo(), txsPayResultMsgs.toString());
			throw e;
		}

		try {
			//通知商户和更新RC不应该影响主流程
			if (StringUtils.isNotBlank(txsSplitOrder.getNotifyUrl())) {
				self.notifyMerchant(txsSplitOrder);
			}
			//进入此过程一定是内转成功
			List<TxsPayResultMsg> txsPayResultMsgs = self.createInturnTxsPayResultMsg(insidePayRequest, txsSplitOrder.getOutTradeNo(), txsSplitOrder.getSplitModel(), true);
			kafkaTemplate.send("TXS_RC_RESULT", txsSplitOrder.getTransactionNo(), txsPayResultMsgs.toString());
		}
		catch(Exception e) {
			commonService.payLog(txsSplitOrder.getTransactionNo() + "通知商户或rc异常" + e.getMessage());
			commonService.logException(e);
		}
	}
	
	/**
	 * 分账风险计算
	 * @param txsWithdrawTradeOrder
	 * @param bankCardNo
	 */
//	@Logable(businessTag = "splitCalcWithRc")
//	public void splitCalcWithRc(TxsSplitOrder txsSplitOrder,List<TxsSplitRecord> txsSplitRecords) {
//		
//		Map<String, String> indexs = new HashMap<>();
//		indexs.put(TxsConstants.RcIndex.AMOUNT.code, txsSplitOrder.getRealAmount() + "");
//		indexs.put(TxsConstants.RcIndex.TRADE_NUM.code, "1");
//		Map<String, String> businessTargetIds = new HashMap<>();
//		businessTargetIds.put(TxsConstants.BusinessTagerType.CUSTOMER_CODE.code, txsSplitOrder.getCustomerCode());
//		CommonOuterResponse result = CommonOuterResponse.fail(TxsCode.RC_NOTPASS.code, TxsCode.RC_NOTPASS.message);
//		try {
//			RcCalculateRequest rcCalculateRequest = riskCalcService.calculate(Constants.rcBusinessType.INSIDE_PAY_OUT.code,Constants.BusinessCode.FZ.code,null, txsSplitOrder.getOutTradeNo(),
//					txsSplitOrder.getTransactionNo(), indexs, businessTargetIds);
//			List<RcCalculateRequest> rcCalculateRequests = new ArrayList<>();
//			rcCalculateRequests.add(rcCalculateRequest);
//			result = rcService.calculateResponse(rcCalculateRequests);
//		} catch (Exception e) {
//			transactionService.logException("调用风控系统异常:" + txsSplitOrder.getTransactionNo());
//			transactionService.logException(e);
//			result = CommonOuterResponse.success(CommonOuterResponse.SUCCEE, null);
//		}
//		if (!result.isSuccess()) {
//			TxsSplitOrder updSplitOrder = new TxsSplitOrder();
//			updSplitOrder.setTransactionNo(txsSplitOrder.getTransactionNo());
//			updSplitOrder.setErrorCode(result.getReturnCode());
//			updSplitOrder.setUpdateTime(new Date());
//			
//			self.updateDb(new Date(), null, txsSplitRecords, txsSplitOrder, result.getReturnCode(),
//					TxsConstants.SplitRecordState.UNEXECUTED, TxsConstants.SplitOrderState.UNEXECUTED);
//			AppException e = new AppException(result.getReturnCode(), result.getReturnMsg());
//			throw e;
//			
//		}
//	}

	/**
	 * 更新数据库的数据为成功分账
	 * 注意: 该方法是以SUCCESS为终态的
	 * 如果splitOrder或splitRecord已经SUCCESS状态,则不再更新。
	 * 因此,基于SUCCESS上再更新的，不要调用此方法! 2023-04-12
	 *
	 */
	@Logable(businessTag="updateDb",outputArgs = false)
	@Transactional
	public void updateDb(Date now, Date endTime, List<TxsSplitRecord> txsSplitRecords,
			TxsSplitOrder txsSplitOrder, String errorCode, TxsConstants.SplitRecordState splitState, TxsConstants.SplitOrderState splitOrderState) {


		if(txsSplitOrder != null){
/*			TxsSplitOrder splitOrderForUpdate = new TxsSplitOrder();
			splitOrderForUpdate.setTransactionNo(txsSplitOrder.getTransactionNo());
			splitOrderForUpdate.setEndTime(endTime);
			splitOrderForUpdate.setUpdateTime(now);
			splitOrderForUpdate.setErrorCode(errorCode);
			splitOrderForUpdate.setState(splitOrderState.code);
			txsSplitOrderMapper.updateByPrimaryKeySelective(splitOrderForUpdate);*/

			int i = txsSplitOrderMapper.updateStateNotSuccess(txsSplitOrder.getTransactionNo(),
					endTime,
					now,
					errorCode,
					splitOrderState.code);

			commonService.payLog(txsSplitOrder.getTransactionNo() + ": OrderState-" + splitOrderState.code+";i:"+i);
		}


		if (txsSplitRecords != null) {
			Collections.sort(txsSplitRecords);//排序，防止死锁
			int i = 0;
			for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
/*				TxsSplitRecord forupdate = new TxsSplitRecord();
				forupdate.setId(txsSplitRecord.getId());
				forupdate.setState(splitState.code);
				forupdate.setUpdateTime(now);
				forupdate.setErrorCode(errorCode);
				txsSplitRecordMapper.updateByPrimaryKeySelective(forupdate);*/

				i += txsSplitRecordMapper.updateStateNotSuccess(txsSplitRecord.getId(),
						now,
						errorCode,
						splitState.code);
			}
			commonService.payLog(txsSplitOrder.getTransactionNo() + ": RecordState-" + splitState.code+";i:"+i);
		}

	}

	/**
	 * 异步通知，要求txsSplitOrder已经设置好状态
	 *
	 * @param txsSplitOrder
	 */
	public void notifyMerchant(TxsSplitOrder txsSplitOrder) {
		try
		{
			notifyMerchantThrowExp(txsSplitOrder);
		}
		catch(Exception e)
		{

		}
	}

	@Logable(businessTag="notifyMerchantThrowExp")
	public void notifyMerchantThrowExp(TxsSplitOrder txsSplitOrder) {

		if(TxsConstants.SplitOrderState.SUCCESS.code.equals(txsSplitOrder.getState())
			|| TxsConstants.SplitOrderState.REVOKE.code.equals(txsSplitOrder.getState())) {
			List<TxsSplitRecord> txsSplitRecords = txsSplitRecordMapper
					.selectByTransactionNo(txsSplitOrder.getTransactionNo());
			SplitResultNotify notify = new SplitResultNotify();

			notify.setCustomerCode(txsSplitOrder.getCustomerCode());
			notify.setOutTradeNo(txsSplitOrder.getOutTradeNo());
			notify.setSplitTransactionNo(txsSplitOrder.getTransactionNo());
			notify.setRevokeTransactionNo(txsSplitOrder.getRevokeTransactionNo());
			notify.setTransactionNo(txsSplitOrder.getTransactionNo());
			notify.setAmount(txsSplitOrder.getAmount());
			notify.setRealAmount(txsSplitOrder.getRealAmount());
			notify.setAttachData(txsSplitOrder.getAttachData());

			notify.setSplitState(txsSplitOrder.getState());
			SimpleDateFormat formater = new SimpleDateFormat("yyyyMMddHHmmss");
			notify.setSplitTime(formater.format(txsSplitOrder.getEndTime()));
			notify.setProcedureFee(txsSplitOrder.getProcedureFee());
			notify.setAttachData(txsSplitOrder.getAttachData());
			if(!"-".equals(txsSplitOrder.getOutSplitTradeNo())){
				notify.setOutSplitTradeNo(txsSplitOrder.getOutSplitTradeNo());
			}
			notify.setNonceStr(UUIDUtils.uuid());


			List<SplitResultInfo> infoList = new ArrayList<SplitResultInfo>();
			for(TxsSplitRecord record:txsSplitRecords)
			{
				SplitResultInfo info = new SplitResultInfo();
				info.setAmount(record.getAmount());
				info.setCustomerCode(record.getCustomerCode());
				info.setProcedureFee(record.getProcedurefee());
				info.setSettleCycle(record.getSettleCycle());
				infoList.add(info);
			}
			notify.setSplitResultInfoList(infoList);
			sendToNts.notifyCustomer(txsSplitOrder.getNotifyUrl(),
					txsSplitOrder.getTransactionNo(), FZTransaction, JSON.toJSONString(notify),
					txsSplitOrder.getCustomerCode());
		}

	}


	/**
	 * 构造分账订单，包含手续费计算
	 * 状态设置为"尚未执行"
	 * @param payAmount
	 *            支付金额
	 * @param payProcedureFee
	 *            支付手续费
	 * @param now
	 * @param customerCode
	 * @param outTradeNo
	 * @param notifyUrl
	 * @param splitInfoList
	 * @return
	 */
	//@Logable(businessTag="createTxsSplitOrder")//todo 上生产要注释掉
	public TxsSplitOrder createTxsSplitOrder(Long payAmount, Long payProcedureFee, Date now, String customerCode,
			String outTradeNo, String notifyUrl, List<SplitInfo> splitInfoList,Long slaveAmountSum, ProcedureFeeResult splitProcedureFeeResult, String splitMain,
											 String outSplitTradeNo, boolean bMultiAndFirst, String splitModel) {
		// 交易单号
		String random = String.format("%06d", sequenceService.nextValue("splitImpl"));
		String dateString = DateFormatUtils.format(now, format);
		String transactionNo = TransactionType.FZJY.no + dateString + random;

		//ProcedureFeeResult result = transactionService.calcProcedure(customerCode, payAmount, null, BusinessCode.FZ.code,null);//20190529,只是获取业务实例,不需要计算手续费.不要过多耦合,用transactionService.getBusinessInstId代替
		//String businessExamId = transactionService.getBusinessInstId(customerCode, null, BusinessCode.FZ.code, null);
//		CustomerBusinessInstance customerBusinessInstance = transactionService.getBusinessInstId(customerCode, null, BusinessCode.FZ.code, null);
//		String businessExamId = customerBusinessInstance.getBusinessExamId();
//		long splitProcedureFee = 0l;//一定要写死为0，不然产品指定的允许客户指定从哪个被分账子商户扣支付手续费的需求无法无漏洞搞定result.getProcedureFee();

//		ProcedureFeeResult splitProcedureFeeResult = transactionService.calcProcedure(customerCode, payAmount,
//				null, Constants.BusinessCode.FZ.code, FZTransaction, null, null);

		if (payProcedureFee == null) {
			//这种情况应该不会出现，如果出现，说明程序有Bug，需要仔细检查原因
			throw new AppException(TxsCode.ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL.code,TxsCode.ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL.message);
		}

		//计算分账手续费
		long splitProcedureFee = splitProcedureFeeResult.getProcedureFee();
		String businessExamId = splitProcedureFeeResult.getBusinessInst().getBusinessExamId();

		String isMultiOrderFist = null;
		Long judgePayAmount = payAmount;
		Long judgePayProcedureFee = payProcedureFee;
		Long judgeSplitProcedureFee = splitProcedureFee;
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(splitModel)){
			judgePayAmount = self.genTotalSplitAmount(splitInfoList);
			if(bMultiAndFirst){
				isMultiOrderFist = (Constants.YesOrNoByNum.YES.code);
			}else{
				judgePayProcedureFee = 0L;
				judgeSplitProcedureFee = 0L;
			}

		}

		TxsSplitOrder txsSplitOrder = new TxsSplitOrder();
		txsSplitOrder.setTransactionNo(transactionNo);
		txsSplitOrder.setTransactionType(TransactionType.FZ.code);
		txsSplitOrder.setOutTradeNo(outTradeNo);
		txsSplitOrder.setCustomerCode(customerCode);
		txsSplitOrder.setNotifyUrl(notifyUrl);
		txsSplitOrder.setState(TxsConstants.SplitOrderState.INIT.code);
		txsSplitOrder.setAmount(judgePayAmount);// 支付金额
		txsSplitOrder.setProcedureFee(judgeSplitProcedureFee);
		txsSplitOrder.setRealAmount(judgePayAmount - judgePayProcedureFee);
		for(SplitInfo splitInfo: splitInfoList)
		{
			if(splitInfo.getIsProcedureCustomer().intValue()==1)
			{
				txsSplitOrder.setProcedureCustomerCode(splitInfo.getCustomerCode());
			}

			if (Constants.IsProcedureCustomerEnum.split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				txsSplitOrder.setSplitProcedureCustomerCode(splitInfo.getCustomerCode());
			}

			if (Constants.IsProcedureCustomerEnum.trade_split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				txsSplitOrder.setProcedureCustomerCode(splitInfo.getCustomerCode());
				txsSplitOrder.setSplitProcedureCustomerCode(splitInfo.getCustomerCode());
			}

			if (null != txsSplitOrder.getProcedureCustomerCode() && null != txsSplitOrder.getSplitProcedureCustomerCode()) {
				break;
			}
		}

		//如果是旧接口,只传了1的情况,直接将收单手续费承担者来承担分账手续费
		//拆单分账,只有第一笔需要有手续费承担者
		if (null == txsSplitOrder.getSplitProcedureCustomerCode() ) {
			if( !TxsConstants.SplitModel.MULTI_ORDER.code.equals(splitModel)){
				txsSplitOrder.setSplitProcedureCustomerCode(txsSplitOrder.getProcedureCustomerCode() );
			}else{

			}

		}

		txsSplitOrder.setCreateTime(now);
		txsSplitOrder.setSplitInfoList(JSONObject.toJSONString(splitInfoList));
		txsSplitOrder.setSalveAmountSum(slaveAmountSum);
		txsSplitOrder.setBusinessInstId(businessExamId);
		txsSplitOrder.setSplitMain(splitMain);
		txsSplitOrder.setOutSplitTradeNo(outSplitTradeNo);
		txsSplitOrder.setIsFirst(isMultiOrderFist);

		return txsSplitOrder;
	}

	/**
	 * 重新计算分账订单的实际分账金额以及付支付手续费的被分账子商户的被分账记录的金额，传入分账订单以及付手续费的被分账商户的被分账记录
	 * 要求order以及recordList中的手续费已经计算过一遍(十有八九是使用最高手续费的支付方式计算的)
	 * payProcedure为实际的支付订单的手续费
	 * 如果重新计算了，返回true，否则返回false
	 * @param payProcedure
	 * @param order
	 * @param recordList
	 * 其实是二次计费的..针对交易二次计费,分账也进行二次计费 2022-11-17
	 */
	//@Logable(businessTag = "reCalcProcedureIfNeed")//todo Loggable要注释掉
	public boolean reCalcProcedureIfNeed(TxsPayTradeOrder payOrder, TxsSplitOrder splitOrder, TxsSplitRecord record, String bankIcon) {
//		ProcedureFeeResult newPayProcedure = transactionService.calcProcedure(payOrder.getCustomerCode(),
//				payOrder.getAmount(), payOrder.getPayMethod(), null ,payOrder.getTransactionType(), bankIcon);

//		if(!order.getRealAmount().equals(payOrder.getAmount() - newPayProcedure.getProcedureFee() - order.getProcedureFee())) {
//			//如果实际分账金额有变化，重新计算付手续费的客户的被分账金额
//			order.setRealAmount(payOrder.getAmount() - newPayProcedure.getProcedureFee() - order.getProcedureFee());
//			//order.setProcedureFee(newPayProcedure.getProcedureFee());
////			order.setProcedureFee(0l);//分账手续费永远为0，业务需求  去掉这个update ********，创建记录时 分账手续费 已经确认
//			record.setAmount(order.getRealAmount()-order.getSalveAmountSum());
//			return true;
//		}
//		else {
//			return false;
//		}
		boolean allowCashAmountResult = Constants.IsSettWithCashAmount.YES.code.equals(payOrder.getIsSettWithCashAmount()) ? true: false;
		Long payAmount = payOrder.getAmount();
		if(allowCashAmountResult) {
			payAmount = payOrder.getCashAmount();//使用实收金额分账
		}

		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(splitOrder.getSplitModel())){
			payAmount = splitOrder.getAmount();
			commonService.payLog("payAmount1:"+payAmount);
			if(allowCashAmountResult && Constants.YesOrNoByNum.YES.code.equals(splitOrder.getIsFirst())) {
				payAmount = splitOrder.getAmount() - (payOrder.getAmount() -payOrder.getCashAmount() );
				commonService.payLog("payAmount2:"+payAmount);
			}
		}
		Long newRealAmount = payAmount - payOrder.getProcedureFee();
		splitOrder.setOriBusinessCode(payOrder.getBusinessCode());//此种更新，简直神坑，splitOrder不同步设置下 外层使用有问题
		if(!splitOrder.getRealAmount().equals(newRealAmount)) {
			//如果实际分账金额有变化，重新计算付手续费的客户的被分账金额
			commonService.payLog("getRealAmount:"+splitOrder.getRealAmount()+";newRealAmount:"+newRealAmount);
			splitOrder.setRealAmount(newRealAmount);

			if(payOrder.getBusinessCode().startsWith(FZTransaction)) {
				record.setAmount(splitOrder.getRealAmount()-splitOrder.getSalveAmountSum());
				splitOrder.setProcedureFee(0l);//收银台,旧的分账模式,手续费为必须为0
				payOrder.setSplitProcedureFee(0l);
			}
			else {
				Long newSplitAmount = record.getOrigAmount() - payOrder.getProcedureFee();
				//此处的record不是普通的被分账记录，而是承担交易手续费的分账记录
				if(allowCashAmountResult) {
					newSplitAmount -= (payOrder.getAmount() - payOrder.getCashAmount());//理论上就是payOrder.getCouponAmount()优惠券金额
				}
				if(record.getCustomerCode().equals(splitOrder.getSplitProcedureCustomerCode())) {
					newSplitAmount -= splitOrder.getProcedureFee();
					
				}
//				Long newSplitAmount = record.getAmount() + record.getPayProcedureFee() - payOrder.getProcedureFee();
				//分账手续费不变，收单手续费如果变化，则按原来的分账金额  加回扣过的收单手续费， 再重新减去新的收单手续费，以兼容此商户既是收单手续费扣除方，也是分账手续费扣除方
				record.setAmount(newSplitAmount);
			}
			return true;
		}
		else {
			return false;
		}
	}
	
	
	/**
	 * 如果有必要，重新计算分账手续费，更新分账金额；更新主客户的被分账记录的分账金额和手续费
	 * 并持久化到数据库
	 * 更新后的金额直接设置到入参splitOrder和splitRecordList中
	 * @param order
	 * @param splitOrder
	 * @param splitRecordList
	 */
	@Logable(businessTag="reCalcProcedureAndPersistIfNeed", outputArgs=false, outputResult= false)
	public void reCalcProcedureAndPersistIfNeed(TxsPayTradeOrder order, TxsSplitOrder splitOrder,
			List<TxsSplitRecord> splitRecordList) {
		TxsSplitRecord mainCusSplitRecord = null;
		for(TxsSplitRecord record:splitRecordList)
		{
			if(record.getCustomerCode().equalsIgnoreCase(splitOrder.getProcedureCustomerCode()))
			{
				mainCusSplitRecord = record;
				break;
			}
		}
		String bankIcon = order.getBankCode();
		boolean recalc = self.reCalcProcedureIfNeed(order, splitOrder, mainCusSplitRecord, bankIcon);
		
		TxsSplitOrder orderForUpdate = new TxsSplitOrder();
		if(recalc)
		{
			TxsSplitRecord recordForUpdate = new TxsSplitRecord();
			recordForUpdate.setId(mainCusSplitRecord.getId());
			recordForUpdate.setAmount(mainCusSplitRecord.getAmount());
			recordForUpdate.setProcedurefee(mainCusSplitRecord.getProcedurefee());
			recordForUpdate.setUpdateTime(new Date());
			recordForUpdate.setCouponAmount(order.getCouponAmount());
			//order.getAmount()-order.getCashAmount()理论上就是优惠券金额，不使用order.getCouponAmount()是怕这个不准，一切以为cashAmount为准
			if(order.getCashAmount() != null) {
				recordForUpdate.setCashAmount(mainCusSplitRecord.getOrigAmount() - (order.getAmount()-order.getCashAmount()));
			}
			
			txsSplitRecordMapper.updateByPrimaryKeySelective(recordForUpdate);
			orderForUpdate.setRealAmount(splitOrder.getRealAmount());
			orderForUpdate.setOriBusinessCode(splitOrder.getOriBusinessCode());
			orderForUpdate.setProcedureFee(splitOrder.getProcedureFee());
			
		}
		splitOrder.setSettCycleRuleCode(order.getSettCycleRuleCode());
		
		orderForUpdate.setSettCycleRuleCode(order.getSettCycleRuleCode());
		orderForUpdate.setTransactionNo(splitOrder.getTransactionNo());
		orderForUpdate.setUpdateTime(new Date());
		txsSplitOrderMapper.updateByPrimaryKeySelective(orderForUpdate);
		
		
	}





	/**
	 * 创建分账记录
	 * @param splitInfo
	 *
	 * @param splitedAmout
	 *            每个客户被分账金额
	 * @param slaveAmountSum
	 *            非主客户的被分账金额总和
	 * @param splitCustomerCode
	 *            分账客户编码
	 * @param txsSplitOrder
	 *            分账订单
	 * @param now
	 *            分账时间
	 * @param sourceCustomerName 分账主商户客户名称
	 * @return
	 */
	@Logable(businessTag = "createSplitRecord")
	public TxsSplitRecord createSplitRecord(CustomerBusinessInstance businessInst, Long payProcedureFee, SplitInfo splitInfo, long slaveAmountSum,
			TxsSplitOrder txsSplitOrder, Date now, String sourceCustomerName, TxsPreOrder txsPreOrder) {
		TxsSplitRecord txsSplitRecord = new TxsSplitRecord();
		Long id = sequenceService.nextValue("splitRecord");
		txsSplitRecord.setId(id);
		txsSplitRecord.setTransactionNo(txsSplitOrder.getTransactionNo());
		txsSplitRecord.setSourceCustomerCode(txsSplitOrder.getCustomerCode());
		txsSplitRecord.setCustomerCode(splitInfo.getCustomerCode());
		txsSplitRecord.setState(TxsConstants.SplitRecordState.UNEXECUTED.code);
		txsSplitRecord.setSplitRatio(splitInfo.getSplitRatio());
		txsSplitRecord.setTransactionType(txsSplitOrder.getTransactionType());

		txsSplitRecord.setOutSplitTradeNo(txsSplitOrder.getOutSplitTradeNo());
		txsSplitRecord.setIsFirst(txsSplitOrder.getIsFirst());
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			String settleCycleType = self.genSettltCycleType(txsPreOrder.getSettCycleRuleCode(), splitInfo.getSettleCycle());
			txsSplitRecord.setSettleCycleType(settleCycleType);
			txsSplitRecord.setSettleCycle(splitInfo.getSettleCycle());
		}


		String businessCode = null;

		String businessExamId = "";
		Long splitProcedureFee = 0l;
		if(businessInst != null) {
			businessExamId = businessInst.getBusinessExamId();
			businessCode = businessInst.getBusinessCode();
		}
		else {
			businessCode = txsPreOrder.getBusinessCode();
		}
		
		boolean newSplitModel = false;
		if(StringUtils.isBlank(businessCode)) {
			if(StringUtils.isBlank(txsSplitOrder.getSplitModel())) {
				newSplitModel = false;//没有业务，则按splitModel没传的按旧的
			}
			else {
				newSplitModel = true;
			}
		}
		else if(businessCode.startsWith(FZTransaction)) {
			newSplitModel = false;//FZ开头的业务为旧分账模式
		}
		else {
			newSplitModel = true;
		}
		if(!newSplitModel){
			if (TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsSplitOrder.getSplitModel())) {
				throw  new AppException(TxsCode.MULTI_SPLIT_MUST_NEW.code);
			}
			//旧模式
			ProcedureFeeResult procedureFeeResult = transactionService.calcProcedure(splitInfo.getCustomerCode(),splitInfo.getAmount(), null,
					BusinessCode.BFZ.code,null, null);
			Long procedureFee = procedureFeeResult.getProcedureFee();//0l;
			txsSplitRecord.setProcedurefee(procedureFee);
			txsSplitRecord.setBusinessInstId(procedureFeeResult.getBusinessInst().getBusinessExamId());

			// 如果当前分账的客户编码是付手续费的客户编码
			if (splitInfo.getIsProcedureCustomer().intValue()==1) {
				// 付款的被分账金额 （分账订单的真实金额-所有其他客户被分账金额总和）
				txsSplitRecord.setAmount(txsSplitOrder.getRealAmount() - slaveAmountSum);
			} else {
				txsSplitRecord.setAmount(splitInfo.getAmount());

			}
		}else {
			//新模式
			Long tradeProcedureFee = payProcedureFee;//收单手续费
			Long finalSplitAmount = 0l;
			if (Constants.IsProcedureCustomerEnum.trade_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				//收单手续费
				finalSplitAmount = splitInfo.getAmount() - tradeProcedureFee;
			}
			else if (Constants.IsProcedureCustomerEnum.split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				//txsSplitOrder.getProcedureFee()=分账手续费
				finalSplitAmount = splitInfo.getAmount() - txsSplitOrder.getProcedureFee();

				//只有设置为分账手续费扣除方时，才记录
				splitProcedureFee = txsSplitOrder.getProcedureFee();
			}
			else if(Constants.IsProcedureCustomerEnum.trade_split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				//收单手续费 + 分账手续费
				finalSplitAmount = splitInfo.getAmount() - tradeProcedureFee - txsSplitOrder.getProcedureFee();

				splitProcedureFee = txsSplitOrder.getProcedureFee();
			}
			else {
				finalSplitAmount = splitInfo.getAmount();
			}
			txsSplitRecord.setAmount(finalSplitAmount);
			txsSplitRecord.setProcedurefee(0L);
			
			if(TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code.equals(txsPreOrder.getPayState())) {
				txsSplitRecord.setBusinessInstId(txsPreOrder.getBusinessInstId());
			}
			else {
				txsSplitRecord.setBusinessInstId(businessExamId);
			}
			
		}
		//分账手续费
		txsSplitRecord.setSplitProcedureFee(splitProcedureFee);

		String customerName = cumCacheService.getCustomerInfo(splitInfo.getCustomerCode(),
				splitInfo.getCustomerCode(), "1").getName();
		txsSplitRecord.setCustomerName(customerName);
		txsSplitRecord.setOrigAmount(splitInfo.getAmount());

		txsSplitRecord.setCreateTime(now);
		txsSplitRecord.setSourceCustomerName(sourceCustomerName);

		if (null != txsSplitOrder.getSplitMain()) {
			String customerNameSplitMain = cumCacheService.getCustomerInfo(txsSplitOrder.getSplitMain(),
					txsSplitOrder.getSplitMain(), "1").getName();
			txsSplitRecord.setSplitMain(txsSplitOrder.getSplitMain());
			txsSplitRecord.setSplitMainName(customerNameSplitMain);
		}
		txsSplitRecord.setSplitModel(txsSplitOrder.getSplitModel());

		//2022-11-07新增商户订单号、原订单实收金额（元）、分账对象属性
		txsSplitRecord.setOutTradeNo(txsSplitOrder.getOutTradeNo());
		txsSplitRecord.setPayCashAmount(txsPreOrder.getCashAmount());
		if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getSplitMain())){
			txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.SPLIT_MAIN.code);
		}else if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getSplitProcedureCustomerCode())
				&& txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getProcedureCustomerCode())){
			txsSplitRecord.setPayProcedureFee(payProcedureFee);
			txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.PAY_AND_SPLIT_PROCEDURE_CUSTOMER_CODE.code);
		}else if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getSplitProcedureCustomerCode())){
			txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.SPLIT_PROCEDURE_CUSTOMER_CODE.code);
		}else if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getProcedureCustomerCode())){
			txsSplitRecord.setPayProcedureFee(payProcedureFee);
			txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.PROCEDURE_CUSTOMER_CODE.code);
		}else {
			txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.SPLIT_CUSTOMER_CODE.code);
		}
		return txsSplitRecord;
	}

	/**
	 * 交易分账，获取交易商户号
	 * @param insidePayRequest
	 * @return
	 */
	private String getFZJYTradeCustomerCode(InsidePayRequest insidePayRequest) {
		List<InsidePayCustomer> sourceCustomerList = insidePayRequest.getSourceCustomerList();
		if (TransactionType.FZJY.isEqualsTo(insidePayRequest.getTransactionType()) && !sourceCustomerList.isEmpty()) {
			return sourceCustomerList.get(0).getCustomerCode();
		} else {
			return null;
		}
	}

	/**
	 * 内转（分账）风险控制
	 * @param insidePayRequest
	 * @param outTradeNo
	 * @param txsSplitOrder
	 * @param txsSplitRecords
	 * @param txsRefundSplitOrder
	 * @param txsRefundSplitRecords
	 */
	@Logable(businessTag = "calcInturnRc",outputArgs = false)
	public void calcInturnRc(InsidePayRequest insidePayRequest, String outTradeNo,
			TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords,
			TxsRefundSplitOrder txsRefundSplitOrder, List<TxsRefundSplitRecord> txsRefundSplitRecords) {
		String txnCustomerCode = getFZJYTradeCustomerCode(insidePayRequest);
		List<InsidePayCustomer> sourceCustomerList = insidePayRequest.getSourceCustomerList();
		List<RcCalculateRequest> rcCalculateRequests = new ArrayList<>();
		for (InsidePayCustomer insidePayCustomer : sourceCustomerList) {
			Map<String, String> indexs = new HashMap<>();
			indexs.put(TxsConstants.RcIndex.AMOUNT.code, insidePayCustomer.getAmount() + "");
			indexs.put(TxsConstants.RcIndex.TRADE_NUM.code, "1");
			Map<String, String> businessTargetIds = new HashMap<>();
			businessTargetIds.put(TxsConstants.BusinessTagerType.CUSTOMER_CODE.code, insidePayCustomer.getCustomerCode());
			RcCalculateRequest rcCalculateRequest = riskCalcService.calculate(Constants.rcBusinessType.INSIDE_PAY_OUT.code, Constants.BusinessCode.BFZ.code, insidePayRequest.getPayMethod(), outTradeNo, insidePayRequest.getTransactionNo(),
					indexs, businessTargetIds);
			rcCalculateRequests.add(rcCalculateRequest);
		}
		List<InsidePayCustomer> targetCustomerList = insidePayRequest.getTargetCustomerList();
		for (InsidePayCustomer insidePayCustomer : targetCustomerList) {
			//如果收单商户参与分账，不用再次风控
			if (txnCustomerCode != null && txnCustomerCode.equals(insidePayCustomer.getCustomerCode())) {
				continue;
			}
			Map<String, String> indexs = new HashMap<>();
			indexs.put(TxsConstants.RcIndex.AMOUNT.code, insidePayCustomer.getAmount() + "");
			indexs.put(TxsConstants.RcIndex.TRADE_NUM.code, "1");
			Map<String, String> businessTargetIds = new HashMap<>();
			businessTargetIds.put(TxsConstants.BusinessTagerType.CUSTOMER_CODE.code, insidePayCustomer.getCustomerCode());
			String fzBusinessCode = Constants.BusinessCode.FZ.code;
			if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsSplitOrder.getSplitModel())){
				fzBusinessCode = Constants.BusinessCode.CDFZ.code;
			}
			RcCalculateRequest rcCalculateRequest = riskCalcService.calculate(Constants.rcBusinessType.INSIDE_PAY_IN.code, fzBusinessCode, insidePayRequest.getPayMethod(), outTradeNo, insidePayRequest.getTransactionNo(),
					indexs, businessTargetIds);
			rcCalculateRequests.add(rcCalculateRequest);
		}
		Date now = new Date();
		CommonOuterResponse result = CommonOuterResponse.fail(TxsCode.RC_NOTPASS.code, TxsCode.RC_NOTPASS.message);

		try {
			result = rcService.calculateResponse(rcCalculateRequests);
		} catch (Exception e) {
			//20190916调用风控未知异常后，直接进行后续交易
			transactionService.logException("调用风控系统异常:" + outTradeNo);
			transactionService.logException(e);
			result = CommonOuterResponse.fail(TxsCode.FEIGN_RCEXCEPTION.code, TxsCode.FEIGN_RCEXCEPTION.message);
//			String errorCode = FeignExceptionUtils.parseException(e, TxsCode.FEIGN_RCEXCEPTION.code);
//			if (txsSplitOrder != null && txsSplitRecords != null) {
//				self.updateDb(now, now, txsSplitRecords, txsSplitOrder, errorCode,
//						TxsConstants.SplitRecordState.FAIL, TxsConstants.SplitOrderState.FAIL);
//			}
//			if (txsRefundSplitOrder != null && txsSplitRecords != null) {
//				//反分账退款这里不处理数据，由外部调用去处理
//			}
//			throw new AppException(errorCode);
		}
		if (!result.isSuccess()) {
			//风控不过
			AppException e = new AppException(result.getReturnCode(), result.getReturnMsg());
			if (txsSplitOrder != null && txsSplitRecords != null) {
				
				if("RC账户止付".equals(result.getReturnMsg())) {
					self.updateDb(now, now, txsSplitRecords, txsSplitOrder, e.getErrorCode(),
							TxsConstants.SplitRecordState.UNEXECUTED, TxsConstants.SplitOrderState.UNEXECUTED);
				}
				else{
					self.updateDb(now, now, txsSplitRecords, txsSplitOrder, e.getErrorCode(),
							TxsConstants.SplitRecordState.FAIL, TxsConstants.SplitOrderState.FAIL);
				}
			}
			if (txsRefundSplitOrder != null && txsSplitRecords != null) {
				//反分账退款这里不处理数据，由外部调用去处理
			}
			throw e;
		}
	}

	/**
	 * 封装内转风控消息
	 * @param insidePayRequest
	 * @param outTradeNo
	 * @return
	 */
	@Logable(businessTag = "createInturnTxsPayResultMsg", outputArgs = false, outputResult = false)
	public List<TxsPayResultMsg> createInturnTxsPayResultMsg(InsidePayRequest insidePayRequest, String outTradeNo, String splitModel, boolean isSuccess) {
		String txnCustomerCode = getFZJYTradeCustomerCode(insidePayRequest);

		List<TxsPayResultMsg> txsPayResultMsgs = new ArrayList<>();
		List<InsidePayCustomer> sourceCustomerList = insidePayRequest.getSourceCustomerList();

		String fzBusinessCode = Constants.BusinessCode.FZ.code;
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(splitModel)){
			fzBusinessCode = Constants.BusinessCode.CDFZ.code;
		}
		for (InsidePayCustomer insidePayCustomer : sourceCustomerList) {
			TxsPayResultMsg txsPayResultMsg = self.createTxsPayResultMsg(insidePayCustomer.getAmount(),
					insidePayCustomer.getCustomerCode(), Constants.rcBusinessType.INSIDE_PAY_OUT.code, fzBusinessCode,insidePayRequest.getPayMethod(),
					outTradeNo, insidePayRequest.getTransactionNo(), insidePayRequest.getTransactionType(), isSuccess);
			txsPayResultMsgs.add(txsPayResultMsg);
		}
		List<InsidePayCustomer> targetCustomerList = insidePayRequest.getTargetCustomerList();
		for (InsidePayCustomer insidePayCustomer : targetCustomerList) {
			//如果收单商户参与分账，不用再次风控
			if (txnCustomerCode != null && txnCustomerCode.equals(insidePayCustomer.getCustomerCode())) {
				continue;
			}
			TxsPayResultMsg txsPayResultMsg = self.createTxsPayResultMsg(insidePayCustomer.getAmount(),
					insidePayCustomer.getCustomerCode(), Constants.rcBusinessType.INSIDE_PAY_IN.code,Constants.BusinessCode.BFZ.code,insidePayRequest.getPayMethod(),
					outTradeNo, insidePayRequest.getTransactionNo(), insidePayRequest.getTransactionType(), isSuccess);
			txsPayResultMsgs.add(txsPayResultMsg);
		}

		return txsPayResultMsgs;
	}

	/**
	 * 创建风控消息
	 * @param amount
	 * @param customerCode
	 * @param businessType
	 * @param outTradeNo
	 * @param transactionNo
	 * @return
	 */
	@Logable(businessTag = "createTxsPayResultMsg")
	public TxsPayResultMsg createTxsPayResultMsg(long amount, String customerCode,
			String businessType, String businessCode, String payMethod, String outTradeNo, String transactionNo, String transactionType, boolean isSuccess) {
		TxsPayResultMsg txsPayResultMsg = new TxsPayResultMsg();
		txsPayResultMsg.setOutTradeNo(outTradeNo);
		txsPayResultMsg.setTransactionNo(transactionNo);
		txsPayResultMsg.setBusinessType(businessType);
		txsPayResultMsg.setBusinessCode(businessCode);
		txsPayResultMsg.setPayMethod(payMethod);
		txsPayResultMsg.setTransactionType(transactionType);
		txsPayResultMsg.setPayState(isSuccess ? TxsConstants.SplitOrderState.SUCCESS.code : TxsConstants.SplitOrderState.FAIL.code);
		Map<String, String> indexs = new HashMap<>();
		indexs.put(TxsConstants.RcIndex.AMOUNT.code, amount + "");
		indexs.put(TxsConstants.RcIndex.TRADE_NUM.code, "1");
		txsPayResultMsg.setIndexs(indexs);
		Map<String, String> businessTargetIds = new HashMap<>();
		businessTargetIds.put(TxsConstants.BusinessTagerType.CUSTOMER_CODE.code, customerCode);

		txsPayResultMsg.setBusinessTargetIds(businessTargetIds);
		return txsPayResultMsg;
	}

	/**
	 *
	 * 检查分账商户是否足以支付收单,分账手续费
	 * @param splitInfoList
	 * @return
	 */
//	@Logable(businessTag="checkSplitAmountCardBudernProcedure")
	public void checkSplitAmountCardBudernProcedure(List<SplitInfo> splitInfoList, Long tradeProcedureFee,  Long splitProcedureFee) {
		if (splitInfoList == null || splitInfoList.isEmpty()) {
			throw new AppException(TxsCode.SPLITINFOLIST_MUSTHAS.code);
		}

		for (SplitInfo splitInfo : splitInfoList) {
			String customerCode = splitInfo.getCustomerCode();

			//1.扣收单手续费 2.扣分账手续费  3.扣收单和分账手续费
			//1.扣收单手续费
			if (splitInfo.getIsProcedureCustomer().intValue() == 1 ) {
				AccountQueryResponse accountQueryResponse = accService.accountQuery(customerCode, customerCode, "2");
				if (splitInfo.getAmount() + accountQueryResponse.getAvailableBalance() - tradeProcedureFee < 0L) {
					throw new AppException(TxsCode.MAINCUSTOMER_NOTFULLAMOUNT.code);
				}
			}
			//2.扣分账手续费
			if(Constants.IsProcedureCustomerEnum.split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				AccountQueryResponse accountQueryResponse = accService.accountQuery(customerCode, customerCode, "2");
				if (splitInfo.getAmount() + accountQueryResponse.getAvailableBalance() - splitProcedureFee < 0L) {
					throw new AppException(TxsCode.MAINCUSTOMER_NOTFULLAMOUNT.code);
				}
			}
			//3.扣收单和分账手续费
			if(Constants.IsProcedureCustomerEnum.trade_split_procedure.code.equals(splitInfo.getIsProcedureCustomer().intValue())) {
				AccountQueryResponse accountQueryResponse = accService.accountQuery(customerCode, customerCode, "2");
				if (splitInfo.getAmount() + accountQueryResponse.getAvailableBalance() - tradeProcedureFee  - splitProcedureFee < 0L) {
					throw new AppException(TxsCode.MAINCUSTOMER_NOTFULLAMOUNT.code);
				}
			}

		}

	}
	
	/**
	 * @param payMethod
	 * @return
	 */
	@Logable(businessTag = "allowCashAmountCharge")
	public boolean allowCashAmountCharge(String payMethod, Long cashAmount, Long amount) {
		boolean result = false;
		if(cashAmount == null || cashAmount.equals(amount)) {
			result = false;
			return result;
		}
		if(!StringUtils.isEmpty(cashAmountChargePaymethod)) {
			String[] paymethods = cashAmountChargePaymethod.split(",");
			for(String item: paymethods) {
				if(payMethod.equals(item)) {
					result = true;
					break;
				}
			}
		}
		return result;
	}
	
	/**
	 * @param payMethod
	 * @return
	 */
	public boolean allowCardTypeCharge(String payMethod) {
		boolean result = false;
		if(!StringUtils.isEmpty(cardTypeChargePaymethod)) {
			String[] paymethods = cardTypeChargePaymethod.split(",");
			for(String item: paymethods) {
				if(payMethod.equals(item)) {
					result = true;
					break;
				}
			}
		}
		return result;
	}
	@Logable(businessTag = "updateTxsSplitRecord")
    @Transactional
    public void updateTxsSplitRecord(Long preOrderId) {
        TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByPrimaryKey(preOrderId);
        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode(), null);
        if (txsSplitOrder != null) {
        	
            List<TxsSplitRecord> txsSplitRecords = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
            TxsSplitOrder splitOrderForupdate = new TxsSplitOrder();
            splitOrderForupdate.setCouponAmount(txsPreOrder.getCouponAmount());
            splitOrderForupdate.setCashAmount(txsPreOrder.getCashAmount());
            splitOrderForupdate.setOriBusinessCode(txsPreOrder.getBusinessCode());
            splitOrderForupdate.setSettCycleRuleCode(txsPreOrder.getSettCycleRuleCode());
            splitOrderForupdate.setUpdateTime(new Date());
            splitOrderForupdate.setTransactionNo(txsSplitOrder.getTransactionNo());
            //不直接更新txsSplitOrder，是为了防止查询出TxsSplitOrder之后，结算通知先到，更新了TxsSplitOrder表的执行状态，此处直接更新TxsSplitOrder全量
            //会导致覆盖掉原来的数据。
            txsSplitOrderMapper.updateByPrimaryKeySelective(splitOrderForupdate);
            
            Collections.sort(txsSplitRecords);//排序
            for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
                TxsSplitRecord forupdate = new TxsSplitRecord();
                forupdate.setId(txsSplitRecord.getId());
                forupdate.setPayAmount(txsPreOrder.getAmount());
                forupdate.setPayTransactionNo(txsPreOrder.getTransactionNo());
				forupdate.setPayCashAmount(txsPreOrder.getCashAmount());
				if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getProcedureCustomerCode())) {
					forupdate.setPayProcedureFee(txsPreOrder.getProcedureFee());
				}

                if(txsSplitRecord.getCustomerCode().equalsIgnoreCase(txsSplitOrder.getProcedureCustomerCode()) && txsPreOrder.getCashAmount() != null) {
                    //不全部设置优惠券和实收，按实际情况
                    forupdate.setCouponAmount(txsPreOrder.getCouponAmount());
                    forupdate.setCashAmount(txsSplitRecord.getOrigAmount() - (txsPreOrder.getAmount()-txsPreOrder.getCashAmount()));
                }
                else {
                	forupdate.setCouponAmount(0l);
                    forupdate.setCashAmount(txsSplitRecord.getOrigAmount());
                }
                
                if (StringUtils.isBlank(txsSplitRecord.getBusinessInstId())) {
                    forupdate.setBusinessInstId(txsPreOrder.getBusinessInstId());
                }

                txsSplitRecordMapper.updateByPrimaryKeySelective(forupdate);
            }
            
        }
    }
	
//	@Logable(businessTag = "updateTxsSplitRecord")
//    @Transactional
//    public void updateTxsSplitRecord(TxsPayTradeOrder order) {
//        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByOutTradeNoAndCustomerCode(order.getOutTradeNo(), order.getCustomerCode());
//        if (txsSplitOrder != null) {
//            List<TxsSplitRecord> txsSplitRecords = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
//            TxsSplitOrder splitOrderForupdate = new TxsSplitOrder();
//            splitOrderForupdate.setCouponAmount(order.getCouponAmount());
//            splitOrderForupdate.setCashAmount(order.getCashAmount());
//            splitOrderForupdate.setOriBusinessCode(order.getBusinessCode());
//            splitOrderForupdate.setUpdateTime(new Date());
//            splitOrderForupdate.setTransactionNo(txsSplitOrder.getTransactionNo());
//            //不直接更新txsSplitOrder，是为了防止查询出TxsSplitOrder之后，结算通知先到，更新了TxsSplitOrder表的执行状态，此处直接更新TxsSplitOrder全量
//            //会导致覆盖掉原来的数据。
//            txsSplitOrderMapper.updateByPrimaryKeySelective(splitOrderForupdate);
//            
//            Collections.sort(txsSplitRecords);//排序，防止死锁
//            for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
//                TxsSplitRecord forupdate = new TxsSplitRecord();
//                forupdate.setId(txsSplitRecord.getId());
//                forupdate.setPayAmount(order.getAmount());
//                forupdate.setPayTransactionNo(order.getTransactionNo());
//                forupdate.setPayProcedureFee(order.getProcedureFee());
//                if(txsSplitRecord.getCustomerCode().equalsIgnoreCase(txsSplitOrder.getProcedureCustomerCode()) && order.getCashAmount() != null) {
//                    //不全部设置优惠券和实收，按实际情况
//                    forupdate.setCouponAmount(order.getCouponAmount());
//                    forupdate.setCashAmount(txsSplitRecord.getOrigAmount() - (order.getAmount()-order.getCashAmount()));
//                }else {
//                	forupdate.setCouponAmount(0l);
//                    forupdate.setCashAmount(txsSplitRecord.getOrigAmount());
//                }
//
//                if (StringUtils.isBlank(txsSplitRecord.getBusinessInstId())) {
//                    forupdate.setBusinessInstId(order.getBusinessInstId());
//                }
//                txsSplitRecordMapper.updateByPrimaryKeySelective(forupdate);
//            }
//            
//            
//        }
//    }
	
	@Logable(businessTag = "updateTxsSplitRecord", outputArgs = false)
//    @Transactional
    public void updateTxsSplitRecord(TxsPayTradeOrder order, TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords) {
        if (txsSplitOrder != null) {
            TxsSplitRecord mainCusSplitRecord = null;
            
            TxsSplitOrder orderForUpdate = new TxsSplitOrder();

			boolean isMultiFirst = Constants.YesOrNoByNum.YES.code.equalsIgnoreCase(txsSplitOrder.getIsFirst());
            boolean isMulti = TxsConstants.SplitModel.MULTI_ORDER.code.equalsIgnoreCase(txsSplitOrder.getSplitModel());
            
            Collections.sort(txsSplitRecords);//排序，防止死锁
            for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
                TxsSplitRecord forupdate = new TxsSplitRecord();
                forupdate.setId(txsSplitRecord.getId());
                forupdate.setPayAmount(order.getAmount());
                forupdate.setPayTransactionNo(order.getTransactionNo());
				forupdate.setPayCashAmount(order.getCashAmount());

				if(isMulti){
					String settleCycleType = self.genSettltCycleType(order.getSettCycleRuleCode(), txsSplitRecord.getSettleCycle());
					forupdate.setSettleCycleType(settleCycleType);
				}
				if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getProcedureCustomerCode())) {
					if(isMulti && !isMultiFirst){
						//拆单分账非第一笔,收单手续费为0。因为第一笔已经收完。
						commonService.payLog("拆单非第一次,手续费为0");
						forupdate.setPayProcedureFee(0L);
					}else{
						forupdate.setPayProcedureFee(order.getProcedureFee());
					}

				}
                if(txsSplitRecord.getCustomerCode().equalsIgnoreCase(txsSplitOrder.getProcedureCustomerCode()) && order.getCashAmount() != null) {
                    //不全部设置优惠券和实收，按实际情况
					if(isMulti && !isMultiFirst){
						//拆单分账非第一笔,为0。因为第一笔已经收完。
						forupdate.setCouponAmount(0L);
						forupdate.setCashAmount(txsSplitRecord.getOrigAmount());
					}else{
						forupdate.setCouponAmount(order.getCouponAmount());
						forupdate.setCashAmount(txsSplitRecord.getOrigAmount() - (order.getAmount()-order.getCashAmount()));
					}

                }else {
                	forupdate.setCouponAmount(0L);
                    forupdate.setCashAmount(txsSplitRecord.getOrigAmount());
                }

                if (StringUtils.isBlank(txsSplitRecord.getBusinessInstId())) {
                    forupdate.setBusinessInstId(order.getBusinessInstId());
					txsSplitRecord.setBusinessInstId(order.getBusinessInstId());
                }
                
                
                if(txsSplitRecord.getCustomerCode().equalsIgnoreCase(txsSplitOrder.getProcedureCustomerCode()))
    			{
					if(isMulti && !isMultiFirst){
						//即是拆单分账,又不是第一次,则nothing todo
					}else{
						mainCusSplitRecord = txsSplitRecord;

						String bankIcon = order.getBankCode();
						boolean recalc = self.reCalcProcedureIfNeed(order, txsSplitOrder, mainCusSplitRecord, bankIcon);


						if(recalc)
						{
							forupdate.setAmount(mainCusSplitRecord.getAmount());
							forupdate.setProcedurefee(mainCusSplitRecord.getProcedurefee());
							forupdate.setUpdateTime(new Date());
							forupdate.setCouponAmount(order.getCouponAmount());
							//order.getAmount()-order.getCashAmount()理论上就是优惠券金额，不使用order.getCouponAmount()是怕这个不准，一切以为cashAmount为准
							if(order.getCashAmount() != null) {
								forupdate.setCashAmount(mainCusSplitRecord.getOrigAmount() - (order.getAmount()-order.getCashAmount()));
							}

							orderForUpdate.setRealAmount(txsSplitOrder.getRealAmount());
							orderForUpdate.setProcedureFee(txsSplitOrder.getProcedureFee());
						}
					}

    			}//如果是收单手续费承担者
                
                txsSplitRecordMapper.updateByPrimaryKeySelective(forupdate);
            }
            
//            String bankIcon = order.getBankCode();
//    		boolean recalc = self.reCalcProcedureIfNeed(order, txsSplitOrder, mainCusSplitRecord, bankIcon);
//    		
//    		if(recalc)
//    		{
//    			TxsSplitRecord recordForUpdate = new TxsSplitRecord();
//    			recordForUpdate.setId(mainCusSplitRecord.getId());
//    			recordForUpdate.setAmount(mainCusSplitRecord.getAmount());
//    			recordForUpdate.setProcedurefee(mainCusSplitRecord.getProcedurefee());
//    			recordForUpdate.setUpdateTime(new Date());
//    			recordForUpdate.setCouponAmount(order.getCouponAmount());
//    			//order.getAmount()-order.getCashAmount()理论上就是优惠券金额，不使用order.getCouponAmount()是怕这个不准，一切以为cashAmount为准
//    			if(order.getCashAmount() != null) {
//    				recordForUpdate.setCashAmount(mainCusSplitRecord.getOrigAmount() - (order.getAmount()-order.getCashAmount()));
//    			}
//    			
//    			txsSplitRecordMapper.updateByPrimaryKeySelective(recordForUpdate);
//    			
//    			orderForUpdate.setRealAmount(txsSplitOrder.getRealAmount());
//    			orderForUpdate.setProcedureFee(txsSplitOrder.getProcedureFee());
//    		}
    		
            
            
			orderForUpdate.setOriBusinessCode(order.getBusinessCode());
			orderForUpdate.setSettCycleRuleCode(order.getSettCycleRuleCode());
    		orderForUpdate.setTransactionNo(txsSplitOrder.getTransactionNo());
    		orderForUpdate.setUpdateTime(new Date());
    		
    		orderForUpdate.setCouponAmount(order.getCouponAmount());
            orderForUpdate.setCashAmount(order.getCashAmount());
    		
            //不直接更新txsSplitOrder，是为了防止查询出TxsSplitOrder之后，结算通知先到，更新了TxsSplitOrder表的执行状态，此处直接更新TxsSplitOrder全量
            //会导致覆盖掉原来的数据。
            txsSplitOrderMapper.updateByPrimaryKeySelective(orderForUpdate);
            
    		txsSplitOrder.setSettCycleRuleCode(order.getSettCycleRuleCode());
    		txsSplitOrder.setOriBusinessCode(order.getBusinessCode());
    		
        }
		
    }
	
	public void confirmExecuteSplit(ExecuteSplitRequest executeSplitRequest) {


		TxsPayTradeOrder order = txsPayTradeOrderMapper.selectByTransactionNo(executeSplitRequest.getTransactionNo());
		if(order == null) {
			throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code);
		}

/*		if(!redisLockService.setReturnSplitMutex(order.getTransactionNo())){
			throw new AppException(TxsCode.SPLIT_RETURN_SAME_TIME.code);
		}*/
		
		List<TxsSplitOrder> splitOrderList = txsSplitOrderMapper.selectByOutTradeNoAndCustomerCode(executeSplitRequest.getOutTradeNo(), executeSplitRequest.getCustomerCode());
		if(null == splitOrderList || splitOrderList.size() == 0){
			commonService.payLog("找不到订单");
			return;
		}

		for(TxsSplitOrder splitOrder : splitOrderList){
			if(splitOrder!=null
					&& !splitOrder.getState().equals(TxsConstants.SplitOrderState.SUCCESS.code)
					&& (txsPreOrderMapper.selectTotalRefundAmt(order.getOutTradeNo(), order.getCustomerCode())  == 0))
			{
				self.checkAndUpdateStateReadyToExcute(splitOrder.getTransactionNo());
				List<TxsSplitRecord> splitRecordList = txsSplitRecordMapper.selectByTransactionNo(splitOrder.getTransactionNo());
				self.updateTxsSplitRecord(order, splitOrder, splitRecordList);

//			self.reCalcProcedureAndPersistIfNeed(order, splitOrder, splitRecordList);
				InsidePayRequest insidePayRequest = self.createInsidePayRequest(splitOrder, splitRecordList, order.getControlled());
				self.executeSplit(insidePayRequest, new Date(), splitOrder, splitRecordList);
			}else{
				commonService.payLog("未达到分账条件");
			}
		}

		//redisLockService.deleteReturnSplitMutex(order.getTransactionNo());

	}
	
	/*
	 * 将初始化或处理中更新为处理中.通过再往下执行
	 * 		
		解决初插时允许退货:
		fz:
		初插入数据的时候,一律使用初始化.
		进行处理的时候,将初始化51更新为未处理03,更新条数为1再往下执行. 
		    update TXS_SPLIT_ORDER
    set STATE = '03',
      UPDATE_TIME = sysdate
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
    	and (STATE = '51' or STATE = '03')                      ------------->这个03是为了兼容历史数据
		
		txs:
		退货的时候,成功,失败,或者未处理都可以退货.如果是未处理(未处理时,必定是全额退货),则先更改为已撤销再进行退货。
	 *///这个只能针对有splitOrder的情况下锁.2024-03-01
    @Logable(businessTag = "checkAndUpdateStateReadyToExcute")
    public void checkAndUpdateStateReadyToExcute(String transactionNo) {
        int i = txsSplitOrderMapper.updateState51To03(transactionNo);
    	if(1 != i) {
    		throw new AppException(TxsCode.STATE_NOT_ALLOW_SPLIT.code);
    	}
    }

    String isTheNonFirstOfTheMulti(TxsPreOrder txsPreOrder){
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			Long i = txsPreOrderMapper.selectTotalDivideAmount(txsPreOrder.getOrderId());
			if(i.longValue() >0L){
				return Constants.YesOrNoByNum.YES.code;
			}
		}
		return Constants.YesOrNoByNum.NO.code;
	}

	@Logable(businessTag = "isMultiOrderAndFirst", outputArgs = false)
	boolean isMultiOrderAndFirst(TxsPreOrder txsPreOrder){
		if(TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())){
			Long i = txsPreOrderMapper.selectTotalDivideAmount(txsPreOrder.getOrderId());
			if(i.longValue() == 0){
				return true;
			}
		}
		return false;
	}

	@Logable(businessTag = "updateDividedDividingAmount")
	public int updateDividedDividingAmount(TxsSplitOrder txsSplitOrder){
		if(!TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsSplitOrder.getSplitModel())){
    		return 0;
		}
    	int i = 0;

		if(TxsConstants.SplitOrderState.SUCCESS.code.equals(txsSplitOrder.getState())){
			i = txsPreOrderMapper.updateAddAddDividedForSuccess(txsSplitOrder.getAmount(),txsSplitOrder.getOutTradeNo(), txsSplitOrder.getCustomerCode());
		}else if(TxsConstants.SplitOrderState.FAIL.code.equals(txsSplitOrder.getState())){
			i = txsPreOrderMapper.updateAddReduceDividingForFail(txsSplitOrder.getAmount(),txsSplitOrder.getOutTradeNo(), txsSplitOrder.getCustomerCode());
		}
		txsPreOrderMapper.updateSyncDivToPayTradeOrder(txsSplitOrder.getOutTradeNo(), txsSplitOrder.getCustomerCode());
    	return i;
	}

	@Logable(businessTag = "adjustDividedDividing")
	public void adjustDividedDividing(String outTradeNo, String customerCode){
		int i1 = txsPreOrderMapper.adjustDividing(outTradeNo, customerCode);
		int i2 = txsPreOrderMapper.adjustDivided(outTradeNo, customerCode);
		txsPreOrderMapper.updateSyncDivToPayTradeOrder(outTradeNo, customerCode);
		commonService.payLog("adjustDividing:"+i1);
		commonService.payLog("adjustDivided:"+i2);
	}

	@Logable(businessTag = "selectByThree")
	public TxsSplitOrder selectByThree(String outTradeNo, String customerCode, String outSplitTradeNo){
		TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(outTradeNo,
				customerCode, outSplitTradeNo);
		return txsSplitOrder;
	}


	@Logable(businessTag = "genSettltCycleType")
	public String genSettltCycleType(String settCycleRuleCode, Integer settleCycle){
    	if(StringUtils.isEmpty(settCycleRuleCode)){
    		return null;
		}

    	String[] arrs = settCycleRuleCode.split("\\+");
    	if("RealTime".equalsIgnoreCase(arrs[0]) || (null != settleCycle && 0 == settleCycle)){
    		return "D";
		}else{
    		return arrs[0].substring(0,1);
		}

	}

	public void fillSplitProcedureFeeAndCheckBurden(TxsPreOrder txsPreOrder, ProcedureFeeResult splitProcedureFeeResult, List<SplitInfo> splitInfoList, Long payProcedureFee){
		txsPreOrder.setSplitProcedureFee(splitProcedureFeeResult.getProcedureFee());
		txsPreOrder.setSplitProcedureRate(splitProcedureFeeResult.getBusinessInst().getRateParam());
		//检查分员列表的成中有无能力承担手续费
		self.checkSplitAmountCardBudernProcedure(splitInfoList, payProcedureFee, (null == txsPreOrder.getSplitProcedureFee())? 0L:txsPreOrder.getSplitProcedureFee());
	}

	/**
	 *
	 * @param
	 * @return
	 */
	@Logable(businessTag = "updateCdSettCycle")
	public UpdateCdSettCycleResponse  updateCdSettCycle(UpdateCdSettCycleRequest request){

		UpdateCdSettCycleResponse response = new UpdateCdSettCycleResponse();
		TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(request.getOutTradeNo(),
				request.getCustomerCode(),
				request.getOutSplitTradeNo());
		if(null == txsSplitOrder){
			throw new AppException(TxsCode.SPLITORDER_NOTFOUND.code);
		}

		response.setOutTradeNo(txsSplitOrder.getOutTradeNo());
		response.setOutSplitTradeNo(txsSplitOrder.getOutSplitTradeNo());
		response.setSplitTransactionNo(txsSplitOrder.getTransactionNo());

		List<SplitResultInfo> splitResultInfoList = new ArrayList<>();
		/**
		 * 校验是否已结算
		 */
		for (SplitInfo splitInfo: request.getSplitInfoList()){
			TxsSplitRecord splitRecord = txsSplitRecordMapper.selectByTransactionAndCustomerCode(txsSplitOrder.getTransactionNo(), splitInfo.getCustomerCode());
			if(null == splitRecord){
				response.setReturnCode(TxsCode.SPLITRECORD_NOTFOUND.code);
				response.setReturnMsg(TxsCode.SPLITRECORD_NOTFOUND.message+":"+splitInfo.getCustomerCode());
				return response;
			}

			if(Constants.YesOrNoByNum.YES.code.equals(splitRecord.getSettleState())){
				response.setReturnCode(FzCode.SPLIT_ALREADY_SETTLE.code);
				response.setReturnMsg(FzCode.SPLIT_ALREADY_SETTLE.message+":"+splitRecord.getCustomerCode());
				return response;
			}

			if(splitInfo.getSettleCycle() == null || splitInfo.getSettleCycle().intValue() >30 || splitInfo.getSettleCycle().intValue() < 0){
				response.setReturnCode(TxsCode.SPLIT_SETTLE_CYCLE_ERROR.code);
				response.setReturnMsg(TxsCode.SPLIT_SETTLE_CYCLE_ERROR.message+":"+splitInfo.getSettleCycle());
				return response;
			}
		}

		JSONObject unsuccefful = new JSONObject();
		for (SplitInfo splitInfo: request.getSplitInfoList()){
			TxsSplitRecord splitRecord = txsSplitRecordMapper.selectByTransactionAndCustomerCode(txsSplitOrder.getTransactionNo(), splitInfo.getCustomerCode());
			if(splitInfo.getSettleCycle().intValue() == splitRecord.getSettleCycle().intValue()){
				SplitResultInfo resultInfo = self.buildResultInfo(txsSplitOrder.getProcedureCustomerCode(),	txsSplitOrder.getSplitProcedureCustomerCode(),
						txsSplitOrder.getSplitModel(),txsSplitOrder.getIsFirst(),splitRecord);
				splitResultInfoList.add(resultInfo);
				continue;
			}else{
				settUpdtSettCycleRequest settUpdtSettCycleRequest = new settUpdtSettCycleRequest();
				settUpdtSettCycleRequest.setBusinessInstId(splitRecord.getBusinessInstId());
				settUpdtSettCycleRequest.setCustomerCode(splitRecord.getCustomerCode());
				settUpdtSettCycleRequest.setDelayDays(splitInfo.getSettleCycle());
				settUpdtSettCycleRequest.setTransactionNo(txsSplitOrder.getTransactionNo());
				CommonOuterResponse commonOuterResponse = settService.updateSettCycle(settUpdtSettCycleRequest);
				if(null == commonOuterResponse || commonOuterResponse.isSuccess()){
					splitRecord.setSettleCycle(splitInfo.getSettleCycle());
					splitRecord.setUpdateTime(new Date());
					txsSplitRecordMapper.updateByPrimaryKeySelective(splitRecord);
				}else{
					commonService.payLog("该笔调用sett不成功,transaction:"+txsSplitOrder.getTransactionNo()
							+";customerCode:"+splitRecord.getCustomerCode()
							+";settleCycle:"+splitInfo.getSettleCycle()
							+";returnCode:"+(null == commonOuterResponse?"null response.":(commonOuterResponse.getReturnCode()
							+"-"+commonOuterResponse.getReturnMsg())));

					unsuccefful.put(splitRecord.getCustomerCode(), commonOuterResponse.getReturnCode()+commonOuterResponse.getReturnMsg());
				}
				SplitResultInfo resultInfo = self.buildResultInfo(txsSplitOrder.getProcedureCustomerCode(),	txsSplitOrder.getSplitProcedureCustomerCode(),
						txsSplitOrder.getSplitModel(),txsSplitOrder.getIsFirst(),splitRecord);
				splitResultInfoList.add(resultInfo);
			}
		}
		response.setSplitResultInfoList(splitResultInfoList);
		//response.setData(unsuccefful);
		return response;
	}


	@Logable(businessTag = "buildResultInfo")
	public SplitResultInfo buildResultInfo(String payProcedureCustomerCode,
										   String splitProcedureCustomerCode,
										   String splitModel,
										   String isFirst,
										   TxsSplitRecord splitRecord){
		SplitResultInfo resultInfo = new SplitResultInfo();
		resultInfo.setCustomerCode(splitRecord.getCustomerCode());
		resultInfo.setAmount(splitRecord.getAmount());
		resultInfo.setSettleCycle(splitRecord.getSettleCycle());
		resultInfo.setSettleState(splitRecord.getSettleState());
		if(Constants.YesOrNoByNum.YES.code.equals(splitModel)
				&& !Constants.YesOrNoByNum.YES.code.equals(isFirst)){
			resultInfo.setIsProcedureCustomer(Integer.parseInt(TxsConstants.SplitAttr.SPLIT_CUSTOMER_CODE.code));
		}else{
			if(StringUtils.equals(resultInfo.getCustomerCode(), payProcedureCustomerCode)
					&& StringUtils.equals(resultInfo.getCustomerCode(), splitProcedureCustomerCode)){
				resultInfo.setIsProcedureCustomer(Integer.parseInt(TxsConstants.SplitAttr.PAY_AND_SPLIT_PROCEDURE_CUSTOMER_CODE.code));
			}else if (StringUtils.equals(resultInfo.getCustomerCode(), payProcedureCustomerCode)){
				resultInfo.setIsProcedureCustomer(Integer.parseInt(TxsConstants.SplitAttr.PROCEDURE_CUSTOMER_CODE.code));
			}else if (StringUtils.equals(resultInfo.getCustomerCode(), splitProcedureCustomerCode)) {
				resultInfo.setIsProcedureCustomer(Integer.parseInt(TxsConstants.SplitAttr.SPLIT_PROCEDURE_CUSTOMER_CODE.code));
			}else{
				resultInfo.setIsProcedureCustomer(Integer.parseInt(TxsConstants.SplitAttr.SPLIT_CUSTOMER_CODE.code));
			}
		}

		return resultInfo;
	}

	public int updateTxsPayTradeOrderSplitProcedureFee(String transactionNo, ProcedureFeeResult splitProcedureFeeResult){
		TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNo(transactionNo);
		if(null == txsPayTradeOrder){
			return -1;
		}
		TxsPayTradeOrder txsPayForUpd = new TxsPayTradeOrder();
		txsPayForUpd.setId(txsPayTradeOrder.getId());
		txsPayForUpd.setSplitProcedureFee(splitProcedureFeeResult.getProcedureFee());
		txsPayForUpd.setSplitProcedureRate(splitProcedureFeeResult.getBusinessInst().getRateParam());
		int i = txsPayTradeOrderMapper.updateByPrimaryKeySelective(txsPayForUpd);
		commonService.payLog("updated splitProcedureFee:"+splitProcedureFeeResult.getProcedureFee());
		return i;
	}
}
