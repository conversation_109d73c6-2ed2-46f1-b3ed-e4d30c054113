package com.epaylinks.efps.txs.transaction.domain.split;

import com.epaylinks.efps.common.business.CommonOuterResponse;

public class AccountSplitResponse extends CommonOuterResponse {

    /**
     * 商户订单号
     */
    private String outTradeNo;

    private String customerCode;

    /**
     * 易票联订单号
     */
    private String transactionNo;

    /**
     * 分账订单号
     */
    private String splitTransactionNo;

    /**
     * 撤销订单号
     */
    private String revokeTransactionNo;

    /**
     * 总金额
     * 单位分，总金额=总分账金额+手续费
     */
    //private Long amount;

    /**
     * 总分账金额
     */
    //private Long actualFee;

    /**
     * 手续费
     */
    private Long procedureFee;
    private String state;




    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getSplitTransactionNo() {
        return splitTransactionNo;
    }

    public void setSplitTransactionNo(String splitTransactionNo) {
        this.splitTransactionNo = splitTransactionNo;
    }

    public String getRevokeTransactionNo() {
        return revokeTransactionNo;
    }

    public void setRevokeTransactionNo(String revokeTransactionNo) {
        this.revokeTransactionNo = revokeTransactionNo;
    }
}
