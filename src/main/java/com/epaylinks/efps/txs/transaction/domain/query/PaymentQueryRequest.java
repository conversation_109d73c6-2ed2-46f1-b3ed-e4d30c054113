package com.epaylinks.efps.txs.transaction.domain.query;


import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;

/**
 * 支付结果查询
 * <AUTHOR>
 *
 * @date 2018年2月5日 上午9:58:39
 */
public class PaymentQueryRequest {
	
	private String outTradeNo;
	private String transactionNo;
	@NotBlank
	@Size(max = 32, message = "最长32个字符")
	private String customerCode;
	private String nonceStr;
	private String memberId;
	private String version;

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getNonceStr() {
		return nonceStr;
	}
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
}
