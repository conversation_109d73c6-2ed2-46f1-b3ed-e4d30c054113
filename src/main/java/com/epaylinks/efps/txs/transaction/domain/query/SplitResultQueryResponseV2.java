package com.epaylinks.efps.txs.transaction.domain.query;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.txs.transaction.domain.split.SplitOrder;

import java.util.List;

public class SplitResultQueryResponseV2 extends CommonOuterResponse<String> {
    private String customerCode;
    private String outTradeNo;
    private String transactionNo;
    private Long amount;
    private Long realAmount;
    private Long procedureFee;
    private String attachData;
    private String nonceStr;
    private String splitModel;
    private List<SplitOrder> splitOrderList;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(Long realAmount) {
        this.realAmount = realAmount;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public String getSplitModel() {
        return splitModel;
    }

    public void setSplitModel(String splitModel) {
        this.splitModel = splitModel;
    }

    @Override
    public String getNonceStr() {
        return nonceStr;
    }

    @Override
    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public List<SplitOrder> getSplitOrderList() {
        return splitOrderList;
    }

    public void setSplitOrderList(List<SplitOrder> splitOrderList) {
        this.splitOrderList = splitOrderList;
    }


}
