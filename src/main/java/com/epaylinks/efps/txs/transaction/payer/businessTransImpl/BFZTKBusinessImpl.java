package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsRefundSplitRecordMapper;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("BFZTKBusiness")
public class BFZTKBusinessImpl implements BusinessTransactionCheck {
	@Autowired
	private TxsRefundSplitRecordMapper txsRefundSplitRecordMapper;

	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		List<TxsRefundSplitRecord> txsRefundSplitRecords = txsRefundSplitRecordMapper.selectByBusinessInstId(businessExamId);
		if (txsRefundSplitRecords != null && !txsRefundSplitRecords.isEmpty()) {
			return true;
		}
		return false;
	}

}
