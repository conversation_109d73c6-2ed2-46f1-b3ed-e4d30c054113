package com.epaylinks.efps.txs.transaction.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder;

@Mapper
public interface TxsRefundPreOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TxsRefundPreOrder record);

    int insertSelective(TxsRefundPreOrder record);

    TxsRefundPreOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TxsRefundPreOrder record);

    int updateByPrimaryKey(TxsRefundPreOrder record);
    
    TxsRefundPreOrder selectByOutRefundNoAndCustomerCode(@Param("outRefundNo")String outRefundNo, @Param("customerCode") String customerCode);
    
    List<TxsRefundPreOrder> selectByOutTradeNoAndCustomerCode(@Param("outTradeNo")String outTradeNo, @Param("customerCode") String customerCode);
    
    int updateAmountByPrimaryKey(TxsRefundPreOrder record);
    
    /**
     * 分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsRefundPreOrder> selectByPage(Map map);
    
    int selectCount(Map map);
    
    /**
     * 不分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsRefundPreOrder> selectByNotPage(Map map);

	List<TxsRefundPreOrder> selectByCustomerCodeAndStateAndUpdateTimeInDate(@Param("customerCode") String customerCode,
			@Param("payState")String payState, @Param("updateTime")Date updateTime);
	
	List<TxsRefundPreOrder> selectByCustomerCodeAndUpdateTimeInDate(@Param("customerCode") String customerCode,
			@Param("updateTime")Date updateTime);
	
	List<TxsRefundPreOrder> selectByCustomerCodeAndEndTimeInDate(@Param("customerCode") String customerCode,
			@Param("endTime")Date updateTime);
	
    int countSplitRefundPreOrder(Map map);

    List<TxsRefundPreOrder> splitRefundQueryByPage(Map map);

    List<TxsRefundPreOrder> splitRefundQueryNotPage(Map map);
    
    List<TxsRefundPreOrder> selectByOutTradeNoAndPayState(@Param("outTradeNo")String outTradeNo, @Param("payState")String payState);
    
    List<TxsRefundPreOrder> selectSuccessAndDoingByOutTradeNo(@Param("outTradeNo")String outTradeNo,@Param("customerCode") String customerCode);

    TxsRefundPreOrder selectByTransactionNo(@Param("transactionNo")String transactionNo);

    List<TxsRefundPreOrder> selectAuditList(@Param("startTime") Date startTime, @Param("endTime")Date endTime);
}