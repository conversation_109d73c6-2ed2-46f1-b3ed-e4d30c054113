package com.epaylinks.efps.txs.transaction.domain.query;

import java.util.List;

/**
 * 交易查询结果封装
 * <AUTHOR>
 *
 */
public class TransactionQueryResult {
	
	/**
     * 总数
     */
    private long total;
    /**
     * 总页数
     */
    private int pages;
	
	private List list ;

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}

	public int getPages() {
		return pages;
	}

	public void setPages(int pages) {
		this.pages = pages;
	}

	public List getList() {
		return list;
	}

	public void setList(List list) {
		this.list = list;
	}
	
	

}
