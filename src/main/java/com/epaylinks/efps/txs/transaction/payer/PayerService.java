package com.epaylinks.efps.txs.transaction.payer;

import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.txs.transaction.domain.Payer;

public interface PayerService {
	Trader getPayer(Map<String, Object> map) ;
	
	Payer setPayer(JSONObject payerJson);
	
	/**
	 * true,即该支付方式不需要检查是否携带参数
	 * @param payer
	 * @return
	 */
	boolean checkPayMethodHasParam(JSONObject payer);
	
	/**
	 * 风控处理
	 * @param trader
	 * @return
	 */
	Map<String, String> checkBusinessTagerType(Trader trader);
}
