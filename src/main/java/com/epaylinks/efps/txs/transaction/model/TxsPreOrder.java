package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.epaylinks.efps.txs.transaction.domain.split.CommodityInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
/**
 * 注意: 可变部分，例如支付方式、手续费等，只有在该订单变为终态时才更新至PreOrder中
 */
public class TxsPreOrder {
    private Long orderId;

    /**
     * 交易订单号（成功后写入）
     */
    @FieldAnnotation(fieldName="易票联订单号")
    private String transactionNo;

    /**
     * 商户订单号
     */
//    @FieldAnnotation(fieldName="商户订单号",sql="select * from txs_split_record t where t.transaction_no = (select o.transaction_no from txs_split_order o where o.out_trade_no = ?)")
    @FieldAnnotation(fieldName="商户订单号")
    private String outTradeNo;

    /**
     * 客户编号
     */
    @FieldAnnotation(fieldName="商户编号")
    private String customerCode;
    
    /**
     * 委托方客户编号
     */
    private String commissionedCustomerCode;
    
    /**
     * 目标客户编号
     */
    private String targetCustomerCode;

    /**
     * 交易类型（支付：ZF充值：CZ）可为空
     */
    @FieldAnnotation(fieldName="交易类型",dictionaries="ZF:支付,CZ:充值,FZ:分账,HYCZ:会员充值")
    private String transactionType;

    /**
     * 付款人
     */
    private String payer;

    /**
     * 外部指定买家
     */
    private String extUserInfoJson;

    private String payerType;

    /**
     * 支付方ID
     */
    private String payerId;

    /**
     * 终端号
     */
    private String terminalNo;

    private String clientIp;

    private String srcChannelType;
    @FieldAnnotation(fieldName="支付方式",dictionaries="0:账号支付,1:微信公众号支付,2:个人网银_贷记卡,3:个人网银_借记卡,"
    		+ "4:支付宝生活号支付,5:代付,6:微信扫码支付,7:支付宝扫码支付,8:快捷支付,9:微信app支付,10:微信H5支付,13:微信被扫,14:支付宝被扫,20:手机银联支付,22:企业银联,24:银联扫码主扫,25:银联扫码被扫")
    private String payMethod;

    @FieldAnnotation(fieldName="交易金额(元)" , yuanHandler = true)
    private Long amount;

    private String currencyType;

    private String orderInfo;

    private String notifyUrl;

    private String redirectUrl;

    private String attachData;

    private Date transactionStartTime;

    private Date transactionEndTime;

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldAnnotation(fieldName="交易状态", dictionaries="0:处理中,1:交易成功,2:交易失败,3:订单超时失败,4:待支付")
    private String payState;
    
    private Date settCycleStartTime;
    private Date settCycleEndTime;

    /**
     * 状态更新时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 交易关闭时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="支付时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String errorCode;
    private String errorMsg;
    @FieldAnnotation(fieldName="备注")
    private String remark;
    
    private Date timeInterval;
    /**
     * 结算状态
     */
    private String settlementState;
    @FieldAnnotation(fieldName="手续费(元)" , yuanHandler = true)
    private Long procedureFee;

    private String encryResult;
    
    @FieldAnnotation(fieldName="商户名称")
    private String customerName;
    
    /**
     * 渠道返回的订单
     */
    @FieldAnnotation(fieldName="上游渠道订单号")
    private String channelOrder;

    /**
     * 渠道名字
     */
    @FieldAnnotation(fieldName="上游渠道")
    private String channelName;
    
    private Long refundFee;
    
    private Long refundingFee;
    
    private String businessCode;
    
    private String businessName;
    
    private String withdrawTransactionNo;
    @FieldAnnotation(fieldName="支付通道", userType = "2")
    private String payPassWay;
    
    private String agentCustomerCode;
    @FieldAnnotation(fieldName="所属代理商", userType = "2")
    private String agentCustomerName;
    
    private String redirectFailUrl;
    
    private String quickpayCardno;
    
    private String bankName;
    
    private String noCreditCards; 
    //银行卡号
    private String bankAccountNo;
    //支付方式名称
    private String payMethodName;
    
    private Long payChannelId;
    
    /**
     * 收单机构号
     */
    private String acqOrgCode;

    /**
     * 收单机构服务商 ID
     */
    private String acqSpId;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 终端信息，终端 IP、经纬度等信息， JSON 格式
     */
    private String termInfo;

    /**
     * 地区信息
     */
    private String areaInfo;

    /**
     * 用户标识，支付宝服务窗为用户的 buyer_id，微信公众号为用户的 openid，银联 JS 支付为用户的 userId
     */
    private String userId;

    /**
     * 子商户公众号id
     */
    private String subAppId;

    /**
     * 第三方平台商户号，如银联云闪付
     */
    private String platformCustomerCode;

    /**
     * 商户门店编号
     */
    private String storeId;

    /**
     * 商户操作员编号
     */
    private String operatorId;

    /**
     * 服务商自定义域
     */
    private String reqReserved;

    /**
     * 第三方平台自定义域，如云闪付
     */
    private String cupsReqReserved;

    /**
     * 付款 APP 名称
     */
    private String payerAppName;

    /**
     * 买家付款金额
     */
    private Long actualPayAmount;

    /**
     * 可打折金额，支付宝特有参数，单位：分
     */
    private Long discountableAmount;

    /**
     * 应结订单金额
     */
    private Long settlementAmount;

    /**
     * 交易来源1:EPSP平台,3:云闪付开放平台，4:旧系统
     */
    private String sourceChannel;
    /**
     * 二维码发行方,1： 服务商
     */
    private String qrCodeIssuer;
    /**
     * 费率(下划线隔开，第二部分表示封顶值，示例：30_10，不同的费率有不同的参数格式，对于固定费率：30表示30分钱；对于按比例：30表示费率为万分之30，封顶10分钱
     */
    private String procedureRate;

    /**
     * 费率模式(1：固定费率，2：按比例)
     */
    private Short rateMode;
    
    private String maxProfit;
    
    //马赛克卡号
    private String cardNoMosaic; //CARD_NO_MOSAIC; 打马赛克的卡号
    
    //加密过的卡号
    private String cardNoEnc; //CARD_NO_ENC; 加密的卡号
    
    //卡种类
    private String cardType;	//CARD_TYPE;   
    
    //撤销状态
    private String cancelState;	// 如果该订单发生撤销.撤销状态,在撤销返回时更新(置值) 00--撤销成功; 01--撤销失败
    
    //撤销订单号
    private String cancelNo;	//如果该订单发生撤销.最后一次撤销的系统撤销单号.注,原交易不论成功,失败,处理中,均可以发起多次撤销.更新时更新至第一次撤销成功或最后一次撤销失败 
    
    //撤销返回信息
    private String cancelReturnMsg;	//如果该订单发生撤销.撤销返回信息;  
    
    //撤销更新时间
    private Date cancelUpdateTime;	//如果该订单发生撤销.撤销更新时间; 
    
    /**
     * 持卡人姓名（可逆密文）
     */
    private String cardOwner;   
    
    /**
     * 持卡人姓名（打马赛克）
     */
    private String cardOwnerMosaic;  
    
    /**
     * 持卡人姓名（不可逆密文）
     */
    private String cardOwnerHash;
    
    /**
     * 持卡人身份证号（可逆密文）
     */
    private String certNo;
    
    /**
     * 持卡人身份证号（打马赛克）
     */
    private String certNoMosaic;
    
    /**
     * 持卡人身份证号（不可逆密文）
     */
    private String certNoHash;
    /**
     * 分账关系序列号
     */
    private String splitRelationId;
    

    /**
     * 银行编号
     */
    private String bankCode;
    
    
    /**
     * 应答码
     */
    private String channelRespCode;
    
    /**
     * 应答信息
     */
    private String channelRespMsg;
    
    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 会员编号
     */
    private String memberId;
    /**
     * 分账模式 1-普通分账 2-其他分账
     */
    private String splitModel;
    
    /**
	 * 交易商户号
	 * @return
	 */
	private String tradeCustomerCode;

    /**
     * 子商户号
     * @return
     */
    private String subCustomerCode;

	/**
	 * 分账费率(下划线隔开，第二部分表示封顶值，示例：30_10，不同的费率有不同的参数格式，对于固定费率：30表示30分钱；对于按比例：30表示费率为万分之30，封顶10分钱
	 */
	private String splitProcedureRate;

	/**
	 * 分账手续费
	 */
	private Long splitProcedureFee;
	
	private String openId;
	
	private String buyerLogonId;

	/**
	 * 商品金额
	 */
	private Long commodityAmount;

	private String commodityInfoList;
	
    //买家支付宝账号,长度28
    private String buyerUserId;
    
    /**
	 * 结算周期
	 */
	private String settCycleRuleCode;
	/**
	 * 业务实例ID
	 */
	private String businessInstId;

	/**
	 * 支付场景 条码支付，取值：bar_code; 声波支付，取值：wave_code 人脸识别，取值：face_code
	 */
	private String scene;
	
	/*
	 * RequestSrc, 0-裸接口调用, 1-收银台. 1个字节
	 */
	private String requestSrc;
	/**
	 * 授权商户编号
	 */
	private String  authCustomerCode;
	
	/**
	 * 交易来源
	 */
	private String tradeSource;

	/**
	 * 业务员
	 */
	private String businessMan;

	/**
	 * 业务员ID
	 */
	private Long businessManId;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 公司ID
	 */
	private Long companyId;

	/**
	 * 最大手续费,分
	 */
	private Long maxFee;
	/**
	 * 最小手续费，分
	 */
	private Long minFee;

    /**
     * 碰一碰支付的nfcTagId
     */
    private String nfcTagId;

    /**
     * 单笔固定费率，分
     */
    private Long feePer;
    
	/**
	 * D1交易节假日附加收费的费率万份比.比如值为200,表示万分之200,即百二
	 */
	private String D1HolidayChargeRate;

	/**
	 * D1交易节假日附加收费的单笔加收费用.单位为分. 比如3表示3分
	 */
	private Long D1HolidayChargePer;
	
	/**
	 * 是否实际应收D1交易节假日附加收费,1表是应收.0为不收(不收可能因为非D1,非节假日,非要收取的业务)
	 */
	private String D1HolidayCharged ;
	
	
	/*
	 * 值为1时,表示这是一笔按银行卡计价且是按区域性银行计价
	 */
	private String chargedByBankCodeArea;
	
	private String fundChannel;
	
	/**
	 * 现金支付金额，分
	 */
	private Long cashAmount;
	/**
	 * 代金券金额，分
	 */
	private Long couponAmount;
	/**
	 * 行业码类型 0：普通行业码，1：互联互通码
	 */
	private String industryCodeType;
	/**
	 * 行业码名称
	 */
	private String industryName;
	/**
	 * 机器码
	 */
	private String machineCode;
	
	/**
	 * 初始计费时业务代码
	 */
	private String businessCodeFst;
	
	/**
	 * 初始计费时业务实例id
	 */
	private String businessInstIdFst;
	
	/**
	 * 是否执行了二次计费
	 */
	private String secondCharged;
	
	/**
	 * 终端类型
	 */
	private String terminalType;
	/**
	 * 批次号
	 */
	private String batchNo;
	

    /**
     * 可用渠道，用户只能在指定渠道范围内支付 当有多个渠道时用“,”分隔
     */
    private String enablePayChannels;

    /**
     * 花呗分期数
     */
    private Integer instalmentsNum;

    /**
     * 代表卖家承担收费比例，商家承担手续费传入100，用户承担手续费传入0，仅支持传入100、0两种，其他比例暂不支持，传入会报错。
     */
    private String instalmentsSellerPercent;

    private String subOpenId;
    /**
     * 原预授权订单号
     */
    private String oriTransactionNo;
    /**
     * 授权完成金额
     */
    private Long authFinishAmount;
    /**
     * 授权中金额
     */
    private Long authingAmount;
    /**
     * 授权模式，COMPLETE：转交易支付完成结束预授权，解冻剩余金额; NOT_COMPLETE：转交易支付完成不结束预授权，不解冻剩余金额
     */
    private String authConfirmMode;
    /**
     * 授权状态，00-预授权完成，01-待转支付，02-转支付中
     */
    private String authStatus;
    /**
     * 是否笔笔结算，1-是，0-否
     */
    private String settleType;
    /**
     * 分期商户贴息方式.0-不贴息，1-贴息，2-全额贴息
     */
    private String ipMode;
    /**
     * 分期手续费
     */
    private Long ipProcedureFee;   
    /**
     * 是否分期1-是
     */
    private String isInstalments;   
    
    /**
     * 分期的业务实例
     */
    private String ipBusinessInstId;
    
    /**
     * 分期手续费费率
     */
    private String ipProcedureRate;

    /**
     * 手续费承担者.2022-06-29时是给账户分账,另扣的时候用的
     * @return
     */
    private String procedureCustomercode;

    /**
     * 是否订单分账, 1-是
     * @return
     */
    private String isOrderSplit;

    /**
     * 延迟天数
     * @return
     */
    private String delayDays;

    /**
    * 本地优惠券总金额。local_Coupon number, full_Reduce number, payer_amount number, divided_amount number, dividing_amount number, is_ticket varchar2(1), coupons varchar2(50)
     */
    private Long localCoupon;


    /**
     * 本地满减总金额
     */
    private Long fullReduce;

    /**
     * 实付金额
     * 	5.1.订单金额, amount, 订单的总金额
     * 	5.2.上游优惠, coupon_amount,
     * 	5.3.出资商优惠满减,	localCoupon+fullReduce
     * 	5.4.客户实付 ,payer_amount =  amount - (localCoupon+fullReduce), 也即传到上游的金额
     * 	5.5.订单实收, cash_amount =  payer_amount - procedure_fee
     * 	5.6.手续费, procedure ,使用payer_amount来算
     */
    private Long payerAmount;

    /**
     * 已拆单分账金额
     */
    private Long dividedAmount;

    /**
     * 正在拆单位分账金额
     */
    private Long dividingAmount;

    /**
     * 该订单是否带券（固定金额优惠或满减券）
     */
    private String isTicket;

    /**
     * 券的代码。 多个时用逗号隔开
     */
    private String coupons;

    /**
     * 优惠券的出资总金额. ticketAmount+payerAmount = amount
     */
    private Long ticketAmount;

    /**
     * payerCashAmount
     * @return
     */
    private Long payerCashAmount;


    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getEnablePayChannels() {
        return enablePayChannels;
    }

    public void setEnablePayChannels(String enablePayChannels) {
        this.enablePayChannels = enablePayChannels;
    }

    public Integer getInstalmentsNum() {
        return instalmentsNum;
    }

    public void setInstalmentsNum(Integer instalmentsNum) {
        this.instalmentsNum = instalmentsNum;
    }

    public String getInstalmentsSellerPercent() {
        return instalmentsSellerPercent;
    }

    public void setInstalmentsSellerPercent(String instalmentsSellerPercent) {
        this.instalmentsSellerPercent = instalmentsSellerPercent;
    }

    public String getD1HolidayChargeRate() {
		return D1HolidayChargeRate;
	}

	public void setD1HolidayChargeRate(String d1HolidayChargeRate) {
		D1HolidayChargeRate = d1HolidayChargeRate;
	}

	public Long getD1HolidayChargePer() {
		return D1HolidayChargePer;
	}

	public void setD1HolidayChargePer(Long d1HolidayChargePer) {
		D1HolidayChargePer = d1HolidayChargePer;
	}

	public String getD1HolidayCharged() {
		return D1HolidayCharged;
	}

	public void setD1HolidayCharged(String d1HolidayCharged) {
		D1HolidayCharged = d1HolidayCharged;
	}

	public Long getFeePer() {
        return feePer;
    }

    public void setFeePer(Long feePer) {
        this.feePer = feePer;
    }

    public String getScene() {
		return scene;
	}

	public void setScene(String scene) {
		this.scene = scene;
	}

	public String getSettCycleRuleCode() {
		return settCycleRuleCode;
	}

	public void setSettCycleRuleCode(String settCycleRuleCode) {
		this.settCycleRuleCode = settCycleRuleCode;
	}

	public String getBusinessInstId() {
		return businessInstId;
	}

	public void setBusinessInstId(String businessInstId) {
		this.businessInstId = businessInstId;
	}

	public Long getCommodityAmount() {
		return commodityAmount;
	}

	public void setCommodityAmount(Long commodityAmount) {
		this.commodityAmount = commodityAmount;
	}

	public String getCommodityInfoList() {
		return commodityInfoList;
	}

	public void setCommodityInfoList(String commodityInfoList) {
		this.commodityInfoList = commodityInfoList;
	}

	public String getSplitProcedureRate() {
		return splitProcedureRate;
	}

	public void setSplitProcedureRate(String splitProcedureRate) {
		this.splitProcedureRate = splitProcedureRate;
	}

	public Long getSplitProcedureFee() {
		return splitProcedureFee;
	}

	public void setSplitProcedureFee(Long splitProcedureFee) {
		this.splitProcedureFee = splitProcedureFee;
	}

	public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getSplitModel() {
        return splitModel;
    }

    public void setSplitModel(String splitModel) {
        this.splitModel = splitModel;
    }

    public String getSubCustomerCode() {
        return subCustomerCode;
    }

    public void setSubCustomerCode(String subCustomerCode) {
        this.subCustomerCode = subCustomerCode;
    }

    public String getTerminalName() {
		return terminalName;
	}

	public void setTerminalName(String terminalName) {
		this.terminalName = terminalName;
	}
    
	public String getSplitRelationId() {
		return splitRelationId;
	}

	public void setSplitRelationId(String splitRelationId) {
		this.splitRelationId = splitRelationId;
	}

    
	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
    
	public String getCardOwner() {
		return cardOwner;
	}

	public void setCardOwner(String cardOwner) {
		this.cardOwner = cardOwner;
	}

	public String getCardOwnerMosaic() {
		return cardOwnerMosaic;
	}

	public void setCardOwnerMosaic(String cardOwnerMosaic) {
		this.cardOwnerMosaic = cardOwnerMosaic;
	}

	public String getCardOwnerHash() {
		return cardOwnerHash;
	}

	public void setCardOwnerHash(String cardOwnerHash) {
		this.cardOwnerHash = cardOwnerHash;
	}

	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	public String getCertNoMosaic() {
		return certNoMosaic;
	}

	public void setCertNoMosaic(String certNoMosaic) {
		this.certNoMosaic = certNoMosaic;
	}

	public String getCertNoHash() {
		return certNoHash;
	}

	public void setCertNoHash(String certNoHash) {
		this.certNoHash = certNoHash;
	}

	public String getCardNoMosaic() {
		return cardNoMosaic;
	}

	public void setCardNoMosaic(String cardNoMosaic) {
		this.cardNoMosaic = cardNoMosaic;
	}

	public String getCardNoEnc() {
		return cardNoEnc;
	}

	public void setCardNoEnc(String cardNoEnc) {
		this.cardNoEnc = cardNoEnc;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public String getProcedureRate() {
		return procedureRate;
	}

	public void setProcedureRate(String procedureRate) {
		this.procedureRate = procedureRate;
	}

	public Short getRateMode() {
		return rateMode;
	}

	public void setRateMode(Short rateMode) {
		this.rateMode = rateMode;
	}

	public String getQrCodeIssuer() {
		return qrCodeIssuer;
	}

	public void setQrCodeIssuer(String qrCodeIssuer) {
		this.qrCodeIssuer = qrCodeIssuer;
	}
    
    public String getAcqOrgCode() {
		return acqOrgCode;
	}

	public void setAcqOrgCode(String acqOrgCode) {
		this.acqOrgCode = acqOrgCode;
	}

	public String getAcqSpId() {
		return acqSpId;
	}

	public void setAcqSpId(String acqSpId) {
		this.acqSpId = acqSpId;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(String operatorId) {
		this.operatorId = operatorId;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getTermInfo() {
		return termInfo;
	}

	public void setTermInfo(String termInfo) {
		this.termInfo = termInfo;
	}

	public String getAreaInfo() {
		return areaInfo;
	}

	public void setAreaInfo(String areaInfo) {
		this.areaInfo = areaInfo;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getSubAppId() {
		return subAppId;
	}

	public void setSubAppId(String subAppId) {
		this.subAppId = subAppId;
	}

	public String getPlatformCustomerCode() {
		return platformCustomerCode;
	}

	public void setPlatformCustomerCode(String platformCustomerCode) {
		this.platformCustomerCode = platformCustomerCode;
	}

	public String getReqReserved() {
		return reqReserved;
	}

	public void setReqReserved(String reqReserved) {
		this.reqReserved = reqReserved;
	}

	public String getCupsReqReserved() {
		return cupsReqReserved;
	}

	public void setCupsReqReserved(String cupsReqReserved) {
		this.cupsReqReserved = cupsReqReserved;
	}

	public String getPayerAppName() {
		return payerAppName;
	}

	public void setPayerAppName(String payerAppName) {
		this.payerAppName = payerAppName;
	}

	public Long getActualPayAmount() {
		return actualPayAmount;
	}

	public void setActualPayAmount(Long actualPayAmount) {
		this.actualPayAmount = actualPayAmount;
	}

	public Long getDiscountableAmount() {
		return discountableAmount;
	}

	public void setDiscountableAmount(Long discountableAmount) {
		this.discountableAmount = discountableAmount;
	}

	public Long getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(Long settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public String getPayerType() {
        return payerType;
    }

    public void setPayerType(String payerType) {
        this.payerType = payerType;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getSrcChannelType() {
        return srcChannelType;
    }

    public void setSrcChannelType(String srcChannelType) {
        this.srcChannelType = srcChannelType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Long getAmount() {
		return amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

	public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public Date getTransactionStartTime() {
        return transactionStartTime;
    }

    public void setTransactionStartTime(Date transactionStartTime) {
        this.transactionStartTime = transactionStartTime;
    }

    public Date getTransactionEndTime() {
        return transactionEndTime;
    }

    public void setTransactionEndTime(Date transactionEndTime) {
        this.transactionEndTime = transactionEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

	public Date getTimeInterval() {
		return timeInterval;
	}

	public void setTimeInterval(Date timeInterval) {
		this.timeInterval = timeInterval;
	}

	public String getSettlementState() {
		return settlementState;
	}

	public void setSettlementState(String settlementState) {
		this.settlementState = settlementState;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public String getEncryResult() {
		return encryResult;
	}

	public void setEncryResult(String encryResult) {
		this.encryResult = encryResult;
	}

	public Date getSettCycleStartTime() {
		return settCycleStartTime;
	}

	public void setSettCycleStartTime(Date settCycleStartTime) {
		this.settCycleStartTime = settCycleStartTime;
	}

	public Date getSettCycleEndTime() {
		return settCycleEndTime;
	}

	public void setSettCycleEndTime(Date settCycleEndTime) {
		this.settCycleEndTime = settCycleEndTime;
	}
	
	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

    public String getChannelOrder() {
        return channelOrder;
    }

    public void setChannelOrder(String channelOrder) {
        this.channelOrder = channelOrder;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Long getRefundFee() {
		return refundFee;
	}

	public void setRefundFee(Long refundFee) {
		this.refundFee = refundFee;
	}

	public Long getRefundingFee() {
		return refundingFee;
	}

	public void setRefundingFee(Long refundingFee) {
		this.refundingFee = refundingFee;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getWithdrawTransactionNo() {
		return withdrawTransactionNo;
	}

	public void setWithdrawTransactionNo(String withdrawTransactionNo) {
		this.withdrawTransactionNo = withdrawTransactionNo;
	}

	public String getPayPassWay() {
		return payPassWay;
	}

	public void setPayPassWay(String payPassWay) {
		this.payPassWay = payPassWay;
	}

	public String getAgentCustomerCode() {
		return agentCustomerCode;
	}

	public void setAgentCustomerCode(String agentCustomerCode) {
		this.agentCustomerCode = agentCustomerCode;
	}

	public String getAgentCustomerName() {
		return agentCustomerName;
	}

	public void setAgentCustomerName(String agentCustomerName) {
		this.agentCustomerName = agentCustomerName;
	}

	public String getRedirectFailUrl() {
		return redirectFailUrl;
	}

	public void setRedirectFailUrl(String redirectFailUrl) {
		this.redirectFailUrl = redirectFailUrl;
	}

	public String getQuickpayCardno() {
		return quickpayCardno;
	}

	public void setQuickpayCardno(String quickpayCardno) {
		this.quickpayCardno = quickpayCardno;
	}
	
	public String getNoCreditCards() {
		return noCreditCards;
	}
	public void setNoCreditCards(String noCreditCards) {
		this.noCreditCards = noCreditCards;
	}

	public String getBusinessName() {
		return businessName;
	}

	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankAccountNo() {
		return bankAccountNo;
	}

	public void setBankAccountNo(String bankAccountNo) {
		this.bankAccountNo = bankAccountNo;
	}

	public String getPayMethodName() {
		return payMethodName;
	}

	public void setPayMethodName(String payMethodName) {
		this.payMethodName = payMethodName;
	}

	public Long getPayChannelId() {
		return payChannelId;
	}

	public void setPayChannelId(Long payChannelId) {
		this.payChannelId = payChannelId;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public String getCommissionedCustomerCode() {
		return commissionedCustomerCode;
	}

	public void setCommissionedCustomerCode(String commissionedCustomerCode) {
		this.commissionedCustomerCode = commissionedCustomerCode;
	}

	public String getTargetCustomerCode() {
		return targetCustomerCode;
	}

	public void setTargetCustomerCode(String targetCustomerCode) {
		this.targetCustomerCode = targetCustomerCode;
	}

	public String getMaxProfit() {
		return maxProfit;
	}

	public void setMaxProfit(String maxProfit) {
		this.maxProfit = maxProfit;
	}

	public String getCancelState() {
		return cancelState;
	}

	public void setCancelState(String cancelState) {
		this.cancelState = cancelState;
	}

	public String getCancelNo() {
		return cancelNo;
	}

	public void setCancelNo(String cancelNo) {
		this.cancelNo = cancelNo;
	}

	public String getCancelReturnMsg() {
		return cancelReturnMsg;
	}

	public void setCancelReturnMsg(String cancelReturnMsg) {
		this.cancelReturnMsg = cancelReturnMsg;
	}

	public Date getCancelUpdateTime() {
		return cancelUpdateTime;
	}

	public void setCancelUpdateTime(Date cancelUpdateTime) {
		this.cancelUpdateTime = cancelUpdateTime;
	}

	public String getChannelRespCode() {
		return channelRespCode;
	}

	public void setChannelRespCode(String channelRespCode) {
		this.channelRespCode = channelRespCode;
	}

	public String getChannelRespMsg() {
		return channelRespMsg;
	}

	public void setChannelRespMsg(String channelRespMsg) {
		this.channelRespMsg = channelRespMsg;
	}

	public String getTradeCustomerCode() {
		return tradeCustomerCode;
	}

	public void setTradeCustomerCode(String tradeCustomerCode) {
		this.tradeCustomerCode = tradeCustomerCode;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getBuyerLogonId() {
		return buyerLogonId;
	}

	public void setBuyerLogonId(String buyerLogonId) {
		this.buyerLogonId = buyerLogonId;
	}

	public String getBuyerUserId() {
		return buyerUserId;
	}

	public void setBuyerUserId(String buyerUserId) {
		this.buyerUserId = buyerUserId;
	}

	public String getRequestSrc() {
		return requestSrc;
	}

	public void setRequestSrc(String requestSrc) {
		this.requestSrc = requestSrc;
	}

	public String getAuthCustomerCode() {
		return authCustomerCode;
	}

	public void setAuthCustomerCode(String authCustomerCode) {
		this.authCustomerCode = authCustomerCode;
	}

	public String getTradeSource() {
		return tradeSource;
	}

	public void setTradeSource(String tradeSource) {
		this.tradeSource = tradeSource;
	}

	public String getBusinessMan() {
		return businessMan;
	}

	public void setBusinessMan(String businessMan) {
		this.businessMan = businessMan;
	}

	public Long getBusinessManId() {
		return businessManId;
	}

	public void setBusinessManId(Long businessManId) {
		this.businessManId = businessManId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getMaxFee() {
		return maxFee;
	}

	public void setMaxFee(Long maxFee) {
		this.maxFee = maxFee;
	}

	public Long getMinFee() {
		return minFee;
	}

	public void setMinFee(Long minFee) {
		this.minFee = minFee;
	}

	public String getNfcTagId() {
		return nfcTagId;
	}

	public void setNfcTagId(String nfcTagId) {
		this.nfcTagId = nfcTagId;
	}
	
	public String getChargedByBankCodeArea() {
		return chargedByBankCodeArea;
	}

	public void setChargedByBankCodeArea(String chargedByBankCodeArea) {
		this.chargedByBankCodeArea = chargedByBankCodeArea;
	}

	public String getFundChannel() {
		return fundChannel;
	}

	public void setFundChannel(String fundChannel) {
		this.fundChannel = fundChannel;
	}
	
	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}
	

	public String getIndustryCodeType() {
		return industryCodeType;
	}

	public void setIndustryCodeType(String industryCodeType) {
		this.industryCodeType = industryCodeType;
	}

	public String getIndustryName() {
		return industryName;
	}

	public void setIndustryName(String industryName) {
		this.industryName = industryName;
	}

	public String getMachineCode() {
		return machineCode;
	}

	public void setMachineCode(String machineCode) {
		this.machineCode = machineCode;
	}

	public String getBusinessCodeFst() {
		return businessCodeFst;
	}

	public void setBusinessCodeFst(String businessCodeFst) {
		this.businessCodeFst = businessCodeFst;
	}

	public String getBusinessInstIdFst() {
		return businessInstIdFst;
	}

	public void setBusinessInstIdFst(String businessInstIdFst) {
		this.businessInstIdFst = businessInstIdFst;
	}

	public String getSecondCharged() {
		return secondCharged;
	}

	public void setSecondCharged(String secondCharged) {
		this.secondCharged = secondCharged;
	}
	

	public String getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(String terminalType) {
		this.terminalType = terminalType;
	}
	
	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

    public String getSubOpenId() {
        return subOpenId;
    }

    public void setSubOpenId(String subOpenId) {
        this.subOpenId = subOpenId;
    }

    public String getOriTransactionNo() {
        return oriTransactionNo;
    }

    public void setOriTransactionNo(String oriTransactionNo) {
        this.oriTransactionNo = oriTransactionNo;
    }

    public Long getAuthFinishAmount() {
        return authFinishAmount;
    }

    public void setAuthFinishAmount(Long authFinishAmount) {
        this.authFinishAmount = authFinishAmount;
    }

    public Long getAuthingAmount() {
        return authingAmount;
    }

    public void setAuthingAmount(Long authingAmount) {
        this.authingAmount = authingAmount;
    }

    public String getAuthConfirmMode() {
        return authConfirmMode;
    }

    public void setAuthConfirmMode(String authConfirmMode) {
        this.authConfirmMode = authConfirmMode;
    }

    public String getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(String authStatus) {
        this.authStatus = authStatus;
    }

    public String getIpMode() {
		return ipMode;
	}

	public void setIpMode(String ipMode) {
		this.ipMode = ipMode;
	}

	public Long getIpProcedureFee() {
		return ipProcedureFee;
	}

	public void setIpProcedureFee(Long ipProcedureFee) {
		this.ipProcedureFee = ipProcedureFee;
	}

	public String getIsInstalments() {
		return isInstalments;
	}

	public void setIsInstalments(String isInstalments) {
		this.isInstalments = isInstalments;
	}

	public String getIpBusinessInstId() {
		return ipBusinessInstId;
	}

	public void setIpBusinessInstId(String ipBusinessInstId) {
		this.ipBusinessInstId = ipBusinessInstId;
	}

	public String getIpProcedureRate() {
		return ipProcedureRate;
	}

	public void setIpProcedureRate(String ipProcedureRate) {
		this.ipProcedureRate = ipProcedureRate;
	}

    public String getExtUserInfoJson() {
        return extUserInfoJson;
    }

    public void setExtUserInfoJson(String extUserInfoJson) {
        this.extUserInfoJson = extUserInfoJson;
    }

    public String getProcedureCustomercode() {
        return procedureCustomercode;
    }

    public void setProcedureCustomercode(String procedureCustomercode) {
        this.procedureCustomercode = procedureCustomercode;
    }

    public String getIsOrderSplit() {
        return isOrderSplit;
    }

    public void setIsOrderSplit(String isOrderSplit) {
        this.isOrderSplit = isOrderSplit;
    }

    public String getDelayDays() {
        return delayDays;
    }

    public void setDelayDays(String delayDays) {
        this.delayDays = delayDays;
    }


    public Long getLocalCoupon() {
        return localCoupon;
    }

    public void setLocalCoupon(Long localCoupon) {
        this.localCoupon = localCoupon;
    }

    public Long getFullReduce() {
        return fullReduce;
    }

    public void setFullReduce(Long fullReduce) {
        this.fullReduce = fullReduce;
    }

    public Long getPayerAmount() {
        return payerAmount;
    }

    public void setPayerAmount(Long payerAmount) {
        this.payerAmount = payerAmount;
    }

    public Long getDividedAmount() {
        return dividedAmount;
    }

    public void setDividedAmount(Long dividedAmount) {
        this.dividedAmount = dividedAmount;
    }

    public Long getDividingAmount() {
        return dividingAmount;
    }

    public void setDividingAmount(Long dividingAmount) {
        this.dividingAmount = dividingAmount;
    }

    public String getIsTicket() {
        return isTicket;
    }

    public void setIsTicket(String isTicket) {
        this.isTicket = isTicket;
    }

    public String getCoupons() {
        return coupons;
    }

    public void setCoupons(String coupons) {
        this.coupons = coupons;
    }

    public Long getTicketAmount() {
        return ticketAmount;
    }

    public void setTicketAmount(Long ticketAmount) {
        this.ticketAmount = ticketAmount;
    }

    public Long getPayerCashAmount() {
        return payerCashAmount;
    }

    public void setPayerCashAmount(Long payerCashAmount) {
        this.payerCashAmount = payerCashAmount;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}