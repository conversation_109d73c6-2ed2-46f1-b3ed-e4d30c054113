package com.epaylinks.efps.txs.transaction.domain.split;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

public class AccountSplitRequest {
    /**
     * 版本号
     */
    private String version;

    /**
     * 商户订单号
     */
    @NotBlank(message="商户订单号不能为空")
    @Size(min = 1, max = 32, message = "订单号长度要求1到32之间。")
    private String outTradeNo;

    /**
     * 商户号
     */
    @NotBlank(message="商户号不能为空")
    private String customerCode;

    /**
     * 总商品金额
     * 分账金额，RMB，单位分，必需大于0
     */
    @NotNull(message = "总商品金额不能为空")
    @Min(value = 0,message = "总商品金额必需大于0")
    private Long commodityAmount;

    /**
     * 商品信息列表
     */
    private List<CommodityInfo> commodityInfoList;

    /**
     *分账信息列表
     */
    private List<SplitInfo> splitInfoList;

    /**
     * 分账结果通知地址
     */
    @NotBlank(message="分账结果通知地址不能为空")
    private String notifyUrl;

    /**
     * 附加数据
     */
    private String attachData;

    private String remark;

    /**
     * 随机字符串
     */
    @NotBlank(message="随机字符串不能为空")
    private String nonceStr;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getCommodityAmount() {
        return commodityAmount;
    }

    public void setCommodityAmount(Long commodityAmount) {
        this.commodityAmount = commodityAmount;
    }

    public List<CommodityInfo> getCommodityInfoList() {
        return commodityInfoList;
    }

    public void setCommodityInfoList(List<CommodityInfo> commodityInfoList) {
        this.commodityInfoList = commodityInfoList;
    }

    public List<SplitInfo> getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(List<SplitInfo> splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
