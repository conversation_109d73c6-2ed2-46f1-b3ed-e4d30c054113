package com.epaylinks.efps.txs.transaction.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.txs.transaction.model.TxsSplitRelation;
@Mapper
public interface TxsSplitRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TxsSplitRelation record);

    int insertSelective(TxsSplitRelation record);

    TxsSplitRelation selectByPrimaryKey(Long id);
    
    List<TxsSplitRelation> selectByRelationId(@Param("splitRelationId")String splitRelationId);
    
    int deleteByRelationId(@Param("splitRelationId")String splitRelationId, @Param("belongCustomerCode")String belongCustomerCode);

    int updateByPrimaryKeySelective(TxsSplitRelation record);

    int updateByPrimaryKey(TxsSplitRelation record);
}