package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.WeChatAppTrader;
import com.epaylinks.efps.common.business.pay.request.trader.WeChatH5Trader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;

@Service("WeChatH5")
public class WeChatH5PayerServiceImpl implements PayerService {

	@Override
	public Trader getPayer(Map<String, Object> map) {
		WeChatH5Trader weChatH5Trader =  new WeChatH5Trader();
		weChatH5Trader.setWapName(MapUtils.getString(map, "wapName"));
        weChatH5Trader.setWapUrl(MapUtils.getString(map, "wapUrl"));
		weChatH5Trader.setType(TraderType.WeChatH5);
		return weChatH5Trader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		Payer payer = new Payer();
		payer.setPayerId(payerJson.getString(""));
		payer.setPayerType(TraderType.WeChatH5.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
