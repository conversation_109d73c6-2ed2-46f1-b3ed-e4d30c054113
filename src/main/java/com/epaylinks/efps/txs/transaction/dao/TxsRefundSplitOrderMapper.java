package com.epaylinks.efps.txs.transaction.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitOrder;

@Mapper
public interface TxsRefundSplitOrderMapper {
    int deleteByPrimaryKey(String transactionNo);

    int insert(TxsRefundSplitOrder record);

    int insertSelective(TxsRefundSplitOrder record);

    TxsRefundSplitOrder selectByPrimaryKey(String transactionNo);
    
    List<TxsRefundSplitOrder> selectByOutRefundTradeNo(@Param("outRefundTradeNo")String outRefundTradeNo,
			@Param("customerCode") String customerCode);
    
    TxsRefundSplitOrder selectByOutRefundTradeNoAndBusinessCode(@Param("outRefundTradeNo")String outRefundTradeNo,
    		@Param("customerCode") String customerCode, @Param("businessCode") String businessCode);

    int updateByPrimaryKeySelective(TxsRefundSplitOrder record);

    int updateByPrimaryKey(TxsRefundSplitOrder record);
    
    List<TxsRefundSplitOrder> selectByBusinessInstId(@Param("businessInstId") String businessInstId);
    
    List<TxsRefundSplitOrder> selectByOutRefundTradeNoAndState(@Param("outRefundTradeNo")String outRefundTradeNo,
			@Param("customerCode") String customerCode , @Param("state") String state);
}