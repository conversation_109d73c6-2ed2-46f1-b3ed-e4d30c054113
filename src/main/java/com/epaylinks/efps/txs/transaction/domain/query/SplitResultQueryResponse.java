package com.epaylinks.efps.txs.transaction.domain.query;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
/**
 * 分账结果查询响应参数
 * <AUTHOR>
 *
 * @date 2018年1月25日 下午5:05:19
 */
public class SplitResultQueryResponse extends CommonOuterResponse<String> {
	private String customerCode;
	private String outTradeNo;
	private String transactionNo;

	/**
	 * 分账订单号
	 */
	private String splitTransactionNo;

	/**
	 * 撤销订单号
	 */
	private String revokeTransactionNo;
	private Long amount;
	private Long realAmount;
	private String splitState;
	private String splitTime;
//	private String settCycle;
//	private Long settCycleInterval;
	private Long procedureFee;
	private String attachData;
	private List<JSONObject> splitResultInfoList;
	private String nonceStr;
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public Long getRealAmount() {
		return realAmount;
	}
	public void setRealAmount(Long realAmount) {
		this.realAmount = realAmount;
	}
	public String getSplitState() {
		return splitState;
	}
	public void setSplitState(String splitState) {
		this.splitState = splitState;
	}
	public String getSplitTime() {
		return splitTime;
	}
	public void setSplitTime(String splitTime) {
		this.splitTime = splitTime;
	}
//	public String getSettCycle() {
//		return settCycle;
//	}
//	public void setSettCycle(String settCycle) {
//		this.settCycle = settCycle;
//	}
//	public Long getSettCycleInterval() {
//		return settCycleInterval;
//	}
//	public void setSettCycleInterval(Long settCycleInterval) {
//		this.settCycleInterval = settCycleInterval;
//	}
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}
	public String getAttachData() {
		return attachData;
	}
	public void setAttachData(String attachData) {
		this.attachData = attachData;
	}
	public List<JSONObject> getSplitResultInfoList() {
		return splitResultInfoList;
	}
	public void setSplitResultInfoList(List<JSONObject> splitResultInfoList) {
		this.splitResultInfoList = splitResultInfoList;
	}

	public String getSplitTransactionNo() {
		return splitTransactionNo;
	}

	public void setSplitTransactionNo(String splitTransactionNo) {
		this.splitTransactionNo = splitTransactionNo;
	}

	public String getRevokeTransactionNo() {
		return revokeTransactionNo;
	}

	public void setRevokeTransactionNo(String revokeTransactionNo) {
		this.revokeTransactionNo = revokeTransactionNo;
	}

	public String getNonceStr() {
		return nonceStr;
	}
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
}
