package com.epaylinks.efps.txs.transaction.domain.split;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.epaylinks.efps.common.business.CommonOuterResponse;

public class SplitRelationQueryResponse extends CommonOuterResponse<String>{
	/**
     * 分账关系序列号
     */
	@NotBlank(message="分账关系序列号不能为空")
    private String splitRelationId;
    /**
     * 所属商户号
     */
	@NotBlank(message="所属商户号不能为空")
    private String belongCustomerCode;
    /**
     * 分账关系数据
     */
	@NotNull(message="分账关系数据不能为空")
    private List<SplitRelation> splitRelationList;
    
	public String getSplitRelationId() {
		return splitRelationId;
	}
	public void setSplitRelationId(String splitRelationId) {
		this.splitRelationId = splitRelationId;
	}
	public String getBelongCustomerCode() {
		return belongCustomerCode;
	}
	public void setBelongCustomerCode(String belongCustomerCode) {
		this.belongCustomerCode = belongCustomerCode;
	}
	public List<SplitRelation> getSplitRelationList() {
		return splitRelationList;
	}
	public void setSplitRelationList(List<SplitRelation> splitRelationList) {
		this.splitRelationList = splitRelationList;
	}
	
}
