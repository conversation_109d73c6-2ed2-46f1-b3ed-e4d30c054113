package com.epaylinks.efps.txs.transaction.model;

public class OrderCommodityInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户号
     */
    private String customerCode;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 商品订单号
     */
    private String commodityOrderNo;

    /**
     * 商品订单金额
     */
    private Long commodityOrderAmount;

    /**
     * 商品订单时间
     */
    private String commodityOrderTime;

    /**
     * 商品订单描述
     */
    private String commodityDescription;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCommodityOrderNo() {
        return commodityOrderNo;
    }

    public void setCommodityOrderNo(String commodityOrderNo) {
        this.commodityOrderNo = commodityOrderNo;
    }

    public Long getCommodityOrderAmount() {
        return commodityOrderAmount;
    }

    public void setCommodityOrderAmount(Long commodityOrderAmount) {
        this.commodityOrderAmount = commodityOrderAmount;
    }

    public String getCommodityOrderTime() {
        return commodityOrderTime;
    }

    public void setCommodityOrderTime(String commodityOrderTime) {
        this.commodityOrderTime = commodityOrderTime;
    }

    public String getCommodityDescription() {
        return commodityDescription;
    }

    public void setCommodityDescription(String commodityDescription) {
        this.commodityDescription = commodityDescription;
    }
}