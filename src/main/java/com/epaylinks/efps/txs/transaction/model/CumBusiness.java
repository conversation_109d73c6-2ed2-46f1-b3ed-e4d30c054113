package com.epaylinks.efps.txs.transaction.model;

import com.alibaba.fastjson.JSON;

public class CumBusiness {
    private Long id;

    private String name;

    private String code;

    private String type;

    private String ratioMode;

    private String ratio;

    private String remark;

    private String state;
    /**
     * 业务类别：efpsAccountService: efps账务服务, efpsPayService: efps支付服务
     */
    private String businessCategory;

    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRatioMode() {
        return ratioMode;
    }

    public void setRatioMode(String ratioMode) {
        this.ratioMode = ratioMode;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

	public CumBusiness(Long id, String name, String code, String type, String ratioMode, String ratio, String remark,
			String state, String businessCategory) {
		super();
		this.id = id;
		this.name = name;
		this.code = code;
		this.type = type;
		this.ratioMode = ratioMode;
		this.ratio = ratio;
		this.remark = remark;
		this.state = state;
        this.businessCategory = businessCategory;
	}
	
	
	public CumBusiness() {
		super();
	}

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}

    public String getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }
}
