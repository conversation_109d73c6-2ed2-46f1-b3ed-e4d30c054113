package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class TxsMultiOrderRefundAmount {
    /**
     * 表id
     */
    private Long id;

    /**
     * 交易订单号
     */
    private String transactionNo;

    /**
     * 退款到的商户.比如出资方,实收方
     */
    private String customerCode;

    /**
     * 总金额
     */
    private Long totalAmount;

    /**
     * 退款中金额
     */
    private Long refundingAmount;

    /**
     * 已退款金额
     */
    private Long refundedAmount;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *  是否收单商户 1-是
     * @return
     */
    private String isTradeCustomerCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getRefundingAmount() {
        return refundingAmount;
    }

    public void setRefundingAmount(Long refundingAmount) {
        this.refundingAmount = refundingAmount;
    }

    public Long getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(Long refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getIsTradeCustomerCode() {
        return isTradeCustomerCode;
    }

    public void setIsTradeCustomerCode(String isTradeCustomerCode) {
        this.isTradeCustomerCode = isTradeCustomerCode;
    }
}