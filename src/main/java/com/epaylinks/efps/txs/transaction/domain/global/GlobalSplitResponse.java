package com.epaylinks.efps.txs.transaction.domain.global;

import com.epaylinks.efps.common.business.CommonOuterResponse;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class GlobalSplitResponse extends CommonOuterResponse {

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 易票联订单号
     */
    private String transactionNo;

    private String state;

    private Long procedureFee;

    /**
     * 收款金额
     * 单位分，必需大于0
     */
    private Long commodityAmount;

    /**
     * 随机字符串
     */
    private String nonceStr;


    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    @Override
    public String getNonceStr() {
        return nonceStr;
    }

    @Override
    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public Long getCommodityAmount() {
        return commodityAmount;
    }

    public void setCommodityAmount(Long commodityAmount) {
        this.commodityAmount = commodityAmount;
    }

}
