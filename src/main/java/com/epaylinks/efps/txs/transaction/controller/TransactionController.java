package com.epaylinks.efps.txs.transaction.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.domain.InsidePayRequest;
import com.epaylinks.efps.common.business.split.ExecuteSplitRequest;
import com.epaylinks.efps.common.business.split.SplitByRelationRequest;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.controller.maintain.dto.MainTainSplitResponse;
import com.epaylinks.efps.txs.transaction.dao.TxsPayTradeOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsPreOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitRecordMapper;
import com.epaylinks.efps.txs.transaction.domain.query.PaymentQueryRequest;
import com.epaylinks.efps.txs.transaction.domain.query.SplitResultQueryRequest;
import com.epaylinks.efps.txs.transaction.domain.query.SplitResultQueryResponse;
import com.epaylinks.efps.txs.transaction.domain.query.SplitResultQueryResponseV2;
import com.epaylinks.efps.txs.transaction.domain.split.*;
import com.epaylinks.efps.txs.transaction.model.*;
import com.epaylinks.efps.txs.transaction.service.*;
import com.netflix.discovery.converters.Auto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 交易接口
 *
 * <AUTHOR>
 */

@RestController
@Api(value = "TransactionController", description = "交易控制器")
@RequestMapping("/pay")
public class TransactionController {

	@Autowired
	private TransactionService transactionService;
	@Autowired
	private SplitPayService splitPayService;
	@Autowired
	private SplitOrderQueryService splitOrderQueryService;
	@Autowired
	private TxsSplitOrderMapper txsSplitOrderMapper;
	@Autowired
    private TxsPayTradeOrderMapper txsPayTradeOrderMapper;
	@Autowired
    private TxsPreOrderMapper txsPreOrderMapper;
	@Autowired
	private TxsSplitRecordMapper txsSplitRecordMapper;
	@Autowired
	private AccountSplitService accountSplitService;

	private final String format = "yyyyMMddHHmmss";

	@Autowired
    HessianService hessianService;
	@Autowired
	private ReturnCodeUtil returnCodeUtil;

	@Autowired
	private AutoSplitService autoSplitService;

	@Autowired
	private CommonService commonService;

	@Autowired
	private RedisLockService redisLockService;

	/**
	 * 分账接口
	 *
	 * @param customerCodeHead
	 * @param fenZhangRequest
	 * @return
	 */
	@PostMapping("/SplitOrder")
	@Logable(businessTag = "SplitOrder-controller")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "分账接口处理(延时分账)", notes = "分账处理(延时分账)", httpMethod = "POST")
	@ResponseBody
	public FenZhangResponse SplitOrder(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) FenZhangRequest fenZhangRequest) {
		FenZhangResponse fenZhangResponse = new FenZhangResponse();
		fenZhangResponse.setOutTrandNo(fenZhangRequest.getOutTradeNo());

		try {
			if(null != fenZhangRequest.getSplitInfoList()){
				for(SplitInfo splitInfo: fenZhangRequest.getSplitInfoList()){
					if(null != splitInfo.getSettleCycle() && splitInfo.getSettleCycle() == 0){
						//接口传的是1-30
						//只有自动的是0
						//神经
						throw new AppException(TxsCode.SPLIT_SETTLE_CYCLE_ERROR.code);
					}
				}
			}
			fenZhangResponse = splitPayService.splitPay(customerCodeHead, fenZhangRequest);
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(fenZhangResponse, e, TxsCode.FAIL.code);
		}
		fenZhangResponse.setOutSplitTradeNo(fenZhangRequest.getOutSplitTradeNo());
		return fenZhangResponse;
	}

	/**
	 * 分账关系分账
	 *
	 * @param customerCodeHead
	 * @param fenZhangRequest
	 * @return
	 */
	@PostMapping("/splitByRelationId")
	@Logable(businessTag = "splitByRelationId-controller")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "分账关系分账", notes = "分账关系分账", httpMethod = "POST")
	@ResponseBody
	public FenZhangResponse splitByRelationId(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) SplitByRelationRequest splitByRelationRequest) {
		FenZhangResponse fenZhangResponse = new FenZhangResponse();
		fenZhangResponse.setOutTrandNo(splitByRelationRequest.getOutTradeNo());
		try {
			transactionService.checkCustomerCodeAndHead(customerCodeHead, splitByRelationRequest.getCustomerCode());
			fenZhangResponse = splitPayService.splitByRelationId(splitByRelationRequest);
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(fenZhangResponse, e, TxsCode.FAIL.code);
		}
		return fenZhangResponse;
	}

	/**
	 * 确认执行分账
	 *
	 * @param customerCodeHead
	 * @param fenZhangRequest
	 * @return
	 */
	@PostMapping("/confirmExecuteSplit")
	@Logable(businessTag = "confirmExecuteSplit-controller")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "确认执行分账", notes = "确认执行分账", httpMethod = "POST")
	@ResponseBody
	public FenZhangResponse confirmExecuteSplit(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) @Valid ExecuteSplitRequest executeSplitRequest) {
		FenZhangResponse fenZhangResponse = new FenZhangResponse();
		try {
			splitPayService.confirmExecuteSplit(executeSplitRequest);
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(fenZhangResponse, e, TxsCode.FAIL.code);
		}
		return fenZhangResponse;
	}



	/**
	 * 校验分账主体
	 *
	 * @param customerCodeHead
	 * @param fenZhangRequest
	 * @return
	 */
	@PostMapping("/checkSplitMain")
	@Logable(businessTag = "checkSplitMain-controller")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "校验分账主体", notes = "校验分账主体", httpMethod = "POST")
	@ResponseBody
	public CommonOuterResponse<String> checkSplitMain(
			@RequestParam(value = "customerCode", required = false) String customerCode,
			@RequestParam(value = "splitMain", required = false) String splitMain,
			@RequestParam(value = "spilitModel", required = false) String spilitModel
			) {
		CommonOuterResponse<String> response = new CommonOuterResponse<>();
		try {
			splitPayService.checkSplitMain(customerCode, splitMain, spilitModel);
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(response, e, TxsCode.FAIL.code);
		}
		return response;
	}


	@PostMapping("/SplitOrderResultQuery")
	@Logable(businessTag = "SplitOrderResultQuery-controller")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "分账查询", notes = "分账查询", httpMethod = "POST")
	@ResponseBody
	public SplitResultQueryResponse SplitOrderResultQuery(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) PaymentQueryRequest paymentQueryRequest) {
		SplitResultQueryResponse splitResultQueryResponse = new SplitResultQueryResponse();
		String outTradeNo = paymentQueryRequest.getOutTradeNo();
		String transactionNo = paymentQueryRequest.getTransactionNo();
		String customerCode = paymentQueryRequest.getCustomerCode();
		String nonceStr = paymentQueryRequest.getNonceStr();
		try {
			transactionService.checkCustomerCodeAndHead(customerCodeHead, customerCode);

			if (StringUtils.isBlank(outTradeNo) && StringUtils.isBlank(transactionNo)) {
				splitResultQueryResponse.setReturnCode(TxsCode.OUTTRADENO_TRANSACTIONNO_ISNULL.code);
				splitResultQueryResponse.setReturnMsg(TxsCode.OUTTRADENO_TRANSACTIONNO_ISNULL.message);
				return splitResultQueryResponse;
			} else if (StringUtils.isBlank(nonceStr)) {
				splitResultQueryResponse.setReturnCode(TxsCode.NONCESTR_MUSTHAS.code);
				splitResultQueryResponse.setReturnMsg(TxsCode.NONCESTR_MUSTHAS.message);
				return splitResultQueryResponse;
			} else {
				TxsSplitOrder txsSplitOrder = null;
				if (StringUtils.isNotBlank(outTradeNo) && StringUtils.isNotBlank(transactionNo)) {
					txsSplitOrder = splitOrderQueryService.selectSplitOrderByTransactionNoAndCustomerCode(transactionNo,
							customerCode);
				} else {
					if (StringUtils.isNotBlank(outTradeNo)) {
						List<TxsSplitOrder> txsSplitOrders = splitOrderQueryService.selectByOutTradeNoToResultQuery(
								outTradeNo, TxsConstants.SplitOrderState.SUCCESS.code, customerCode);
						if (txsSplitOrders.size() > 0) {
							txsSplitOrder = txsSplitOrders.get(0);
						}
						if (txsSplitOrders.size() <= 0) {
							List<TxsSplitOrder> txsSplitOrders2 = splitOrderQueryService
									.selectByOutTradeNoAndCustomerCodes(outTradeNo, customerCode);
							if (txsSplitOrders2.size() > 0) {
								txsSplitOrder = txsSplitOrders2.get(0);
							}
						}
					}
					if (StringUtils.isNotBlank(transactionNo)) {
						txsSplitOrder = splitOrderQueryService.selectSplitOrderByTransactionNoAndCustomerCode(transactionNo,
								customerCode);
					}
				}

				if (txsSplitOrder == null) {
					splitResultQueryResponse.setReturnCode(TxsCode.ORDERNOTFOUND.code);
					splitResultQueryResponse.setReturnMsg(TxsCode.ORDERNOTFOUND.message);
					return splitResultQueryResponse;
				}
				if (txsSplitOrder != null) {
					splitResultQueryResponse.setReturnCode(TxsConstants.detailReturnCode.RETURN_SUCCESS.code);
					splitResultQueryResponse.setReturnMsg(TxsConstants.detailReturnCode.RETURN_SUCCESS.comment);
					splitResultQueryResponse.setCustomerCode(txsSplitOrder.getCustomerCode());
					splitResultQueryResponse.setOutTradeNo(txsSplitOrder.getOutTradeNo());
					splitResultQueryResponse.setAmount(txsSplitOrder.getAmount());
					splitResultQueryResponse.setTransactionNo(txsSplitOrder.getTransactionNo());
					splitResultQueryResponse.setSplitTransactionNo(txsSplitOrder.getTransactionNo());
					splitResultQueryResponse.setRevokeTransactionNo(txsSplitOrder.getRevokeTransactionNo());
					splitResultQueryResponse.setRealAmount(txsSplitOrder.getRealAmount());
					splitResultQueryResponse.setSplitState(txsSplitOrder.getState());
					if (txsSplitOrder.getState().equals(TxsConstants.SplitOrderState.SUCCESS.code)
							|| txsSplitOrder.getState().equals(TxsConstants.SplitOrderState.FAIL.code)) {
						splitResultQueryResponse.setSplitTime(DateFormatUtils.format(txsSplitOrder.getEndTime(), format));
					}
					// TODO 当前只有 “天”结算周期
					if (txsSplitOrder.getState().equals(TxsConstants.SplitOrderState.SUCCESS.code)
							|| txsSplitOrder.getState().equals(TxsConstants.PayState.FAIL.code)) {
						Calendar day = Calendar.getInstance();
						day.setTime(txsSplitOrder.getEndTime());// 24小时制
						day.set(Calendar.HOUR_OF_DAY, 0);
						day.set(Calendar.MINUTE, 0);
						day.set(Calendar.SECOND, 0);
						day.set(Calendar.MILLISECOND, 0);
//						splitResultQueryResponse.setSettCycle(DateFormatUtils.format(day.getTime(), "yyyyMMddHH"));
					}
//					splitResultQueryResponse.setSettCycleInterval(24L);
					splitResultQueryResponse.setProcedureFee(txsSplitOrder.getProcedureFee());
					splitResultQueryResponse.setAttachData(txsSplitOrder.getAttachData());
					splitResultQueryResponse.setSplitResultInfoList(
							JSONArray.parseArray(txsSplitOrder.getSplitInfoList(), JSONObject.class));
					splitResultQueryResponse.setNonceStr(UUIDUtils.uuid());
				}
			}
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(splitResultQueryResponse, e, TxsCode.FAIL.code);
		}

		return splitResultQueryResponse;
	}

	@PostMapping("/SplitOrderResultQueryV2")
	@Logable(businessTag = "SplitOrderResultQueryV2")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "分账查询V2（适用于拆单分账）", notes = "分账查询V2（适用于拆单分账）", httpMethod = "POST")
	@ResponseBody
	public SplitResultQueryResponseV2 SplitOrderResultQueryV2(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody SplitResultQueryRequest request) {
		SplitResultQueryResponseV2 responseV2 = new SplitResultQueryResponseV2();
		String outTradeNo = request.getOutTradeNo();
		String customerCode = request.getCustomerCode();
		String nonceStr = request.getNonceStr();
		try {
			if (StringUtils.isBlank(outTradeNo)) {
				responseV2.setReturnCode(TxsCode.OUTTRADENO_MUSTHAS.code);
				responseV2.setReturnMsg(TxsCode.OUTTRADENO_MUSTHAS.message);
				return responseV2;
			}else if(StringUtils.isBlank(customerCode)){
				responseV2.setReturnCode(TxsCode.CUSTOMERCODE_MUSTHAS.code);
				responseV2.setReturnMsg(TxsCode.CUSTOMERCODE_MUSTHAS.message);
				return responseV2;
			}
			else if (StringUtils.isBlank(nonceStr)) {
				responseV2.setReturnCode(TxsCode.NONCESTR_MUSTHAS.code);
				responseV2.setReturnMsg(TxsCode.NONCESTR_MUSTHAS.message);
				return responseV2;
			} else {
				transactionService.checkCustomerCodeAndHead(customerCodeHead, customerCode);
				TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(request.getOutTradeNo(), request.getCustomerCode());
				if(txsPreOrder == null){
                    responseV2.setReturnCode(TxsCode.TRADEORDER_NOTFOUND.code);
                    responseV2.setReturnMsg(TxsCode.TRADEORDER_NOTFOUND.message);
                    return responseV2;
                }
				responseV2.setAmount(txsPreOrder.getAmount());
				responseV2.setRealAmount(txsPreOrder.getDividedAmount() + txsPreOrder.getDividingAmount());
				responseV2.setCustomerCode(txsPreOrder.getCustomerCode());
				responseV2.setOutTradeNo(txsPreOrder.getOutTradeNo());
				responseV2.setTransactionNo(txsPreOrder.getTransactionNo());
				responseV2.setProcedureFee(txsPreOrder.getProcedureFee());
				responseV2.setAttachData(txsPreOrder.getAttachData());
				responseV2.setSplitModel(txsPreOrder.getSplitModel());
				List<SplitOrder> splitOrderList = new ArrayList<>();

				List<TxsSplitOrder> txsSplitOrderList = txsSplitOrderMapper.selectByOutTradeNoAndCustomerCode(
						request.getOutTradeNo(), request.getCustomerCode());
				txsSplitOrderList.forEach(txsSplitOrder -> {
					SplitOrder splitOrder = new SplitOrder();
					splitOrder.setAmount(txsSplitOrder.getAmount());
					splitOrder.setSplitTransactionNo(txsSplitOrder.getTransactionNo());
					splitOrder.setOutSplitTradeNo(txsSplitOrder.getOutSplitTradeNo());
					splitOrder.setSplitState(txsSplitOrder.getState());
					splitOrder.setSplitTime(txsSplitOrder.getEndTime() == null ? null :
							Timex.ofDate(txsSplitOrder.getEndTime()).to(Timex.Format.yyMMddHHmmss));
					List<TxsSplitRecord> txsSplitRecordList = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
//					splitOrder.setSplitResultInfoList(CumCacheServiceImpl.copy(txsSplitRecordList, SplitRecord.class));
					List<SplitRecord> splitRecordList = new ArrayList<>();
					txsSplitRecordList.forEach(txsSplitRecord -> {
						SplitRecord splitRecord = new SplitRecord();
						splitRecord.setAmount(txsSplitRecord.getAmount());
						splitRecord.setSplitAttr(txsSplitRecord.getSplitAttr());
						splitRecord.setCustomerCode(txsSplitRecord.getCustomerCode());
						splitRecord.setSettleCycle(txsSplitRecord.getSettleCycleType() + txsSplitRecord.getSettleCycle());
						splitRecord.setSettleState(txsSplitRecord.getSettleState());
						splitRecordList.add(splitRecord);
					});
					splitOrder.setSplitResultInfoList(splitRecordList);
					splitOrderList.add(splitOrder);
				});
				responseV2.setSplitOrderList(splitOrderList);
			}
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(responseV2, e, TxsCode.FAIL.code);
		}
		responseV2.setNonceStr(UUIDUtils.getUUID());
		return responseV2;
	}

	/**
	 *  触发分账
	 * @param customerCode 客户编码
	 * @param transactionNosParam 交易单号，逗号隔开
	 */
	@Logable(businessTag = "execSplit-controller")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "customerCode", value = "客户编码", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "transactionNosParam", value = "交易单号，逗号隔开", required = true, dataType = "String", paramType = "query")})
	@ApiOperation(value = "模拟结算触发分账", notes = "模拟结算触发分账", httpMethod = "POST")
	@Exceptionable
	@PostMapping("/execSplit")
	public MainTainSplitResponse execSplit(@RequestParam(required = true)String customerCode,
			@RequestParam(required = true)String transactionNosParam) {
		MainTainSplitResponse response = new MainTainSplitResponse();
		String[] transactionNos = transactionNosParam.split(",");
		List<String> transactionNoList = new ArrayList<>();
		for (int i = 0; i < transactionNos.length; i++) {
			transactionNoList.add(transactionNos[i]);
		}
		if (transactionNoList == null || transactionNoList.isEmpty()) {
			response.setReturnCode(TxsCode.TRANSACTIONNO_MUSTHAS.code);
			response.setReturnMsg(TxsCode.TRANSACTIONNO_MUSTHAS.message);
			return response;
		}
		List<String> detailedTransactionNos = new ArrayList<>();
		List<String> notDetailTransactionNos = new ArrayList<>();
		for (String transactionNo : transactionNoList) {
			TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNo(transactionNo);
			if (txsPayTradeOrder != null) {
				//支付成功才进行分账
				if (!StringUtils.equals(txsPayTradeOrder.getState(), TxsConstants.PayState.SUCCESS.code)) {
					notDetailTransactionNos.add(transactionNo+" : 订单未支付成功，不能执行分账");
					continue;
				}
/*				if(!redisLockService.setReturnSplitMutex(transactionNo)){
					commonService.payLog(transactionNo+"退款中,不执行分账!继续下一条");
					continue;
				}*/
				customerCode = txsPayTradeOrder.getCustomerCode();
				List<TxsSplitOrder> listSplitOrder = txsSplitOrderMapper.selectByOutTradeNoAndCustomerCode(txsPayTradeOrder.getOutTradeNo(), customerCode);
				if(null != listSplitOrder && listSplitOrder.size() != 0){
					for(TxsSplitOrder splitOrder: listSplitOrder){
						if(splitOrder!=null
								&& !splitOrder.getState().equals(TxsConstants.SplitOrderState.SUCCESS.code)
								&& !splitOrder.getState().equals(TxsConstants.SplitOrderState.REVOKE.code))
						{
							if (txsPreOrderMapper.selectTotalRefundAmt(txsPayTradeOrder.getOutTradeNo(), customerCode) != 0) {
								notDetailTransactionNos.add(transactionNo+TxsCode.ORDER_REFUND.message+"不能执行分账");
								continue;
							}
							try {
								List<TxsSplitRecord> splitRecordList = txsSplitRecordMapper.selectByTransactionNo(splitOrder.getTransactionNo());

								InsidePayRequest insidePayRequest = null;
								if(Constants.transactionType.FZTRANSACTION.code.equals(txsPayTradeOrder.getTransactionType())) {
									if(TxsConstants.SplitOrderState.INIT.code.equals(splitOrder.getState())) {
										//INIT的要切换到UNEXCETED先,以免退货那边又来了
										splitPayService.checkAndUpdateStateReadyToExcute(splitOrder.getTransactionNo());
									}
									splitPayService.reCalcProcedureAndPersistIfNeed(txsPayTradeOrder, splitOrder, splitRecordList);
									insidePayRequest = splitPayService.createInsidePayRequest(splitOrder, splitRecordList, txsPayTradeOrder.getControlled());
								}
								else if(Constants.transactionType.ZHFZTRANSACTION.code.equals(txsPayTradeOrder.getTransactionType())) {
									insidePayRequest = accountSplitService.createInsidePayRequest(splitOrder, splitRecordList);
								}
								else {
									continue;
								}
								splitPayService.executeSplit(insidePayRequest, new Date(), splitOrder, splitRecordList);
								detailedTransactionNos.add(transactionNo);
								try{
									int i = txsSplitRecordMapper.updatePaytransactionNoByTransactionNoWhenNull(splitOrder.getTransactionNo(), transactionNo);
									commonService.payLog(splitOrder.getTransactionNo()+"补充了paytransactionNo,条数:"+i);
								}catch (Exception e){
									commonService.logException(e);
								}
							} catch (Exception e) {
								transactionService.logException(e);
								notDetailTransactionNos.add(transactionNo+" : "+e.getMessage());
							}
						}else {
							notDetailTransactionNos.add(transactionNo+" : 分账单不存在或该订单已经分账成功,或已撤销");
						}
					}
				}
				//redisLockService.deleteReturnSplitMutex(transactionNo);
			}else {
				notDetailTransactionNos.add(transactionNo+" : 支付订单不存在");
			}

		}
		response.setDetailedTransactionNos(detailedTransactionNos);
		response.setNotDetailTransactionNos(notDetailTransactionNos);
		return response;
	}


	/**
	 *  测试算手续费的
	 * @param customerCode 客户编码
	 * @param transactionNosParam 交易单号，逗号隔开
	 */
	@Logable(businessTag = "test-calcfee-controller")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerCode", value = "客户编码", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "amount", value = "金额", required = true, dataType = "Long", paramType = "query"),
			@ApiImplicitParam(name = "payMethod", value = "支付方法", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "businessCode", value = "业务", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "transactionType", value = "类型,如ZF,FZ", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "trandateyyyyMdd", value = "日期,主要是用于算是否节假日", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "bankIcon", value = "类型,如ZF,FZ", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "splitModel", value = "类型,如ZF,FZ", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "cardType", value = "借贷记类型,D,C", required = false, dataType = "String", paramType = "query")})
	@ApiOperation(value = "测试计费", notes = "测试计费", httpMethod = "POST")
	@Exceptionable
	@PostMapping("/testCalcProcefure")
	public ProcedureFeeResult testCalcProcefure(@RequestParam(required = true)String customerCode,
										   			@RequestParam(required = true)Long amount,
												   @RequestParam(required = false)String payMethod,
												   @RequestParam(required = false)String businessCode,
												   @RequestParam(required = false)String transactionType,
												   @RequestParam(required = false)String trandateyyyyMdd,
												   @RequestParam(required = false)String bankIcon,
												   @RequestParam(required = false)String splitModel,
												   @RequestParam(required = false)String cardType) {
		ProcedureFeeResult result = null;
		try{
			result = transactionService.calcProcedure(customerCode, amount, payMethod,
					businessCode,transactionType, trandateyyyyMdd, bankIcon, splitModel, cardType);
		}catch (Exception e){
			e.printStackTrace();
		}

		return result;
	}

	/**
	 *
	 */
	@Logable(businessTag = "adjustDividedDividing")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerCode", value = "客户编码", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "outTradeNo", value = "客户订单号", required = true, dataType = "String", paramType = "query")})
	@ApiOperation(value = "校准拆单分账的已分正在分", notes = "校准拆单分账的已分正在分", httpMethod = "POST")
	@Exceptionable
	@PostMapping("/adjustDividedDividing")
	public void adjustDividedDividing(@RequestParam(required = true)String customerCode,
												@RequestParam(required = true)String outTradeNo) {
		splitPayService.adjustDividedDividing(outTradeNo, customerCode);
	}

	@Logable(businessTag = "testSelectByThree")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerCode", value = "客户编码", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "outTradeNo", value = "客户订单号", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "outSplitTradeNo", value = "客户拆单订单号", required = false, dataType = "String", paramType = "query")})
	@ApiOperation(value = "校准拆单分账的已分正在分", notes = "校准拆单分账的已分正在分", httpMethod = "POST")
	@Exceptionable
	@PostMapping("/testSelectByThree")
	public CommonOuterResponse<TxsSplitOrder> testSelectByThree(@RequestParam(required = true)String customerCode,
									  @RequestParam(required = true)String outTradeNo,
									@RequestParam(required = false)String outSplitTradeNo) {

		TxsSplitOrder txsSplitOrder = null;
		CommonOuterResponse response = new CommonOuterResponse();
		try{
			txsSplitOrder = splitPayService.selectByThree(outTradeNo, customerCode, outSplitTradeNo);
			response.setData(txsSplitOrder);
			return response;
		}catch (Exception e){
			e.printStackTrace();
			response.setReturnCode("fail");
			response.setReturnMsg(e.getMessage());
			return response;
		}

	}

	@Logable(businessTag = "trigManualSpitForMulti")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerCode", value = "客户编码", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "outTradeNo", value = "客户订单号", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "searchDaysSpecial", value = "找30天往前推几天的数据", required = false, dataType = "Integer", paramType = "query")})
	@ApiOperation(value = "模拟手工一把触发拆单分账的.指定的天数不能超过7天不然没效果,如若不传使用git配置", notes = "模拟手工一把触发拆单分账的.指定的天数不能超过7天不然没效果.如若不传使用git配置" +
			"不指天数时,不会检查是否到达30天", httpMethod = "POST")
	@Exceptionable
	@PostMapping("/trigManualSpitForMulti")
	public CommonOuterResponse<TxsSplitOrder> trigManualSpitForMulti(@RequestParam(required = false)String customerCode,
																@RequestParam(required = false)String outTradeNo,
																@RequestParam(required = false)Integer searchDaysSpecial) {

		CommonOuterResponse response = new CommonOuterResponse();
		try{
			autoSplitService.processAutoSpecial(outTradeNo, customerCode, searchDaysSpecial);
		}catch(Exception e){
			commonService.logException(e);
			e.printStackTrace();
			return CommonOuterResponse.fail(e.getMessage(),e.getMessage());
		}
		return response;
	}

	/**
	 * 修改拆单分账的分账结算周期
	 *
	 * @param
	 * @param
	 * @return
	 */
	@PostMapping("/ChangeSplitOrderSettCycle")
	@Logable(businessTag = "ChangeSplitOrderSettCycle-controller")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "修改拆单分账的分账结算周期", notes = "修改拆单分账的分账结算周期", httpMethod = "POST")
	@ResponseBody
	public UpdateCdSettCycleResponse ChangeSplitOrderSettCycle(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) @Valid UpdateCdSettCycleRequest request) {
		UpdateCdSettCycleResponse response = new UpdateCdSettCycleResponse();
		try {
			transactionService.checkCustomerCodeAndHead(customerCodeHead, request.getCustomerCode());
			response = splitPayService.updateCdSettCycle(request);
		} catch (Exception e) {
			transactionService.logException(e);
			returnCodeUtil.buildResponse(response, e, TxsCode.FAIL.code);
		}
		response.setNonceStr(UUIDUtils.uuid());
		if(StringUtils.isEmpty(response.getOutSplitTradeNo())){
			response.setOutSplitTradeNo(request.getOutSplitTradeNo());
		}
		if(StringUtils.isEmpty(response.getOutTradeNo())){
			response.setOutTradeNo(request.getOutTradeNo());
		}
		return response;
	}

}
