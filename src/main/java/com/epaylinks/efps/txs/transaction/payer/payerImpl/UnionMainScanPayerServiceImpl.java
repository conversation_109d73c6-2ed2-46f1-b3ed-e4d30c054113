package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.UnionMainScanTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;

@Service("UnionMainScan")
public class UnionMainScanPayerServiceImpl implements PayerService {

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		UnionMainScanTrader unionMainScanTrader = new UnionMainScanTrader();
		unionMainScanTrader.setType(TraderType.UnionMainScan);
		return unionMainScanTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		Payer payer = new Payer();
		payer.setPayerType(TraderType.UnionMainScan.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
