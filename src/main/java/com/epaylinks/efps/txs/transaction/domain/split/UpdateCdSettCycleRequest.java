package com.epaylinks.efps.txs.transaction.domain.split;

import com.alibaba.fastjson.JSONObject;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
public class UpdateCdSettCycleRequest {

    private String version;

    @NotBlank
    private String customerCode;

    @NotBlank
    private String outTradeNo;

    @NotBlank
    private String outSplitTradeNo;

    @NotNull
    private List<SplitInfo> splitInfoList;

    @NotBlank
    @Size(max = 32, message = "最长32个字符")
    private String nonceStr;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutSplitTradeNo() {
        return outSplitTradeNo;
    }

    public void setOutSplitTradeNo(String outSplitTradeNo) {
        this.outSplitTradeNo = outSplitTradeNo;
    }

    public List<SplitInfo> getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(List<SplitInfo> splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
