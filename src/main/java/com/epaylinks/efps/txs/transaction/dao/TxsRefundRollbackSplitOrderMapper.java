package com.epaylinks.efps.txs.transaction.dao;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.txs.transaction.model.TxsRefundRollbackSplitOrder;

@Mapper
public interface TxsRefundRollbackSplitOrderMapper {
    int deleteByPrimaryKey(String transactionNo);

    int insert(TxsRefundRollbackSplitOrder record);

    int insertSelective(TxsRefundRollbackSplitOrder record);

    TxsRefundRollbackSplitOrder selectByPrimaryKey(String transactionNo);

    int updateByPrimaryKeySelective(TxsRefundRollbackSplitOrder record);

    int updateByPrimaryKey(TxsRefundRollbackSplitOrder record);
}