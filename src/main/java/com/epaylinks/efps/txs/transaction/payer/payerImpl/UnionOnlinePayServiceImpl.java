package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.UnionOnlineTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;
@Service("UnionOnline")
public class UnionOnlinePayServiceImpl implements PayerService {

	@Override
	public Trader getPayer(Map<String, Object> map) {
		UnionOnlineTrader unionOnlineTrader = new UnionOnlineTrader();
		unionOnlineTrader.setType(TraderType.UnionOnline);
		return unionOnlineTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO Auto-generated method stub
		Payer payer = new Payer();
		payer.setPayerType(TraderType.UnionOnline.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
