package com.epaylinks.efps.txs.transaction.service;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.Constants.SettRuleCode;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.DateUtils.Field;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.service.PasService;
import com.epaylinks.efps.txs.transaction.dao.CumBusinessMapper;
import com.epaylinks.efps.txs.transaction.dao.PasHolidayMapper;
import com.epaylinks.efps.txs.transaction.model.CumBusiness;
import com.epaylinks.efps.txs.transaction.model.PasHoliday;

@Service
public class ProcedureAisistService {

	@Autowired
	private CommonService commonService;
	
	@Autowired
	private CumBusinessMapper cumBusinessMapper;
	
	@Autowired
	private PasHolidayMapper pasHolidayMapper;
	
	/*
	 * 用于判断一笔交易是否要收取D1节假上浮费用
	 * 理论上,所有计费的地方,都可以调此方法和应调用此方法
	 */
	@Logable(businessTag = "judgeNeedD1TranDoHolidayCharge")
	Boolean judgeNeedD1TranDoHolidayCharge(String businessCode, String settleCycleCode, String yyyyMMdd) {
		
		/*
		 * 检查入参业务是否为空
		 */
		if(StringUtils.isEmpty(businessCode) ) {
			commonService.payLog("judgeNeedD1TranDoHolidayCharge: empty param businessCode Exception");
			throw new AppException(TxsCode.CHECK_FLOAT_CHARGE_ERROR.code, 
					TxsCode.CHECK_FLOAT_CHARGE_ERROR.message);
		}
		
		/*
		 * 检查业务所属大类是否为收单或线下类 (先检查业务,有些业务是没有结算周期的)
		 */
		CumBusiness cumBusiness = cumBusinessMapper.selectByBusinessCode(businessCode);
		if (null == cumBusiness) {
			commonService.payLog("judgeNeedD1TranDoHolidayCharge: got null cumBusiness Exception");
			throw new AppException(TxsCode.CHECK_FLOAT_CHARGE_ERROR.code, 
					TxsCode.CHECK_FLOAT_CHARGE_ERROR.message);
		}
		
		String businessCategory = cumBusiness.getBusinessCategory();
		
		if (!Constants.BusinessCategory.EFPS_BASIC_PAY_SERVICE.code.equals(businessCategory)
				&&!Constants.BusinessCategory.EFPS_OFFLINE_SERVICE.code.equals(businessCategory)) {
			return false;
		}
		
		/*
		 * 如果是以上两种业务大类下的业务,则必定有结算周期的
		 */
		if(StringUtils.isEmpty(settleCycleCode) 
				|| StringUtils.isEmpty(yyyyMMdd)) {
			commonService.payLog("judgeNeedD1TranDoHolidayCharge: empty param Exception");
			throw new AppException(TxsCode.CHECK_FLOAT_CHARGE_ERROR.code, 
					TxsCode.CHECK_FLOAT_CHARGE_ERROR.message);
		}
		
		/*
		 * 检查结算周期是否为D1
		 */
		if (!SettRuleCode.D1.code.equals(settleCycleCode)) {
			return false;
		}
		
		
		/*
		 * 检查日期是否为节假日
		 */
		String dateStr = yyyyMMdd;
		if (dateStr.length() > 8) {
			dateStr = dateStr.substring(0, 8);
		}
		
		//2020-09-08,改为交易日期的明天是否节假日. 如果明天不是节假,则明天清算,不收上浮. 这种情况下,周五上浮、周六上浮,周日不上浮;改之前是周六周日上浮
		Date todayDate = DateUtils.parseDate(dateStr, "yyyyMMdd");
		Date tomorrowDate = DateUtils.add(todayDate, Field.DATE, 1);
		String tomorrowDateStr = DateUtils.formatDate(tomorrowDate, "yyyyMMdd");
		
		
		PasHoliday pasHoliday = pasHolidayMapper.selectByDate(tomorrowDateStr);
		if (null != pasHoliday) {
			return true;
		}
		
		return false;
	}

	public void checkProcedureBiggerThanAmount(Long amount, Long procedureFee, String procedureCustomerCode){
		if(StringUtils.isEmpty(procedureCustomerCode) && (procedureFee.longValue() > amount.longValue())){
			throw new AppException(TxsCode.AMOUNT_MORETHAN_PROCEDUREFEE.code);
		}
	}
}
