package com.epaylinks.efps.txs.transaction.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退款交易订单表
 * <AUTHOR>
 *
 */
public class TxsRefundTradeOrder {
    private Long id;
    /**
     * 交易订单号
     */
    private String transactionNo;

    /**
     * 源商户订单号
     */
    private String outTradeNo;

    /**
     * 商户退款订单号
     */
    private String outRefundNo;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 终端号
     */
    private String terminalNo;

    /**
     * 退款原因
     */
    private String refundDesc;

    /**
     * 订单金额
     */
    private Long totalFee;

    /**
     * 退款金额
     */
    private Long refundFee;

    /**
     * 退款币种
     */
    private String refundCurrency;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 提现结果通知地址
     */
    private String notifyUrl;

    /**
     * 商家提现结果地址
     */
    private String redirectUrl;
    
    /**
     * 订单创建时间
     */
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /**
     * 支付状态
     */
    private String payState;
    
    /**
     * 查询条件-交易开始时间
     */
    private Date beginTime;
    
    /**
     * 查询条件-交易结束时间
     */
    private Date endTime;
    
    private String customerName;
    
    private String businessInstId;
    
    private String businessCode;
    
    private String errorCode;
    
    private Date updateTime;
    
    private Long procedureFee;
    
    private Long backpayProcedurefee;
    
    private String sourceType;
    
    private String txsTransactionNo;//交易订单号
    
    /**
     * 终端名称
     */
    private String terminalName;
    
	public String getTerminalName() {
		return terminalName;
	}

	public void setTerminalName(String terminalName) {
		this.terminalName = terminalName;
	}
    
	public String getTxsTransactionNo() {
		return txsTransactionNo;
	}

	public void setTxsTransactionNo(String txsTransactionNo) {
		this.txsTransactionNo = txsTransactionNo;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTransactionNo() {
		return transactionNo;
	}

	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getOutRefundNo() {
		return outRefundNo;
	}

	public void setOutRefundNo(String outRefundNo) {
		this.outRefundNo = outRefundNo;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getTerminalNo() {
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo) {
		this.terminalNo = terminalNo;
	}

	public String getRefundDesc() {
		return refundDesc;
	}

	public void setRefundDesc(String refundDesc) {
		this.refundDesc = refundDesc;
	}

	public Long getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Long totalFee) {
		this.totalFee = totalFee;
	}

	public Long getRefundFee() {
		return refundFee;
	}

	public void setRefundFee(Long refundFee) {
		this.refundFee = refundFee;
	}

	public String getRefundCurrency() {
		return refundCurrency;
	}

	public void setRefundCurrency(String refundCurrency) {
		this.refundCurrency = refundCurrency;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getPayState() {
		return payState;
	}

	public void setPayState(String payState) {
		this.payState = payState;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getBusinessInstId() {
		return businessInstId;
	}

	public void setBusinessInstId(String businessInstId) {
		this.businessInstId = businessInstId;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public Long getBackpayProcedurefee() {
		return backpayProcedurefee;
	}

	public void setBackpayProcedurefee(Long backpayProcedurefee) {
		this.backpayProcedurefee = backpayProcedurefee;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}
}