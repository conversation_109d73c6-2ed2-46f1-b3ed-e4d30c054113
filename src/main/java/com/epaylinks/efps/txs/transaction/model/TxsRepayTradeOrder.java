package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;

public class TxsRepayTradeOrder {
    private Long id;
    private Long orderId;
    @FieldAnnotation(fieldName="易票联订单号")
	private String transactionNo;
    @FieldAnnotation(fieldName="商户订单号")
	private String outTradeNo;
    @FieldAnnotation(fieldName="客户编码")
	private String customerCode;
	@FieldAnnotation(fieldName="支付方式",dictionaries="0:账号支付,1:微信公众号支付,2:个人网银_贷记卡,3:个人网银_借记卡,"
    		+ "4:支付宝生活号支付,5:代付,6:微信扫码支付,7:支付宝扫码支付,8:快捷支付,9:微信app支付,10:微信H5支付,13:微信被扫,14:支付宝被扫,20:手机银联支付,24:银联扫码主扫,25:银联扫码被扫")
	private String payMethod;
	private String transactionType;
	@FieldAnnotation(fieldName="交易金额(元)" , yuanHandler = true)
	private Long amount;
	private String currencyType;
	private Long procedureFee;
	private String payerType;
	private String payerId;
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	private String state;
	@FieldAnnotation(fieldName="交易状态", dictionaries="0:非重复支付订单,1:重复支付订单,2:重复支付处理中,3:重复支付处理完成")
	private String repayState;
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date updateTime;
	private String errorCode;
	private String errorMsg;
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date endTime;
	private String remark;
	private String businessInstId;
	private String businessCode;
	private String businessName;
	private String bankName;
    
    private Date settCycleStartTime;
    private Date settCycleEndTime;
    /**
     * 退款状态
     */
    private String refundState;
    /**
     * 退款终态时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date refundTime;
    /**
     * 退款单号
     */
    private String refundTransactionNo;
    /**
     * 处理状态,00-已退款，01-未处理，02-处理失败
     */
    @FieldAnnotation(fieldName="处理状态",dictionaries="00:已退款,01:未处理,02:处理失败")
    private String oprState;
    /**
     * 处理时间
     */
    @FieldAnnotation(fieldName="处理时间")
    private String oprTime;
    
	public String getOprState() {
		return oprState;
	}
	public void setOprState(String oprState) {
		this.oprState = oprState;
	}
	public String getOprTime() {
		return oprTime;
	}
	public void setOprTime(String oprTime) {
		this.oprTime = oprTime;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	public String getTransactionType() {
		return transactionType;
	}
	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public String getCurrencyType() {
		return currencyType;
	}
	public void setCurrencyType(String currencyType) {
		this.currencyType = currencyType;
	}
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}
	public String getPayerType() {
		return payerType;
	}
	public void setPayerType(String payerType) {
		this.payerType = payerType;
	}
	public String getPayerId() {
		return payerId;
	}
	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getRepayState() {
		return repayState;
	}
	public void setRepayState(String repayState) {
		this.repayState = repayState;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getBusinessInstId() {
		return businessInstId;
	}
	public void setBusinessInstId(String businessInstId) {
		this.businessInstId = businessInstId;
	}
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public Date getSettCycleStartTime() {
		return settCycleStartTime;
	}
	public void setSettCycleStartTime(Date settCycleStartTime) {
		this.settCycleStartTime = settCycleStartTime;
	}
	public Date getSettCycleEndTime() {
		return settCycleEndTime;
	}
	public void setSettCycleEndTime(Date settCycleEndTime) {
		this.settCycleEndTime = settCycleEndTime;
	}
	public String getRefundState() {
		return refundState;
	}
	public void setRefundState(String refundState) {
		this.refundState = refundState;
	}
	public Date getRefundTime() {
		return refundTime;
	}
	public void setRefundTime(Date refundTime) {
		this.refundTime = refundTime;
	}
	public String getRefundTransactionNo() {
		return refundTransactionNo;
	}
	public void setRefundTransactionNo(String refundTransactionNo) {
		this.refundTransactionNo = refundTransactionNo;
	}
	public String getBusinessName() {
		return businessName;
	}
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getErrorMsg() {
		return errorMsg;
	}
	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	
}