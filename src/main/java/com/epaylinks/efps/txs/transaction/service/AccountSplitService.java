package com.epaylinks.efps.txs.transaction.service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.systemcode.SystemCodeService;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.txs.constants.FzCode;
import com.epaylinks.efps.txs.service.PospService;
import com.epaylinks.efps.txs.service.dto.acc.DirectVoucherRequest;
import com.epaylinks.efps.txs.service.dto.acc.DirectVoucherResponse;
import com.epaylinks.efps.txs.transaction.dao.*;
import com.epaylinks.efps.txs.transaction.domain.acc.AccountVoucher;
import com.epaylinks.efps.txs.transaction.domain.global.GlobalSplitRequest;
import com.epaylinks.efps.txs.transaction.domain.global.GlobalSplitResponse;
import com.epaylinks.efps.txs.transaction.domain.split.*;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.AccountVoucherRes;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.business.acc.AccountQueryResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.domain.CumBusinessParamInst;
import com.epaylinks.efps.common.business.domain.CustomerBusinessInfo;
import com.epaylinks.efps.common.business.domain.InsidePayCustomer;
import com.epaylinks.efps.common.business.domain.InsidePayRequest;
import com.epaylinks.efps.common.business.pay.request.PayMethod;
import com.epaylinks.efps.common.business.pay.request.trader.EpaylinksTrader;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceCategory;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.service.AccService;
import com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo;
import com.epaylinks.efps.txs.transaction.model.TxsPayRequest;
import com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import com.epaylinks.efps.txs.transaction.model.TxsRefundPreOrder;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord;
import com.epaylinks.efps.txs.transaction.model.TxsSplitOrder;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRecord;
import com.epaylinks.efps.txs.util.ReflectUtils;

@Service
public class AccountSplitService {

    @Autowired
    private AccountSplitService self;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private SplitPayService splitPayService;
    @Autowired
    private ReturnCodeUtil returnCodeUtil;

    @Autowired
    private TxsPreOrderMapper txsPreOrderMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private TxsPayTradeOrderMapper txsPayTradeOrderMapper;

    @Autowired
    private TxsSplitOrderMapper txsSplitOrderMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private AccService accService;

    private final String format = "yyyyMMddHHmmss";

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    @Autowired
    private OrderCommodityInfoMapper orderCommodityInfoMapper;

    @Autowired
    private TransactionQueryService transactionQueryService;

    @Autowired
    private TxsRefundPreOrderMapper txsRefundPreOrderMapper;

    @Autowired
    private TxsRefundSplitRecordMapper txsRefundSplitRecordMapper;

    @Autowired
    private TxsSplitRecordMapper txsSplitRecordMapper;
    @Autowired
    private AuthInfoService authInfoService;
    @Autowired
    private SystemCodeService systemCodeService;
    @Autowired
    private ProcedureAisistService  procedureAisistService;

    @Autowired
    private PospService pospService;

    //配置的商户可以退大于订单金额
    @Value("${accountSplit.refundSuperCustomerCode:***************}")
    private String refundSuperCustomerCode;

    @Value("${fz.systemId}")
    private String systemId;

    @Value("${TKTransaction}")
    private String TKTransaction;
    @Value("${multiSplitPlatCustomerCode:****************}")
    private String multiSplitPlatCustomerCode;

    @Logable(businessTag = "AccountSplitService.accountSplit", outputArgs = false)
    public AccountSplitResponse accountSplit(AccountSplitRequest accountSplitRequest, AccountSplitResponse accountSplitResponse) {
        List<CommodityInfo> commodityInfoList = accountSplitRequest.getCommodityInfoList();
        TxsPayRequest request = new TxsPayRequest();
        request.setNeedSplit(true);
        request.setSplitInfoList(accountSplitRequest.getSplitInfoList());
        request.setCustomerCode(accountSplitRequest.getCustomerCode());
        request.setOutTradeNo(accountSplitRequest.getOutTradeNo());
        request.setCommodityAmount(accountSplitRequest.getCommodityAmount());
        request.setCommodityInfoList(JSONObject.toJSONString(commodityInfoList));
        request.setNotifyUrl(accountSplitRequest.getNotifyUrl());
        request.setAttachData(accountSplitRequest.getAttachData());
//        request.setRemark(accountSplitRequest.getAttachData());
        request.setPayMethod(PayMethod.ACCOUNT_SPLIT.code);//账户分账
        request.setCurrencyType(TxsConstants.CurrencyType.CNY.code);
        request.setTransactionType(Constants.transactionType.ZHFZTRANSACTION.code);
        request.setRemark(accountSplitRequest.getRemark() == null ? accountSplitRequest.getAttachData() : accountSplitRequest.getRemark());

        if (!checkAccountSplitParam(request, accountSplitRequest, accountSplitResponse)) {
            return accountSplitResponse;
        }

        self.judgeProcedureCustomercode(request);

        TxsPreOrder txsPreOrderCheck = transactionService.checkCumStatusAndGetPreOrder(request.getOutTradeNo(), request.getCustomerCode());
        // 当前时间
        Date now = new Date();
        request.setCreateTime(now);
        request.setTransactionStartTime(now);

        if (txsPreOrderCheck != null) {
            throw new AppException(TxsCode.REPEAT_ORDER.code, TxsCode.REPEAT_ORDER.message);
        }

        CustomerBusinessInstance businessInst = self.accountSplitProcedure(request, Constants.EfpsAccountService.ACCOUNT_SPLIT.code);

        List<CumBusinessParamInst> businessParamInst = cumCacheService.getCustomerBusinessParamInst(businessInst.getBusinessExamId());
        boolean isExternalOrder = true;//默认支持外部订单
        for (CumBusinessParamInst bpi : businessParamInst) {

            if (Constants.BusinessParamCode.EXTERNAL_ORDER.code.equalsIgnoreCase(bpi.getCode())) {
                if ("2".equals(bpi.getValue())) {
                    isExternalOrder = false;
                }
            }
        }

        String splitModel = null;
        if (!isExternalOrder) {//isExternalOrder为false，只支持内部订单时，需按普通分账校验分账比例，否则splitModel传null不校验分账比例
            splitModel = TxsConstants.SplitModel.COMMON.code;
        }

        TxsPreOrder txsPreOrder = self.createAndInsertOrder(request, commodityInfoList, isExternalOrder);
//        TxsPreOrder txsPreOrder = transactionService.createAndInsertPreOrder(request);
        EpaylinksTrader epaylinksTrader = new EpaylinksTrader();
        epaylinksTrader.setCustomerCode(request.getCustomerCode());
        epaylinksTrader.setBusinessCode(businessInst.getBusinessCode());
        epaylinksTrader.setBusinessInstanceId(businessInst.getBusinessExamId());
        request.setPayer(epaylinksTrader);

        TxsPayTradeOrder txsPayTradeOrder = transactionService.createAndInsertTxsPayTradeOrder(request, businessInst);
        transactionService.updateTxsPreOrderState(txsPayTradeOrder, txsPreOrder);


        List<SplitInfo> splitInfoList = request.getSplitInfoList();
        TxsSplitOrder txsSplitOrder = null;
        if (splitInfoList != null && splitInfoList.size() > 0) {
            txsSplitOrder = doAccountSplit(txsPreOrder, splitInfoList, businessInst, request.getIsAccountSplitFeeAuth());
        } else {
            txsSplitOrder = self.createTxsSplitOrder(txsPreOrder.getAmount(), txsPreOrder.getProcedureFee(), now, txsPreOrder.getCustomerCode(),
                    txsPreOrder.getOutTradeNo(), txsPreOrder.getNotifyUrl(), splitInfoList, txsPreOrder.getAmount(), businessInst, null, txsPreOrder.getProcedureCustomercode(),
                    request.getIsAccountSplitFeeAuth());
            txsSplitOrderMapper.insertSelective(txsSplitOrder);

            InsidePayRequest insidePayRequest = self.createInsidePayRequest(txsSplitOrder, null);
            splitPayService.executePreSplit(insidePayRequest, now, txsSplitOrder);

        }

        //账户分账成功
        accountSplitResponse.setReturnCode(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.code);
        accountSplitResponse.setReturnMsg(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.comment);
        accountSplitResponse.setState(txsSplitOrder.getState());

        accountSplitResponse.setTransactionNo(txsPayTradeOrder.getTransactionNo());
        accountSplitResponse.setSplitTransactionNo(txsSplitOrder.getTransactionNo());
        return accountSplitResponse;
    }

    @Logable(businessTag = "genSplitRecords", outputArgs = false)
    public List<TxsSplitRecord> genSplitRecords(AccountSplitResponse accountSplitResponse,
                                                TxsSplitOrder txsSplitOrder, TxsPreOrder txsPreOrder,
                                                List<SplitInfo> splitInfoList, Date now){

        Long negativeSplitAmount = 0L;
        Long positiveSplitAmount = 0L;
        int countNegative = 0;

        List<TxsSplitRecord> txsSplitRecordList;
        for (SplitInfo splitInfo : splitInfoList) {
            if (splitInfo.getAmount() < 0) {

                if (!checkMultiSplitSource(txsPreOrder.getCustomerCode())) {//不允许多主体出金的话，则报错
                    throw new AppException(TxsCode.PARAM_ERROR.getCode(), TxsCode.PARAM_ERROR.getMessage() + "，分账金额不能小于0");
                }
                negativeSplitAmount += Math.abs(splitInfo.getAmount());
                countNegative++;
            } else {
                positiveSplitAmount += splitInfo.getAmount();
            }
        }
        Long diffProcedure = 0L;
        if (negativeSplitAmount > 0) {//有负分账才另外算手续费
            TxsPayRequest request = new TxsPayRequest();
            request.setNeedSplit(true);
            request.setCustomerCode(txsSplitOrder.getCustomerCode());
            request.setOutTradeNo(txsSplitOrder.getOutTradeNo());
            request.setAmount(positiveSplitAmount);

            request.setPayMethod(PayMethod.ACCOUNT_SPLIT.code);//账户分账
            request.setCurrencyType(TxsConstants.CurrencyType.CNY.code);
            request.setTransactionType(Constants.transactionType.ZHFZTRANSACTION.code);
            request.setRequestSrc(TxsConstants.RequestSrc.INNER.code);//设置这个，不校验业务最大最小金额等

            self.accountSplitProcedure(request, Constants.EfpsAccountService.ACCOUNT_SPLIT.code);

            diffProcedure = request.getProcedureFee() - txsSplitOrder.getProcedureFee();
        }


        txsSplitRecordList = new ArrayList<>();
        long amountCheck = 0;
        Long oriAmount = txsSplitOrder.getAmount();
        Long itemProcedureFeeAdd = 0L;
        for (SplitInfo splitInfo : splitInfoList) {
            amountCheck += splitInfo.getAmount();

            TxsSplitRecord txsSplitRecord = self.createSplitRecordConfirm(txsPreOrder, splitInfo, txsSplitOrder, now, 0L);

            if (diffProcedure > 0 && txsSplitRecord.getAmount() < 0) {
                Long itemProcedure = 0L;
                countNegative--;
                if (countNegative == 0) {//最后一笔负数
                    itemProcedure = diffProcedure - itemProcedureFeeAdd;
                } else {
                    itemProcedure = diffProcedure * txsSplitRecord.getAmount() / negativeSplitAmount;

                    itemProcedureFeeAdd += itemProcedure;
                }

                txsSplitRecord.setProcedurefee(itemProcedure);
            }

            txsSplitRecordList.add(txsSplitRecord);
        }

        if (!txsSplitOrder.getAmount().equals(amountCheck)) {
            throw new AppException(TxsCode.AMOUNT_ATYPISM.code, TxsCode.AMOUNT_ATYPISM.message);
        }

        return txsSplitRecordList;
    }

    @Logable(businessTag = "AccountSplitService.accountSplit")
    public AccountSplitResponse accountSplitConfirm(AccountSplitConfirmRequest splitConfirmRequest) {
        AccountSplitResponse accountSplitResponse = new AccountSplitResponse();

        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(splitConfirmRequest.getOutTradeNo(), splitConfirmRequest.getCustomerCode(), null);
        if (txsSplitOrder == null) {
            throw new AppException(TxsCode.SPLITORDER_NOTFOUND.code, TxsCode.SPLITORDER_NOTFOUND.message);
        }
        if (!TxsConstants.SplitOrderState.PRE_SUCCESS.code.equals(txsSplitOrder.getState())) {
            throw new AppException(FzCode.ORDER_NOT_SUPPORT.code, FzCode.ORDER_NOT_SUPPORT.message);
        }
        List<TxsSplitRecord> splitRecords = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
        if (splitRecords != null && splitRecords.size() > 0 ) {
            //产品积压工作项 11653:【PJ24】账户分账确认失败支持再次确认或取消-
            commonService.payLog(txsSplitOrder.getTransactionNo()+"已有splitRecords");
            //throw new AppException(TxsCode.REPEAT_SPLIT.code, TxsCode.REPEAT_SPLIT.message);
        }

        boolean bSuccess = self.isAccountSplitConfirmSuccess(txsSplitOrder.getTransactionNo());
        if(bSuccess){
            throw new AppException(TxsCode.ALREADY_ZHFZ_CONFIRMED.code, TxsCode.ALREADY_ZHFZ_CONFIRMED.message);
        }

        List<SplitInfo> splitInfoList = splitConfirmRequest.getSplitInfoList();

        TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(splitConfirmRequest.getOutTradeNo(), splitConfirmRequest.getCustomerCode());

        Date now = new Date();
        String splitState = null;
        // 所有分账记录
        List<TxsSplitRecord> txsSplitRecordList = null;
        if (TxsConstants.ComfirmType.confirm_split.code.equals(splitConfirmRequest.getComfirmType())) {

            if(txsSplitRecordMapper.fenBuFenZhangHasRecords(txsSplitOrder.getTransactionNo())){
                commonService.payLog(txsSplitOrder.getTransactionNo()+"txsSplitRecord已有记录!比较传参的splitInfo是否相等");
                List<SplitInfo> splitInfoListOri = JSONObject.parseArray(txsSplitOrder.getSplitInfoList(), SplitInfo.class);
                boolean b = self.compareList(splitInfoListOri, splitInfoList);//org.apache.commons.collections4.CollectionUtils.isEqualCollection(splitInfoList, splitInfoListOri);
                if(!b){
                    commonService.payLog(txsSplitOrder.getTransactionNo()+"不相等,这次:"+JSONObject.toJSONString(splitInfoList));
                    throw new AppException(TxsCode.ZHFZ_CONFIRTM_SPLIT_INFO_DIFF.code, TxsCode.ZHFZ_CONFIRTM_SPLIT_INFO_DIFF.message);
                }
                txsSplitRecordList = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
            }else{
                txsSplitRecordList = self.genSplitRecords(accountSplitResponse, txsSplitOrder, txsPreOrder,splitInfoList, now);
            }


            splitState = TxsConstants.SplitOrderState.SUCCESS.code;

            splitPayService.dealSplitRecords(txsSplitOrder, txsSplitRecordList, splitState, splitInfoList);
            InsidePayRequest insidePayRequest = self.createConfirmInsidePayRequest(txsSplitOrder, txsSplitRecordList);
            try{
                splitPayService.executeConfirmSplit(insidePayRequest, now, txsSplitOrder, txsSplitRecordList);
            } catch (Exception e) {
                // 产品积压工作项 11653:【PJ24】账户分账确认失败支持再次确认或取消
                commonService.logException(e);
                bSuccess = self.isAccountSplitConfirmSuccess(txsSplitOrder.getTransactionNo());
                if(!bSuccess){
                    //这里00的来源码是由于上面dealSplitRecords,与撤销抢锁
                    int i = txsSplitOrderMapper.updateAccountSplit00to04(txsSplitOrder.getTransactionNo());
                    commonService.payLog(txsSplitOrder.getTransactionNo()+"分步分账异常,将状态置回预分账成功状态(04).更新条数:"+i);
                }
                throw e;
            }

        } else if (TxsConstants.ComfirmType.confirm_rollback.code.equals(splitConfirmRequest.getComfirmType())) {

            splitState = TxsConstants.SplitOrderState.REVOKE.code;
            String revokeNo = transactionService.generatorOrderNo(TransactionType.ZHFZCX.code, "payTradeNo", new Date());
            txsSplitOrder.setRevokeTransactionNo(revokeNo);
            splitPayService.dealSplitRecords(txsSplitOrder, txsSplitRecordList, splitState, splitInfoList);

            AccountVoucherRes accountVoucherRes = accService.accountVoucherRevoke(revokeNo, txsSplitOrder.getTransactionNo());
            if (Constants.SUCCESS.equals(accountVoucherRes.getResult())) {
                self.updatePreOrder(txsPreOrder, TxsConstants.PreOrderState.CANCEL.code, Constants.detailReturnCode.RETURN_SUCCESS.code, Constants.detailReturnCode.RETURN_SUCCESS.comment, revokeNo);
            } else {
                if (!StringUtils.isBlank(accountVoucherRes.getResult())) {
                    accountSplitResponse.setReturnCode(accountVoucherRes.getResult());
                    accountSplitResponse.setReturnMsg(systemCodeService.getMessage(accountVoucherRes.getResult()));
                } else {
                    accountSplitResponse.setReturnCode(Constants.detailReturnCode.RETURN_FAIL.code);
                    accountSplitResponse.setReturnMsg(Constants.detailReturnCode.RETURN_FAIL.comment);
                }

                return accountSplitResponse;

            }

        } else {
            throw new AppException(TxsCode.PARAM_ERROR.code);
        }

        //账户分账成功
        accountSplitResponse.setReturnCode(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.code);
        accountSplitResponse.setReturnMsg(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.comment);

        accountSplitResponse.setProcedureFee(txsSplitOrder.getProcedureFee());
        accountSplitResponse.setState(txsSplitOrder.getState());

        accountSplitResponse.setSplitTransactionNo(txsSplitOrder.getTransactionNo());
        accountSplitResponse.setRevokeTransactionNo(txsSplitOrder.getRevokeTransactionNo());
        accountSplitResponse.setTransactionNo(txsPreOrder.getTransactionNo());
        return accountSplitResponse;
    }

    public TxsSplitOrder doAccountSplit(TxsPreOrder txsPreOrder, List<SplitInfo> splitInfoList, CustomerBusinessInstance businessInst, String isAccountSplitAuth) {

        Long slaveAmountSum = splitPayService.checkSplitInfoAmountRight(txsPreOrder, splitInfoList);
        String splitModel = txsPreOrder.getSplitModel();

        //分账校验
        CommonOuterResponse commonOuterResponse = splitPayService.checkSplitDetail(splitInfoList, txsPreOrder.getCustomerCode(), txsPreOrder.getCustomerCode(), txsPreOrder.getAmount(), null, splitModel);
        if (!commonOuterResponse.isSuccess()) {
            throw new AppException(commonOuterResponse.getReturnCode(), commonOuterResponse.getReturnMsg());
        }

        // 查询数据库是否有分账订单数据
        TxsSplitOrder txsSplitOrderSql = txsSplitOrderMapper.selectByThree(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode(), null);
        if (txsSplitOrderSql != null) {// 构造重复请求响应返回
            throw new AppException(TxsCode.REPEAT_SPLIT.code, TxsCode.REPEAT_SPLIT.message);
        }

        Date now = new Date();


        TxsSplitOrder txsSplitOrder = self.createTxsSplitOrder(txsPreOrder.getAmount(), txsPreOrder.getProcedureFee(), now, txsPreOrder.getCustomerCode(),
                txsPreOrder.getOutTradeNo(), txsPreOrder.getNotifyUrl(), splitInfoList, slaveAmountSum, businessInst, null, txsPreOrder.getProcedureCustomercode(), isAccountSplitAuth);
        txsSplitOrder.setOriBusinessCode(txsPreOrder.getBusinessCode());
        //分账收单的客户名称
        String sourceCustomerName = cumCacheService.getCustomerInfo(txsSplitOrder.getCustomerCode(), txsSplitOrder.getCustomerCode(), "1").getName();
        //cumService.getCustomerInfo(txsSplitOrder.getCustomerCode(), txsSplitOrder.getCustomerCode(), "1").getName();
        Long negativeSplitAmount = 0L;
        Long positiveSplitAmount = 0L;
        int countNegative = 0;

        for (SplitInfo splitInfo : splitInfoList) {
            if (splitInfo.getAmount() < 0) {
                negativeSplitAmount += Math.abs(splitInfo.getAmount());
                countNegative++;
            } else {
                positiveSplitAmount += splitInfo.getAmount();
            }
        }
        Long diffProcedure = 0L;
        if (negativeSplitAmount > 0) {//有负分账才另外算手续费
            TxsPayRequest request = new TxsPayRequest();
            request.setNeedSplit(true);
            request.setCustomerCode(txsSplitOrder.getCustomerCode());
            request.setOutTradeNo(txsSplitOrder.getOutTradeNo());
            request.setAmount(positiveSplitAmount);

            request.setPayMethod(PayMethod.ACCOUNT_SPLIT.code);//账户分账
            request.setCurrencyType(TxsConstants.CurrencyType.CNY.code);
            request.setTransactionType(Constants.transactionType.ZHFZTRANSACTION.code);
            request.setRequestSrc(TxsConstants.RequestSrc.INNER.code);//设置这个，不校验业务最大最小金额等

            self.accountSplitProcedure(request, Constants.EfpsAccountService.ACCOUNT_SPLIT.code);

            diffProcedure = request.getProcedureFee() - txsSplitOrder.getProcedureFee();
        }

        // 所有分账记录
        List<TxsSplitRecord> txsSplitRecordList = new ArrayList<>();
        Long itemProcedureFeeAdd = 0L;
        for (SplitInfo splitInfo : splitInfoList) {
            TxsSplitRecord txsSplitRecord = self.createSplitRecord(businessInst, 0L, splitInfo, txsSplitOrder, now, sourceCustomerName);
            txsSplitRecord.setPayTransactionNo(txsPreOrder.getTransactionNo());

            if (diffProcedure > 0 && txsSplitRecord.getAmount() < 0) {
                Long itemProcedure = 0L;
                countNegative--;
                if (countNegative == 0) {//最后一笔负数
                    itemProcedure = diffProcedure - itemProcedureFeeAdd;
                } else {
                    itemProcedure = diffProcedure * txsSplitRecord.getAmount() / negativeSplitAmount;

                    itemProcedureFeeAdd += itemProcedure;
                }

                txsSplitRecord.setProcedurefee(itemProcedure);
            }
            txsSplitRecordList.add(txsSplitRecord);
        }

        splitPayService.insertSplitOrderAndRecords(txsPreOrder, txsSplitOrder, txsSplitRecordList);

//        //更新分账记录
//        splitPayService.updateTxsSplitRecord(txsPreOrder.getOrderId());

        //拼装账户分账交易账户内转报文
        InsidePayRequest insidePayRequest = self.createInsidePayRequest(txsSplitOrder, txsSplitRecordList);
        //执行分账
        splitPayService.executeSplit(insidePayRequest, now, txsSplitOrder, txsSplitRecordList);


        return txsSplitOrder;
    }


    @Logable(businessTag = "AccountSplitService.globalSplit")
    public GlobalSplitResponse globalSplit(GlobalSplitRequest globalSplitRequest) {

        GlobalSplitResponse accountSplitResponse = new GlobalSplitResponse();
        accountSplitResponse.setNonceStr(UUIDUtils.uuid());
        accountSplitResponse.setOutTradeNo(globalSplitRequest.getOutTradeNo());
        accountSplitResponse.setCommodityAmount(globalSplitRequest.getCommodityAmount());

        List<CommodityInfo> commodityInfoList = globalSplitRequest.getCommodityInfoList();
        TxsPayRequest request = new TxsPayRequest();
        request.setNeedSplit(true);
        request.setCustomerCode(globalSplitRequest.getCustomerCode());
        request.setOutTradeNo(globalSplitRequest.getOutTradeNo());
        request.setCommodityAmount(globalSplitRequest.getCommodityAmount());
        request.setCommodityInfoList(JSONObject.toJSONString(commodityInfoList));
        request.setNotifyUrl(globalSplitRequest.getNotifyUrl());
        request.setAttachData(globalSplitRequest.getAttachData());
        request.setPayMethod(PayMethod.ACCOUNT_SPLIT.code);//账户分账
        request.setCurrencyType(TxsConstants.CurrencyType.CNY.code);
        request.setTransactionType(Constants.transactionType.ZHFZTRANSACTION.code);
        request.setReceiveCustomerCode(globalSplitRequest.getReceiveCustomerCode());
        request.setCrossPlatCustomerCode(globalSplitRequest.getCrossPlatCustomerCode());
        request.setAmount(globalSplitRequest.getCommodityAmount());

        checkGlobalSplitParam(globalSplitRequest);

        TxsPreOrder txsPreOrderCheck = transactionService.checkCumStatusAndGetPreOrder(request.getOutTradeNo(), request.getCustomerCode());
        // 当前时间
        Date now = new Date();
        request.setCreateTime(now);
        request.setTransactionStartTime(now);

        if (txsPreOrderCheck != null) {
            throw new AppException(TxsCode.REPEAT_ORDER.code, TxsCode.REPEAT_ORDER.message);
        }
        String businessCode = null;
        String authType = null;
        String authCustomerCode = globalSplitRequest.getCrossPlatCustomerCode();
        if (Constants.EfpsAccountService.GlobalCardPay.code.equals(globalSplitRequest.getBusinessType())) {
            businessCode = Constants.EfpsAccountService.GlobalCardPay.code;
            authType = Constants.AuthType.globalCardPay.code;
        } else if (Constants.EfpsAccountService.GlobalBalancePay.code.equals(globalSplitRequest.getBusinessType())) {
            businessCode = Constants.EfpsAccountService.GlobalBalancePay.code;
            authType = Constants.AuthType.globalBalancePay.code;
        } else {
            throw new AppException(TxsCode.BUSINESS_NOTFOUND.code, TxsCode.BUSINESS_NOTFOUND.message);

        }

        if (!StringUtils.isBlank(authCustomerCode)) {
            boolean authResult = authInfoService.checkAuthInfoByType(globalSplitRequest.getCustomerCode(), authCustomerCode, authType);
            if (!authResult) {
                throw new AppException(FzCode.AUTH_ERROR.code, FzCode.AUTH_ERROR.message);
            }
        }

        CustomerBusinessInstance businessInst = self.accountSplitProcedure(request, businessCode);

        List<CumBusinessParamInst> businessParamInst = cumCacheService.getCustomerBusinessParamInst(businessInst.getBusinessExamId());
        boolean isExternalOrder = true;//默认支持外部订单
        for (CumBusinessParamInst bpi : businessParamInst) {

            if (Constants.BusinessParamCode.EXTERNAL_ORDER.code.equalsIgnoreCase(bpi.getCode())) {
                if ("2".equals(bpi.getValue())) {
                    isExternalOrder = false;
                }
            }
        }

        TxsPreOrder txsPreOrder = self.createAndInsertOrder(request, commodityInfoList, isExternalOrder);
//        TxsPreOrder txsPreOrder = transactionService.createAndInsertPreOrder(request);

        List<SplitInfo> splitInfoList = new ArrayList<SplitInfo>();
        SplitInfo buildSplitInfo = new SplitInfo();

        if (TxsConstants.ProcedureTypeEnum.receiver_procedure.code.equals(globalSplitRequest.getProcedureType())) {
            buildSplitInfo.setAmount(request.getCommodityAmount() - request.getProcedureFee());
        } else {
            buildSplitInfo.setAmount(request.getCommodityAmount());
        }

        buildSplitInfo.setCustomerCode(request.getReceiveCustomerCode());
        buildSplitInfo.setIsProcedureCustomer(0);

        splitInfoList.add(buildSplitInfo);

        //List<SplitInfo> splitInfoList = splitPayService.checkSplitInfoParamIsNotNull(request.getSplitInfoList(), txsPreOrder.getSplitModel());

        String splitModel = null;


        //分账校验
        CommonOuterResponse commonOuterResponse = splitPayService.checkSplitDetailSimple(splitInfoList);
        if (!commonOuterResponse.isSuccess()) {
            throw new AppException(commonOuterResponse.getReturnCode(), commonOuterResponse.getReturnMsg());
        }

        // 查询数据库是否有分账订单数据
        TxsSplitOrder txsSplitOrderSql = txsSplitOrderMapper.selectByThree(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode(), null);
        if (txsSplitOrderSql != null) {// 构造重复请求响应返回
            throw new AppException(TxsCode.REPEAT_SPLIT.code, TxsCode.REPEAT_SPLIT.message);
        }

        EpaylinksTrader epaylinksTrader = new EpaylinksTrader();
        epaylinksTrader.setCustomerCode(request.getCustomerCode());
        epaylinksTrader.setBusinessCode(businessInst.getBusinessCode());
        epaylinksTrader.setBusinessInstanceId(businessInst.getBusinessExamId());
        request.setPayer(epaylinksTrader);

        TxsPayTradeOrder txsPayTradeOrder = transactionService.createAndInsertTxsPayTradeOrder(request, businessInst);
        transactionService.updateTxsPreOrderState(txsPayTradeOrder, txsPreOrder);


        TxsSplitOrder txsSplitOrder = self.createTxsSplitOrder(request.getAmount(), request.getProcedureFee(), now, request.getCustomerCode(),
                request.getOutTradeNo(), request.getNotifyUrl(), splitInfoList, request.getAmount(), businessInst, globalSplitRequest.getProcedureType(), null, null);
        txsSplitOrder.setCrossPlatCustomerCode(request.getCrossPlatCustomerCode());

        //分账收单的客户名称
        String sourceCustomerName = cumCacheService.getCustomerInfo(txsSplitOrder.getCustomerCode(), txsSplitOrder.getCustomerCode(), "1").getName();
        //cumService.getCustomerInfo(txsSplitOrder.getCustomerCode(), txsSplitOrder.getCustomerCode(), "1").getName();

        // 所有分账记录
        List<TxsSplitRecord> txsSplitRecordList = new ArrayList<>();
        for (SplitInfo splitInfo : splitInfoList) {
            TxsSplitRecord txsSplitRecord = self.createSplitRecord(businessInst, 0L, splitInfo, txsSplitOrder, now, sourceCustomerName);
            txsSplitRecord.setPayTransactionNo(txsPayTradeOrder.getTransactionNo());
            txsSplitRecordList.add(txsSplitRecord);
        }

        splitPayService.insertSplitOrderAndRecords(txsPreOrder, txsSplitOrder, txsSplitRecordList);

        //更新分账记录
        splitPayService.updateTxsSplitRecord(txsPreOrder.getOrderId());

        //拼装账户分账交易账户内转报文
        InsidePayRequest insidePayRequest = self.createInsidePayRequest(txsSplitOrder, txsSplitRecordList);
        try {
            //执行分账
            splitPayService.executeSplit(insidePayRequest, now, txsSplitOrder, txsSplitRecordList);
        } catch (Exception e) {
            returnCodeUtil.buildResponse(accountSplitResponse, e, null);
            if (StringUtils.isBlank(accountSplitResponse.getReturnCode())) {
                accountSplitResponse.setReturnCode(TxsCode.SUCCESS.code);
                accountSplitResponse.setReturnMsg(TxsCode.SUCCESS.message);

                TxsSplitOrder finalSplitOrder = txsSplitOrderMapper.selectByPrimaryKey(txsSplitOrder.getTransactionNo());
                accountSplitResponse.setState(finalSplitOrder.getState());
                accountSplitResponse.setProcedureFee(finalSplitOrder.getProcedureFee());
            }
            return accountSplitResponse;
        }


        //账户分账成功
        accountSplitResponse.setReturnCode(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.code);
        accountSplitResponse.setReturnMsg(TxsConstants.FenZhangReturnCode.DETAIL_SUCCESS.comment);

        accountSplitResponse.setProcedureFee(txsSplitOrder.getProcedureFee());
        accountSplitResponse.setState(txsSplitOrder.getState());
        accountSplitResponse.setTransactionNo(txsSplitOrder.getTransactionNo());
        return accountSplitResponse;
    }

    /**
     * 生成和插入表
     *
     * @param request
     */
    @Logable(businessTag = "createAndInsertPreOrder", outputArgs = false, outputResult = false)
    @Transactional
    public TxsPreOrder createAndInsertOrder(TxsPayRequest request, List<CommodityInfo> commodityInfoList, boolean isExternalOrder) {
        TxsPreOrder preOrder = transactionService.createTxsPreOrder(request, sequenceService.nextValue("preOrder"));
        request.setPreOrderId(preOrder.getOrderId());
        try {
            txsPreOrderMapper.insertSelective(preOrder);
        } catch (Exception e) {
            e.printStackTrace();

            throw new AppException(TxsCode.REPEAT_ORDER.code);
        }
        for (CommodityInfo commodityInfo : commodityInfoList) {
            if (!isExternalOrder) {//设置了不支持外部订单，则商品订单号必须为易票订单

                if(Constants.YesOrNoByNum.YES.code.equals(commodityInfo.getCommodityOrderType())){
                    CommonOuterResponse response = pospService.queryIsRealName(commodityInfo.getCommodityOrderNo(), commodityInfo.getCommodityOrderAmount(), request.getCustomerCode());
                    if(!response.isSuccess()){
                        commonService.payLog("queryIsRealName:"+JSONObject.toJSONString(response));
                        throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code, TxsCode.TRADEORDER_NOTFOUND.message);
                    }
                }else{
                    TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNoAndCustomerCode(commodityInfo.getCommodityOrderNo(), request.getCustomerCode());
                    if (txsPayTradeOrder == null) {
                        throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code, TxsCode.TRADEORDER_NOTFOUND.message);
                    }
                    if (!txsPayTradeOrder.getAmount().equals(commodityInfo.getCommodityOrderAmount())) {
                        throw new AppException(TxsCode.AMOUNT_ATYPISM.code, TxsCode.AMOUNT_ATYPISM.message);
                    }
                }

            }
            List<OrderCommodityInfo> existCommodity = orderCommodityInfoMapper.selectByCustomerCodeAndCommodityTradeNo(preOrder.getCustomerCode(), commodityInfo.getCommodityOrderNo());

            if (existCommodity != null && existCommodity.size() > 0) {

                throw new AppException(TxsCode.COMMODITY_ORDERNO_REPEAT.code, TxsCode.COMMODITY_ORDERNO_REPEAT.message);
            }

            OrderCommodityInfo orderCommodityInfo = new OrderCommodityInfo();
            ReflectUtils.copyProperties(orderCommodityInfo, commodityInfo);
            orderCommodityInfo.setCustomerCode(preOrder.getCustomerCode());
            orderCommodityInfo.setOutTradeNo(preOrder.getOutTradeNo());
            orderCommodityInfo.setId(sequenceService.nextValue("orderCommodity"));

            orderCommodityInfoMapper.insertSelective(orderCommodityInfo);
        }
        return preOrder;
    }

    private boolean checkAccountSplitParam(TxsPayRequest request, AccountSplitRequest accountSplitRequest, AccountSplitResponse accountSplitResponse) {
        Long splitAmount = 0L;
        Long commodityAmount = 0L;
        HashSet<String> commodityOrderNoSet = new HashSet<>();
        if (accountSplitRequest.getCommodityInfoList() == null || accountSplitRequest.getCommodityInfoList().size() <= 0) {
            accountSplitResponse.setReturnCode(TxsCode.PARAM_ERROR.getCode());
            accountSplitResponse.setReturnMsg(TxsCode.PARAM_ERROR.getMessage() + "，商品列表不能为空");
            return false;
        }
        for (CommodityInfo commodityInfo : accountSplitRequest.getCommodityInfoList()) {
            if (commodityInfo.getCommodityOrderAmount() <= 0) {
                accountSplitResponse.setReturnCode(TxsCode.PARAM_ERROR.getCode());
                accountSplitResponse.setReturnMsg(TxsCode.PARAM_ERROR.getMessage() + "，商品订单金额必须大于0");
                return false;
            }
            if (StringUtils.isBlank(commodityInfo.getCommodityOrderNo())) {
                accountSplitResponse.setReturnCode(TxsCode.PARAM_ERROR.getCode());
                accountSplitResponse.setReturnMsg(TxsCode.PARAM_ERROR.getMessage() + "，商品订单号不能为空");
                return false;
            }
            commodityAmount += commodityInfo.getCommodityOrderAmount();
            commodityOrderNoSet.add(commodityInfo.getCommodityOrderNo());
        }

        if (accountSplitRequest.getCommodityInfoList() != null && commodityOrderNoSet.size() != accountSplitRequest.getCommodityInfoList().size()) {
            accountSplitResponse.setReturnCode(TxsCode.PARAM_ERROR.getCode());
            accountSplitResponse.setReturnMsg(TxsCode.PARAM_ERROR.getMessage() + "，商品订单号重复");
            return false;
        }

        if (!accountSplitRequest.getCommodityAmount().equals(commodityAmount)) {
            accountSplitResponse.setReturnCode(TxsCode.PARAM_ERROR.getCode());
            accountSplitResponse.setReturnMsg(TxsCode.PARAM_ERROR.getMessage() + "，商品订单金额不等于商品总金额");
            return false;
        }

        if (accountSplitRequest.getSplitInfoList() != null && accountSplitRequest.getSplitInfoList().size() > 0) {
            for (SplitInfo splitInfo : accountSplitRequest.getSplitInfoList()) {
                if (splitInfo.getAmount() < 0) {
                    if (!checkMultiSplitSource(request.getCustomerCode())) {//不允许多主体出金的话，则报错
                        accountSplitResponse.setReturnCode(TxsCode.PARAM_ERROR.getCode());
                        accountSplitResponse.setReturnMsg(TxsCode.PARAM_ERROR.getMessage() + "，分账金额不能小于0");
                        return false;
                    }
                }
                splitAmount += splitInfo.getAmount();
            }


        } else {
            splitAmount = accountSplitRequest.getCommodityAmount();
        }
        //设置总分账金额
        request.setAmount(splitAmount);

        if (accountSplitRequest.getCommodityAmount() < splitAmount) {
            accountSplitResponse.setReturnCode(TxsCode.SPLIT_ANMOUT_GREATER_THAN_COMMODITY_AMOUNT.getCode());
            accountSplitResponse.setReturnMsg(TxsCode.SPLIT_ANMOUT_GREATER_THAN_COMMODITY_AMOUNT.getMessage() + "分账失败");
            return false;
        }
        return true;
    }

    private boolean checkMultiSplitSource(String customerCode) {
        if (StringUtils.isBlank(multiSplitPlatCustomerCode)) {
            return false;
        }
        String[] customerCodes = multiSplitPlatCustomerCode.split(",");
        ArrayList<String> multiSplitCodeList = new ArrayList<>(Arrays.asList(customerCodes));
        if (multiSplitCodeList.contains(customerCode)) {
            return true;
        }
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(customerCode, customerCode, "1");
        if (!StringUtils.isBlank(customerInfo.getPlatCustomerCode()) &&
                multiSplitCodeList.contains(customerInfo.getPlatCustomerCode())) {
            return true;
        }
        return false;

    }

    private void checkGlobalSplitParam(GlobalSplitRequest globalSplitRequest) {
        Long commodityAmount = 0L;
        HashSet<String> commodityOrderNoSet = new HashSet<>();
        for (CommodityInfo commodityInfo : globalSplitRequest.getCommodityInfoList()) {
            if (commodityInfo.getCommodityOrderAmount() <= 0) {
                throw new AppException(TxsCode.PARAM_ERROR.getCode(), TxsCode.PARAM_ERROR.getMessage() + "，商品订单金额必须大于0");
            }
            if (StringUtils.isBlank(commodityInfo.getCommodityOrderNo())) {
                throw new AppException(TxsCode.PARAM_ERROR.getCode(), TxsCode.PARAM_ERROR.getMessage() + "，商品订单号不能为空");
            }
            commodityAmount += commodityInfo.getCommodityOrderAmount();
            commodityOrderNoSet.add(commodityInfo.getCommodityOrderNo());
        }

        if (globalSplitRequest.getCommodityInfoList() != null && commodityOrderNoSet.size() != globalSplitRequest.getCommodityInfoList().size()) {
            throw new AppException(TxsCode.PARAM_ERROR.getCode(), TxsCode.PARAM_ERROR.getMessage() + "，商品订单号重复");
        }

        if (!globalSplitRequest.getCommodityAmount().equals(commodityAmount)) {
            throw new AppException(TxsCode.PARAM_ERROR.getCode(), TxsCode.PARAM_ERROR.getMessage() + "，商品订单金额不等于商品总金额");
        }

    }

    //    @Logable(businessTag = "AccountSplitService.accountSplitProcedure")
    public CustomerBusinessInstance accountSplitProcedure(TxsPayRequest req, String businessCode) {
        CustomerBusinessInfo customerBusinessInfo = cumCacheService.queryBusinessInfoByBusinessCode(req.getCustomerCode(), businessCode);
        if (customerBusinessInfo == null) {
            throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code,
                    TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.message);
        }
//        CustomerBusinessInfo priceCustomerBusinessInfo = null;
        if (Constants.EfpsAccountService.GlobalBalancePay.code.equals(businessCode)
                || Constants.EfpsAccountService.GlobalCardPay.code.equals(businessCode)) {
            //国际收款业务，收款人必须开通该业务，且费率按收款人的来收
            customerBusinessInfo = cumCacheService.queryBusinessInfoByBusinessCode(req.getReceiveCustomerCode(), businessCode);
        }
        if (customerBusinessInfo == null) {
            throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code,
                    TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.message);
        }

        CustomerBusinessInstance businessInst = new CustomerBusinessInstance();

        BeanUtils.copyProperties(customerBusinessInfo, businessInst);
        businessInst.setPriceMode(Long.valueOf(customerBusinessInfo.getPriceMode()));

        Date date = new Date();
        if (!(date.compareTo(businessInst.getBeginTime()) > 0
                && date.compareTo(businessInst.getEndTime()) < 0)) {
            throw new AppException(TxsCode.MERCHANT_BUSINESS_CODE_EXPIRED.code,
                    TxsCode.MERCHANT_BUSINESS_CODE_EXPIRED.message);
        }

        //
        //判断是否要收取d1交易节假日上游手续费 end
        //
        Long procedureFee = businessInst.calcProcedureFee(req.getAmount(), 1, null, null);

        procedureAisistService.checkProcedureBiggerThanAmount(req.getAmount(), procedureFee, req.getProcedureCustomercode());

        if (!TxsConstants.RequestSrc.INNER.code.equals(req.getRequestSrc())) {
            transactionService.checkBusinessParam(req.getAmount(), businessInst);
        }
        if (procedureFee.longValue() < 0L) {
            throw new AppException(TxsCode.AMOUNT_ERROR.code,
                    TxsCode.AMOUNT_ERROR.message);
        }


        if (Constants.EfpsAccountService.GlobalBalancePay.code.equals(businessCode)
                || Constants.EfpsAccountService.GlobalCardPay.code.equals(businessCode)) {
            AccountQueryResponse accountQueryResponse = accService.accountQuery(req.getCustomerCode(), req.getCustomerCode(), "2");
            //国际业务，手续费内扣
            if (req.getAmount() > accountQueryResponse.getAvailableBalance()) {
                throw new AppException(TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.code, TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.message);
            }


            if (!StringUtils.isBlank(req.getCrossPlatCustomerCode())) {
                AccountQueryResponse crossAccountResponse = accService.accountQuery(req.getCrossPlatCustomerCode(), req.getCrossPlatCustomerCode(), "3");
                if (req.getAmount() > crossAccountResponse.getAvailableBalance()) {
                    throw new AppException(TxsCode.AVAILABLE_BALANCE_NOTENOUGH.code, TxsCode.AVAILABLE_BALANCE_NOTENOUGH.message);
                }
            }
        } else {
            //账户分账有热点账户之类的，不校验余额全部放到acc去好了。
//            if (req.getAmount() + procedureFee > accountQueryResponse.getAvailableBalance()) {
//                throw new AppException(TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.code, TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.message);
//            }
        }

        //设置手续费
        req.setProcedureFee(procedureFee);
        return businessInst;
    }

    /**
     * 构造分账订单，包含手续费计算
     * 状态设置为"尚未执行"
     *
     * @return
     */
    public TxsSplitOrder createTxsSplitOrder(Long payAmount, Long payProcedureFee, Date now, String customerCode,
                                             String outTradeNo, String notifyUrl, List<SplitInfo> splitInfoList, Long slaveAmountSum, CustomerBusinessInstance businessInst, String procedureType,
                                             String procedureCustomerCode, String isAccountSplitAuth) {
        // 交易单号
        String random = String.format("%06d", sequenceService.nextValue("splitImpl"));
        String dateString = DateFormatUtils.format(now, format);
        String transactionNo = TransactionType.ZHFZ.no + dateString + random;

        TxsSplitOrder txsSplitOrder = new TxsSplitOrder();
        txsSplitOrder.setTransactionNo(transactionNo);
        txsSplitOrder.setTransactionType(TransactionType.ZHFZ.code);
        txsSplitOrder.setOutTradeNo(outTradeNo);
        txsSplitOrder.setCustomerCode(customerCode);
        txsSplitOrder.setNotifyUrl(notifyUrl);
        txsSplitOrder.setState(TxsConstants.SplitOrderState.UNEXECUTED.code);
        txsSplitOrder.setAmount(payAmount);// 支付金额
        txsSplitOrder.setProcedureFee(payProcedureFee);
        txsSplitOrder.setProcedureType(procedureType);
        //设置业务编码
        txsSplitOrder.setOriBusinessCode(businessInst.getBusinessCode());
        if (payProcedureFee == null) {
            //这种情况应该不会出现，如果出现，说明程序有Bug，需要仔细检查原因
            throw new AppException(TxsCode.ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL.code, TxsCode.ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL.message);
        } else {
            if (TxsConstants.ProcedureTypeEnum.receiver_procedure.code.equals(procedureType)) {
                txsSplitOrder.setRealAmount(payAmount);
            } else {
                txsSplitOrder.setRealAmount(payAmount + payProcedureFee);
            }
        }
        if (splitInfoList == null || splitInfoList.size() <= 0) {
            txsSplitOrder.setSplitType(TxsConstants.SplitType.multy_split.code);
        } else {
            txsSplitOrder.setSplitType(TxsConstants.SplitType.common_split.code);
        }
        txsSplitOrder.setOriBusinessCode(businessInst.getBusinessCode());
        txsSplitOrder.setProcedureCustomerCode(customerCode);
        txsSplitOrder.setCreateTime(now);
        txsSplitOrder.setSplitInfoList(JSONObject.toJSONString(splitInfoList));
        txsSplitOrder.setSalveAmountSum(slaveAmountSum);
        txsSplitOrder.setBusinessInstId(businessInst.getBusinessExamId());
        if (Constants.YesOrNoByNum.YES.code.equals(isAccountSplitAuth)) {
            txsSplitOrder.setProcedureCustomerCode(procedureCustomerCode);
        }

        txsSplitOrder.setIsAccountSplitAuth(isAccountSplitAuth);
        return txsSplitOrder;
    }


    /**
     * 构造分账订单，包含手续费计算
     * 状态设置为"尚未执行"
     *
     * @return
     */
    public TxsSplitOrder createTxsSplitOrder(TxsPreOrder txsPreOrder, List<SplitInfo> splitInfoList, Long slaveAmountSum, Date now, String notifyUrl, String procedureType) {
        Long payAmount = txsPreOrder.getAmount();
        Long payProcedureFee = txsPreOrder.getProcedureFee();
        String customerCode = txsPreOrder.getCustomerCode();

        // 交易单号
        String random = String.format("%06d", sequenceService.nextValue("splitImpl"));
        String dateString = DateFormatUtils.format(now, format);
        String transactionNo = TransactionType.ZHFZ.no + dateString + random;

        TxsSplitOrder txsSplitOrder = new TxsSplitOrder();
        txsSplitOrder.setTransactionNo(transactionNo);
        txsSplitOrder.setTransactionType(TransactionType.ZHFZ.code);
        txsSplitOrder.setOutTradeNo(txsPreOrder.getOutTradeNo());
        txsSplitOrder.setCustomerCode(customerCode);
        txsSplitOrder.setNotifyUrl(notifyUrl);
        txsSplitOrder.setState(TxsConstants.SplitOrderState.UNEXECUTED.code);
        txsSplitOrder.setAmount(payAmount);// 支付金额
        txsSplitOrder.setProcedureFee(payProcedureFee);
        txsSplitOrder.setProcedureType(procedureType);
        //设置业务编码
        txsSplitOrder.setOriBusinessCode(txsPreOrder.getBusinessCode());
        if (payProcedureFee == null) {
            //这种情况应该不会出现，如果出现，说明程序有Bug，需要仔细检查原因
            throw new AppException(TxsCode.ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL.code, TxsCode.ORIG_PAY_ORDER_PROCEDURE_FEE_IS_NULL.message);
        } else {
            if (TxsConstants.ProcedureTypeEnum.receiver_procedure.code.equals(procedureType)) {
                txsSplitOrder.setRealAmount(payAmount);
            } else {
                txsSplitOrder.setRealAmount(payAmount + payProcedureFee);
            }


        }
        txsSplitOrder.setProcedureCustomerCode(customerCode);
        txsSplitOrder.setCreateTime(now);
        txsSplitOrder.setSplitInfoList(JSONObject.toJSONString(splitInfoList));
        txsSplitOrder.setSalveAmountSum(slaveAmountSum);
        txsSplitOrder.setBusinessInstId(txsPreOrder.getBusinessInstId());
        txsSplitOrder.setOriBusinessCode(txsPreOrder.getBusinessCode());

        return txsSplitOrder;
    }

    /**
     * 创建分账记录
     */
    public TxsSplitRecord createSplitRecord(CustomerBusinessInstance businessInst, Long payProcedureFee, SplitInfo splitInfo, TxsSplitOrder txsSplitOrder, Date now, String sourceCustomerName) {
        TxsSplitRecord txsSplitRecord = new TxsSplitRecord();
        Long id = sequenceService.nextValue("splitRecord");
        txsSplitRecord.setId(id);
        txsSplitRecord.setTransactionNo(txsSplitOrder.getTransactionNo());
        txsSplitRecord.setSourceCustomerCode(txsSplitOrder.getCustomerCode());
        txsSplitRecord.setCustomerCode(splitInfo.getCustomerCode());
        txsSplitRecord.setState(TxsConstants.SplitRecordState.UNEXECUTED.code);
        txsSplitRecord.setSplitRatio(splitInfo.getSplitRatio());
        txsSplitRecord.setTransactionType(txsSplitOrder.getTransactionType());

        if (businessInst == null) {
            businessInst = new CustomerBusinessInstance();
        }


        txsSplitRecord.setAmount(splitInfo.getAmount());
        txsSplitRecord.setProcedurefee(0L);
        txsSplitRecord.setBusinessInstId(businessInst.getBusinessExamId());

        String customerName = cumCacheService.getCustomerInfo(splitInfo.getCustomerCode(),
                splitInfo.getCustomerCode(), "1").getName();
        txsSplitRecord.setCustomerName(customerName);
        txsSplitRecord.setOrigAmount(splitInfo.getAmount());
        txsSplitRecord.setCreateTime(now);
        txsSplitRecord.setSourceCustomerName(sourceCustomerName);


        if (null != txsSplitOrder.getSplitMain()) {
            String customerNameSplitMain = cumCacheService.getCustomerInfo(txsSplitOrder.getSplitMain(),
                    txsSplitOrder.getSplitMain(), "1").getName();
            txsSplitRecord.setSplitMain(txsSplitOrder.getSplitMain());
            txsSplitRecord.setSplitMainName(customerNameSplitMain);
        }
        txsSplitRecord.setSplitModel(txsSplitOrder.getSplitModel());

        //2022-11-07新增商户订单号、原订单实收金额（元）、分账对象属性
        fillAccountSplitRecord(txsSplitRecord, txsSplitOrder, payProcedureFee);

        return txsSplitRecord;
    }


    /**
     * 创建分账记录
     */
    public TxsSplitRecord createSplitRecordConfirm(TxsPreOrder txsPreOrder, SplitInfo splitInfo, TxsSplitOrder txsSplitOrder, Date now, Long payProcedureFee) {
        TxsSplitRecord txsSplitRecord = new TxsSplitRecord();
        Long id = sequenceService.nextValue("splitRecord");
        txsSplitRecord.setId(id);
        txsSplitRecord.setTransactionNo(txsSplitOrder.getTransactionNo());
        txsSplitRecord.setSourceCustomerCode(txsSplitOrder.getCustomerCode());
        txsSplitRecord.setCustomerCode(splitInfo.getCustomerCode());
        txsSplitRecord.setState(TxsConstants.SplitRecordState.UNEXECUTED.code);
        txsSplitRecord.setSplitRatio(splitInfo.getSplitRatio());
        txsSplitRecord.setTransactionType(txsSplitOrder.getTransactionType());

        txsSplitRecord.setAmount(splitInfo.getAmount());
        Long procedureFee = 0L;
        txsSplitRecord.setProcedurefee(procedureFee);
        txsSplitRecord.setBusinessInstId(txsSplitOrder.getBusinessInstId());
        txsSplitRecord.setPayTransactionNo(txsPreOrder.getTransactionNo());


        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(txsSplitRecord.getCustomerCode(), txsSplitRecord.getCustomerCode(), UserType.PAS_USER.code);
        String customerName = customerInfo.getName();
        txsSplitRecord.setCustomerName(customerName);
        txsSplitRecord.setOrigAmount(splitInfo.getAmount());
        txsSplitRecord.setCreateTime(now);
        txsSplitRecord.setSourceCustomerName(txsPreOrder.getCustomerName());
        txsSplitRecord.setSplitModel(txsSplitOrder.getSplitModel());

        txsSplitRecord.setBusinessMan(customerInfo.getBusinessMan());
        txsSplitRecord.setBusinessManId(customerInfo.getBusinessManId());
        txsSplitRecord.setCompanyName(customerInfo.getCompanyName());
        txsSplitRecord.setCompanyId(customerInfo.getCompanyId());

        //历史bug: BUG-12093 , 账户分账立马分有以下字段,延时分(确认)没有以下字段 begin
        fillAccountSplitRecord(txsSplitRecord, txsSplitOrder, payProcedureFee);
        //历史bug: BUG-12093 begin
        return txsSplitRecord;
    }

    public void fillAccountSplitRecord(TxsSplitRecord txsSplitRecord, TxsSplitOrder txsSplitOrder, Long payProcedureFee){
        txsSplitRecord.setOutTradeNo(txsSplitOrder.getOutTradeNo());
        txsSplitRecord.setPayCashAmount(txsSplitOrder.getAmount());
        if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getSplitMain())){
            txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.SPLIT_MAIN.code);
        }else if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getSplitProcedureCustomerCode())){
            txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.SPLIT_PROCEDURE_CUSTOMER_CODE.code);
        }else if(txsSplitRecord.getCustomerCode().equals(txsSplitOrder.getProcedureCustomerCode())){
            txsSplitRecord.setPayProcedureFee(payProcedureFee);
            txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.PROCEDURE_CUSTOMER_CODE.code);
        }else {
            txsSplitRecord.setSplitAttr(TxsConstants.SplitAttr.SPLIT_CUSTOMER_CODE.code);
        }
    }


    //    @Logable(businessTag = "createInsidePayRequest")
    public InsidePayRequest createInsidePayRequest(TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords) {

        String oriBusinessCode = txsSplitOrder.getOriBusinessCode();
        InsidePayRequest insidePayRequest = new InsidePayRequest();
        insidePayRequest.setFromSystemId(systemId);
        insidePayRequest.setTransactionNo(txsSplitOrder.getTransactionNo());
        insidePayRequest.setTransactionType(Constants.transactionType.ZHFZTRANSACTION.code);
        insidePayRequest.setPayMethod(PayMethod.ACCOUNT_SPLIT.code);
        insidePayRequest.setPayCurrency(TxsConstants.CurrencyType.CNY.code);
        insidePayRequest.setChannelType(TxsConstants.ChannelType.SERVICE.code);
        insidePayRequest.setBusinessCode(oriBusinessCode);

        boolean bHaveOtherProcedureCustomerCode = (Constants.YesOrNoByNum.YES.code.equalsIgnoreCase(txsSplitOrder.getIsAccountSplitAuth()));
        // 构造源客户
        InsidePayCustomer insidePayCustomerSource = new InsidePayCustomer();

        Long sourceProcedureFee = txsSplitOrder.getProcedureFee();
        Long sourceAmount = txsSplitOrder.getRealAmount();
        Long anotherSourceAmount = 0L;
        //
        //由另外的人承担手续费begin
        //
//        "sourceCustomerList": [{
//            "amount": 100,
//                    "businessCode": "AccountSplit",
//                    "businessInstanceId": "AccountSplit_***************_20201117111046",
//                    "customerCode": "***************",
//                    "procedureFee": 17
//        }, {
//            "amount": 17,
//                    "businessCode": "AccountSplit",
//                    "businessInstanceId": "AccountSplit_***************_20201117111046",
//                    "customerCode": "****************",
//                    "procedureFee": 0
//        }]
        ////=============================
//        "sourceCustomerList": [{
//            "amount": 117,
//                    "businessCode": "AccountSplit",
//                    "businessInstanceId": "AccountSplit_***************_20201117111046",
//                    "customerCode": "***************",
//                    "procedureFee": 17
//        }],
        if (bHaveOtherProcedureCustomerCode) {
            anotherSourceAmount = sourceProcedureFee.longValue();
            sourceAmount = txsSplitOrder.getAmount().longValue();
        }
        //
        //由另外的人承担手续费end
        //
        insidePayCustomerSource.setAmount(sourceAmount);
        insidePayCustomerSource.setCustomerCode(txsSplitOrder.getCustomerCode());
        insidePayCustomerSource.setProcedureFee(txsSplitOrder.getProcedureFee());
        insidePayCustomerSource.setBusinessCode(oriBusinessCode);
        insidePayCustomerSource.setBusinessInstanceId(txsSplitOrder.getBusinessInstId());
        List<InsidePayCustomer> insidePayCustomerSources = new ArrayList<>();
        insidePayCustomerSources.add(insidePayCustomerSource);
        //
        //由另外的人承担手续费begin
        //
        if (bHaveOtherProcedureCustomerCode) {
            InsidePayCustomer insidePayCustomerSource1 = new InsidePayCustomer();
            insidePayCustomerSource1.setAmount(anotherSourceAmount);
            insidePayCustomerSource1.setCustomerCode(txsSplitOrder.getProcedureCustomerCode());
            insidePayCustomerSource1.setProcedureFee(0L);
            insidePayCustomerSource1.setBusinessCode(oriBusinessCode);
            insidePayCustomerSource1.setBusinessInstanceId(txsSplitOrder.getBusinessInstId());
            insidePayCustomerSources.add(insidePayCustomerSource1);
        }
        //
        //由另外的人承担手续费end
        //
        if (!StringUtils.isBlank(txsSplitOrder.getCrossPlatCustomerCode()) && (Constants.EfpsAccountService.GlobalBalancePay.code.equals(txsSplitOrder.getOriBusinessCode())
                || Constants.EfpsAccountService.GlobalCardPay.code.equals(txsSplitOrder.getOriBusinessCode()))) {
            InsidePayCustomer insidePayCrossCustomerSource = new InsidePayCustomer();
            insidePayCrossCustomerSource.setAmount(txsSplitOrder.getRealAmount());
            insidePayCrossCustomerSource.setCustomerCode(txsSplitOrder.getCrossPlatCustomerCode());
            insidePayCrossCustomerSource.setProcedureFee(txsSplitOrder.getProcedureFee());
            insidePayCrossCustomerSource.setBusinessCode(oriBusinessCode);
            insidePayCrossCustomerSource.setBusinessInstanceId(txsSplitOrder.getBusinessInstId());
            insidePayCrossCustomerSource.setCustomerType(Constants.AccCustomerRole.SOURCE_PROVIDER_CUSTOMERCODE.code);//簿记账户扣钱

            insidePayCustomerSources.add(insidePayCrossCustomerSource);
        }


        insidePayRequest.setSourceCustomerList(insidePayCustomerSources);
        // 构造目标客户
        List<InsidePayCustomer> insidePayCustomerTargets = new ArrayList<>();
        if (txsSplitRecords != null) {
            for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
                InsidePayCustomer insidePayCustomerTarget = new InsidePayCustomer();
                insidePayCustomerTarget.setAmount(txsSplitRecord.getAmount() - txsSplitRecord.getProcedurefee());
                insidePayCustomerTarget.setCustomerCode(txsSplitRecord.getCustomerCode());
                insidePayCustomerTarget.setProcedureFee(txsSplitRecord.getProcedurefee());
                insidePayCustomerTarget.setBusinessInstanceId(txsSplitRecord.getBusinessInstId());
                insidePayCustomerTarget.setBusinessCode(oriBusinessCode);
                insidePayCustomerTargets.add(insidePayCustomerTarget);
            }
        }

        insidePayRequest.setTargetCustomerList(insidePayCustomerTargets);
        return insidePayRequest;
    }

    //    @Logable(businessTag = "createConfirmInsidePayRequest")
    public InsidePayRequest createConfirmInsidePayRequest(TxsSplitOrder txsSplitOrder, List<TxsSplitRecord> txsSplitRecords) {

        String oriBusinessCode = txsSplitOrder.getOriBusinessCode();
        InsidePayRequest insidePayRequest = new InsidePayRequest();
        insidePayRequest.setFromSystemId(systemId);
        insidePayRequest.setTransactionNo(txsSplitOrder.getTransactionNo() + "_1");
        insidePayRequest.setTransactionType(Constants.transactionType.ZHFZTRANSACTION.code);
        insidePayRequest.setPayMethod(PayMethod.ACCOUNT_SPLIT.code);
        insidePayRequest.setPayCurrency(TxsConstants.CurrencyType.CNY.code);
        insidePayRequest.setChannelType(TxsConstants.ChannelType.SERVICE.code);
        insidePayRequest.setBusinessCode(oriBusinessCode);

        List<InsidePayCustomer> insidePayCustomerSources = new ArrayList<>();
        // 构造目标客户
        List<InsidePayCustomer> insidePayCustomerTargets = new ArrayList<>();

        if (txsSplitRecords != null) {
            for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
                InsidePayCustomer insidePayCustomerTarget = new InsidePayCustomer();
                insidePayCustomerTarget.setAmount(txsSplitRecord.getAmount() - txsSplitRecord.getProcedurefee());
                insidePayCustomerTarget.setCustomerCode(txsSplitRecord.getCustomerCode());
                insidePayCustomerTarget.setProcedureFee(txsSplitRecord.getProcedurefee());
                insidePayCustomerTarget.setBusinessInstanceId(txsSplitRecord.getBusinessInstId());
                insidePayCustomerTarget.setBusinessCode(oriBusinessCode);
                insidePayCustomerTargets.add(insidePayCustomerTarget);
            }
        }
        insidePayRequest.setSourceCustomerList(insidePayCustomerSources);
        insidePayRequest.setTargetCustomerList(insidePayCustomerTargets);

        return insidePayRequest;
    }

    @Logable(businessTag = "TxsAccountPayService.refund")
    public AccountSplitRefundResponse refund(AccountSplitRefundRequest request, AccountSplitRefundResponse refundResponse) {
        TxsRefundPreOrder txsRefundPreOrder = txsRefundPreOrderMapper.selectByOutRefundNoAndCustomerCode(request.getOutRefundNo(), request.getCustomerCode());
        if (null != txsRefundPreOrder) {
            throw new AppException(TxsCode.REPEAT_ORDER.code);
        }

        TxsPreOrder txsPreOrder = transactionQueryService.queryTxsPreOrder(request.getCustomerCode(), request.getOutTradeNo(), request.getTransactionNo());
        if (txsPreOrder == null) {
            throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code, TxsCode.TRADEORDER_NOTFOUND.message);
        }

        if (!txsPreOrder.getCustomerCode().equals(request.getCustomerCode())) {
            throw new AppException(TxsCode.CUSTOMER_ATYPISM.getCode(), "与原交易订单" + TxsCode.CUSTOMER_ATYPISM.getMessage());
        }

        //判断原交易订单是否成功
        if (!TxsConstants.PayState.SUCCESS.code.equals(txsPreOrder.getPayState())) {
            throw new AppException(TxsCode.TRADEORDER_NOTSUCCESSPAY.code, TxsCode.TRADEORDER_NOTSUCCESSPAY.message);
        }

        //判断原交易订单是否已退款
        if (txsPreOrder.getRefundFee() != null && txsPreOrder.getRefundFee().equals(txsPreOrder.getAmount())) {
            throw new AppException(TxsCode.ORDER_REFUND.code, "原交易" + TxsCode.ORDER_REFUND.message + "成功");
        }

        // 查询数据库是否有分账订单数据
        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode(), null);
        if (null == txsSplitOrder) {
            throw new AppException(TxsCode.SPLITRECORD_NOTFOUND.code, TxsCode.SPLITRECORD_NOTFOUND.message);
        }
        if (TxsConstants.SplitOrderState.UNEXECUTED.code.equals(txsSplitOrder.getState())) {
            //此种情况是表刚插入,插入后立即执行分账的,此时,不允许退.要过一会,待变为成功或失败后再退
            throw new AppException(TxsCode.FZ_PROCESSING_NOT_ALLOW_REFUND.code, TxsCode.FZ_PROCESSING_NOT_ALLOW_REFUND.message);
        }

        //只传SplitInfoList不传RefundAmount
        if(request.getRefundAmount() == null && request.getSplitInfoList() != null && request.getSplitInfoList().size() > 0){
            Long refundAmount = 0L;
            for (SplitInfo splitInfo : request.getSplitInfoList()){
                refundAmount += splitInfo.getAmount();
            }
            request.setRefundAmount(refundAmount);
        }

        //判断是否部分退款
        boolean partialRefund = false;
        if (request.getRefundAmount() != null) {
            if(request.getRefundAmount() <= 0){
                throw new AppException(TxsCode.ERROR_REFUNDFEE.code, "申请退款金额必须大于0元");
            }
            if (request.getRefundAmount() > (txsPreOrder.getAmount() - (txsPreOrder.getRefundingFee() == null ? 0 : txsPreOrder.getRefundingFee()) - (txsPreOrder.getRefundFee() == null ? 0 : txsPreOrder.getRefundFee()))) {
                throw new AppException(TxsCode.ERROR_REFUNDFEE.code, "申请退款金额大于可退款金额");
            }

            boolean ratio = false;
            if (request.getSplitInfoList() == null || request.getSplitInfoList().size() == 0) {
                List<SplitInfo> splitInfoList = new ArrayList<>();
                List<TxsSplitRecord> txsSplitRecordList = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
                //按金额排序.thenComparingLong(TxsSplitRecord::getRefundFee).reversed()
                txsSplitRecordList.sort(Comparator.comparingLong(TxsSplitRecord::getAmount));

                long tmpRefundAmount = 0;
                for (int i = 0; i < txsSplitRecordList.size(); i++){
                    TxsSplitRecord txsSplitRecord = txsSplitRecordList.get(i);
                    SplitInfo splitInfo = new SplitInfo();
                    if(txsPreOrder.getAmount() - (txsPreOrder.getRefundingFee() == null ? 0 : txsPreOrder.getRefundingFee()) - (txsPreOrder.getRefundFee() == null ? 0 : txsPreOrder.getRefundFee()) == request.getRefundAmount()){
                        splitInfo.setAmount(txsSplitRecord.getAmount() - (txsSplitRecord.getRefundFee() == null ? 0 : txsSplitRecord.getRefundFee()));
                    }else {
                        if((i + 1) == txsSplitRecordList.size()){
                            splitInfo.setAmount(request.getRefundAmount() - tmpRefundAmount);
                        }else {
                            splitInfo.setAmount(self.calcBackProcedure(request.getRefundAmount(), txsPreOrder.getAmount(), txsSplitRecord.getAmount()));
                            tmpRefundAmount += splitInfo.getAmount();
                        }
                    }
                    splitInfo.setCustomerCode(txsSplitRecord.getCustomerCode());
                    splitInfoList.add(splitInfo);
                }
                request.setSplitInfoList(splitInfoList);
                ratio = true;
            }

            if (request.getSplitInfoList() != null && request.getSplitInfoList().size() > 0) {
                transactionService.checkCumStatus(request.getCustomerCode(), TxsConstants.CustomerType.PAYER.code);
                List<SplitInfo> splitInfos = request.getSplitInfoList();
                Long splitedAmount = 0L;
                for (SplitInfo splitInfo : splitInfos) {
                    TxsSplitRecord txsSplitRecord = txsSplitRecordMapper.selectByTransactionAndCustomerCode(txsSplitOrder.getTransactionNo(), splitInfo.getCustomerCode());
                    if(txsSplitRecord == null){
                        throw new AppException(TxsCode.CUSTOMER_DIFFER.code, TxsCode.CUSTOMER_DIFFER.message);
                    }
                    if(splitInfo.getAmount() < 0){
                        throw new AppException(TxsCode.ERROR_REFUNDFEE.code, "退款金额必须大于0");
                    }
                    //按比例退款或者非特殊退款商户需要限制
                    if((ratio || !refundSuperCustomerCode.contains(splitInfo.getCustomerCode()))
                            && txsSplitRecord.getAmount() - (txsSplitRecord.getRefundFee() == null ? 0 : txsSplitRecord.getRefundFee()) - splitInfo.getAmount() < 0){
                        throw new AppException(TxsCode.ERROR_REFUNDFEE.code, txsSplitRecord.getCustomerCode() + "商户申请退款金额大于可退款金额");
                    }

                    splitedAmount += splitInfo.getAmount();
                    //判断商户是否正常
                    transactionService.checkCumStatus(splitInfo.getCustomerCode(), TxsConstants.CustomerType.PAYER.code);
                }
                if (!request.getRefundAmount().equals(splitedAmount)) {
                    throw new AppException(TxsCode.REFUND_SUMSPLITAMOUNT_NOT_EQUAL_REFUND.code);
                }
            }
            partialRefund = true;
        } else {
            //判断原交易订单是否已退款
            List<TxsRefundPreOrder> refundList = txsRefundPreOrderMapper.selectSuccessAndDoingByOutTradeNo(txsPreOrder.getOutTradeNo(), request.getCustomerCode());
            if (refundList != null && refundList.size() > 0) {
                throw new AppException(TxsCode.ORDER_REFUND.code, "原交易" + TxsCode.ORDER_REFUND.message + "，不可在发起全额退款");
            }
        }

        Date now = new Date();
        if (request.getRefundAmount() == null) {
            request.setRefundAmount(txsPreOrder.getAmount());
        }

        List<TxsRefundSplitRecord> refundSplitRecordList = new ArrayList<>();
        //创建退款订单
        txsRefundPreOrder = self.createRefundOrder(request, refundResponse, txsPreOrder, now, txsSplitOrder, refundSplitRecordList);
        if (refundSplitRecordList.size() > 0) {
            for (TxsRefundSplitRecord refundSplitRecord : refundSplitRecordList) {
                AccountQueryResponse accountQueryResponse = accService.accountQuery(refundSplitRecord.getCustomerCode(), refundSplitRecord.getCustomerCode(), "2");
                //判断退款账户可用金额是否大于退款金额
                if (refundSplitRecord.getAmount() > accountQueryResponse.getAvailableBalance()) {
                    refundResponse.setReturnCode(TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.code);
                    refundResponse.setReturnMsg(refundSplitRecord.getCustomerCode() + TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.message);
                    self.updateRefundPreOrder(txsRefundPreOrder, TxsConstants.RefundState.FAIL.code, refundResponse.getReturnCode(), refundResponse.getReturnMsg());
                    return refundResponse;
                }
            }
        }
        AccountVoucherRes accountVoucherRes;
        //发起账户分账退款
        if (partialRefund) {
            DirectVoucherRequest directVoucherRequest = new DirectVoucherRequest();
            directVoucherRequest.setTransactionNo(txsRefundPreOrder.getTransactionNo());
            directVoucherRequest.setTransactionType(TransactionType.TK.code);
            directVoucherRequest.setPayType(DirectVoucherRequest.PayType.REFUND);
            directVoucherRequest.setBusinessCode(txsRefundPreOrder.getBusinessCode());
            if (txsPreOrder.getProcedureCustomercode() != null && !txsPreOrder.getProcedureCustomercode().equals(txsPreOrder.getCustomerCode())) {
                directVoucherRequest.addDetail(txsRefundPreOrder.getCustomerCode(), request.getRefundAmount(), DirectVoucherRequest.AccountType.JY, DirectVoucherRequest.BalanceType.USEFUL_AMOUNT);
                directVoucherRequest.addDetail(txsPreOrder.getProcedureCustomercode(), txsRefundPreOrder.getBackpayProcedurefee(), DirectVoucherRequest.AccountType.JY, DirectVoucherRequest.BalanceType.USEFUL_AMOUNT);
            } else {
                directVoucherRequest.addDetail(txsRefundPreOrder.getCustomerCode(), request.getRefundAmount() + txsRefundPreOrder.getBackpayProcedurefee(), DirectVoucherRequest.AccountType.JY, DirectVoucherRequest.BalanceType.USEFUL_AMOUNT);
            }
            for (TxsRefundSplitRecord refundSplitRecord : refundSplitRecordList) {
                directVoucherRequest.addDetail(refundSplitRecord.getCustomerCode(), -refundSplitRecord.getRealRefundFee(), DirectVoucherRequest.AccountType.JY, DirectVoucherRequest.BalanceType.USEFUL_AMOUNT);
            }
            DirectVoucherResponse response = accService.direct(directVoucherRequest);
            accountVoucherRes = new AccountVoucherRes();
            if (response.isSuccess()) {
                accountVoucherRes.setResult(Constants.SUCCESS);
            } else {
                accountVoucherRes.setResult(Constants.FAIL);
            }

        } else {
            accountVoucherRes = accService.accountVoucherRevoke(txsRefundPreOrder.getTransactionNo(), txsSplitOrder.getTransactionNo());
        }
        if (Constants.SUCCESS.equals(accountVoucherRes.getResult())) {
            refundResponse.setReturnCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            refundResponse.setReturnMsg(Constants.detailReturnCode.RETURN_SUCCESS.comment);
            self.updateRefundPreOrder(txsRefundPreOrder, TxsConstants.RefundState.SUCCESS.code, refundResponse.getReturnCode(), refundResponse.getReturnMsg());
            self.updateTxsPreOrderAddAmount(txsPreOrder, txsRefundPreOrder.getRefundFee());
            self.updateTxsSplitRecordAddRefundFee(refundSplitRecordList, txsSplitOrder);
        } else {
            refundResponse.setReturnCode(Constants.detailReturnCode.RETURN_FAIL.code);
            refundResponse.setReturnMsg(Constants.detailReturnCode.RETURN_FAIL.comment);
            self.updateRefundPreOrder(txsRefundPreOrder, TxsConstants.RefundState.FAIL.code, refundResponse.getReturnCode(), refundResponse.getReturnMsg());
        }
        return refundResponse;
    }


    @Logable(businessTag = "TxsAccountPayService.revoke")
    public CommonResponse revoke(String customerCode, String outTradeNo, String transactionNo) {
        CommonResponse pageResult = new CommonResponse();
        TxsPreOrder txsPreOrder = transactionQueryService.queryTxsPreOrder(customerCode, outTradeNo, transactionNo);
        if (txsPreOrder == null) {
            pageResult.setResult(TxsCode.TRADEORDER_NOTFOUND.getCode());
            pageResult.setMessage(TxsCode.TRADEORDER_NOTFOUND.getMessage());
            return pageResult;
        }

        if ((txsPreOrder.getRefundingFee() == null ? 0 : txsPreOrder.getRefundingFee()) > 0 || (txsPreOrder.getRefundFee() == null ? 0 : txsPreOrder.getRefundFee()) > 0) {
            pageResult.setResult(TxsCode.ORDER_REFUND.getCode());
            pageResult.setMessage(TxsCode.ORDER_REFUND.getMessage());
            return pageResult;
        }

        if (!TxsConstants.PayState.SUCCESS.code.equals(txsPreOrder.getPayState())) {
            pageResult.setResult(TxsCode.TRADEORDER_NOTSUCCESSPAY.code);
            pageResult.setMessage(TxsCode.TRADEORDER_NOTSUCCESSPAY.message);
            return pageResult;
        }

        if (!txsPreOrder.getCustomerCode().equals(customerCode)) {
            pageResult.setResult(TxsCode.CUSTOMER_ATYPISM.getCode());
            pageResult.setMessage("与原交易订单" + TxsCode.CUSTOMER_ATYPISM.getMessage());
            return pageResult;
        }
        // 查询数据库是否有分账订单数据
        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode(), null);

        if (TxsConstants.SplitOrderState.SUCCESS.code.equals(txsSplitOrder.getState()) && !isSameDay(new Date(), txsPreOrder.getEndTime())) {
            pageResult.setResult(TxsCode.ORI_ORDER_TIME_OUT_CANCEL.code);
            pageResult.setMessage(TxsCode.ORI_ORDER_TIME_OUT_CANCEL.message);
            return pageResult;
        }
        List<TxsSplitRecord> txsSplitRecordList = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
        if (txsSplitRecordList.size() > 0) {
            for (TxsSplitRecord txsSplitRecord : txsSplitRecordList) {
                AccountQueryResponse accountQueryResponse = accService.accountQuery(txsSplitRecord.getCustomerCode(), txsSplitRecord.getCustomerCode(), "2");
                if (txsSplitRecord.getAmount() > accountQueryResponse.getAvailableBalance()) {
                    pageResult.setResult(TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.code);
                    pageResult.setMessage(txsSplitRecord.getCustomerCode() + TxsCode.TRANS_ACCOUNT_AVAILABLE_BALANCE_SHORTAGE.message);
                    return pageResult;
                }
            }
        }
        String revokeNo = transactionService.generatorOrderNo(TransactionType.ZHFZCX.code, "payTradeNo", new Date());
        AccountVoucherRes accountVoucherRes = accService.accountVoucherRevoke(revokeNo, txsSplitOrder.getTransactionNo());
        if (Constants.SUCCESS.equals(accountVoucherRes.getResult())) {
            pageResult.setResult(Constants.detailReturnCode.RETURN_SUCCESS.code);
            pageResult.setMessage(Constants.detailReturnCode.RETURN_SUCCESS.comment);
            self.updatePreOrder(txsPreOrder, TxsConstants.PreOrderState.CANCEL.code, Constants.detailReturnCode.RETURN_SUCCESS.code, Constants.detailReturnCode.RETURN_SUCCESS.comment, revokeNo);
        } else {
            pageResult.setResult(Constants.detailReturnCode.RETURN_FAIL.code);
            pageResult.setMessage(Constants.detailReturnCode.RETURN_FAIL.comment);
        }
        return pageResult;
    }

    private static boolean isSameDay(Date dt1, Date dt2) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String strDate1 = format.format(dt1);
        String strDate2 = format.format(dt2);
        return strDate1.equalsIgnoreCase(strDate2);
    }

    @Logable(businessTag = "reviewSplitState")
    public CommonOuterResponse<List<String>> reviewSplitState(List<String> transactionNoList) {
        CommonOuterResponse<List<String>> response = new CommonOuterResponse<List<String>>();
        List<String> successList = new ArrayList<>();
        if (transactionNoList == null || transactionNoList.size() <= 0) {
            return response;
        }

        for (String transactionNo : transactionNoList) {
            try {
                Date now = new Date();
                TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByPrimaryKey(transactionNo);
                List<TxsSplitRecord> splitRecords = txsSplitRecordMapper.selectByTransactionNo(transactionNo);

                if (txsSplitOrder == null) {
                    continue;
                }
                //分账成功，或者已有撤销订单号，不处理
                if (TxsConstants.SplitOrderState.SUCCESS.code.equals(txsSplitOrder.getState())
                        || TxsConstants.SplitOrderState.REVOKE.code.equals(txsSplitOrder.getState())
                        || !StringUtils.isBlank(txsSplitOrder.getRevokeTransactionNo())) {
                    continue;
                }

                if (splitRecords == null || splitRecords.size() == 0) {

                    if (TxsConstants.SplitType.multy_split.code.equals(txsSplitOrder.getSplitType())) {
                        if (!TxsConstants.SplitOrderState.PRE_SUCCESS.code.equals(txsSplitOrder.getState())) {
                            AccountVoucher accountVoucher = accService.accountVoucherQuery(transactionNo);
                            TxsConstants.SplitOrderState splitOrderState = null;
                            if (accountVoucher == null || TxsConstants.VoucherStatus.ROLBACK.code.equals(accountVoucher.getStatus()) || TxsConstants.VoucherStatus.REVOKE.code.equals(accountVoucher.getStatus())) {

                                if (!TxsConstants.SplitOrderState.FAIL.code.equals(txsSplitOrder.getState())) {
                                    self.updateReviewSplitState(now, now, null, txsSplitOrder, null,
                                            TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.FAIL);
                                    successList.add(transactionNo);
                                }

                            }
                            //还未执行确认分账
                            else if (TxsConstants.VoucherStatus.SUCCESS.code.equals(accountVoucher.getStatus())) {

                                self.updateReviewSplitState(now, accountVoucher.getAccountDateTime(), null, txsSplitOrder, null,
                                        TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.PRE_SUCCESS);
                                successList.add(transactionNo);
                            }

                        }
                    }

                } else {
                    //已经有分账明细记录
                    if (TxsConstants.SplitType.multy_split.code.equals(txsSplitOrder.getSplitType())) {

                        TxsSplitRecord txsSplitRecord = splitRecords.get(0);
                        Date timeBefor = DateUtils.getDateAfter(-60 * 5);
                        if (timeBefor.compareTo(txsSplitRecord.getCreateTime()) < 0) {
                            commonService.payLog("新订单，暂不处理：" + txsSplitOrder.getTransactionNo());
                            //该订单还不足5分钟，先不稽核，防止系统间处理超时的情况
                            continue;

                        }

                        AccountVoucher accountVoucherConfirm = accService.accountVoucherQuery(transactionNo + "_1");

                        if (accountVoucherConfirm != null) {


                            if (TxsConstants.VoucherStatus.SUCCESS.code.equals(accountVoucherConfirm.getStatus())) {
                                self.updateReviewSplitState(now, accountVoucherConfirm.getAccountDateTime(), splitRecords, txsSplitOrder, null,
                                        TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.SUCCESS);
                                successList.add(transactionNo);

                            }

                        } else {
                            //分步分账，且有分账明细，说明调用过确认分账。 但是如果acc没记录，说明确认分账超时或失败
                            InsidePayRequest insidePayRequest = self.createConfirmInsidePayRequest(txsSplitOrder, splitRecords);
                            splitPayService.executeConfirmSplit(insidePayRequest, now, txsSplitOrder, splitRecords);
                            successList.add(transactionNo);
                        }
                    } else {
                        //普通账户分账超时
                        TxsSplitRecord txsSplitRecord = splitRecords.get(0);
                        Date timeBefor = DateUtils.getDateAfter(-60 * 5);
                        if (timeBefor.compareTo(txsSplitRecord.getCreateTime()) < 0) {
                            commonService.payLog("新订单，暂不处理：" + txsSplitOrder.getTransactionNo());
                            //该订单还不足5分钟，先不稽核，防止系统间处理超时的情况
                            continue;

                        }
                        //普通账户分账，只处理处理中的
                        if (TxsConstants.SplitOrderState.UNEXECUTED.code.equals(txsSplitOrder.getState())) {


                            AccountVoucher accountVoucher1 = accService.accountVoucherQuery(transactionNo);

                            if (accountVoucher1 == null || TxsConstants.VoucherStatus.ROLBACK.code.equals(accountVoucher1.getStatus()) || TxsConstants.VoucherStatus.REVOKE.code.equals(accountVoucher1.getStatus())) {

                                if ("2822".equals(txsSplitOrder.getErrorCode())) {
                                    continue;//风控冻结止付的订单， 这边不处理
                                }

                                self.updateReviewSplitState(now, now, splitRecords, txsSplitOrder, null,
                                        TxsConstants.SplitRecordState.FAIL, TxsConstants.SplitOrderState.FAIL);
                                successList.add(transactionNo);

                            }
                            //还未执行确认分账
                            else if (TxsConstants.VoucherStatus.SUCCESS.code.equals(accountVoucher1.getStatus())) {

                                self.updateReviewSplitState(now, accountVoucher1.getAccountDateTime(), splitRecords, txsSplitOrder, null,
                                        TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.SUCCESS);
                                successList.add(transactionNo);
                            }

                        }


                    }
                }
            } catch (Exception e) {
                commonService.payLog("订单处理异常：" + transactionNo);
                commonService.logException(e);
            }
        }
        response.setData(successList);
        return response;
    }

    @Logable(businessTag = "updateReviewSplitState")
    public void updateReviewSplitState(Date now, Date endTime, List<TxsSplitRecord> txsSplitRecords,
                                       TxsSplitOrder txsSplitOrder, String errorCode, TxsConstants.SplitRecordState splitState, TxsConstants.SplitOrderState splitOrderState) {

        if (txsSplitOrder != null) {
            TxsSplitOrder splitOrderForUpdate = new TxsSplitOrder();
            splitOrderForUpdate.setTransactionNo(txsSplitOrder.getTransactionNo());
            splitOrderForUpdate.setEndTime(endTime);
            splitOrderForUpdate.setUpdateTime(now);
            splitOrderForUpdate.setErrorCode(errorCode);
            splitOrderForUpdate.setState(splitOrderState.code);
            txsSplitOrderMapper.updateByPrimaryKeySelective(splitOrderForUpdate);
        }


        if (txsSplitRecords != null) {
            Collections.sort(txsSplitRecords);//排序，防止死锁

            for (TxsSplitRecord txsSplitRecord : txsSplitRecords) {
                TxsSplitRecord forupdate = new TxsSplitRecord();
                forupdate.setId(txsSplitRecord.getId());
                forupdate.setState(splitState.code);
                forupdate.setUpdateTime(now);
                forupdate.setErrorCode(errorCode);
                txsSplitRecordMapper.updateByPrimaryKeySelective(forupdate);
            }
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Logable(businessTag = "createRefundOrder")
    public TxsRefundPreOrder createRefundOrder(AccountSplitRefundRequest request, AccountSplitRefundResponse response, TxsPreOrder txsPreOrder,
                                               Date now, TxsSplitOrder txsSplitOrder, List<TxsRefundSplitRecord> refundSplitRecordList) {

        TxsRefundPreOrder txsRefundPreOrder = self.insertTxsRefundPreOrder(request, txsPreOrder, now);
        if (TxsConstants.SplitOrderState.SUCCESS.code.equals(txsSplitOrder.getState())) {
            List<TxsSplitRecord> txsSplitRecordList = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
            self.createAndInsertRefundSplitRecords(request, txsPreOrder.getCustomerCode(), now, txsSplitRecordList, txsRefundPreOrder, refundSplitRecordList);
        }
        response.setOutTradeNo(txsRefundPreOrder.getOutTradeNo());
        response.setOutRefundNo(txsRefundPreOrder.getOutRefundNo());
        response.setTransactionNo(txsRefundPreOrder.getTransactionNo());

        return txsRefundPreOrder;
    }

    /**
     * 退款订单
     *
     * @param txsPreOrder
     * @param now
     * @return
     */
    @Logable(businessTag = "insertTxsRefundPreOrder")
    public TxsRefundPreOrder insertTxsRefundPreOrder(AccountSplitRefundRequest request, TxsPreOrder txsPreOrder, Date now) {
        TxsRefundPreOrder txsRefundPreOrderInsert = new TxsRefundPreOrder();
        String transactionNo = transactionService.generatorOrderNo(TKTransaction, SequenceCategory.refundTransactionNo.name, now);
        txsRefundPreOrderInsert.setId(sequenceService.nextValue("preRefund"));
        txsRefundPreOrderInsert.setOutTradeNo(txsPreOrder.getOutTradeNo());
        txsRefundPreOrderInsert.setOutRefundNo(request.getOutRefundNo());
        txsRefundPreOrderInsert.setCustomerCode(request.getCustomerCode());
        txsRefundPreOrderInsert.setTotalFee(txsPreOrder.getAmount());
        txsRefundPreOrderInsert.setRefundFee(request.getRefundAmount());
        txsRefundPreOrderInsert.setRefundCurrency(TxsConstants.CurrencyType.CNY.code);
        txsRefundPreOrderInsert.setTransactionNo(transactionNo);
        txsRefundPreOrderInsert.setPayState(TxsConstants.RefundState.IN_PROCESS.code);
        txsRefundPreOrderInsert.setCreateTime(now);
        txsRefundPreOrderInsert.setBusinessCode(Constants.EfpsAccountService.REFUND.code);
        txsRefundPreOrderInsert.setProcedureFee(0L);//退款不需要手续费，默认0
        //如果是最后一笔退款则不按比例退，直接退剩余手续费
        if(txsPreOrder.getAmount() - (txsPreOrder.getRefundingFee() == null ? 0 : txsPreOrder.getRefundingFee()) - (txsPreOrder.getRefundFee() == null ? 0 : txsPreOrder.getRefundFee()) == request.getRefundAmount()){
            txsRefundPreOrderInsert.setBackpayProcedurefee(txsPreOrder.getProcedureFee() - getBackpayProcedurefee(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode()));
        }else {
            txsRefundPreOrderInsert.setBackpayProcedurefee(self.calcBackProcedure(request.getRefundAmount(), txsPreOrder.getAmount(), txsPreOrder.getProcedureFee()));
        }
        txsRefundPreOrderInsert.setPayProcedurefee(txsPreOrder.getProcedureFee());
        txsRefundPreOrderInsert.setPayTransactionNo(txsPreOrder.getTransactionNo());
        txsRefundPreOrderInsert.setPayMethod(txsPreOrder.getPayMethod());
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(txsPreOrder.getCustomerCode(),
                txsPreOrder.getCustomerCode(), "1");
        txsRefundPreOrderInsert.setCustomername(customerInfo.getName());
        txsRefundPreOrderInsert.setAgentCustomerCode(txsPreOrder.getAgentCustomerCode());
        txsRefundPreOrderInsert.setAgentCustomerName(txsPreOrder.getAgentCustomerName());
        txsRefundPreOrderInsert.setPayPassWay(txsPreOrder.getPayPassWay());
        txsRefundPreOrderInsert.setSourceType(TxsConstants.RefundSourceType.MerchantApply.code);
        txsRefundPreOrderInsert.setIsBackRefundProcedure(TxsConstants.isAllowRefundProcedure.YES.code);
        txsRefundPreOrderInsert.setTerminalNo(txsPreOrder.getTerminalNo());
        txsRefundPreOrderInsert.setTerminalName(txsPreOrder.getTerminalName());
        txsRefundPreOrderInsert.setAcqOrgCode(txsPreOrder.getAcqOrgCode());
        txsRefundPreOrderInsert.setAcqSpId(txsPreOrder.getAcqSpId());
        txsRefundPreOrderInsert.setOrderType(txsPreOrder.getOrderType());
        txsRefundPreOrderInsert.setTermInfo(txsPreOrder.getTermInfo());
        txsRefundPreOrderInsert.setAreaInfo(txsPreOrder.getAreaInfo());
        txsRefundPreOrderInsert.setUserId(txsPreOrder.getUserId());
        txsRefundPreOrderInsert.setSubAppId(txsPreOrder.getSubAppId());
        txsRefundPreOrderInsert.setPlatformCustomerCode(txsPreOrder.getPlatformCustomerCode());
        txsRefundPreOrderInsert.setStoreId(txsPreOrder.getStoreId());
        txsRefundPreOrderInsert.setOperatorId(txsPreOrder.getOperatorId());
        txsRefundPreOrderInsert.setReqReserved(txsPreOrder.getReqReserved());
        txsRefundPreOrderInsert.setCupsReqReserved(txsPreOrder.getCupsReqReserved());
        txsRefundPreOrderInsert.setPayerAppName(txsPreOrder.getPayerAppName());
        txsRefundPreOrderInsert.setActualPayAmount(txsPreOrder.getActualPayAmount());
        txsRefundPreOrderInsert.setDiscountableAmount(txsPreOrder.getDiscountableAmount());
        txsRefundPreOrderInsert.setSettlementAmount(txsPreOrder.getSettlementAmount());
        txsRefundPreOrderInsert.setSourceChannel(txsPreOrder.getSourceChannel());
        txsRefundPreOrderInsert.setQrCodeIssuer(txsPreOrder.getQrCodeIssuer());
        txsRefundPreOrderInsert.setProcedureRate(txsPreOrder.getProcedureRate());
        txsRefundPreOrderInsert.setRateMode(txsPreOrder.getRateMode());

        txsRefundPreOrderInsert.setCardNoEnc(txsPreOrder.getCardNoEnc());
        txsRefundPreOrderInsert.setCardNoMosaic(txsPreOrder.getCardNoMosaic());
        txsRefundPreOrderInsert.setCardType(txsPreOrder.getCardType());
        txsRefundPreOrderInsert.setBusinessType("0");
        txsRefundPreOrderInsert.setRemark(request.getAttachData());
        txsRefundPreOrderInsert.setBackSplitProcedurefee(0L);
        txsRefundPreOrderInsert.setOrigTotalSplitProcedurefee(0L);
        txsRefundPreOrderInsert.setBusinessMan(customerInfo.getBusinessMan());
        txsRefundPreOrderInsert.setBusinessManId(customerInfo.getBusinessManId());
        txsRefundPreOrderInsert.setCompanyId(customerInfo.getCompanyId());
        txsRefundPreOrderInsert.setCompanyName(customerInfo.getCompanyName());
        txsRefundPreOrderInsert.setIsSettWithCashAmount(Constants.IsSettWithCashAmount.NO.code);
        txsRefundPreOrderInsert.setCashAmount(txsPreOrder.getCashAmount());
        txsRefundPreOrderInsert.setCouponAmount(txsPreOrder.getCouponAmount());
        txsRefundPreOrderInsert.setRealRefundFee(txsRefundPreOrderInsert.getRefundFee());

        try {
            txsRefundPreOrderMapper.insertSelective(txsRefundPreOrderInsert);
        } catch (DuplicateKeyException e) {// 唯一索引异常
            throw new AppException(TxsCode.OUTREFUNDNO_EXIST.code);
        } catch (Exception e) {
            throw e;
        }
        return txsRefundPreOrderInsert;
    }

    private Long getBackpayProcedurefee(String outTradeNo, String customerCode){
        long backpayProcedurefee = 0;
        List<TxsRefundPreOrder> txsRefundPreOrderList = txsRefundPreOrderMapper.selectSuccessAndDoingByOutTradeNo(outTradeNo, customerCode);
        for(TxsRefundPreOrder txsRefundPreOrder : txsRefundPreOrderList){
            backpayProcedurefee += txsRefundPreOrder.getBackpayProcedurefee() == null ? 0 : txsRefundPreOrder.getBackpayProcedurefee();
        }
        return backpayProcedurefee;
    }

    /**
     * 创建分账订单的退款记录
     *
     * @param sourceCustomerCode 分账收单的客户
     * @param now
     * @return
     */
    @Logable(businessTag = "createAndInsertRefundSplitRecords")
    public List<TxsRefundSplitRecord> createAndInsertRefundSplitRecords(AccountSplitRefundRequest request, String sourceCustomerCode, Date now, List<TxsSplitRecord> txsSplitRecordList,
                                                                        TxsRefundPreOrder txsRefundPreOrder, List<TxsRefundSplitRecord> refundSplitRecordList) {
        for (TxsSplitRecord txsSplitRecord : txsSplitRecordList) {
            TxsRefundSplitRecord record = self.createTxsRefundSplitRecord(request, now, txsRefundPreOrder, txsSplitRecord,
                    sourceCustomerCode);
            txsRefundSplitRecordMapper.insertSelective(record);
            refundSplitRecordList.add(record);
        }
        return refundSplitRecordList;
    }

    /**
     * 分账收单的分账退款记录表只记录数据，无需业务实例和手续费记录（默认为0）
     *
     * @param now
     * @param sourceCustomerCode
     * @return
     */
    @Logable(businessTag = "TxsAccountPayService.createTxsRefundSplitRecord")
    public TxsRefundSplitRecord createTxsRefundSplitRecord(AccountSplitRefundRequest request, Date now, TxsRefundPreOrder txsRefundPreOrder,
                                                           TxsSplitRecord txsSplitRecord, String sourceCustomerCode) {
        Long refundAmount = getSplitInfoAmount(txsSplitRecord.getCustomerCode(), request.getSplitInfoList());
        refundAmount = refundAmount == null ? txsSplitRecord.getAmount() : refundAmount;
        TxsRefundSplitRecord txsRefundSplitRecord = new TxsRefundSplitRecord();
        Long id = sequenceService.nextValue("splitRefundRecord");
        txsRefundSplitRecord.setId(id);
        txsRefundSplitRecord.setTransactionNo(txsRefundPreOrder.getTransactionNo());
        txsRefundSplitRecord.setSourceCustomerCode(sourceCustomerCode);
        txsRefundSplitRecord.setCustomerCode(txsSplitRecord.getCustomerCode());
        txsRefundSplitRecord.setState(TxsConstants.SplitRecordState.UNEXECUTED.code);
        txsRefundSplitRecord.setAmount(refundAmount);
        txsRefundSplitRecord.setProcedurefee(0L);//默认值为0，
        txsRefundSplitRecord.setCreateTime(now);
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(txsSplitRecord.getCustomerCode(), txsSplitRecord.getCustomerCode(), "1");
        txsRefundSplitRecord.setCustomerName(customerInfo.getName());
        txsRefundSplitRecord.setBackPayProcedureFee(txsRefundPreOrder.getBackpayProcedurefee());
        txsRefundSplitRecord.setRefundAllAmount(txsRefundPreOrder.getRefundFee());
        txsRefundSplitRecord.setBusinessMan(customerInfo.getBusinessMan());
        txsRefundSplitRecord.setBusinessManId(customerInfo.getBusinessManId());
        txsRefundSplitRecord.setCompanyName(customerInfo.getCompanyName());
        txsRefundSplitRecord.setCompanyId(customerInfo.getCompanyId());
        txsRefundSplitRecord.setApiAmount(refundAmount);
        txsRefundSplitRecord.setCashAmount(refundAmount);
        txsRefundSplitRecord.setCouponAmount(0L);
        txsRefundSplitRecord.setRealRefundFee(refundAmount);
        txsRefundSplitRecord.setTransactionType(txsSplitRecord.getTransactionType());

        //2022-11-04新增商户退款单号、实际退回金额（元）、分账对象属性
        txsRefundSplitRecord.setPayRealRefundFee(txsRefundPreOrder.getRealRefundFee());
        txsRefundSplitRecord.setSplitAttr(txsSplitRecord.getSplitAttr());
        txsRefundSplitRecord.setOutRefundNo(txsRefundPreOrder.getOutRefundNo());

        return txsRefundSplitRecord;
    }

    private Long getSplitInfoAmount(String customerCode, List<SplitInfo> splitInfoList) {
        if(splitInfoList == null || splitInfoList.size() == 0){
            return null;
        }
        for (SplitInfo splitInfo : splitInfoList) {
            if (customerCode.equals(splitInfo.getCustomerCode())) {
                return splitInfo.getAmount();
            }
        }
        return null;
    }

    /**
     * 更新商户订单，如果有分账信息，更新分账订单和分账记录金额
     *
     * @param txsRefundPreOrder
     * @param errorCode
     */
    @Logable(businessTag = "updateRefundPreOrder")
    @Transactional
    public void updateRefundPreOrder(TxsRefundPreOrder txsRefundPreOrder, String refundState, String errorCode, String errorMsg) {
        Date now = new Date();
        TxsRefundPreOrder txsRefundPreOrderUpdate = new TxsRefundPreOrder();
        txsRefundPreOrderUpdate.setId(txsRefundPreOrder.getId());
        txsRefundPreOrderUpdate.setPayState(refundState);
        if (!StringUtils.isBlank(errorCode)) {
            txsRefundPreOrderUpdate.setErrorCode(errorCode);
        }
        if (!StringUtils.isBlank(errorMsg)) {
            txsRefundPreOrderUpdate.setErrorMsg(errorMsg);
        }
        if (StringUtils.equals(refundState, TxsConstants.RefundState.SUCCESS.code) ||
                StringUtils.equals(refundState, TxsConstants.RefundState.FAIL.code)) {
            //updateTime,成功或失败更新
            txsRefundPreOrderUpdate.setUpdateTime(now);
        }
        if (org.apache.commons.lang.StringUtils.equals(refundState, TxsConstants.RefundState.SUCCESS.code)) {
            //如果成功了，那么更新endTime
            txsRefundPreOrderUpdate.setEndTime(now);
        }
        txsRefundPreOrderMapper.updateByPrimaryKeySelective(txsRefundPreOrderUpdate);
        List<TxsRefundSplitRecord> txsRefundSplitRecords = txsRefundSplitRecordMapper.selectByTransactionNo(txsRefundPreOrder.getTransactionNo());
        String refundSplitRecordState = null;
        if (StringUtils.equals(refundState, TxsConstants.RefundState.SUCCESS.code)) {
            refundSplitRecordState = TxsConstants.SplitRecordState.SUCCESS.code;
        } else if (StringUtils.equals(refundState, TxsConstants.RefundState.FAIL.code)) {
            refundSplitRecordState = TxsConstants.SplitRecordState.FAIL.code;
        }
        if (null != refundSplitRecordState) {
            for (TxsRefundSplitRecord txsRefundSplitRecord : txsRefundSplitRecords) {
                TxsRefundSplitRecord txsRefundSplitRecordUpdate = new TxsRefundSplitRecord();
                txsRefundSplitRecordUpdate.setId(txsRefundSplitRecord.getId());
                txsRefundSplitRecordUpdate.setState(refundSplitRecordState);
                txsRefundSplitRecordMapper.updateByPrimaryKeySelective(txsRefundSplitRecordUpdate);
            }
        }
    }


    @Logable(businessTag = "updateTxsPreOrderAddRefundingFee")
    public void updateTxsSplitRecordAddRefundFee(List<TxsRefundSplitRecord> refundSplitRecordList, TxsSplitOrder txsSplitOrder) {
        List<TxsSplitRecord> list = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
        for (TxsSplitRecord txsSplitRecord : list){
            long refundAmount = 0;
            TxsSplitRecord txsSplitRecordUpdate = new TxsSplitRecord();
            txsSplitRecordUpdate.setId(txsSplitRecord.getId());
            for (TxsRefundSplitRecord refundSplitRecord : refundSplitRecordList){
                if(refundSplitRecord.getCustomerCode().equals(txsSplitRecord.getCustomerCode())){
                    refundAmount = refundSplitRecord.getAmount();
                }
            }
            txsSplitRecordUpdate.setRefundFee((txsSplitRecord.getRefundFee() == null ? 0 : txsSplitRecord.getRefundFee()) + refundAmount);
            txsSplitRecordMapper.updateByPrimaryKeySelective(txsSplitRecordUpdate);
        }
    }

    /**
     * 更新预下单金额(退款中的金额减掉一笔,已退款增加一笔)
     *
     * @param txsPreOrder
     */
    @Logable(businessTag = "updateTxsPreOrderAddAmount")
    public int updateTxsPreOrderAddAmount(TxsPreOrder txsPreOrder, Long refundAmount) {
        TxsPreOrder txsPreOrderUpdate = new TxsPreOrder();
        txsPreOrderUpdate.setOrderId(txsPreOrder.getOrderId());
        txsPreOrderUpdate.setRefundFee((txsPreOrder.getRefundFee() == null ? 0 : txsPreOrder.getRefundFee()) + refundAmount);
        int i = txsPreOrderMapper.updateByPrimaryKeySelective(txsPreOrderUpdate);
        return i;
    }

    /**
     * 更新商户订单，如果有分账信息，更新分账订单和分账记录金额
     *
     * @param txsPreOrder
     * @param errorCode
     */
    @Logable(businessTag = "updatePreOrder", outputArgs = false)
    @Transactional(rollbackFor = Exception.class)
    public void updatePreOrder(TxsPreOrder txsPreOrder, String state, String errorCode, String errorMsg, String revokeTransactionNo) {
        commonService.payLog(txsPreOrder.getTransactionNo() + ":state-" + state);
        Date now = new Date();
        //更新txs_pre_order订单状态
        TxsPreOrder txsPreOrderUpdate = new TxsPreOrder();
        txsPreOrderUpdate.setOrderId(txsPreOrder.getOrderId());
        txsPreOrderUpdate.setPayState(state);
        if (!StringUtils.isBlank(errorCode)) {
            txsPreOrderUpdate.setErrorCode(errorCode);
        }
        if (!StringUtils.isBlank(errorMsg)) {
            txsPreOrderUpdate.setErrorMsg(errorMsg);
        }
        //updateTime,成功或失败更新
        txsPreOrderUpdate.setUpdateTime(now);
        txsPreOrderMapper.updateByPrimaryKeySelective(txsPreOrderUpdate);

        //更新txs_pay_trade_order订单状态
        TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNo(txsPreOrder.getTransactionNo());
        TxsPayTradeOrder txsPayTradeOrderUpdate = new TxsPayTradeOrder();
        txsPayTradeOrderUpdate.setId(txsPayTradeOrder.getId());
        txsPayTradeOrderUpdate.setState(TxsConstants.PayState.CANCEL.code);
        //updateTime,成功或失败更新
        txsPreOrderUpdate.setUpdateTime(now);
        txsPayTradeOrderMapper.updateByPrimaryKeySelective(txsPayTradeOrderUpdate);

        //更新分账订单状态
        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByThree(txsPreOrder.getOutTradeNo(), txsPreOrder.getCustomerCode(), null);
        TxsSplitOrder txsSplitOrderUpdate = new TxsSplitOrder();
        txsSplitOrderUpdate.setTransactionNo(txsSplitOrder.getTransactionNo());
        txsSplitOrderUpdate.setState(TxsConstants.SplitOrderState.REVOKE.code);
        txsSplitOrderUpdate.setUpdateTime(now);
        txsSplitOrderUpdate.setRevokeTransactionNo(revokeTransactionNo);
        txsSplitOrderMapper.updateByPrimaryKeySelective(txsSplitOrderUpdate);

        //更新分账记录状态
        List<TxsSplitRecord> txsSplitRecordList = txsSplitRecordMapper.selectByTransactionNo(txsSplitOrder.getTransactionNo());
        if (txsSplitRecordList != null && txsSplitRecordList.size() > 0) {
            for (TxsSplitRecord txsSplitRecord : txsSplitRecordList) {
                TxsSplitRecord txsSplitRecordUpdate = new TxsSplitRecord();
                txsSplitRecordUpdate.setId(txsSplitRecord.getId());
                txsSplitRecordUpdate.setUpdateTime(now);
                txsSplitRecordUpdate.setState(TxsConstants.SplitRecordState.REVOKE.code);
                txsSplitRecordUpdate.setRevokeTransactionNo(revokeTransactionNo);
                txsSplitRecordMapper.updateByPrimaryKeySelective(txsSplitRecordUpdate);
            }
        }

    }

    public void judgeProcedureCustomercode(TxsPayRequest request) {
        request.setProcedureCustomercode(null);
        request.setIsAccountSplitFeeAuth(Constants.YesOrNoByNum.NO.code);
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(request.getCustomerCode(), request.getCustomerCode(), "1");
        String procedureCustomercode = authInfoService.getAccountSplitProcedureCustomerAuthCustomer(customerInfo.getCustomerCode(), customerInfo.getPlatCustomerCode());
        if (!StringUtils.isEmpty(procedureCustomercode)) {
            request.setProcedureCustomercode(procedureCustomercode);
            request.setIsAccountSplitFeeAuth(Constants.YesOrNoByNum.YES.code);
        }
    }

    /**
     * 计算退回的收单手续费，按比例计算，直接抹去小数点后位数
     *
     * @return
     */
    @Logable(businessTag = "calcBackProcedure-by-ratio")
    public long calcBackProcedure(long refundAmount, long orgiPayAmount, long orgiPayProcedure) {

        BigDecimal bigRefundAmt = new BigDecimal(refundAmount);
        BigDecimal biOrgiPayAmount = new BigDecimal(orgiPayAmount);
        BigDecimal bigOrgiPayProcedure = new BigDecimal(orgiPayProcedure);
        BigDecimal result = bigOrgiPayProcedure.multiply(bigRefundAmt.divide(biOrgiPayAmount, 5, BigDecimal.ROUND_HALF_UP));
        result = result.setScale(0, BigDecimal.ROUND_HALF_UP);
        return result.longValue();
    }

    /**
     * 账户分账分账分账是否已经记账，即需要走确认的情况下,确认分账是否已经成功
     * select * from ACC_ACCOUNTVOUCHER where TRANSACTIONNO in ('66202410181438073400561_1');
     * 由于分账是出金操作,优先做为已经成功处理!
     * @param transactionNo
     * @return
     */
    @Logable(businessTag = "isAccountSplitConfirmSuccess")
    public boolean isAccountSplitConfirmSuccess(String transactionNo){
        AccountVoucher accountVoucherConfirm = accService.accountVoucherQuery(transactionNo + "_1");
        if (accountVoucherConfirm != null) {
            return true;
        }else{
            return false;
        }
    }

    @Logable(businessTag = "syncFenBuFangState")
    public CommonOuterResponse syncFenBuFangState(String transactionNo) {
        CommonOuterResponse commonOuterResponse = new CommonOuterResponse();
        if(!txsSplitRecordMapper.fenBuFenZhangHasRecords(transactionNo)){
            commonOuterResponse.setReturnMsg("txsSplitRecord尚无记录或不属于分步分账,不执行操作");
        }

        List<TxsSplitRecord> splitRecords = txsSplitRecordMapper.selectByTransactionNo(transactionNo);
        TxsSplitOrder txsSplitOrder = txsSplitOrderMapper.selectByTransactionNo(transactionNo);

        AccountVoucher accountVoucherConfirm = accService.accountVoucherQuery(transactionNo + "_1");

        if (accountVoucherConfirm != null
                && TxsConstants.VoucherStatus.SUCCESS.code.equals(accountVoucherConfirm.getStatus())) {
            self.updateReviewSplitState(new Date(), accountVoucherConfirm.getAccountDateTime(), splitRecords, txsSplitOrder, null,
                    TxsConstants.SplitRecordState.SUCCESS, TxsConstants.SplitOrderState.SUCCESS);
            commonOuterResponse.setReturnMsg("已经更新txsSplitOrder, txsSplitRecord为成功!");

        }else{
            commonOuterResponse.setReturnMsg("记账尚未成功");
        }
        return commonOuterResponse;
    }

    @Logable(businessTag = "compareList")
    public boolean compareList(List<SplitInfo> splitInfoListOri, List<SplitInfo> splitInfoListNow){
        if(null == splitInfoListOri || null == splitInfoListNow){
            return  false;
        }

        Collections.sort(splitInfoListOri, Comparator.comparing(SplitInfo::getCustomerCode));
        Collections.sort(splitInfoListNow, Comparator.comparing(SplitInfo::getCustomerCode));

        if (splitInfoListOri.size()!= splitInfoListNow.size()) {
            return false;
        }
        for (int i = 0; i < splitInfoListOri.size(); i++) {
            SplitInfo splitInfo1 = splitInfoListOri.get(i);
            SplitInfo splitInfo2 = splitInfoListNow.get(i);
            if (!Objects.equals(splitInfo1.getCustomerCode(), splitInfo2.getCustomerCode()) ||
                    !Objects.equals(splitInfo1.getAmount(), splitInfo2.getAmount()) ||
                    !Objects.equals(splitInfo1.getIsProcedureCustomer(), splitInfo2.getIsProcedureCustomer()) ||
                    !Objects.equals(splitInfo1.getSettleCycle(), splitInfo2.getSettleCycle()) ||
                    !Objects.equals(splitInfo1.getSplitRatio(), splitInfo2.getSplitRatio()) ||
                    !Objects.equals(splitInfo1.getReturnTo(), splitInfo2.getReturnTo())) {
                return false;
            }
        }
        return true;

    }
}
