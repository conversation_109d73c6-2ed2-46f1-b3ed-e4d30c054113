package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

import com.alibaba.fastjson.JSONObject;

/**
 * 用于封装发往kafka的对象
 * <AUTHOR>
 *
 */
public class CustomerInMq {
	/**
	 * 客户编码
	 */
	private String customerCode;
	/**
	 * 发生金额
	 * 使用的币种必须与对应支付交易单的币种一致 指原始金额
	 */
	private Long amount;
	/**
	 * 手续费
	 */
	private Long procedureFee;
	/**
	 * 到账金额
	 * 不同的支付指令和交易类型会有不同的计算规则，见“到账金额计算”
	 */
	private Long arrivedAmount;
	/**
	 * 该客户在该笔交易中使用的业务实例编码
	 */
	private String businessInstanceId;
	/**
	 * 决定该结算周期所使用的结算周期规则实例ID
	 */
	private Long settCycleRuleInstId;
	
	private Date settCycleStartTime;
	
	private Date settCycleEndTime;
	
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}
	public Long getArrivedAmount() {
		return arrivedAmount;
	}
	public void setArrivedAmount(Long arrivedAmount) {
		this.arrivedAmount = arrivedAmount;
	}
	public String getBusinessInstanceId() {
		return businessInstanceId;
	}
	public void setBusinessInstanceId(String businessInstanceId) {
		this.businessInstanceId = businessInstanceId;
	}
	public Long getSettCycleRuleInstId() {
		return settCycleRuleInstId;
	}
	public void setSettCycleRuleInstId(Long settCycleRuleInstId) {
		this.settCycleRuleInstId = settCycleRuleInstId;
	}
	public Date getSettCycleStartTime() {
		return settCycleStartTime;
	}
	public void setSettCycleStartTime(Date settCycleStartTime) {
		this.settCycleStartTime = settCycleStartTime;
	}
	public Date getSettCycleEndTime() {
		return settCycleEndTime;
	}
	public void setSettCycleEndTime(Date settCycleEndTime) {
		this.settCycleEndTime = settCycleEndTime;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSONObject.toJSONString(this);
	}
}
