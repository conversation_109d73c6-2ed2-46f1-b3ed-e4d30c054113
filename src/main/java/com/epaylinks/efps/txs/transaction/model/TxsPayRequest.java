package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.domain.CumBusinessParamInst;
import com.epaylinks.efps.common.business.pay.QuickPayParam;
import com.epaylinks.efps.common.business.pay.request.OrderInfo;
import com.epaylinks.efps.common.business.pay.request.PayMethod;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.domain.split.SplitInfo;
import com.epaylinks.efps.txs.util.SignUtils;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR> 代表商户发送的一个下单请求，不需持久化数据库，需缓存至Reids，使用EFPS收银台时返回的token代表该对象
 * <p>
 * 指定支付方式的微信公众号支付接口等和不指定支付方式的统一下单接口的入参均使用该对象建模
 */
public class TxsPayRequest {

    public static final String format = "yyyyMMddHHmmss";
    public static final String format_day = "yyyyMMdd";
    public static final String format_time = "HHmmssSSS";

    private String outTradeNo;
    private String customerCode;

    /**
     * 委托方客户编号
     */
    private String commissionedCustomerCode;

    private String targetCustomerCode;

    private String payMethod;
    private Long amount;
    private String currencyType;
    private Long procedureFee;
    private Trader payer;//代表付款方
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    private String errorCode;
    private String remark;

    private String clientIp;
    private String srcChannelType;
    private String orderInfo;
    private String notifyUrl;
    private String redirectUrl;

    private String attachData;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transactionStartTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transactionEndTime;
    private String terminalNo;
    private String terminalType;

    private Date lastConfirmTime;// 上次针对该订单调用确认下单的时间戳，用于控制点击确认支付的时间间隔
    private String transactionType;
    private Long preOrderId;// PreOrderId

    private static final Map<String, String> payMethodMap = new HashMap<>();

    static {
        payMethodMap.put("10", "13");
        payMethodMap.put("11", "13");
        payMethodMap.put("12", "13");
        payMethodMap.put("13", "13");
        payMethodMap.put("14", "13");
        payMethodMap.put("15", "13");
        payMethodMap.put("25", "14");
        payMethodMap.put("26", "14");
        payMethodMap.put("27", "14");
        payMethodMap.put("28", "14");
        payMethodMap.put("29", "14");
        payMethodMap.put("30", "14");
        payMethodMap.put("62", "25");
    }

    /**
     * 是否分账
     */
    private Boolean needSplit;
    /**
     * 分账信息列表
     */
    private List<SplitInfo> splitInfoList;
    /**
     * 分账结果异步通知URL
     */
    private String splitNotifyUrl;
    /**
     * 分账的通知和查询接口中原样返回
     */
    private String splitAttachData;

    private String rechargeMemCustCode;//会员充值的目标会员的客户编码，要求客户开通会员充值类支付业务

    private String signKey;//快捷支付，cum缓存四要素到redis的key

    private List<CumBusinessParamInst> cumBusinessParamInsts;

    private String agentCustomerCode;

    private String payPassWay;

    /**
     * 交易失败前台跳转地址（银联支付）
     */
    private String redirectFailUrl;
    /**
     * 发卡机构代码（银联支付）
     */
    private String issInsCode;

    private QuickPayParam quickPayParam;

    /**
     * 收单机构号
     */
    private String acqOrgCode;

    /**
     * 收单机构服务商 ID
     */
    private String acqSpId;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 终端信息，终端 IP、经纬度等信息， JSON 格式
     */
    private String termInfo;

    /**
     * 地区信息
     */
    private String areaInfo;

    /**
     * 用户标识，支付宝服务窗为用户的 buyer_id，微信公众号为用户的 openid，银联 JS 支付为用户的 userId
     */
    private String userId;

    /**
     * 子商户公众号id
     */
    private String subAppId;

    /**
     * 第三方平台商户号，如银联云闪付
     */
    private String platformCustomerCode;

    /**
     * 商户门店编号
     */
    private String storeId;

    /**
     * 商户操作员编号
     */
    private String operatorId;

    /**
     * 服务商自定义域
     */
    private String reqReserved;

    /**
     * 第三方平台自定义域，如云闪付
     */
    private String cupsReqReserved;

    /**
     * 付款 APP 名称
     */
    private String payerAppName;

    /**
     * 买家付款金额
     */
    private Long actualPayAmount;

    /**
     * 可打折金额，支付宝特有参数，单位：分
     */
    private Long discountableAmount;

    /**
     * 应结订单金额
     */
    private Long settlementAmount;

    /**
     * 交易来源1:EPSP平台,3:云闪付开放平台，4:旧系统
     */
    private String sourceChannel;
    /**
     * 二维码发行方,1： 服务商
     */
    private String qrCodeIssuer;
    /**
     * 费率(下划线隔开，第二部分表示封顶值，示例：30_10，不同的费率有不同的参数格式，对于固定费率：30表示30分钱；对于按比例：30表示费率为万分之30，封顶10分钱
     */
    private String procedureRate;

    /**
     * 费率模式(1：固定费率，2：按比例)
     */
    private Short rateMode;

    /**
     * 最大手续费,分
     */
    private Long maxFee;
    /**
     * 最小手续费，分
     */
    private Long minFee;

    private String businessCode;
    /**
     * 分账关系序列号
     */
    private String splitRelationId;
    /**
     * 是否重绑卡
     */
    private boolean rebindCard;

    /**
     * 会员编号
     */
    private String memberId;
    /**
     * 分账模式 1-普通分账 2-其他分账
     */
    private String splitModel;

    /**
     * 子商户号
     *
     * @return
     */
    private String subCustomerCode;
    /**
     * 交易商户号
     *
     * @return
     */
    private String tradeCustomerCode;

    /**
     * 商品金额
     */
    private Long commodityAmount;

    private String commodityInfoList;

    private String version;

    /**
     * 支付场景 条码支付，取值：bar_code; 声波支付，取值：wave_code 人脸识别，取值：face_code
     */
    private String scene;

    /**
     * 所属平台商户号
     */
    private String platCustomerCode;
    /**
     * 授权商户编号
     */
    private String authCustomerCode;

    /**
     * 交易来源
     */
    private String tradeSource;

    /**
     * 单笔固定费率，分
     */
    private Long feePer;

    /*
     * RequestSrc, 0-裸接口调用, 1-收银台. 1个字节
     */
    private String requestSrc = TxsConstants.RequestSrc.BAKEAPI.code;

    /**
     * D1交易节假日附加收费的费率万份比.比如值为200,表示万分之200,即百二
     */
    private String D1HolidayChargeRate;

    /**
     * D1交易节假日附加收费的单笔加收费用.单位为分. 比如3表示3分
     */
    private Long D1HolidayChargePer;

    /**
     * 是否实际应收D1交易节假日附加收费,1表是应收.0为不收(不收可能因为非D1,非节假日,非要收取的业务)
     */
    private String D1HolidayCharged;

    /*
     * 如果是按银行卡计费且是按区域性银行计费,则取值为1
     */
    private String chargedByBankCodeArea;
    /**
     * 渠道商户号
     */
    private String channelMchtNo;
    /**
     * 行业码类型 0-普通行业码，1-互联互通码
     */
    private String industryCodeType;
    /**
     * 行业码名称
     */
    private String industryName;
    /**
     * 机器码
     */
    private String machineCode;
    /**
     * 跨境平台商户号
     */
    private String crossPlatCustomerCode;
    /**
     * 收款方
     */
    private String receiveCustomerCode;
    /**
     * 手续费承担方. 目前用于账户分账.当手续费承担者不是发起者时有值.有值的话是授权的
     */
    private String procedureCustomercode;

    /**
     * 账户分账是否授权
     */
    private String isAccountSplitFeeAuth;


    public String getD1HolidayChargeRate() {
        return D1HolidayChargeRate;
    }

    public void setD1HolidayChargeRate(String d1HolidayChargeRate) {
        D1HolidayChargeRate = d1HolidayChargeRate;
    }

    public Long getD1HolidayChargePer() {
        return D1HolidayChargePer;
    }

    public void setD1HolidayChargePer(Long d1HolidayChargePer) {
        D1HolidayChargePer = d1HolidayChargePer;
    }

    public String getD1HolidayCharged() {
        return D1HolidayCharged;
    }

    public void setD1HolidayCharged(String d1HolidayCharged) {
        D1HolidayCharged = d1HolidayCharged;
    }

    public String getChargedByBankCodeArea() {
        return chargedByBankCodeArea;
    }

    public void setChargedByBankCodeArea(String chargedByBankCodeArea) {
        this.chargedByBankCodeArea = chargedByBankCodeArea;
    }

    public Long getFeePer() {
        return feePer;
    }

    public void setFeePer(Long feePer) {
        this.feePer = feePer;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public Long getCommodityAmount() {
        return commodityAmount;
    }

    public void setCommodityAmount(Long commodityAmount) {
        this.commodityAmount = commodityAmount;
    }

    public String getCommodityInfoList() {
        return commodityInfoList;
    }

    public void setCommodityInfoList(String commodityInfoList) {
        this.commodityInfoList = commodityInfoList;
    }

    public static Map<String, String> getPayMethodMap() {
        return payMethodMap;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getSplitModel() {
        return splitModel;
    }

    public void setSplitModel(String splitModel) {
        this.splitModel = splitModel;
    }

    public boolean isRebindCard() {
        return rebindCard;
    }

    public void setRebindCard(boolean rebindCard) {
        this.rebindCard = rebindCard;
    }

    public String getSplitRelationId() {
        return splitRelationId;
    }

    public void setSplitRelationId(String splitRelationId) {
        this.splitRelationId = splitRelationId;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getProcedureRate() {
        return procedureRate;
    }

    public void setProcedureRate(String procedureRate) {
        this.procedureRate = procedureRate;
    }

    public Short getRateMode() {
        return rateMode;
    }

    public void setRateMode(Short rateMode) {
        this.rateMode = rateMode;
    }

    public String getQrCodeIssuer() {
        return qrCodeIssuer;
    }

    public void setQrCodeIssuer(String qrCodeIssuer) {
        this.qrCodeIssuer = qrCodeIssuer;
    }

    public String getAcqOrgCode() {
        return acqOrgCode;
    }

    public void setAcqOrgCode(String acqOrgCode) {
        this.acqOrgCode = acqOrgCode;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getTermInfo() {
        return termInfo;
    }

    public void setTermInfo(String termInfo) {
        this.termInfo = termInfo;
    }

    public String getAreaInfo() {
        return areaInfo;
    }

    public void setAreaInfo(String areaInfo) {
        this.areaInfo = areaInfo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSubAppId() {
        return subAppId;
    }

    public void setSubAppId(String subAppId) {
        this.subAppId = subAppId;
    }

    public String getPlatformCustomerCode() {
        return platformCustomerCode;
    }

    public void setPlatformCustomerCode(String platformCustomerCode) {
        this.platformCustomerCode = platformCustomerCode;
    }

    public String getAcqSpId() {
        return acqSpId;
    }

    public void setAcqSpId(String acqSpId) {
        this.acqSpId = acqSpId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getReqReserved() {
        return reqReserved;
    }

    public void setReqReserved(String reqReserved) {
        this.reqReserved = reqReserved;
    }

    public String getCupsReqReserved() {
        return cupsReqReserved;
    }

    public void setCupsReqReserved(String cupsReqReserved) {
        this.cupsReqReserved = cupsReqReserved;
    }

    public String getPayerAppName() {
        return payerAppName;
    }

    public void setPayerAppName(String payerAppName) {
        this.payerAppName = payerAppName;
    }

    public Long getActualPayAmount() {
        return actualPayAmount;
    }

    public void setActualPayAmount(Long actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    public Long getDiscountableAmount() {
        return discountableAmount;
    }

    public void setDiscountableAmount(Long discountableAmount) {
        this.discountableAmount = discountableAmount;
    }

    public Long getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(Long settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public String getSourceChannel() {
        return sourceChannel;
    }

    public void setSourceChannel(String sourceChannel) {
        this.sourceChannel = sourceChannel;
    }

    private Boolean noCreditCards = false;

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getSrcChannelType() {
        return srcChannelType;
    }

    public void setSrcChannelType(String srcChannelType) {
        this.srcChannelType = srcChannelType;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        //各种入口都有OrderInfo字段，在此处统一截断为1000
        if (StringUtils.isNotBlank(orderInfo) && orderInfo.length() >= 1000) {
            OrderInfo orderInfoObj = JSON.parseObject(orderInfo, OrderInfo.class);
            while (true) {
                orderInfoObj.getGoodsList().remove(orderInfoObj.getGoodsList().size() - 1);
                if (JSON.toJSONString(orderInfoObj).length() < 1000) {
                    break;
                }
            }
            this.orderInfo = JSON.toJSONString(orderInfoObj);
        } else {
            this.orderInfo = orderInfo;
        }


    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public Date getTransactionStartTime() {
        return transactionStartTime;
    }

    public void setTransactionStartTime(Date transactionStartTime) {
        this.transactionStartTime = transactionStartTime;
    }

    public Date getTransactionEndTime() {
        return transactionEndTime;
    }

    public void setTransactionEndTime(Date transactionEndTime) {
        this.transactionEndTime = transactionEndTime;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public Boolean getNeedSplit() {
        return needSplit;
    }

    public void setNeedSplit(Boolean needSplit) {
        this.needSplit = needSplit;
    }

    public List<SplitInfo> getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(List<SplitInfo> splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

    public String getSplitNotifyUrl() {
        return splitNotifyUrl;
    }

    public void setSplitNotifyUrl(String splitNotifyUrl) {
        this.splitNotifyUrl = splitNotifyUrl;
    }

    public String getSplitAttachData() {
        return splitAttachData;
    }

    public void setSplitAttachData(String splitAttachData) {
        this.splitAttachData = splitAttachData;
    }

    public String getRechargeMemCustCode() {
        return rechargeMemCustCode;
    }

    public void setRechargeMemCustCode(String rechargeMemCustCode) {
        this.rechargeMemCustCode = rechargeMemCustCode;
    }


    /**
     * 计算非重复订单的不变量的md5
     */
    public String calcEncryResult() {
        Map<String, Object> map = new HashMap<>();
        map.put("amount", String.valueOf(getAmount()));
        map.put("currencyType", getCurrencyType());
        map.put("terminalNo", getTerminalNo());
        map.put("orderInfo", getOrderInfo());
        map.put("needSplit", getNeedSplit());
        map.put("splitInfoList", JSON.toJSONString(getSplitInfoList()));
        map.put("rechargeMemCustCode", getRechargeMemCustCode());//重复支付是否要添加会员客户编码进行判断
        return SignUtils.md5Result(map);
    }

    public Date getLastConfirmTime() {
        return lastConfirmTime;
    }

    public void setLastConfirmTime(Date lastConfirmTime) {
        this.lastConfirmTime = lastConfirmTime;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Long getPreOrderId() {
        return preOrderId;
    }

    public void setPreOrderId(Long preOrderId) {
        this.preOrderId = preOrderId;
    }





    /**
     * 通用的属性检查
     *
     * @param defaultTransValidSecond,如果不填入transactionEndTime，则使用transtart
     * @param maxTransValidSecond                                          注意支付金额和支付方式都变成了非必填字段，考虑一码付和统一下单接口
     */
    public void publicPropCheck(int maxTransValidSecond, int defaultTransValidSecond) {
        if (StringUtils.isBlank(getOutTradeNo())) {
            throw new AppException(TxsCode.OUTTRADENO_MUSTHAS.code);
        }
        if (StringUtils.isBlank(getCustomerCode())) {
            throw new AppException(TxsCode.CUSTOMERCODE_MUSTHAS.code);
        }
//		校验终端号
//		if (StringUtils.isBlank(getTerminalNo())) {
//			throw new AppException(TxsCode.TERMINALNO_MUSTHAS.code);
//		}
        if (!StringUtils.equals(getPayMethod(), PayMethod.WXSWEEPCODE.code) &&
                !StringUtils.equals(getPayMethod(), PayMethod.ALISWEEPCODE.code) &&
                !StringUtils.equals(getPayMethod(), PayMethod.UNIONSWEEPCODE.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.PROTOCOL_PAY.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.PROTOCOL_PAY_CREDIT.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.FZ_NOCARD_PAY.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.FZ_NOCARD_PAY_CREDIT.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.UNION_ONLINE.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.UNION_ONLINE_CREDIT.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.UNION_PAY.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.PERSON_GATEWAY_LOAN.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.PERSON_GATEWAY_DEBIT.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.ENTERPRISEUNION.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.UNION_JS.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.ENTRUST_PAY.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.ENTRUST_PAY_CREDIT.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.ENTRUSTPAY_BATCH.code)
                && !StringUtils.equals(getPayMethod(), PayMethod.ENTRUSTPAY_CREDIT_BATCH.code)
        ) {
            //主扫不需要判断clientIP
            if (StringUtils.isBlank(getClientIp())) {
                throw new AppException(TxsCode.CLIENTIP_MUSTHAS.code);
            }
        }
        if (getOrderInfo() == null) {
            throw new AppException(TxsCode.ORDERINFO_MUSTHAS.code);
        }
        if (getPayMethod() != null && getPayer() == null) {
            // 指定支付方式时,付款方信息必填
            throw new AppException(TxsCode.METHOD_HAS_PAYER.code);
        }
        if (!StringUtils.equals(getCurrencyType(), TxsConstants.CurrencyType.CNY.code)) {
            throw new AppException(TxsCode.NOTSUPPORT_CURRENCY.code);
        }
        if (getAmount() != null && getAmount() < 0) {
            throw new AppException(TxsCode.AMOUNT_ERROR.code);
        }
        if (StringUtils.isEmpty(getSrcChannelType())) {
            setSrcChannelType(TxsConstants.ChannelType.INTERNET.code);
        }
        if (!TxsConstants.ChannelType.INTERNET.code.equalsIgnoreCase(getSrcChannelType())
                && !TxsConstants.ChannelType.MOBILE_TERMINAL.code.equalsIgnoreCase(getSrcChannelType())) {
            // 渠道类型不存在
            throw new AppException(TxsCode.CHANNELTYPE_NOTSUPPORT.code);
        }

        if (getTransactionStartTime() == null) {
            throw new AppException(TxsCode.TRANSACTIONSTARTTIME_MUSTHAS.code);
        }

        if ((Math.abs((getCreateTime().getTime() - getTransactionStartTime().getTime())) / 1000) > 3600) {
            throw new AppException(TxsCode.START_TIME_OVERSIZE.code);
        }
        if (null == getTransactionEndTime()) {
            setTransactionEndTime(new Date(getTransactionStartTime().getTime() + defaultTransValidSecond * 1000l));
        }
        if ((getTransactionEndTime().getTime() - new Date().getTime()) < 0) {
            throw new AppException(TxsCode.NOW_MORETHAN_ENDTIME.code);
        }
        if ((getTransactionEndTime().getTime() - new Date().getTime()) > maxTransValidSecond * 1000l) {
            throw new AppException(TxsCode.TRANSACTIONENDTIME_ERROR.code);
        }
        if (StringUtils.isNotBlank(getSplitModel())) {
            if (TxsConstants.SplitModel.getSplitModel(getSplitModel()) == null) {
                throw new AppException(TxsCode.NEEDSPLIT_TRUE.code);
            }
        }
        if (getNeedSplit() != null && getNeedSplit()) {
            if (StringUtils.isNotBlank(getSplitModel()) && StringUtils.isBlank(getMemberId())) {
                //分账模型有值，但是主分账为空
                throw new AppException(TxsCode.PARAM_ERROR.code, TxsCode.PARAM_ERROR.message + "-splitModel");
            }
        } else {
            if (getSplitInfoList() != null) {
                throw new AppException(TxsCode.NEEDSPLIT_TRUE.code);
            }
        }

    }


    public Trader getPayer() {
        return payer;
    }

    public void setPayer(Trader payer) {
        this.payer = payer;
    }

    public String getSignKey() {
        return signKey;
    }

    public void setSignKey(String signKey) {
        this.signKey = signKey;
    }


    public List<CumBusinessParamInst> getCumBusinessParamInsts() {
        return cumBusinessParamInsts;
    }

    public void setCumBusinessParamInsts(List<CumBusinessParamInst> cumBusinessParamInsts) {
        this.cumBusinessParamInsts = cumBusinessParamInsts;
    }

    public String getAgentCustomerCode() {
        return agentCustomerCode;
    }

    public void setAgentCustomerCode(String agentCustomerCode) {
        this.agentCustomerCode = agentCustomerCode;
    }

    public String getPayPassWay() {
        return payPassWay;
    }

    public void setPayPassWay(String payPassWay) {
        this.payPassWay = payPassWay;
    }

    public String getRedirectFailUrl() {
        return redirectFailUrl;
    }

    public void setRedirectFailUrl(String redirectFailUrl) {
        this.redirectFailUrl = redirectFailUrl;
    }

    public String getIssInsCode() {
        return issInsCode;
    }

    public void setIssInsCode(String issInsCode) {
        this.issInsCode = issInsCode;
    }

    public QuickPayParam getQuickPayParam() {
        return quickPayParam;
    }

    public void setQuickPayParam(QuickPayParam quickPayParam) {
        this.quickPayParam = quickPayParam;
    }

    public String getSubCustomerCode() {
        return subCustomerCode;
    }

    public void setSubCustomerCode(String subCustomerCode) {
        this.subCustomerCode = subCustomerCode;
    }

    public Boolean getNoCreditCards() {
        return noCreditCards;
    }

    public void setNoCreditCards(Boolean noCreditCards) {
        this.noCreditCards = noCreditCards;
    }

    public String getTradeCustomerCode() {
        return tradeCustomerCode;
    }

    public void setTradeCustomerCode(String tradeCustomerCode) {
        this.tradeCustomerCode = tradeCustomerCode;
    }

    public String getCommissionedCustomerCode() {
        return commissionedCustomerCode;
    }

    public void setCommissionedCustomerCode(String commissionedCustomerCode) {
        this.commissionedCustomerCode = commissionedCustomerCode;
    }

    public String getTargetCustomerCode() {
        return targetCustomerCode;
    }

    public void setTargetCustomerCode(String targetCustomerCode) {
        this.targetCustomerCode = targetCustomerCode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRequestSrc() {
        return requestSrc;
    }

    public void setRequestSrc(String requestSrc) {
        this.requestSrc = requestSrc;
    }

    public String getPlatCustomerCode() {
        return platCustomerCode;
    }

    public void setPlatCustomerCode(String platCustomerCode) {
        this.platCustomerCode = platCustomerCode;
    }

    public String getAuthCustomerCode() {
        return authCustomerCode;
    }

    public void setAuthCustomerCode(String authCustomerCode) {
        this.authCustomerCode = authCustomerCode;
    }

    public String getTradeSource() {
        return tradeSource;
    }

    public void setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
    }

    public Long getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(Long maxFee) {
        this.maxFee = maxFee;
    }

    public Long getMinFee() {
        return minFee;
    }

    public void setMinFee(Long minFee) {
        this.minFee = minFee;
    }

    public String getChannelMchtNo() {
        return channelMchtNo;
    }

    public void setChannelMchtNo(String channelMchtNo) {
        this.channelMchtNo = channelMchtNo;
    }

    public String getIndustryCodeType() {
        return industryCodeType;
    }

    public void setIndustryCodeType(String industryCodeType) {
        this.industryCodeType = industryCodeType;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public String getMachineCode() {
        return machineCode;
    }

    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public String getCrossPlatCustomerCode() {
        return crossPlatCustomerCode;
    }

    public void setCrossPlatCustomerCode(String crossPlatCustomerCode) {
        this.crossPlatCustomerCode = crossPlatCustomerCode;
    }

    public String getReceiveCustomerCode() {
        return receiveCustomerCode;
    }

    public void setReceiveCustomerCode(String receiveCustomerCode) {
        this.receiveCustomerCode = receiveCustomerCode;
    }

    public String getProcedureCustomercode() {
        return procedureCustomercode;
    }

    public void setProcedureCustomercode(String procedureCustomercode) {
        this.procedureCustomercode = procedureCustomercode;
    }

    public static String getFormat() {
        return format;
    }

    public String getIsAccountSplitFeeAuth() {
        return isAccountSplitFeeAuth;
    }

    public void setIsAccountSplitFeeAuth(String isAccountSplitFeeAuth) {
        this.isAccountSplitFeeAuth = isAccountSplitFeeAuth;
    }
}