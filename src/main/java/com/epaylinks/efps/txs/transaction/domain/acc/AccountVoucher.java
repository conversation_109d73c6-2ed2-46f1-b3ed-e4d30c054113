package com.epaylinks.efps.txs.transaction.domain.acc;

import java.util.Date;

/**
 * 记账凭证pojo
 * <AUTHOR>
 *
 */
public class AccountVoucher {
	/**
	 * 凭证编号
	 */
    private String accountVouchcerNo;
    /**
     * YYYYMMDDHH24Miss，记账时间
     */
    private Date accountDateTime;
    /**
     * 提交时间
     */
    private Date commitDateTime;
    /**
     * 回滚时间
     */
    private Date rollbackDateTime;
    /**
     * 本次记账所产生的流水总数，账户流水条数
     */
    private Integer accountFlowCount;
    /**
     * 易票联订单编号，唯一索引
     */
    private String transactionNo;
    /**
     * 交易主体客户编码，索引
     */
    private String sourceCustomerList;
    /**
     * 交易对手客户编码，索引
     */
    private String targetCustomerList;
    /**
     * 支付渠道编码
     */
    private String payChannelCode;
    /**
     * 交易类型
     */
    private String transactionType;
    /**
     * 支付指令类型：1：内转 2：充值 3：提现 4：退款
     */
    private Integer payType;
    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 使用的记账模式ID
     */
    private Long accountModeId;
    /**
     * 提现账户类型
     */
    private String withdrawAccountType;
    /**
     * 渠道应付
     */
    private Long channelAmount;
    /**
     * 渠道成本
     */
    private Long channleCost;
    /**
     * 记账类别，来自接口入参realTimeAccounting 0：非实时 1：实时
     */
    private Integer realtimeAccounting;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 1：待结算 2：已结算 3：已回滚
     */
    private Integer status;
    /**
     * 是否已通知会计子系统 0：未通知 1：已通知
     */
    private Integer notifyCOT;
    /**
     * 通知会计子系统的时间
     */
    private Date notifycotDateTime;
    /**
     * 0：未支付成功 1：支付成功 记账凭证对应的交易订单是否已支付成功
     */
    private Integer paySuccess;

	public String getAccountVouchcerNo() {
		return accountVouchcerNo;
	}

	public void setAccountVouchcerNo(String accountVouchcerNo) {
		this.accountVouchcerNo = accountVouchcerNo;
	}

	public Date getAccountDateTime() {
		return accountDateTime;
	}

	public void setAccountDateTime(Date accountDateTime) {
		this.accountDateTime = accountDateTime;
	}

	public Date getCommitDateTime() {
		return commitDateTime;
	}

	public void setCommitDateTime(Date commitDateTime) {
		this.commitDateTime = commitDateTime;
	}

	public Date getRollbackDateTime() {
		return rollbackDateTime;
	}

	public void setRollbackDateTime(Date rollbackDateTime) {
		this.rollbackDateTime = rollbackDateTime;
	}

	public Integer getAccountFlowCount() {
		return accountFlowCount;
	}

	public void setAccountFlowCount(Integer accountFlowCount) {
		this.accountFlowCount = accountFlowCount;
	}

	public String getTransactionNo() {
		return transactionNo;
	}

	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}


	public String getPayChannelCode() {
		return payChannelCode;
	}

	public void setPayChannelCode(String payChannelCode) {
		this.payChannelCode = payChannelCode;
	}

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	
	public Long getAccountModeId() {
		return accountModeId;
	}

	public void setAccountModeId(Long accountModeId) {
		this.accountModeId = accountModeId;
	}

	public String getWithdrawAccountType() {
		return withdrawAccountType;
	}

	public void setWithdrawAccountType(String withdrawAccountType) {
		this.withdrawAccountType = withdrawAccountType;
	}

	public Long getChannelAmount() {
		return channelAmount;
	}

	public void setChannelAmount(Long channelAmount) {
		this.channelAmount = channelAmount;
	}

	public Long getChannleCost() {
		return channleCost;
	}

	public void setChannleCost(Long channleCost) {
		this.channleCost = channleCost;
	}

	public Integer getRealtimeAccounting() {
		return realtimeAccounting;
	}

	public void setRealtimeAccounting(Integer realtimeAccounting) {
		this.realtimeAccounting = realtimeAccounting;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getNotifyCOT() {
		return notifyCOT;
	}

	public void setNotifyCOT(Integer notifyCOT) {
		this.notifyCOT = notifyCOT;
	}

	public Date getNotifycotDateTime() {
		return notifycotDateTime;
	}

	public void setNotifycotDateTime(Date notifycotDateTime) {
		this.notifycotDateTime = notifycotDateTime;
	}

	public Integer getPaySuccess() {
		return paySuccess;
	}

	public void setPaySuccess(Integer paySuccess) {
		this.paySuccess = paySuccess;
	}

	public String getSourceCustomerList() {
		return sourceCustomerList;
	}

	public void setSourceCustomerList(String sourceCustomerList) {
		this.sourceCustomerList = sourceCustomerList;
	}

	public String getTargetCustomerList() {
		return targetCustomerList;
	}

	public void setTargetCustomerList(String targetCustomerList) {
		this.targetCustomerList = targetCustomerList;
	}

	
}