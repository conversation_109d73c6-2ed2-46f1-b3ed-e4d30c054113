package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.WeChatAppTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;

@Service("WeChatApp")
public class WeChatAppPayerServiceImpl implements PayerService {

	public static final String APP_ID="appId";
	@Override
	public Trader getPayer(Map<String, Object> map) {
		WeChatAppTrader weChatAppTrader =  new WeChatAppTrader();
		weChatAppTrader.setAppId(MapUtils.getString(map, APP_ID));
		weChatAppTrader.setType(TraderType.WeChatApp);
		return weChatAppTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		Payer payer = new Payer();
		payer.setPayerId(payerJson.getString(APP_ID));
		payer.setPayerType(TraderType.WeChatApp.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

	
}
