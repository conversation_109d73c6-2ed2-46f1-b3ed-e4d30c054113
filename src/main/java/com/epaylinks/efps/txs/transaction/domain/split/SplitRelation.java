package com.epaylinks.efps.txs.transaction.domain.split;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

public class SplitRelation {

    /**
     * 分账商户号
     */
	@NotBlank(message="分账商户号不能为空")
    private String customerCode;

    /**
     * 1表示该客户为扣除手续费的客户，默认值为0
     */
    private Integer isProcedureCustomer;

    /**
     * 分账比例，万分之n
     */
    @Min(0)
    @Max(10000)
    @NotNull(message="分账比例不能为空")
    private Integer ratio;
    
	public SplitRelation(String customerCode, Integer isProcedureCustomer, Integer ratio) {
		super();
		this.customerCode = customerCode;
		this.isProcedureCustomer = isProcedureCustomer;
		this.ratio = ratio;
	}
	
	public SplitRelation() {
		super();
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public Integer getIsProcedureCustomer() {
		return isProcedureCustomer;
	}

	public void setIsProcedureCustomer(Integer isProcedureCustomer) {
		this.isProcedureCustomer = isProcedureCustomer;
	}

	public Integer getRatio() {
		return ratio;
	}

	public void setRatio(Integer ratio) {
		this.ratio = ratio;
	}

}
