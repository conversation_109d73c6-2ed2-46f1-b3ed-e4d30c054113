package com.epaylinks.efps.txs.transaction.service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.business.cum.BankInfo;
import com.epaylinks.efps.common.business.cum.CumTerminalInfo;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.SettCycleRuleInst;
import com.epaylinks.efps.common.business.cum.customerBusiness.CumSplitBusiness;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.domain.CumBusinessParamInst;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.business.pay.request.trader.*;
import com.epaylinks.efps.common.business.txs.TxsCardBin;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.server.ServerEnv;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.Constants.BusinessCode;
import com.epaylinks.efps.common.util.Constants.BusinessParamCode;
import com.epaylinks.efps.common.util.Constants.EfpsAccountService;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.MD5Utils;
import com.epaylinks.efps.cust.model.Customer;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.service.*;
import com.epaylinks.efps.txs.transaction.dao.TxsCardBinMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsPayTradeOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsPreOrderMapper;
import com.epaylinks.efps.txs.transaction.model.ProcedureFeeResult;
import com.epaylinks.efps.txs.transaction.model.TxsPayRequest;
import com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import com.epaylinks.efps.txs.util.ReflectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.MalformedURLException;
import java.util.*;

/**
 * 支付，充值，确认支付服务
 *
 * <AUTHOR>
 */
@Service
public class TransactionService {

//    @Autowired
//    private TransactionTokenService transactionTokenService;
//
    @Autowired
    private TxsPayTradeOrderMapper txsPayTradeOrderMapper;

    @Autowired
    private TxsPreOrderMapper txsPreOrderMapper;


    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private SplitPayService splitPayService;

    @Autowired
    private TransactionService self;

    @Autowired
    private PayService payService;

    @Autowired
    private CumService cumService;
    @Autowired
    private CustService custService;

    @Autowired
    private AccService accService;

    @Autowired
    private HessianService hessianService;

    @Autowired
    private RiskCalcService riskCalcService;
    @Autowired
    private ReturnCodeUtil returnCodeUtil;

    @Autowired
    private RcService rcService;

    @Autowired
    private AuthInfoService authInfoService;

    @Autowired
    private CumCacheServiceImpl cumCacheService;
    
    @Autowired
    private CommonService commonService;


    private String ZFTransaction;

    @Value("${CZTransaction}")
    private String CZTransaction;

    @Value("${FZTransaction}")
    private String FZTransaction;

    @Value("${HYCZTransaction}")
    private String HYCZTransaction;

    @Value("${HYNZTransaction}")
    private String HYNZTransaction;

    
	@Autowired
	private TxsCardBinMapper txsCardBinMapper;
    @Autowired
    private ServerEnv serverEnv;


//    @Autowired
//    private TransactionQuickPayService transactionQuickPayService;
//    
    @Autowired
    private ProcedureAisistService procedureAisistService;
    
	@Autowired
	private CacheService cacheService;

    private static final Logger TransactionServiceLOGGER = LoggerFactory.getLogger(TransactionService.class);
//
//    /**
//     * 调用支付系统的网关支付的支付方式列表
//     */
//    //这里其实不包括代付类的. 2020-09-09
//    private static List<String> gateWayPayList = new ArrayList<String>() {
//        {
//            add(PayMethod.WECHAT_PUBLIC.code);// 微信公众号支�?
//            add(PayMethod.WX_MINI_PROGRAM.code);// 微信小程序支�?
//            add(PayMethod.PERSON_GATEWAY_LOAN.code);// 个人网银_贷记�?
//            add(PayMethod.PERSON_GATEWAY_DEBIT.code);// 个人网银_借记�?
//            add(PayMethod.ALIPAY_LIFE.code);// 支付宝生活号支付
//            add(PayMethod.AGENTPAY.code);// 代付
//            add(PayMethod.WXSWEEPCODE.code);// 微信扫码支付
//            add(PayMethod.ALISWEEPCODE.code);// 支付宝扫码支�?
//            add(PayMethod.QUICK_PAY.code);// 快捷支付储蓄�?
//            add(PayMethod.WXH5_PAY.code);// 微信H5支付
//            add(PayMethod.WECHAT_SCANINGPAY.code);// 微信被扫支付
//            add(PayMethod.WXAPP_PAY.code); //微信APP支付
//            add(PayMethod.ALI_SCANINGPAY.code);// 支付宝被扫支�?
//            add(PayMethod.UNION_PAY.code);// 手机银联支付
//            add(PayMethod.ENTERPRISEUNION.code);//企业银联
//            add(PayMethod.UNIONSWEEPCODE.code);// 银联主扫
//            add(PayMethod.UNION_SCANINGPAY.code);//银联被扫
//            add(PayMethod.QUICK_PAY_CREDIT.code);//快捷支付贷记�?
//            add(PayMethod.PROTOCOL_PAY.code);//储蓄卡协议支�?
//            add(PayMethod.PROTOCOL_PAY_CREDIT.code);//信用卡协议支�?
//            add(PayMethod.UNION_ONLINE.code);//银联在线储蓄�?
//            add(PayMethod.UNION_ONLINE_CREDIT.code);//银联在线信用�?
//            add(PayMethod.FZ_NOCARD_PAY.code);//无卡快捷储蓄卡分�?
//            add(PayMethod.FZ_NOCARD_PAY_CREDIT.code);//无卡快捷信用卡分�?
//            add(PayMethod.UNION_JS.code);
//            add(PayMethod.ENTRUST_PAY.code);
//            add(PayMethod.ENTRUST_PAY_CREDIT.code);
//        }
//    };
//
//    private static List<String> tranSportMethod = new ArrayList<String>() {
//        {
//            add(PayMethod.WECHAT_SCANINGPAY.code);
//            add(PayMethod.ALI_SCANINGPAY.code);
//            add(PayMethod.UNION_SCANINGPAY.code);
//            add(PayMethod.QUICK_PAY.code);
//            add(PayMethod.QUICK_PAY_CREDIT.code);
//            add(PayMethod.PROTOCOL_PAY.code);
//            add(PayMethod.PROTOCOL_PAY_CREDIT.code);
//            add(PayMethod.FZ_NOCARD_PAY.code);
//            add(PayMethod.FZ_NOCARD_PAY_CREDIT.code);
//            add(PayMethod.ENTRUST_PAY.code);
//            add(PayMethod.ENTRUST_PAY_CREDIT.code);
//        }
//    };
//
    /**
     * 检验客户信息是否正常，不正常抛出异�?customerType仅仅是为了抛出不同的异常而已
     *
     * @param customerCode
     */
//    @Logable(businessTag = "checkCumStatus")
    public void checkCumStatus(String customerCode, String customerType) {
        // 调用客户子系统查询客户信息，客户处于正常状�?
        Map<String, Long> payeeMap = cumCacheService.getCustomerStatus(customerCode);
        //cumService.getCustomerSattus(customerCode);
        if (payeeMap == null || payeeMap.isEmpty()) {
            commonService.payLog(customerCode + "not exists");
            // 客户信息响应失败 抛出异常
            if (customerType.equalsIgnoreCase(TxsConstants.CustomerType.PAYEE.code)) {
                throw new AppException(TxsCode.ERROR_PAYEEINFO.code,
                        new RuntimeException(TxsCode.ERROR_PAYEEINFO.message));
            }

            if (customerType.equalsIgnoreCase(TxsConstants.CustomerType.PAYER.code)) {
                throw new AppException(TxsCode.ERROR_PAYERINFO.code,
                        new RuntimeException(TxsCode.ERROR_PAYERINFO.message));
            }
        }
        //状态（1：正常；2：冻结；3：注销；4：止付；5：禁止入金）
        Long payeeStatus = payeeMap.get("0");
        if (payeeStatus != 1) {
            commonService.payLog(customerCode + "not exists");
            // 客户信息不正??抛出异常
			if (customerType.equalsIgnoreCase(TxsConstants.CustomerType.PAYEE.code)){
					if(payeeStatus != 4) {//止付时可以入金
                            throw new AppException(TxsCode.ERROR_PAYEEINFO.code,
                                new RuntimeException(TxsCode.ERROR_PAYEEINFO.message));
                    }
			}else if (customerType.equalsIgnoreCase(TxsConstants.CustomerType.PAYER.code)){
					if(payeeStatus != 5) {//禁止入金时可以出金
				        throw new AppException(TxsCode.ERROR_PAYERINFO.code,
						    new RuntimeException(TxsCode.ERROR_PAYERINFO.message));
					}
			}else {
				throw new AppException(TxsCode.ERROR_CUSTINFO.code);
			}  
        }	
    }


//    @Logable(businessTag = "decideBusinessInst-plat", format = Logable.Format.JSON, outputArgs=false, outputResult=false)
    public List<CustomerBusinessInstance> decidePlatBusinessInst(List<CustomerBusinessInstance> customerInstanceList,
                                                                 String payMethod, String businessCode, String transactionType) {
        List<CustomerBusinessInstance> businessInstanceList = new ArrayList<>();
        Date now = new Date();
        if (StringUtils.isNotBlank(businessCode)) {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now) && inst.getBusinessCode().equalsIgnoreCase(businessCode)) {
                    if (FZTransaction.equalsIgnoreCase(transactionType)
                            && !inst.getBusinessCode().startsWith(FZTransaction)
                            && !BusinessCode.CDFZ.code.equals(inst.getBusinessCode())) {
                        List<CumSplitBusiness> splitBusinessList = cumCacheService.getSplitBusiness(inst.getCustomerCode(), null, null);
                        //cumService.getSplitBusiness(inst.getCustomerCode(), inst.getBusinessCode(),null);
                        if (splitBusinessList == null || splitBusinessList.size() == 0) {
                            throw new AppException(TxsCode.NOT_OPEN_TRANS_FZ_BUSINESS.code, TxsCode.NOT_OPEN_TRANS_FZ_BUSINESS.message);
                        }
                    }
                    businessInstanceList.add(inst);
                }
            }
        } else if (transactionType.equalsIgnoreCase(FZTransaction)) {// 分账的交易类型，要求业务中包含该支付方式，且业务编码包含FZ两个字符。这个丑，但是先这样，也不是不可接受。如果后续还有更多的FZ等同场景，再考虑通用�?
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    for (String payMethodTemp : inst.getPayMethods()) {
                        if (payMethodTemp.equalsIgnoreCase(payMethod)) {
                            if (inst.getBusinessCode().startsWith(FZTransaction) || BusinessCode.CDFZ.code.equals(inst.getBusinessCode())) {
                                businessInstanceList.add(inst);
                            } else {
                                List<CumSplitBusiness> splitBusinessList = cumCacheService.getSplitBusiness(inst.getCustomerCode(), null, null);
                                if (splitBusinessList != null && splitBusinessList.size() > 0) {
                                    businessInstanceList.add(inst);
                                }
                            }
                        }
                    }
                }
            }
            if(businessInstanceList.size() > 0){
                return businessInstanceList;
            }
            throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code, TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.message);
        } else if (transactionType.equalsIgnoreCase(ZFTransaction)) {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    if (!inst.getBusinessCode().contains(FZTransaction) &&
                            !inst.getBusinessCode().contains("MemberRecharge")) {
                        for (String payMethodTemp : inst.getPayMethods()) {
                            if (payMethodTemp.equalsIgnoreCase(payMethod))
                                businessInstanceList.add(inst);
                        }
                    }
                }
            }
        } else if (transactionType.equalsIgnoreCase(HYCZTransaction)) {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    if (inst.getBusinessCode().contains("MemberRecharge")) {
                        for (String payMethodTemp : inst.getPayMethods()) {
                            if (payMethodTemp.equalsIgnoreCase(payMethod))
                                businessInstanceList.add(inst);
                        }
                    }
                }
            }
        } else {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    for (String payMethodTemp : inst.getPayMethods()) {
                        if (payMethodTemp.equalsIgnoreCase(payMethod))
                            businessInstanceList.add(inst);
                    }
                }
            }
        }
        if(businessInstanceList.size() > 0){
            return businessInstanceList;
        }
        throw new AppException(TxsCode.PAYMETHOD_NOTSUPPORT.code, TxsCode.PAYMETHOD_NOTSUPPORT.message);
    }

    /**
     * 选择适用的业务实例，只会返回唯一的业务实�?如果选择不到，则抛出异常
     * 业务约束�?
     * 可能有多个业务包含同一个支付方式：
     * 三种业务类型�?
     * YMF这种：支付业务处理过程在业务入口处即可明确业务编码；
     * 普通支付业务：例如微信公众号支付业务；
     * 用于分账的支付业务：例如微信公众号_分账支付业务�?
     * 所以这里还需要传一个交易类型过来，如果传了分账的交易类型，则选择分账版本的支付业�?
     *
     * @param customerInstanceList
     * @param payMethod
     * @param businessCode
     * @return
     */
//    @Logable(businessTag = "decideBusinessInst", outputArgs = false)
    public CustomerBusinessInstance decideBusinessInst(List<CustomerBusinessInstance> customerInstanceList,
                                                       String payMethod, String businessCode, String transactionType) {
        if (payMethod == null && businessCode == null)
            throw new AppException(TxsCode.PAYMETHOD_MUSTHAS.code);
        Date now = new Date();
        if (StringUtils.isNotBlank(businessCode)) {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now) && inst.getBusinessCode().equalsIgnoreCase(businessCode)) {
                    if (FZTransaction.equalsIgnoreCase(transactionType)
                            && !inst.getBusinessCode().startsWith(FZTransaction)
                            && !BusinessCode.CDFZ.code.equals(inst.getBusinessCode())) {
                        List<CumSplitBusiness> splitBusinessList = cumCacheService.getSplitBusiness(inst.getCustomerCode(), null, null);
                        if (splitBusinessList == null || splitBusinessList.size() == 0) {
                            throw new AppException(TxsCode.NOT_OPEN_TRANS_FZ_BUSINESS.code, TxsCode.NOT_OPEN_TRANS_FZ_BUSINESS.message);
                        }
                    }
                    return inst;
                }
            }
        } else if (transactionType.equalsIgnoreCase(FZTransaction)) {// 分账的交易类型，要求业务中包含该支付方式，且业务编码包含FZ两个字符。这个丑，但是先这样，也不是不可接受。如果后续还有更多的FZ等同场景，再考虑通用�?
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    for (String payMethodTemp : inst.getPayMethods()) {
                        if (payMethodTemp.equalsIgnoreCase(payMethod)) {
                            if (inst.getBusinessCode().startsWith(FZTransaction) || BusinessCode.CDFZ.code.equals(inst.getBusinessCode())) {
                                return inst;
                            } else {
                                List<CumSplitBusiness> splitBusinessList = cumCacheService.getSplitBusiness(inst.getCustomerCode(), null, null);
                                if (splitBusinessList != null && splitBusinessList.size() > 0) {
                                    return inst;
                                }
                            }
                        }
                    }
                }
            }
            throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code, TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.message);
        } else if (transactionType.equalsIgnoreCase(ZFTransaction)) {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    if (!inst.getBusinessCode().contains(FZTransaction) &&
                            !inst.getBusinessCode().contains("MemberRecharge")) {
                        for (String payMethodTemp : inst.getPayMethods()) {
                            if (payMethodTemp.equalsIgnoreCase(payMethod))
                                return inst;
                        }
                    }
                }
            }
        } else if (transactionType.equalsIgnoreCase(HYCZTransaction)) {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    if (inst.getBusinessCode().contains("MemberRecharge")) {
                        for (String payMethodTemp : inst.getPayMethods()) {
                            if (payMethodTemp.equalsIgnoreCase(payMethod))
                                return inst;
                        }
                    }
                }
            }
        } else {
            for (CustomerBusinessInstance inst : customerInstanceList) {
                if (inst.validAtTime(now)) {
                    for (String payMethodTemp : inst.getPayMethods()) {
                        if (payMethodTemp.equalsIgnoreCase(payMethod))
                            return inst;
                    }
                }
            }
        }
        throw new AppException(TxsCode.PAYMETHOD_NOTSUPPORT.code, TxsCode.PAYMETHOD_NOTSUPPORT.message);
    }
//
//    
//	    /*
//	     * 该方法因为是二次算费,对比第一次算费,不管其是否开分账.
//	     * 也不管其是否在有效期内
//	  *对于生产中未作兼容二次计费处理的商户和业务,这个函数多数返回null
//	  *调用时应作try catch, 如果有异常或返回null,应以放弃二次计费,以第一次计费为准
//	 */
//	@Logable(businessTag = "decideBusinessInstForSecendCalcFee", outputArgs = false)
//	public CustomerBusinessInstance decideBusinessInstForSecendCalcFee(List<CustomerBusinessInstance> customerInstanceList,
//	                                                   String businessCode, Short cardType) {
//	    if (cardType == null && businessCode == null)
//	        throw new AppException(TxsCode.BUSINESS_AND_CARDTYPE_MUSTHAS.code);
//	    
//	    CustomerBusinessInstance hafMatchInst = null;	//半匹配,只匹配业务未匹配卡,用于找组
//	    
//	    
//	    for (CustomerBusinessInstance inst : customerInstanceList) {
//	    	
//	        if (inst.getBusinessCode().equalsIgnoreCase(businessCode) && (null != inst.getSetDefault() && 1 == inst.getSetDefault().shortValue())) {
//	            hafMatchInst = inst;
//	            commonService.payLog("decideBusinessInstForSecendCalcFee-通过setDefault拿到了组所属的业务!");
//	            break;
//	        }
//	    	
//	        //如果没有default为1的, 则业务只会有一条! 2020-06-10伟烨
//	        if (inst.getBusinessCode().equalsIgnoreCase(businessCode) && hafMatchInst == null) {
//	            hafMatchInst = inst;
//	        }
//	    	
//	    }
//	    
//	    commonService.payLog("decideBusinessInstForSecendCalcFee-hafMatchInst:"
//	    		+hafMatchInst.getBusinessExamId()+";组:"+hafMatchInst.getChargeGroup());
//	    
//	    //
//	    //第一步, 如果找到了业务跟卡类别均一致,则直接返回那个
//	    //
//	    for (CustomerBusinessInstance inst : customerInstanceList) {
//	    	
//	        //可能会出现同一业务多个结算周期的情况,优先匹配setDefault,结算值为1的
//	        if (inst.getBusinessCode().equalsIgnoreCase(businessCode)  
//	        		&& cardType.equals(inst.getCardType()) 
//	        		&& (null != inst.getSetDefault() && 1 == inst.getSetDefault().shortValue())) {
//	        	commonService.payLog("decideBusinessInstForSecendCalcFee-不跨业务,找到了setDefault为1的:"+inst.getBusinessExamId());
//	            return inst;
//	        }
//
//	    }
//	    //
//	    //第一步, 如果找到了业务跟卡类别均一致,则直接返回那个
//	    //
//	    for (CustomerBusinessInstance inst : customerInstanceList) {
//	    	        
//	        if (inst.getBusinessCode().equalsIgnoreCase(businessCode)  && cardType.equals(inst.getCardType())) {
//	        	commonService.payLog("decideBusinessInstForSecendCalcFee-不跨业务,找到了非setDefault为1的:"+inst.getBusinessExamId());
//	            return inst;
//	        }
//	    }
//	    
//	    //
//	    //第二步,如果找不到,则使用组+卡类型找到对应的业务
//	    //
//	    if (null != hafMatchInst && StringUtils.isNotEmpty(hafMatchInst.getChargeGroup())) {
//	    	
//	    	
//	    	String chargeGroup = hafMatchInst.getChargeGroup();
//	    	commonService.payLog("decideBusinessInstForSecendCalcFee-尝试跨业务:"+chargeGroup);
//	    	
//	    	//可能会出现同一业务多个结算周期的情况,优先匹配setDefault,结算值为1的
//	        for (CustomerBusinessInstance inst : customerInstanceList) {
//	        	
//	            if (chargeGroup.equals(inst.getChargeGroup()) 
//	            		&& cardType.equals(inst.getCardType())
//	            		&& (null != inst.getSetDefault() && 1 == inst.getSetDefault().shortValue())) {
//	            	commonService.payLog("decideBusinessInstForSecendCalcFee-不跨业务,找到了setDefault为1的:"+inst.getBusinessExamId());
//	                return inst;
//	            }
//	        	
//	        }
//	        
//	        //然后再匹配不考虑 setDefault的
//	        for (CustomerBusinessInstance inst : customerInstanceList) {
//
//	            if (chargeGroup.equals(inst.getChargeGroup()) && cardType.equals(inst.getCardType())) {
//	            	commonService.payLog("decideBusinessInstForSecendCalcFee-不跨业务,找到了非setDefault为1的:"+inst.getBusinessExamId());
//	                return inst;
//	            }
//	        }
//	    }
//	    
//	    return null;
//	}  
//
//    /**
//     * 当请求参数的客户编码与头部客户编码不一致时，校验是否有代理商客�?
//     *
//     * @param customerCodeHead
//     * @param customerCode
//     */
//    public List<CumBusinessParamInst> checkCustomerCodeByDL(String customerCodeHead, String customerCode) {
//
//        CustomerInfo customerInfo = cumService.queryCustomerInfoByCustomerCode(customerCode);
//        String parentCustomerCode = null;
//        List<CumBusinessParamInst> cumBusinessParamInstByDL = null;
//        if (customerInfo == null) {
//            throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code, TxsCode.CUSTOMER_NOT_EXIST.message);
//        }
//        if (Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(customerInfo.getCustomerCategory())) {
//            String platCustomerCode = customerInfo.getPlatCustomerCode();
//            //使用简易商户交易，必须是平台商户，或者平台商户的下级
//            Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(customerCodeHead, platCustomerCode);
//            boolean isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
//            if (!isAncestorCustomer) {
//                throw new AppException(TxsCode.CUSTOMER_ATYPISM.code, TxsCode.CUSTOMER_ATYPISM.message);
//            }
//        } else {
//            // 判断customerCode是否ancestorCustomerCode的子孙商户，如果是 返回parentCustomerCode作为本次交易的代理
//            Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(customerCode, customerCodeHead);
//            boolean isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
//            if (!isAncestorCustomer) {
//                throw new AppException(TxsCode.CUSTOMER_ATYPISM.code, TxsCode.CUSTOMER_ATYPISM.message);
//            }
//            parentCustomerCode = (String) checkResult.get("parentCustomerCode");
//            if (StringUtils.isNotBlank(parentCustomerCode)) {
//                // 请求体中的customerCode与http头中的customerCode不同，返回错误
//                cumBusinessParamInstByDL = cumService.getCustomerBusinessParamInstByDL(
//                        parentCustomerCode, customerCode, 1, null, BusinessParamCode.AGENT_CUSTOMER_CODE.code);
//            }
//        }
//        return cumBusinessParamInstByDL;
//    }
//
//    /**
//     * 当请求参数的客户编码与头部客户编码不一致时，校验合法性
//     *
//     * @param customerCodeHead
//     * @param customerCode
//     */
//    public Constants.TradeSource checkCustomerCodeType(String customerCodeHead, String customerCode, List<CumBusinessParamInst> cumBusinessParamInsts) {
//
//        Constants.TradeSource tradeSource = null;
//        if (StringUtils.equals(customerCode, customerCodeHead)) {
//            tradeSource = Constants.TradeSource.customer_own;
//        } else {
//            CustomerInfo customerInfo = cumService.queryCustomerInfoByCustomerCode(customerCode);
//            if (customerInfo == null) {
//                throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code, TxsCode.CUSTOMER_NOT_EXIST.message);
//            }
//            boolean isAncestorCustomer = false;
//            if (Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(customerInfo.getCustomerCategory())) {
//                String platCustomerCode = customerInfo.getPlatCustomerCode();
//                //使用简易商户交易，必须是平台商户，或者平台商户的下级
//                Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(customerCodeHead, platCustomerCode);
//                isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
//                if (isAncestorCustomer) {
//                    tradeSource = Constants.TradeSource.simple_customer;
//                }
//            } else {
//                // 判断customerCode是否ancestorCustomerCode的子孙商户，如果是 返回parentCustomerCode作为本次交易的代理
//                Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(customerCode, customerCodeHead);
//                isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
//                if (isAncestorCustomer) {
//                    tradeSource = Constants.TradeSource.parent_customer;
//                    String parentCustomerCode = (String) checkResult.get("parentCustomerCode");
//                    if (StringUtils.isNotBlank(parentCustomerCode)) {
//                        // 请求体中的customerCode与http头中的customerCode不同，返回错误
//                        cumBusinessParamInsts = cumService.getCustomerBusinessParamInstByDL(
//                                parentCustomerCode, customerCode, 1, null, BusinessParamCode.AGENT_CUSTOMER_CODE.code);
//                    }
//                }
//            }
//            //既不是简易商户，也不是上级商户
//            if (!isAncestorCustomer) {
//                boolean authResult = authInfoService.checkAuthInfo(customerCode, customerCodeHead);
//                if (authResult) {
//                    tradeSource = Constants.TradeSource.auth_customer;
//                } else {
//                    throw new AppException(TxsCode.CUSTOMER_ATYPISM.code, TxsCode.CUSTOMER_ATYPISM.message);
//                }
//            }
//        }
//
//        return tradeSource;
//    }
//
    /**
     * @param customerCodeHead
     * @param customerCode
     */
//    @Logable(businessTag = "checkCustomerCodeAndHead")
    public void checkCustomerCodeAndHead(String customerCodeHead, String customerCode) {

        CustomerInfo customerInfo = cumService.queryCustomerInfoByCustomerCode(customerCode);
        if (Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(customerInfo.getCustomerCategory())) {
            String platCustomerCode = customerInfo.getPlatCustomerCode();
            //使用简易商户交易，必须是平台商户，或者平台商户的下级
            Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(customerCodeHead, platCustomerCode);
            boolean isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
            if (!isAncestorCustomer) {
                boolean authResult = authInfoService.checkAuthInfo(customerCode, customerCodeHead);
                if (!authResult) {
                    throw new AppException(TxsCode.CUSTOMER_ATYPISM.code, TxsCode.CUSTOMER_ATYPISM.message);
                }
            }
        } else {
            // 判断customerCode是否ancestorCustomerCode的子孙商户，如果是 返回parentCustomerCode作为本次交易的代理
            Map<String, Object> checkResult = cumService.checkIsAncestorAndReturnParentCustomer(customerCode, customerCodeHead);
            boolean isAncestorCustomer = (boolean) checkResult.get("isAncestorCustomer");
            if (!isAncestorCustomer) {
                boolean authResult = authInfoService.checkAuthInfo(customerCode, customerCodeHead);
                if (!authResult) {
                    throw new AppException(TxsCode.CUSTOMER_ATYPISM.code, TxsCode.CUSTOMER_ATYPISM.message);
                }
            }
        }
    }
//
//
//    /**
//     * 计算兑换后金�?
//     *
//     * @param businessExamId
//     * @param amount
//     * @return
//     */
//    @Logable(businessTag = "calcExchangeAmount")
//    public Long calcExchangeAmount(String businessExamId, Long rAmount, Long vAmount) {
//        List<CumBusinessParamInst> cumBusinessParamInsts = cumCacheService.getCustomerBusinessParamInst(businessExamId);
//        Long amount = 0L;
//        if (rAmount != null) {
//            for (CumBusinessParamInst cumBusinessParamInst : cumBusinessParamInsts) {
//                if (cumBusinessParamInst.getCode().equals(BusinessParamCode.SUBSCRIPTION_RATIO.code)) {
//                    BigDecimal bigDecimal = new BigDecimal(cumBusinessParamInst.getValue());
//                    BigDecimal bigDecimalAmount = new BigDecimal(rAmount);
//                    amount = bigDecimal.multiply(bigDecimalAmount).longValue();
//                }
//            }
//        }
//        if (vAmount != null) {
//            for (CumBusinessParamInst cumBusinessParamInst : cumBusinessParamInsts) {
//                if (cumBusinessParamInst.getCode().equals(BusinessParamCode.SUBSCRIPTION_RATIO.code)) {
//                    BigDecimal bigDecimal = new BigDecimal(cumBusinessParamInst.getValue());
//                    BigDecimal bigDecimalAmount = new BigDecimal(vAmount);
//                    //四舍五入
//                    amount = bigDecimalAmount.divide(bigDecimal, 0, RoundingMode.HALF_UP).longValue();
//                }
//            }
//        }
//        return amount;
//    }
//
//    /**
//     * 统一支付，返回值包含收银台地址（需要金额判断）
//     *
//     * @param customerCodeHead
//     * @param unifiedPaymentRequest
//     * @param transactionType
//     * @return
//     */
//    @Logable(businessTag = "unifiedPayForCashier")
//    public UnifiedPaymentResponse unifiedPayForCashier(String customerCodeHead,
//                                                       UnifiedPaymentRequest unifiedPaymentRequest, String transactionType) {
//        if (unifiedPaymentRequest.getPayAmount() == null || unifiedPaymentRequest.getPayAmount() <= 0)
//            throw new AppException(TxsConstants.INVALID_PARAM);
//        return self.unifiedPay(customerCodeHead, unifiedPaymentRequest, transactionType);
//    }
//
//    /**
//     * 统一支付，返回值中包含收银台地址（不需要金额判断，例如一码付�?
//     *
//     * @param customerCodeHead
//     * @param unifiedPaymentRequest unifiedPaymentRequest:允许支付方式为空(商户下单场景)，也允许金额为空(一码付场景)
//     * @param transactionType
//     * @return
//     */
//    @Logable(businessTag = "unifiedPaymentRequest")
//    public UnifiedPaymentResponse unifiedPay(String customerCodeHead, UnifiedPaymentRequest unifiedPaymentRequest,
//                                             String transactionType) {
//        UnifiedPaymentResponse unifiedPaymentResponse = new UnifiedPaymentResponse();
//        // 当前时间
//        Date now = new Date();
//        if (unifiedPaymentRequest == null) {
//            throw new AppException(TxsConstants.INVALID_PARAM);
//        }
//        List<CumBusinessParamInst> cumBusinessParamInsts = null;
//        Constants.TradeSource tradeSource = Constants.TradeSource.customer_own;
//        if (!StringUtils.equals(unifiedPaymentRequest.getCustomerCode(), customerCodeHead)) {
//            tradeSource = self.checkCustomerCodeType(customerCodeHead, unifiedPaymentRequest.getCustomerCode(), cumBusinessParamInsts);
//            if (Constants.TradeSource.auth_customer.equals(tradeSource)) {
//                unifiedPaymentRequest.setAuthCustomerCode(customerCodeHead);
//            }
//
//        }
//        String token = null;
//        TxsPreOrder txsPreOrderCheck = self.checkCumStatusAndGetPreOrder(unifiedPaymentRequest.getOutTradeNo(),
//                unifiedPaymentRequest.getCustomerCode());
//        Long beginTime;
//        Long endTime;
//        if (txsPreOrderCheck != null) {// 重复支付
//            TxsPayRequest request = TxsPayRequest.createTxsPayRequest(unifiedPaymentRequest, now, transactionType,
//                    transactionTime, endTimeSecond, cumBusinessParamInsts);
//
//            checkCustomerInfo(unifiedPaymentRequest, request);
//            token = self.processRepeatRequest(request, now, txsPreOrderCheck);
//            beginTime = request.getTransactionStartTime().getTime();
//            endTime = request.getTransactionEndTime().getTime();
//        } else {// 非重复支�?
//            TxsPayRequest request = TxsPayRequest.createTxsPayRequest(unifiedPaymentRequest, now, transactionType,
//                    transactionTime, endTimeSecond, cumBusinessParamInsts);
//            request.setRequestSrc(TxsConstants.RequestSrc.CASH.code);
//            request.setTradeSource(tradeSource.code);
//            checkCustomerInfo(unifiedPaymentRequest, request);
//            token = self.processNotRepeatRequest(request, now);
//            beginTime = request.getTransactionStartTime().getTime();
//            endTime = request.getTransactionEndTime().getTime();
//        }
//        String url = MessageFormat.format("{0}?token={1}", cashierUrl, token);
//        unifiedPaymentResponse.setReturnCode(TxsConstants.detailReturnCode.RETURN_SUCCESS.code);
//        unifiedPaymentResponse.setOutTradeNo(unifiedPaymentRequest.getOutTradeNo());
//        unifiedPaymentResponse.setCasherUrl(url);
//        unifiedPaymentResponse.setOrderToken(token);
//        return unifiedPaymentResponse;
//    }
//
//    /**
//     * 处理重复支付，返回缓存Request成功后的token
//     *
//     * @param request
//     * @param now
//     * @param txsPreOrderCheck
//     * @param businessCode:可为空，如果为空，则使用request中的支付方式来选择业务
//     * @return
//     */
//    @Logable(businessTag = "processRepeatRequest")
//    public String processRepeatRequest(TxsPayRequest request, Date now, TxsPreOrder txsPreOrderCheck) {
//        //非来自收银台的订单,不允许重复发起支付
//        if (!TxsConstants.RequestSrc.CASH.code.equals(txsPreOrderCheck.getRequestSrc())) {
//            throw new AppException(TxsCode.REPEAT_ORDER.code);
//        }
//        //校验订单状�?
//        checkTxsPreOrderState(txsPreOrderCheck);
//        request.setPreOrderId(txsPreOrderCheck.getOrderId());
//        String inputEncryResult = request.calcEncryResult();
//        if (!StringUtils.equals(inputEncryResult, txsPreOrderCheck.getEncryResult())) {
//            throw new AppException(TxsCode.REPEATPARAM_ATYPISM.code);
//        }
//        if (request.getTransactionEndTime().getTime() < txsPreOrderCheck.getTransactionEndTime().getTime()) {
//            // 传入的结束时间小于订单的结束时间
//            throw new AppException(TxsCode.REPEATENDTIME_ERROR.code);
//        }
//        // 因为重复支付不允许修改分账信息，因此此处不用再调用分账的相关逻辑
//        TxsPreOrder newPre = self.createTxsPreOrderForUpdate(request, txsPreOrderCheck.getOrderId());
//        if (txsPreOrderMapper.updateByPrimaryKeySelectiveForRepeatRequest(newPre) != 1)
//            throw new AppException(TxsCode.REPEAT_PREORDERSUCCESS.code);
//        request.setTransactionType(txsPreOrderCheck.getTransactionType());
//        String token = self.cacheOrder(request);
//        return token;
//    }
//
//    public void checkTxsPreOrderState(TxsPreOrder txsPreOrderCheck) {
//        if (txsPreOrderCheck.getPayState().equals(TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code)) {
//            throw new AppException(TxsCode.REPEAT_PREORDERSUCCESS.code,
//                    new RuntimeException(TxsCode.REPEAT_PREORDERSUCCESS.message));
//        }
//        if (txsPreOrderCheck.getPayState().equals(TxsConstants.PreOrderState.CANCEL.code)
//                || null != txsPreOrderCheck.getCancelState()) {
//            throw new AppException(TxsCode.ORDER_CANCEL.code);
//        }
//        if (txsPreOrderCheck.getPayState().equals(TxsConstants.PreOrderState.CLOSE.code)) {
//            throw new AppException(TxsCode.ORDER_CLOSE.code);
//        }
//    }
//
//    /**
//     * 处理非重复支付，返回缓存中的Request对象的token
//     *
//     * @param request
//     * @param now
//     * @param transactionType
//     */
//    @Logable(businessTag = "processNotRepeatRequest")
//    @Transactional
//    public String processNotRepeatRequest(TxsPayRequest request, Date now) {
//
//        if (request.getNeedSplit() != null && request.getNeedSplit()) {
//
//            //即不允许needSplit为true且同时传递rechargeMemCustCode参数
//            if (StringUtils.isNotBlank(request.getRechargeMemCustCode())) {
//                throw new AppException(TxsCode.NEEDSPLIT_RECHARGEMEMCUSTCODE_NOTSAMEEXIST.code);
//            }
//            request.setTransactionType(FZTransaction);
//            self.createAndInsertPreOrder(request);
//            //是否延迟分账，都先校验主分账商户
//            splitPayService.checkSplitMain(request.getCustomerCode(), request.getMemberId(), request.getSplitModel());
//
//            if (request.getSplitInfoList() != null && request.getSplitInfoList().isEmpty() == false) {
//                splitPayService.splitCommon(request.getOutTradeNo(), request.getCustomerCode(),
//                        request.getSplitNotifyUrl(), request.getSplitInfoList());
//            } else if (StringUtils.isNotBlank(request.getSplitRelationId())) {
//                splitPayService.splitByRelationId(request.getOutTradeNo(), request.getCustomerCode(),
//                        request.getSplitNotifyUrl(), request.getAmount(), request.getSplitRelationId());
//            }
//        } else if (StringUtils.isNotBlank(request.getRechargeMemCustCode())) {
//            //rechargeMemCustCode指定客户必须是“易票联客户会员”且其父客户编码必须等于入参消息体中的customerCode
//            if (!request.getRechargeMemCustCode().equals(request.getCustomerCode()) && !cumService.checkMemberCustomerCode(request.getRechargeMemCustCode(), request.getCustomerCode())) {
//                throw new AppException(TxsCode.MEMBER_NOTBELONG_MERCHANT.code);
//            }
//            request.setTransactionType(HYCZTransaction);
//            self.createAndInsertPreOrder(request);
//            self.createAndInsertMemberInsideTransOrder(request, null, null, request.getOutTradeNo(), request.getCustomerCode(), null);
//
//        } else {
//            self.createAndInsertPreOrder(request);
//        }
//        String token = self.cacheOrder(request);
//        return token;
//
//    }
//
//    /**
//     * 创建会员内转订单
//     *
//     * @param request
//     * @param vAmount       虚拟金额
//     * @param outTradeNo    商户调用内转时必�?
//     * @param outPayTradeNo
//     * @param outPayTradeNo
//     * @return customerCode
//     */
//    @Logable(businessTag = "createAndInsertMemberInsideTransOrder")
//    public TxsMemberInsideTransOrder createAndInsertMemberInsideTransOrder(TxsPayRequest request, Long vAmount,
//                                                                           String outTradeNo, String outPayTradeNo, String customerCode, String sharedInfo) {
//        TxsMemberInsideTransOrder txsMemberInsideTransOrder = self.createTxsMemberInsideTransOrder(request, vAmount, outTradeNo, outPayTradeNo, customerCode, sharedInfo);
//        try {
//            txsMemberInsideTransOrderMapper.insertSelective(txsMemberInsideTransOrder);
//            return txsMemberInsideTransOrder;
//        } catch (Exception e) {
//            self.logException(e);
//            throw new AppException(TxsCode.INSERTORDER_EXCEPTION.code);
//        }
//    }
//
//    @Logable(businessTag = "createTxsMemberInsideTransOrder")
//    public TxsMemberInsideTransOrder createTxsMemberInsideTransOrder(TxsPayRequest txsPayRequest, Long vAmount,
//                                                                     String outTradeNo, String outPayTradeNo, String customerCode, String sharedInfo) {
//        TxsMemberInsideTransOrder txsMemberInsideTransOrder = new TxsMemberInsideTransOrder();
//        Date now = new Date();
//        String random = String.format("%06d", sequenceService.nextValue("memberInside"));
//        String dateString = DateFormatUtils.format(now, TxsPayRequest.format);
//        String transactionNo = HYNZTransaction + dateString + random;
//        txsMemberInsideTransOrder.setTransactionNo(transactionNo);
//        txsMemberInsideTransOrder.setOutTradeNo(outTradeNo);
//        txsMemberInsideTransOrder.setOutPayTradeNo(outPayTradeNo);
//        txsMemberInsideTransOrder.setState(TxsConstants.InsideState.UNEXECUTED.code);
//        txsMemberInsideTransOrder.setCreateTime(now);
//
//
//        //转出客户
//        ProcedureFeeResult sourceProcedureFeeResult = self.calcProcedure(txsPayRequest.getCustomerCode(), txsPayRequest.getAmount(), null, 
//        		EfpsAccountService.MEMBER_INSIDE_PAY.code, HYNZTransaction, null);
//        txsMemberInsideTransOrder.setProcedureFee(sourceProcedureFeeResult.getProcedureFee());
//        txsMemberInsideTransOrder.setSrcCustomerCode(txsPayRequest.getCustomerCode());
//        txsMemberInsideTransOrder.setSrcBusinessInstId(sourceProcedureFeeResult.getBusinessInst().getBusinessExamId());
//        txsMemberInsideTransOrder.setSrcBusinessCode(sourceProcedureFeeResult.getBusinessInst().getBusinessCode());
//
//        //转入客户
//        ProcedureFeeResult targetProcedureFeeResult = self.calcProcedure(txsPayRequest.getRechargeMemCustCode(), txsPayRequest.getAmount(), null, 
//        		EfpsAccountService.MEMBER_INSIDE_PAY.code, HYNZTransaction, null);
//        txsMemberInsideTransOrder.setTargetCustomerCode(txsPayRequest.getRechargeMemCustCode());
//        txsMemberInsideTransOrder.setTargetBusinessInstId(targetProcedureFeeResult.getBusinessInst().getBusinessExamId());
//        txsMemberInsideTransOrder.setTargetBusinessCode(targetProcedureFeeResult.getBusinessInst().getBusinessCode());
//
//        txsMemberInsideTransOrder.setRemark(txsPayRequest.getRemark());
//        txsMemberInsideTransOrder.setCustomerCode(customerCode);
//        txsMemberInsideTransOrder.setCurrency(TxsConstants.CurrencyType.VGG.code);
//        if (vAmount != null) {
//            txsMemberInsideTransOrder.setAmount(vAmount);
//        } else {
//            vAmount = self.calcExchangeAmount(sourceProcedureFeeResult.getBusinessInst().getBusinessExamId(), txsPayRequest.getAmount(), null);
//            txsMemberInsideTransOrder.setAmount(vAmount);
//        }
//        txsMemberInsideTransOrder.setSharedInfoList(sharedInfo);
//        return txsMemberInsideTransOrder;
//    }
//
//    /**
//     * 生成和插入PreOrder
//     *
//     * @param request
//     */
//    public TxsPreOrder createAndInsertPreOrder(TxsPayRequest request) {
//        TxsPreOrder preOrder = self.createTxsPreOrder(request, sequenceService.nextValue("preOrder"));
//        request.setPreOrderId(preOrder.getOrderId());
//        try {
//            txsPreOrderMapper.insertSelective(preOrder);
//            return preOrder;
//        } catch (Exception e) {
//            e.printStackTrace();
//            self.logException(e);
//            throw new AppException(TxsCode.REPEAT_ORDER.code);
//        }
//    }

    /**
     * 1、检查收款方状态是否正常，
     * 2、查询PreOrder记录，如果查询不到，返回�?
     *
     * @param outTradeNo
     * @param customerCode
     * @return
     */
    public TxsPreOrder checkCumStatusAndGetPreOrder(String outTradeNo, String customerCode) {
        //检查收款方状态是否正�?
        self.checkCumStatus(customerCode, TxsConstants.CustomerType.PAYER.code);
        TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(outTradeNo, customerCode);
        return txsPreOrder;
    }
//
//
//    /**
//     * 转换简易商户为平台商户
//     *
//     * @param customerCode
//     * @return
//     */
//    public String switchPlatCustomerCode(String customerCode, String splitModel) {
//        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(customerCode, customerCode, "1");
//
//        if (customerInfo == null) {
//            throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code);
//        }
//        String platCustomerCode = customerInfo.getCustomerCode();
//        if (Constants.customerCategory.EFPS_CUSTOMER_SIMPLE.code.equals(customerInfo.getCustomerCategory())) {
//            if (StringUtils.isBlank(customerInfo.getPlatCustomerCode())) {
//                throw new AppException(TxsCode.PLAT_CUSTOMERCODE_EMPTY.code);
//            }
//            platCustomerCode = customerInfo.getPlatCustomerCode();
//
//        } else if (TxsConstants.SplitModel.ORTHER.code.equals(splitModel)) {
//            if (StringUtils.isNotBlank(customerInfo.getPlatCustomerCode())) {
//                platCustomerCode = customerInfo.getPlatCustomerCode();
//            }
//        }
//        return platCustomerCode;
//
//    }
//

    /**
     * 如果有手续费承担商户,在插表前再校验一下,以防前面request被滥填
     * 1.如果有手续费承担且手续费承担者不是交易发起才校验;
     * 2.校验时先校验商户本身有无被授权;
     * 3.没有的情况下再校验所属的平台商有无被授权
     *
     * @param request
     */
    public void checkProcedureCustomercodeValid(TxsPayRequest request){

        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(request.getCustomerCode(), request.getCustomerCode(), "1");
        if (StringUtils.isNotEmpty(request.getProcedureCustomercode()) && !request.getProcedureCustomercode().equals(request.getCustomerCode())) {

            if(!authInfoService.checkAuthInfoByType(request.getProcedureCustomercode(), request.getCustomerCode(), Constants.AuthType.accSplitFeePayer.code)){
                //如果本身没有被授权,再看看父级有无授权
                if(StringUtils.isNotEmpty(customerInfo.getPlatCustomerCode())){
                    if(!authInfoService.checkAuthInfoByType(request.getProcedureCustomercode(), customerInfo.getPlatCustomerCode(), Constants.AuthType.accSplitFeePayer.code)){
                        throw new AppException(TxsCode.PRODUCE_CUSTOMER_ERROR.code, TxsCode.PRODUCE_CUSTOMER_ERROR.message);
                    }
                }else{
                    throw new AppException(TxsCode.PRODUCE_CUSTOMER_ERROR.code, TxsCode.PRODUCE_CUSTOMER_ERROR.message);
                }
            }

        }
    }
    public TxsPreOrder createTxsPreOrder(TxsPayRequest request, Long dbId) {
        TxsPreOrder txsPreOrder = new TxsPreOrder();

        checkProcedureCustomercodeValid(request);
        ReflectUtils.copyPropertiesIgnoreNull(txsPreOrder, request);//copy 非空值到txsPreOrder进行存储
        txsPreOrder.setSettlementAmount(request.getAmount());
        txsPreOrder.setCashAmount(request.getAmount());

        if (Constants.SourceChannel.UNION_YUN.code.equals(request.getSourceChannel())) {
            Customer customer = custService.queryCustomerByCustomerNo(request.getCustomerCode());
            //服务商号和进件时不一�?
            if (customer == null ||
                    (!request.getAcqSpId().equals(customer.getServiceCustomerNo())
                            && !request.getAcqSpId().equals(customer.getPlatCustomerNo()))) {
                throw new AppException(TxsCode.SERVICE_CUSTOMER_ERROR.code);
            }
            txsPreOrder.setPlatformCustomerCode(customer.getCustomerUid());
        }
        if (request.getPayer() != null) {
            txsPreOrder.setPayer(request.getPayer().toString());

            String bankCardNoMiwen = "";//密文
            String bankCardNoDec = "";//明文
            try {
                if (request.getPayer() instanceof UnionOnlineTrader) {
                    UnionOnlineTrader unionOnlineTrader = (UnionOnlineTrader) request.getPayer();
                    bankCardNoDec = unionOnlineTrader.getBankCardNo();
                    bankCardNoMiwen = hessianService.encryptData(bankCardNoDec);
                    txsPreOrder.setBankCode(unionOnlineTrader.getBankCode());
                }
                if (request.getPayer() instanceof QuickPayTrader) {
                    QuickPayTrader quickPayTrader = (QuickPayTrader) request.getPayer();
                    bankCardNoMiwen = quickPayTrader.getBankCardNo();
                    bankCardNoDec = hessianService.decryptData(bankCardNoMiwen);
                    if (StringUtils.isNotBlank(quickPayTrader.getCardNo())) {
                        txsPreOrder.setCertNo(quickPayTrader.getCardNo());
                        String certNoDec = hessianService.decryptData(quickPayTrader.getCardNo());
                        txsPreOrder.setCertNoMosaic(com.epaylinks.efps.common.util.StringUtils.mosaic(certNoDec, '*', 6, 4));
                        txsPreOrder.setCertNoHash(MD5Utils.getMD5(certNoDec));
                    }
                    if (StringUtils.isNotBlank(quickPayTrader.getCustomerName())) {
                        txsPreOrder.setCardOwner(quickPayTrader.getCustomerName());
                        String cardOwnerDec = hessianService.decryptData(quickPayTrader.getCustomerName());
                        txsPreOrder.setCardOwnerMosaic(com.epaylinks.efps.common.util.StringUtils.mosaic(cardOwnerDec, '*', 0, 1));
                        txsPreOrder.setCardOwnerHash(MD5Utils.getMD5(cardOwnerDec));
                    }
                }

            } catch (MalformedURLException e) {
                logException(e);
                throw new AppException(TxsCode.ENCRYPT_DECRYPT_ERROR.code);
            }

            if (StringUtils.isNotBlank(bankCardNoDec)) {
                txsPreOrder.setCardNoEnc(bankCardNoMiwen);

                TxsCardBin txsCardBin = self.findCardBinInfo(bankCardNoDec);
                txsPreOrder.setBankCode(txsCardBin.getBankIcon());
                txsPreOrder.setCardNoMosaic(com.epaylinks.efps.common.util.StringUtils.mosaic(bankCardNoDec, '*', 6, 4));
                txsPreOrder.setQuickpayCardno(MD5Utils.getMD5(bankCardNoDec));
                if (StringUtils.equals(txsCardBin.getCardType(), "借记卡")) {
                    txsPreOrder.setCardType(TxsConstants.OrderCardType.DEBIT.code);
                } else {
                    txsPreOrder.setCardType(TxsConstants.OrderCardType.CREDIT.code);
                }
            }

            if (request.getPayer() instanceof UnionJsTrader) {
                UnionJsTrader unionJsTrader = (UnionJsTrader) request.getPayer();
                String userId = unionJsTrader.getUserId();
                if (StringUtils.isNotEmpty(userId)) {

                    if (userId.length() >= 128) {
                        txsPreOrder.setPayerId(userId.substring(userId.length() - 128, userId.length()));
                    } else {
                        txsPreOrder.setPayerId(userId);
                    }

                    if (userId.length() >= 32) {
                        txsPreOrder.setUserId(userId.substring(userId.length() - 32, userId.length()));
                    } else {
                        txsPreOrder.setUserId(userId);
                    }

                }
            }

            if (request.getPayer() instanceof AliLifeWindowTrader) {
                AliLifeWindowTrader aliLifeWindowTrader = (AliLifeWindowTrader) request.getPayer();
                String buyerUserId = aliLifeWindowTrader.getBuyerId();
                String buyerLongId = aliLifeWindowTrader.getBuyerLogonId();
                String tradeId = aliLifeWindowTrader.getTraderId();
                String payerType = aliLifeWindowTrader.getTypeStr();

                txsPreOrder.setPayerType(payerType);
                txsPreOrder.setBuyerLogonId(buyerLongId);

                if (StringUtils.isNotEmpty(buyerUserId)) {

                    if (buyerUserId.length() >= 128) {
                        txsPreOrder.setPayerId(buyerUserId.substring(buyerUserId.length() - 128, buyerUserId.length()));
                    } else {
                        txsPreOrder.setPayerId(buyerUserId);
                    }

                    if (buyerUserId.length() >= 32) {
                        txsPreOrder.setUserId(buyerUserId.substring(buyerUserId.length() - 32, buyerUserId.length()));
                    } else {
                        txsPreOrder.setUserId(buyerUserId);
                    }

                }
            }


        }

        txsPreOrder.setPayMethod(request.getPayMethod());
        txsPreOrder.setCommissionedCustomerCode(request.getCommissionedCustomerCode());
        //20190221 yjw
        //如下为被注释掉的为历史遗留代码，通过上述copyPropertiesIgnoreNull方法后，不需再一个个set，为防止意外，先只是注释，后期运行稳定无异常情况后，可把注释代码直接删除
//		txsPreOrder.setTargetCustomerCode(request.getTargetCustomerCode());
        txsPreOrder.setOrderId(dbId);
//		txsPreOrder.setOutTradeNo(request.getOutTradeNo());
//		txsPreOrder.setCustomerCode(request.getCustomerCode());
//		txsPreOrder.setTerminalNo(request.getTerminalNo());
//		txsPreOrder.setCreateTime(request.getCreateTime());
        txsPreOrder.setPayState(TxsConstants.PreOrderState.WAIT_TOPAY.code);
//		txsPreOrder.setTransactionType(request.getTransactionType());
//		txsPreOrder.setAmount(request.getAmount());
//		txsPreOrder.setTransactionStartTime(request.getTransactionStartTime());
//		txsPreOrder.setTransactionEndTime(request.getTransactionEndTime());
        txsPreOrder.setSettlementState(TxsConstants.SettleMentState.NOT_PROCESS.code);
        txsPreOrder.setEncryResult(request.calcEncryResult());
//		txsPreOrder.setPayMethod(request.getPayMethod());
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(request.getCustomerCode(),
                request.getCustomerCode(), "1");
        txsPreOrder.setCustomerName(customerInfo.getName());
        if (request.getCumBusinessParamInsts() != null) {
            for (CumBusinessParamInst bpi : request.getCumBusinessParamInsts()) {
                //判断主商户最高分润比�?
                if (Constants.BusinessParamCode.MAX_PROFIT_PROPORTION.code.equalsIgnoreCase(bpi.getCode())) {
                    txsPreOrder.setMaxProfit(bpi.getValue());
                }
            }
        }

        txsPreOrder.setRequestSrc(request.getRequestSrc());

        if (StringUtils.isNotBlank(request.getTerminalNo())) {
            CumTerminalInfo cumTerminalInfo = cumCacheService.queryCumTerminalInfo(request.getTerminalNo());
            if (cumTerminalInfo != null) {
                txsPreOrder.setTerminalName(cumTerminalInfo.getTerminalName());
            }
        }

        /**
         * 新增商户对应的业务员和公司
         */
        txsPreOrder.setBusinessMan(customerInfo.getBusinessMan());
        txsPreOrder.setBusinessManId(customerInfo.getBusinessManId());
        txsPreOrder.setCompanyName(customerInfo.getCompanyName());
        txsPreOrder.setCompanyId(customerInfo.getCompanyId());

        return txsPreOrder;
    }
//
//    /**
//     * 仅设置重复下单时需要更新的字段�?
//     *
//     * @param request
//     * @param dbId
//     * @return
//     */
//    @Logable(businessTag = "createTxsPreOrderForUpdate")
//    public TxsPreOrder createTxsPreOrderForUpdate(TxsPayRequest request, Long dbId) {
//        TxsPreOrder txsPreOrder = new TxsPreOrder();
//        txsPreOrder.setOrderId(dbId);
//        txsPreOrder.setPayState(TxsConstants.PreOrderState.WAIT_TOPAY.code);
//        txsPreOrder.setTransactionStartTime(request.getTransactionStartTime());
//        txsPreOrder.setTransactionEndTime(request.getTransactionEndTime());
//        txsPreOrder.setSettlementState(TxsConstants.SettleMentState.NOT_PROCESS.code);
//        txsPreOrder.setEncryResult(request.calcEncryResult());
//        txsPreOrder.setUpdateTime(new Date());
//        return txsPreOrder;
//    }
//
//    /**
//     * 缓存请求
//     *
//     * @param txsPayTradeOrder
//     * @return
//     */
//    public String cacheOrder(TxsPayRequest request) {
//        final String token = UUIDUtils.uuid();
//        TxsPayRequest txsPreOrderToken = transactionTokenService.createTxsPreOrderToken(token, request);
//        if (txsPreOrderToken == null) {
//            throw new AppException(TxsCode.ERROR_CACHE.code);
//        }
//        return token;
//    }

    /**
     * 获取交易单号
     *
     * @param outTradeNo
     * @param tokenTxsPreOrder
     * @param now
     * @return
     * @throws Exception
     */
    public String getTransactionNo(TxsPayRequest tokenTxsPreOrder, Date now) {
        return generatorOrderNo(tokenTxsPreOrder.getTransactionType(), "payTradeNo", now);
    }

    /**
     * 生成订单�?
     *
     * @param transactionType
     * @param sequenceName
     * @param now
     * @return
     */
    public String generatorOrderNo(String transactionType, String sequenceName, Date now) {
        int length = 3;//seqnce和随机数长度
        String sequence = com.epaylinks.efps.common.util.StringUtils.formatStr(
                sequenceService.nextValue(sequenceName) + "", length, '0');//数据库seqence序列
//		String sequence = String.format("%03d", sequenceService.nextValue("payTradeNo"));//，确保有三位，否则后续处理有异常
        String dateString = DateFormatUtils.format(now, TxsPayRequest.format_day);//日期
        String timeString = DateFormatUtils.format(now, TxsPayRequest.format_time);//时间
        String timeHash = com.epaylinks.efps.common.util.StringUtils.formatStr(
                Math.abs(timeString.hashCode()) + "", 7, '0');//根据当前时间生成的随机数，Math.abs(hash(hhmmssSSS))，长7位，不足左边边补0，超出取右边7�?
        String random = "" + (new Random().nextInt(900) + 100);//随机�?

        StringBuffer randomAndSequence = new StringBuffer();
        for (int i = 0; i < length; i++) {
            randomAndSequence.append(random.charAt(i)).append(sequence.charAt(i));//随机数和sequnce序列交叉处理
        }

//        String prefix = Constants.transactionNoPrefix.transformTypeToPrefix(transactionType);
//        if(prefix == null || prefix.isEmpty()) {
//        	prefix = transactionType;
//        }
        String prefix = TransactionType.fromTypeCode(transactionType).no;
        StringBuffer tempTransactionNo = new StringBuffer(prefix);//初始化前缀
        tempTransactionNo.append(dateString).append(timeHash).append(randomAndSequence);
//		System.out.println(dateString + "-" + timeString + "-" + sequence  + "-" + random  + "----" + tempTransactionNo.toString());
        return tempTransactionNo.toString();
    }
//
//    /**
//     * 合并入参中收集到的数据和缓存中已经存在的下单请求
//     *
//     * @param payToken
//     * @param request
//     * @return
//     */
//    public TxsPayRequest combineRequestWithCachedReq(String payToken, TxsPayRequest request) {
//        TxsPayRequest cachedRequest = transactionTokenService.getTxsPayRequestToken(payToken);
//        if (cachedRequest == null) {
//            throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code);
//        }
//        checkConfirmPayInterval(payToken, cachedRequest);
//
//        // 缓存中的支付方式
//        if (StringUtils.isEmpty(cachedRequest.getPayMethod())) {
//            if (StringUtils.isEmpty(request.getPayMethod())) {
//                throw new AppException(TxsCode.CONFIRMPAY_METHODISNULL.code,
//                        new RuntimeException(TxsCode.CONFIRMPAY_METHODISNULL.message));
//            }
//        }
//        if (!StringUtils.isEmpty(request.getPayMethod())) {
//            cachedRequest.setPayMethod(request.getPayMethod());
//            cachedRequest.setPayer(request.getPayer());
//        }
//
//        Trader cachedPayer = cachedRequest.getPayer();
//        if (request.getPayer() != null) {
//            cachedRequest.setPayer(request.getPayer());
//        }
//
//        //无卡分账产品填充支付卡信�?
//        if (PayMethod.FZ_NOCARD_PAY.code.equals(cachedRequest.getPayMethod()) || PayMethod.FZ_NOCARD_PAY_CREDIT.code.equals(cachedRequest.getPayMethod())
//                || PayMethod.PROTOCOL_PAY.code.equals(cachedRequest.getPayMethod())
//                || PayMethod.PROTOCOL_PAY_CREDIT.code.equals(cachedRequest.getPayMethod())) {
//            if (request.getPayer() != null && StringUtils.isBlank(request.getPayer().getSubCustomerCode())) {
//                request.getPayer().setSubCustomerCode(cachedPayer.getSubCustomerCode());//子商户号
//                cachedRequest.setSubCustomerCode(cachedPayer.getSubCustomerCode());
//            }
//            cachedRequest.setPayer(request.getPayer());
//        }
//
//        // 支付方式已实现：支付方式需是已经实现的支付方式，否则返回错�?
//        if (PayMethod.ACCOUNT_PAY.code.equals(cachedRequest.getPayMethod())) {
//            // 异常
//            throw new AppException(TxsCode.PAYMETHOD_NOTSUPPORT.code,
//                    new RuntimeException(TxsCode.PAYMETHOD_NOTSUPPORT.message));
//        }
//        // 收集金额
//        if (cachedRequest.getAmount() == null) {
//            if (request.getAmount() == null) {
//                throw new AppException(TxsCode.CONFIRMPAY_AMOUNTISNULL.code,
//                        new RuntimeException(TxsCode.CONFIRMPAY_AMOUNTISNULL.message));
//            }
//            if (request.getAmount() != null)
//                cachedRequest.setAmount(request.getAmount());
//        }
//        // 
//        if (cachedRequest.getActualPayAmount() == null) {
//            if (request.getActualPayAmount() != null)
//                cachedRequest.setActualPayAmount(request.getActualPayAmount());
//        }
//        //2018/10/25 增加快捷支付借记和快捷支付贷记卡的判�?根据卡bin数据)
//        if ((PayMethod.QUICK_PAY.code).equalsIgnoreCase(cachedRequest.getPayMethod()) ||
//                StringUtils.equals(PayMethod.QUICK_PAY_CREDIT.code, cachedRequest.getPayMethod())) {
//            if (cachedRequest.getAmount() > limitedAmount) {
//                throw new AppException(TxsCode.AMOUNT_TOLARGE.code,
//                        new RuntimeException(TxsCode.AMOUNT_TOLARGE.message));
//            }
//            QuickPayTrader quickPayTrader = new QuickPayTrader();
//            quickPayTrader = (QuickPayTrader) request.getPayer();
//            TxsCardBin txsCardBin = transactionQuickPayService.findCardBinInfo(quickPayTrader.getBankCardNo());
//            quickPayTrader.setBankIcon(txsCardBin.getBankIcon());
//            if (StringUtils.equals(txsCardBin.getCardType(), "借记卡")) {
//                cachedRequest.setPayMethod(PayMethod.QUICK_PAY.code);
//                quickPayTrader.setBankCardAttr(TxsConstants.CardType.DEBIT.code);
//            } else {
//                cachedRequest.setPayMethod(PayMethod.QUICK_PAY_CREDIT.code);
//                quickPayTrader.setBankCardAttr(TxsConstants.CardType.CREDIT.code);
//            }
//            quickPayTrader.setSubCustomerCode(cachedRequest.getSubCustomerCode());
//            cachedRequest.setPayPassWay(request.getPayPassWay());
//
//            request.setPayer(quickPayTrader);
//            cachedRequest.setPayer(request.getPayer());
//        }
//        return cachedRequest;
//    }
//
//    /**
//     * 计算手续费，计算成功会设置到入参req中，如果计算错误抛出异常; 返回计算费用选定的业务实�?
//     * 如果入参业务编码字段有值，使用该值进行费用计算；否则根据支付方式确认业务编码后根据该业务编码进行费用计算 计算手续费过程自然而然包含了业务实例的检�?
//     *
//     * @param req
//     *///该计算是实时计算手续费. 即是判断D1交易时,检查节假是用计费时的时间来检查
//    @Logable(businessTag = "calcProcedure")
//    public CustomerBusinessInstance calcProcedure(TxsPayRequest req, String businessCode) {
//        if (req.getAmount() < 0) {
//            throw new AppException(TxsCode.ORDERAMOUNT_ERROR.code);
//        }
//        if (StringUtils.isBlank(req.getPayMethod()) && businessCode == null) {
//            throw new AppException(TxsConstants.INVALID_PARAM);
//        }
//
//        String bankIcon = null;
//        String chargedByBankCodeArea = null;
//        if (null != req.getPayer() && req.getPayer() instanceof QuickPayTrader) {
//            bankIcon = ((QuickPayTrader) req.getPayer()).getBankIcon();
//            try {
//            	BankInfo bankInfo = cacheService.getBankInfo(bankIcon);
//            	if (null != bankInfo && 1 == bankInfo.getIsAreaBank()) {
//            		commonService.payLog("bankIcon["+bankIcon+"]按区域计费!");
//            		bankIcon = CustomerBusinessInstance.AREA_BANK_ICON;
//            		chargedByBankCodeArea = CustomerBusinessInstance.CHARGE_BY_AREA_BANK_ICON;
//            	}
//            }catch(Exception e) {
//            	e.printStackTrace();
//            	commonService.logException(e);
//            }            
//        }
//        
//        if (null != req.getPayer() && req.getPayer() instanceof PersonalBankingTrader) {
//            bankIcon = ((PersonalBankingTrader) req.getPayer()).getBankCode();
//            try {
//            	BankInfo bankInfo = cacheService.getBankInfo(bankIcon);
//            	if (null != bankInfo && 1 == bankInfo.getIsAreaBank()) {
//            		commonService.payLog("bankIcon["+bankIcon+"]按区域计费!");
//            		bankIcon = CustomerBusinessInstance.AREA_BANK_ICON;
//            		chargedByBankCodeArea = CustomerBusinessInstance.CHARGE_BY_AREA_BANK_ICON;
//            	}
//            }catch(Exception e) {
//            	e.printStackTrace();
//            	commonService.logException(e);
//            }            
//        }
//        
//        if (null != req.getPayer() && req.getPayer() instanceof BankTrader) {
//            bankIcon = ((BankTrader) req.getPayer()).getBankCode();
//            try {
//            	BankInfo bankInfo = cacheService.getBankInfo(bankIcon);
//            	if (null != bankInfo && 1 == bankInfo.getIsAreaBank()) {
//            		commonService.payLog("bankIcon["+bankIcon+"]按区域计费!");
//            		bankIcon = CustomerBusinessInstance.AREA_BANK_ICON;
//            		chargedByBankCodeArea = CustomerBusinessInstance.CHARGE_BY_AREA_BANK_ICON;
//            	}
//            }catch(Exception e) {
//            	e.printStackTrace();
//            	commonService.logException(e);
//            }            
//        }
//
//        List<CustomerBusinessInstance> customerInstanceList = null;
//        CustomerBusinessInstance businessInst = null;
//        Long procedureFee = null;
//
//        String platCustomerCode = req.getPlatCustomerCode();
//
//        //判断是否为平台商
//        boolean isPlatCustomer = false;
//        if(StringUtils.isBlank(platCustomerCode)){
//            CustomerInfo customerInfo = cumCacheService.getCustomerInfo(req.getCustomerCode(),req.getCustomerCode(),UserType.PAS_USER.code);
//            if(customerInfo == null){
//                throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code,TxsCode.CUSTOMER_NOT_EXIST.message);
//            }
//            if(StringUtils.isNotBlank(customerInfo.getPlatCustomerCode())){
//                platCustomerCode = customerInfo.getPlatCustomerCode();
//            }
//
//            if(Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())){
//                isPlatCustomer = true;
//            }
//        }
//
//        String chargeFloat = null;
//        CustomerBusinessInstance tradeBusinessInst = null;
//        if (req.getNeedSplit() != null && req.getNeedSplit() && StringUtils.isNotBlank(platCustomerCode)
//                && !isPlatCustomer) {
//
//            //20191014 分账交易 如果平台商户不为空，则用平台商户的手续费
//            customerInstanceList = cumCacheService.getCustomerBusinessInsts(platCustomerCode,
//                    CustomerBusinessInstance.STATUS_VALID, platCustomerCode, UserType.PAS_USER.code);
//
//            //获取当前交易所对应的平台商业务实例
//            List<CustomerBusinessInstance> platCustomerInstanceList = self.decidePlatBusinessInst(customerInstanceList, req.getPayMethod(),
//                    businessCode, req.getTransactionType());
//
//            //使用子商户的交易时，搜索子商户业务对应的平台商户的父业务（根据业务类型+结算周期）
//            //如果存在，则使用该业务作为本次结算的业务
//            //如果不存在，则使用该平台商户该业务的默认业务（兼容现有数据）
//            customerInstanceList = cumCacheService.getCustomerBusinessInsts(req.getCustomerCode(),
//                    CustomerBusinessInstance.STATUS_VALID, req.getCustomerCode(), UserType.PAS_USER.code);
//
//            //获取当前交易商户对应的业务实例
//            tradeBusinessInst = self.decideBusinessInst(customerInstanceList, req.getPayMethod(),
//                    businessCode, req.getTransactionType());
//
//            SettCycleRuleInst tradeSettCycleRuleInst = tradeBusinessInst.getSettCycleRuleInst();
//            if(tradeSettCycleRuleInst == null){
//                tradeSettCycleRuleInst = cumCacheService.querySettCycleRuleInst(tradeBusinessInst.getBusinessExamId());
//                if(tradeSettCycleRuleInst == null){
//                    throw new AppException(TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.code, TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.message);
//                }
//            }
//
//            //根据结算周期获取对应平台商户实例
//            //平台商结算规则实例
//            SettCycleRuleInst platSettCycleRuleInst;
//            for (CustomerBusinessInstance customerBusinessInstance : platCustomerInstanceList){
//                platSettCycleRuleInst = customerBusinessInstance.getSettCycleRuleInst();
//                if(platSettCycleRuleInst == null){
//                    platSettCycleRuleInst = cumCacheService.querySettCycleRuleInst(customerBusinessInstance.getBusinessExamId());
//                    if(platSettCycleRuleInst == null){
//                        throw new AppException(TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.code, TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.message);
//                    }
//                }
//
//                //如果存在，则使用该业务作为本次结算的业务，如果存在则使用平台商默认实例
//                if(tradeSettCycleRuleInst.getSettCycleRuleCode().equals(platSettCycleRuleInst.getSettCycleRuleCode())){
//                    businessInst = customerBusinessInstance;
//                    break;
//                }
//            }
//
//            if(businessInst == null){
//                //使用平台商户交易时,有默认优先使用默认
//                businessInst = getPlatDefaultBusinessInst(platCustomerInstanceList);
//            }
//
//            //
//            //判断是否要收取d1交易节假日上游手续费 begin
//            //
//            Date date = new Date();
//            String dateyyyyMMdd = DateUtils.formatDate(date, "yyyyMMdd");            
//            Boolean needChargeD1HolidayFloat = procedureAisistService.judgeNeedD1TranDoHolidayCharge(businessInst.getBusinessCode(), 
//            		(null == businessInst.getSettCycleRuleInst())? null:businessInst.getSettCycleRuleInst().getSettCycleRuleCode(), dateyyyyMMdd);
//            if (needChargeD1HolidayFloat) {
//            	chargeFloat = CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE;
//            }
//            
//            //
//            //判断是否要收取d1交易节假日上游手续费 end
//            //
//            
//            //使用平台商业务计算手续费
//            commonService.payLog("计费前:"+businessInst.getBusinessExamId()+"|"+req.getAmount()+"|"+1+"|"+bankIcon+"|"+chargeFloat
//            		+"|outTradeNo:"+req.getOutTradeNo());
//            procedureFee = businessInst.calcProcedureFee(req.getAmount(), 1, bankIcon, chargeFloat);
//
//            if (StringUtils.isNotBlank(req.getSplitModel())) {
//                List<CumSplitBusiness> cumSplitBusinessList = cumCacheService.getSplitBusiness(req.getCustomerCode(), businessInst.getBusinessCode(), Short.valueOf(req.getSplitModel()));
//                if (cumSplitBusinessList == null || cumSplitBusinessList.size() == 0) {
//                    throw new AppException(TxsCode.NOT_OPEN_FZ_MODEL.code, TxsCode.NOT_OPEN_FZ_MODEL.message + req.getSplitModel() + "-" + TxsConstants.SplitModel.getSplitModel(req.getSplitModel()));
//                }
//            }
//
//        } else {
//            businessInst = getBusinessInstNew(req.getCustomerCode(), req.getPayMethod(), businessCode, req.getTransactionType());
//            
//            //
//            //判断是否要收取d1交易节假日上游手续费 begin
//            //
//            Date date = new Date();
//            String dateyyyyMMdd = DateUtils.formatDate(date, "yyyyMMdd");            
//            Boolean needChargeD1HolidayFloat = procedureAisistService.judgeNeedD1TranDoHolidayCharge(businessInst.getBusinessCode(), 
//            		(null == businessInst.getSettCycleRuleInst())? null:businessInst.getSettCycleRuleInst().getSettCycleRuleCode(), dateyyyyMMdd);
//            if (needChargeD1HolidayFloat) {
//            	chargeFloat = CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE;
//            }
//            
//            //
//            //判断是否要收取d1交易节假日上游手续费 end
//            //
//            commonService.payLog("计费前:"+businessInst.getBusinessExamId()+"|"+req.getAmount()+"|"+1+"|"+bankIcon+"|"+chargeFloat
//            		+"|outTradeNo:"+req.getOutTradeNo());
//            procedureFee = businessInst.calcProcedureFee(req.getAmount(), 1, bankIcon, chargeFloat);
//        }
//
//          procedureAisistService.checkProcedureBiggerThanAmount(req.getAmount(), procedureFee, req.getProcedureCustomercode());
//
//        req.setProcedureFee(procedureFee);
//        req.setProcedureRate(businessInst.getRateParam());
//        req.setRateMode(businessInst.getRateMode());
//
//        req.setMaxFee(businessInst.getMaxFee());
//        req.setMinFee(businessInst.getMinFee());
//        req.setFeePer(businessInst.getFeePer());
//        
//        req.setD1HolidayChargeRate(businessInst.getD1HolidayChargeRate());
//        req.setD1HolidayChargePer(businessInst.getD1HolidayChargePer());
//        req.setD1HolidayCharged(CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE.equals(chargeFloat)?
//        		CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE:"0");
//        req.setChargedByBankCodeArea(chargedByBankCodeArea);
//        
//       //非其他分账模式，使用平台商户手续费，其他业务参数使用交易商户自己的  //这个必须放最后。不然会手续费参数设置错误
//        if (!TxsConstants.SplitModel.ORTHER.code.equals(req.getSplitModel()) && tradeBusinessInst != null) {
//            tradeBusinessInst.setSettCycleRuleInst(businessInst.getSettCycleRuleInst());
//            businessInst = tradeBusinessInst;
//        }
//        return businessInst;
//    }
//
    /**
     * 获取平台商默认业务
     */
//    @Logable(businessTag = "getPlatDefaultBusinessInst", outputArgs=false)
    public CustomerBusinessInstance getPlatDefaultBusinessInst(List<CustomerBusinessInstance> customerInstanceList){
        CustomerBusinessInstance businessInst = null;
        //使用平台商户交易时,有默认优先使用默认
        for (CustomerBusinessInstance customerBusinessInstance : customerInstanceList){
            if(customerInstanceList.size() == 1 || (customerBusinessInstance.getSetDefault() != null && customerBusinessInstance.getSetDefault() == 1)){
                businessInst = customerBusinessInstance;
                break;
            }
        }
        //如果没有默认值则抛出异常
        if(businessInst == null) {
            throw new AppException(TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.code,TxsCode.CUSTOMER_MUST_OPEN_BUSINESS.message);
        }
        return businessInst;
    }

    /**
     * 获取BusinessInst
     */
    @Logable(businessTag = "getBusinessInstNew")
    public CustomerBusinessInstance getBusinessInstNew(String customerCode,String payMethod, String businessCode, String transactionType){
        String platCustomerCode = null;

        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(customerCode,customerCode,UserType.PAS_USER.code);
        if(customerInfo == null){
            throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code,TxsCode.CUSTOMER_NOT_EXIST.message);
        }

        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);

        CustomerBusinessInstance businessInst = null;
        //平台商多结算周期，使用默认业务
        if(Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())){
            //获取当前交易所对应的平台商业务实例
            List<CustomerBusinessInstance> platCustomerInstanceList = self.decidePlatBusinessInst(customerInstanceList, payMethod,
                    businessCode, transactionType);
            //使用平台商户交易时,获取默认业务
            businessInst = getPlatDefaultBusinessInst(platCustomerInstanceList);
        }else {
            businessInst = self.decideBusinessInst(customerInstanceList, payMethod, businessCode,transactionType);
        }
        return businessInst;
    }
    
    /**
     * 获取BusinessInst
     */
    @Logable(businessTag = "getBusinessInstNew", outputArgs = false)
    public CustomerBusinessInstance getBusinessInstNew(CustomerInfo customerInfo, List<CustomerBusinessInstance> customerInstanceList, 
    		String payMethod, String businessCode, String transactionType){

        if(customerInfo == null){
            throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code,TxsCode.CUSTOMER_NOT_EXIST.message);
        }

        CustomerBusinessInstance businessInst = null;
        //平台商多结算周期，使用默认业务
        if(Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())){
            //获取当前交易所对应的平台商业务实例
            List<CustomerBusinessInstance> platCustomerInstanceList = self.decidePlatBusinessInst(customerInstanceList, payMethod,
                    businessCode, transactionType);
            //使用平台商户交易时,获取默认业务
            businessInst = getPlatDefaultBusinessInst(platCustomerInstanceList);
        }else {
            businessInst = self.decideBusinessInst(customerInstanceList, payMethod, businessCode,transactionType);
        }
        return businessInst;
    }
    
    
    public CustomerBusinessInstance getBusinessCodeByChargeGroup(String chargeGroup, Short busiCardType, List<CustomerBusinessInstance> customerInstanceList) {
    	if(customerInstanceList == null || chargeGroup == null) {
    		return null;
    	}
    	for(CustomerBusinessInstance item: customerInstanceList) {
    		if(StringUtils.equals(item.getChargeGroup(), chargeGroup) && item.getCardType().equals(busiCardType)) {
    			return item;
    		}
    	}
    	return null;
    }

    /**
     * 检查业务参�?
     *
     * @param req
     * @param businessInst
     */
    public void checkBusinessParam(Long amount, CustomerBusinessInstance businessInst) {
        List<CumBusinessParamInst> cumBusinessParamInsts = cumCacheService.getCustomerBusinessParamInst(businessInst.getBusinessExamId());
        for (CumBusinessParamInst cumBusinessParamInst : cumBusinessParamInsts) {
            if (cumBusinessParamInst.getCode().equals(BusinessParamCode.MAX_TXS_AMOUNT.code)) {
                if (amount > Long.valueOf(cumBusinessParamInst.getValue())) {
                    throw new AppException(TxsCode.BUSINESSPARAM_MAXAMOUNT_ERROR.code);
                }
            }
            if (cumBusinessParamInst.getCode().equals(BusinessParamCode.MIN_TXS_AMOUNT.code)) {
                if (amount < Long.valueOf(cumBusinessParamInst.getValue())) {
                    throw new AppException(TxsCode.BUSINESSPARAM_MINAMOUNT_ERROR.code);
                }
            }
        }
    }

    /**
     * 检查业务参�?
     *
     * @param req
     * @param businessInst
     */
    public void checkBusinessParam(Long amount, List<CumBusinessParamInst> cumBusinessParamInsts) {
        for (CumBusinessParamInst cumBusinessParamInst : cumBusinessParamInsts) {
            if (cumBusinessParamInst.getCode().equals(BusinessParamCode.MAX_TXS_AMOUNT.code)) {
                if (amount > Long.valueOf(cumBusinessParamInst.getValue())) {
                    throw new AppException(TxsCode.BUSINESSPARAM_MAXAMOUNT_ERROR.code);
                }
            }
            if (cumBusinessParamInst.getCode().equals(BusinessParamCode.MIN_TXS_AMOUNT.code)) {
                if (amount < Long.valueOf(cumBusinessParamInst.getValue())) {
                    throw new AppException(TxsCode.BUSINESSPARAM_MINAMOUNT_ERROR.code);
                }
            }
        }
    }

    /**
     * 通用的手续费计算 payMethod和businessCode必须设置一个，如果都设置，以businessCode为准 如果未开通此业务，则会抛出异�?
     * transactionType可设置，也可留空
     * @param customerCode
     * @param amount
     * @param payMethod
     * @param businessCode
     * @return
     */
    //trandateyyyyMdd 为2020-02-19新增. 如果是实时计算手续费,这个字段可以不传. 如果不传的话,内部会取当时实时时间处理
    @Logable(businessTag = "calcProcedure-extendParam", outputResult = false)
    public ProcedureFeeResult calcProcedure(String customerCode, Long amount, String payMethod,
                                            String businessCode,String transactionType, String trandateyyyyMdd, String...extendParam) {
        if (amount < 0) {
            throw new AppException(TxsCode.ORDERAMOUNT_ERROR.code);
        }

        String bankIcon = null;
        String chargedByBankCodeArea = null;
		if (extendParam != null && extendParam.length >= 1) {
			bankIcon = extendParam[0];

            try {
                BankInfo bankInfo = cacheService.getBankInfo(bankIcon);
                if (null != bankInfo && 1 == bankInfo.getIsAreaBank()) {
                    commonService.payLog("bankIcon["+bankIcon+"]按区域计费!");
                    bankIcon = CustomerBusinessInstance.AREA_BANK_ICON;
                    chargedByBankCodeArea = CustomerBusinessInstance.CHARGE_BY_AREA_BANK_ICON;
                }
            }catch(Exception e) {
                e.printStackTrace();
                commonService.logException(e);
            }
		}

		String splitModel = null;
		if(extendParam != null && extendParam.length >=2){
		    splitModel = extendParam[1];
        }
		String cardType = null;
		if(extendParam != null && extendParam.length >=3) {
			cardType = extendParam[2];
		}

        if (StringUtils.isBlank(payMethod) &&
                StringUtils.isBlank(businessCode)) {
            throw new AppException(TxsConstants.INVALID_PARAM);
        }
        
        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(customerCode,customerCode,UserType.PAS_USER.code);
        if(customerInfo == null){
            throw new AppException(TxsCode.CUSTOMER_NOT_EXIST.code,TxsCode.CUSTOMER_NOT_EXIST.message);
        }
        
        List<CustomerBusinessInstance> customerInstanceList = null;
        CustomerBusinessInstance businessInst = null;
        CustomerBusinessInstance tradeBusinessInst = null;
      //判断是否为平台商
        boolean isPlatCustomer = false;
        String platCustomerCode = customerInfo.getPlatCustomerCode();

        if(Constants.customerCategory.EFPS_CUSTOMER_PLAT.code.equals(customerInfo.getCustomerCategory())){
            isPlatCustomer = true;
        }
        //businessCode为FZ不走平台商模式
        if (TransactionType.FZ.code.equals(transactionType) && StringUtils.isNotBlank(platCustomerCode) && !isPlatCustomer
        		&& !Constants.BusinessCode.FZ.code.equals(businessCode)
                && !Constants.BusinessCode.CDFZ.code.equals(businessCode)) {
        	//20191014 分账交易 如果平台商户不为空，则用平台商户的手续费
            customerInstanceList = cumCacheService.getCustomerBusinessInsts(platCustomerCode,
                    CustomerBusinessInstance.STATUS_VALID, platCustomerCode, UserType.PAS_USER.code);

            //获取当前交易所对应的平台商业务实例
            List<CustomerBusinessInstance> platCustomerInstanceList = self.decidePlatBusinessInst(customerInstanceList, payMethod,
                    businessCode, transactionType);

            //使用子商户的交易时，搜索子商户业务对应的平台商户的父业务（根据业务类型+结算周期）
            //如果存在，则使用该业务作为本次结算的业务
            //如果不存在，则使用该平台商户该业务的默认业务（兼容现有数据）
            customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
                    CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);

            //获取当前交易商户对应的业务实例
            tradeBusinessInst = self.decideBusinessInst(customerInstanceList, payMethod,
                    businessCode, transactionType);

            SettCycleRuleInst tradeSettCycleRuleInst = tradeBusinessInst.getSettCycleRuleInst();
            if(tradeSettCycleRuleInst == null){
                tradeSettCycleRuleInst = cumCacheService.querySettCycleRuleInst(tradeBusinessInst.getBusinessExamId());
                if(tradeSettCycleRuleInst == null){
                    throw new AppException(TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.code, TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.message);
                }
            }

            //根据结算周期获取对应平台商户实例
            //平台商结算规则实例
            SettCycleRuleInst platSettCycleRuleInst;
            for (CustomerBusinessInstance customerBusinessInstance : platCustomerInstanceList){
                platSettCycleRuleInst = customerBusinessInstance.getSettCycleRuleInst();
                if(platSettCycleRuleInst == null){
                    platSettCycleRuleInst = cumCacheService.querySettCycleRuleInst(customerBusinessInstance.getBusinessExamId());
                    if(platSettCycleRuleInst == null){
                        throw new AppException(TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.code, TxsCode.CUSTOMERSETTLEINFO_NOTEXIST.message);
                    }
                }

                //如果存在，则使用该业务作为本次结算的业务，如果存在则使用平台商默认实例
                if(tradeSettCycleRuleInst.getSettCycleRuleCode().equals(platSettCycleRuleInst.getSettCycleRuleCode())){
                    businessInst = customerBusinessInstance;
                    break;
                }
            }

            if(businessInst == null){
                //使用平台商户交易时,有默认优先使用默认
                businessInst = getPlatDefaultBusinessInst(platCustomerInstanceList);
            }
        }
        else {
        	customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
                    CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);

            businessInst = self.getBusinessInstNew(customerInfo, customerInstanceList, payMethod, businessCode,transactionType);
        }
        
        if(StringUtils.isNotBlank(cardType)) {
        	Short busiCardType = changeCardTypeToBusiness(cardType);
        	if(businessInst.getCardType() != null && !businessInst.getCardType().equals(busiCardType)) {
                
        		//业务设置的借贷类型和实际支付的借贷类型不一致
        		CustomerBusinessInstance chargeGroupBusiness = getBusinessCodeByChargeGroup(businessInst.getChargeGroup(), busiCardType, customerInstanceList);
        		//chargeGroupBusiness为什么不直接用呢，怕那种多结算周期的，所以还是根据新的businessCode再走一遍业务流程  20200611
        		if(chargeGroupBusiness != null) {
        			businessInst = self.getBusinessInstNew(businessInst.getCustomerCode(), null, chargeGroupBusiness.getBusinessCode(),transactionType);
        		}
        	}
        }

        if(StringUtils.isNotBlank(splitModel)){
            String querySplitModel = splitModel;
            if(TxsConstants.SplitModel.MEMBER.code.equalsIgnoreCase(splitModel)){
                querySplitModel = TxsConstants.SplitModel.ORTHER.code;
                commonService.payLog("splitModel:"+splitModel+"转为querySplitModel:"+querySplitModel+";即:找开通业务的时候,5快捷分账按2其他分账来找");
            }else if(TxsConstants.SplitModel.MULTI_ORDER.code.equalsIgnoreCase(splitModel)){
                querySplitModel = TxsConstants.SplitModel.COMMON.code;
                commonService.payLog("splitModel:"+splitModel+"转为querySplitModel:"+querySplitModel+";即:找开通业务的时候,4拆单分账按1普通分账来找");
            }
            List<CumSplitBusiness> cumSplitBusinessList = cumCacheService.getSplitBusiness(customerCode, null, Short.valueOf(querySplitModel));
            if(cumSplitBusinessList == null || cumSplitBusinessList.size() == 0){
                throw new AppException(TxsCode.NOT_OPEN_FZ_MODEL.code, TxsCode.NOT_OPEN_FZ_MODEL.message + querySplitModel + "-" + TxsConstants.SplitModel.getSplitModel(querySplitModel));
            }
        }
        
        //
        //判断是否要收取d1交易节假日上游手续费 begin
        //
        String dateyyyyMMdd = trandateyyyyMdd;
        if (StringUtils.isEmpty(dateyyyyMMdd)) {
        	Date date = new Date();
        	dateyyyyMMdd = DateUtils.formatDate(date, "yyyyMMdd");
        }
        Boolean needChargeD1HolidayFloat = procedureAisistService.judgeNeedD1TranDoHolidayCharge(businessInst.getBusinessCode(),
        		(null == businessInst.getSettCycleRuleInst())? null:businessInst.getSettCycleRuleInst().getSettCycleRuleCode(), dateyyyyMMdd);
        String chargeFloat = null;
        if (needChargeD1HolidayFloat) {
        	chargeFloat = CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE;
        }
        
        //
        //判断是否要收取d1交易节假日上游手续费 end
        //
//        commonService.payLog("计费前:"+businessInst.getBusinessExamId()+"|"+amount+"|"+1+"|"+bankIcon+"|"+chargeFloat);
        Long procedureFee = null;
        procedureFee = businessInst.calcProcedureFee(amount, 1, bankIcon, chargeFloat);
        if ((!StringUtils.equals(BusinessCode.WITHDRAW.code, businessCode)
        		&& !StringUtils.equals(BusinessCode.WITHDRAWTOSETTMENT_DEBITCARD.code, businessCode)
                && !StringUtils.equals(Constants.EfpsAccountService.WITHDRAW_WECHAT.code, businessCode)
                && !StringUtils.equals(EfpsAccountService.WITHDRAW_ALIPAY.code, businessCode))
        		&& (procedureFee > amount)) {
            throw new AppException(TxsCode.AMOUNT_MORETHAN_PROCEDUREFEE.code,
                    new RuntimeException(TxsCode.AMOUNT_MORETHAN_PROCEDUREFEE.message));
        }
        
      //非其他分账模式，使用平台商户手续费，其他业务参数使用交易商户自己的
        if (!TxsConstants.SplitModel.ORTHER.code.equals(splitModel)) {
        	if(tradeBusinessInst != null) {
        		tradeBusinessInst.setSettCycleRuleInst(businessInst.getSettCycleRuleInst());
                businessInst = tradeBusinessInst;
        	}
        }

        checkBusinessParam(amount, businessInst);
        ProcedureFeeResult result = new ProcedureFeeResult();
        result.setBusinessInst(businessInst);
        result.setProcedureFee(procedureFee);
        result.setChargedD1HolidayProcedure(chargeFloat);
        result.setChargedByBankCodeArea(chargedByBankCodeArea);

        commonService.payLog(businessInst.getBusinessExamId());
        return result;
    }
    
    public Short changeCardTypeToBusiness(String cardType) {
    	Short busiCardType = null;
    	if(Constants.BankCardType.DebitCard.code.equals(cardType)) {
    		busiCardType = 1;//业务设置那边没定义enum，又跟公共的不一样，糟心 ，就这样吧
    	}
    	else {
    		busiCardType = 2;
    	}
    	return busiCardType;
    }

    /**
     * 根据业务编码拿到对应的业务实�?
     *
     * @return
     */
    public CustomerBusinessInstance getBusinessInstId(String customerCode, String businessCode) {
//        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
//                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);
//        return decideBusinessInst(customerInstanceList, null, businessCode, null);
        return getBusinessInstNew(customerCode, null, businessCode, null);
    }
//
//    /**
//     * 计算商户的最大可能的支付手续费，注意是支付类交易
//     *
//     * @param customerCode
//     * @param amount
//     * @return
//     */
//    //这个方法已经不再调用了,如需再调用,需谨慎
//    @Logable(businessTag = "calcMaxPayProcedure")
//    public ProcedureFeeResult calcMaxPayProcedure(String customerCode, Long amount) {
//        if (amount < 0) {
//            throw new AppException(TxsCode.ORDERAMOUNT_ERROR.code);
//        }
//
//        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
//                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);
//        Date now = new Date();
//        Long maxProcedureFee = 0l;
//        CustomerBusinessInstance choosedInst = null;
//        for (CustomerBusinessInstance businessInst : customerInstanceList) {
//            if (businessInst.validAtTime(now) && businessInst.getPayMethods() != null
//                    && businessInst.getPayMethods().isEmpty() == false) {
//            	
//            	Date date = new Date();
//                String dateyyyyMMdd = DateUtils.formatDate(date, "yyyyMMdd");
//                //判断是否要收取d1交易节假日上游手续费 begin
//                //
//                Boolean needChargeD1HolidayFloat = procedureAisistService.judgeNeedD1TranDoHolidayCharge(businessInst.getBusinessCode(), 
//                		(null == businessInst.getSettCycleRuleInst())? null:businessInst.getSettCycleRuleInst().getSettCycleRuleCode(), dateyyyyMMdd);
//                String chargeFloat = null;
//                if (needChargeD1HolidayFloat) {
//                	chargeFloat = CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE;
//                }
//                
//                //
//                //判断是否要收取d1交易节假日上游手续费 end
//                //
//            	
//                Long tempProcedureFee = businessInst.calcProcedureFee(amount, 1, null, chargeFloat);
//                if (tempProcedureFee >= maxProcedureFee) {
//                    maxProcedureFee = tempProcedureFee;
//                    choosedInst = businessInst;
//                }
//            }
//        }
//        if (choosedInst == null) {
//            throw new AppException(TxsCode.PAYMETHOD_NOTSUPPORT.code);
//        }
//procedureAisistService.checkProcedureBiggerThanAmount(req.getAmount(), procedureFee, req.getProcedureCustomercode());
//        ProcedureFeeResult result = new ProcedureFeeResult();
//        result.setBusinessInst(choosedInst);
//        result.setProcedureFee(maxProcedureFee);
//        return result;
//    }
//
//    /**
//     * 查询某个客户当前有效的业务编码： 当前有效有两个含义，第一，状态为有效；第二，有效时间段包含当前时�?如果有重复的业务编码，会在返回值中去重
//     *
//     * @param customerCode
//     * @return
//     */
//    @Logable(businessTag = "queryCustomerBusinessCodeList")
//    public List<String> queryCustomerBusinessCodeList(String customerCode) {
//        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
//                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);
//        List<String> businessCodeList = new ArrayList<String>();
//        Set<String> businessCodeSet = new HashSet<String>();
//        Date now = new Date();
//        for (CustomerBusinessInstance inst : customerInstanceList) {
//            if (inst.validAtTime(now)) {
//                businessCodeSet.add(inst.getBusinessCode());
//            }
//        }
//        businessCodeList.addAll(businessCodeSet);
//        return businessCodeList;
//    }
//
//    /*
//     * 查询客户开通的有效业务所包含的所有支付方�?
//     */
//    public List<String> queryCustomerPayMethodList(String customerCode) {
//        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
//                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);
//        List<String> payMethodList = new ArrayList<String>();
//        Set<String> payMethodSet = new HashSet<String>();
//        Date now = new Date();
//        for (CustomerBusinessInstance inst : customerInstanceList) {
//            if (inst.validAtTime(now)) {
//                payMethodSet.addAll(inst.getPayMethods());
//            }
//        }
//        payMethodList.addAll(payMethodSet);
//        return payMethodList;
//    }
//
    /**
     * 创建并插入交易订�?
     *
     * @param req
     * @param businessInst
     * @return
     */
    public TxsPayTradeOrder createAndInsertTxsPayTradeOrder(TxsPayRequest req, CustomerBusinessInstance businessInst) {
        TxsPayTradeOrder order = new TxsPayTradeOrder();

        //该代码后续添�?可去掉下面的一些逐个属性的set，但安全起见，先不动原来的，至少后续加的字段，不要再一个个set了，
        //本该直接从txsPreOrder里copy比较好，省去很多查询动作，但是现有逻辑已经是如此，那样改动大了点�?
        ReflectUtils.copyPropertiesIgnoreNull(order, req);//copy 非空值到txsPreOrder进行存储
        order.setSettlementAmount(req.getAmount());

        if (Constants.SourceChannel.UNION_YUN.code.equals(req.getSourceChannel())) {
            Customer customer = custService.queryCustomerByCustomerNo(req.getCustomerCode());
            order.setPlatformCustomerCode(customer.getCustomerUid());
        }

        if (StringUtils.isNotBlank(req.getCommissionedCustomerCode())) {
            order.setCommissionedCustomerCode(req.getCommissionedCustomerCode());
        }
        Date now = new Date();
        String transactionNo = self.getTransactionNo(req, now);
        order.setId(sequenceService.nextValue("payConfirm"));
        order.setOrderId(req.getPreOrderId());
        order.setTransactionNo(transactionNo);
        order.setOutTradeNo(req.getOutTradeNo());
        order.setCustomerCode(req.getCustomerCode());
        order.setPayMethod(req.getPayMethod());
        Trader traderBak = req.getPayer();
        TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(req.getOutTradeNo(), req.getCustomerCode());
        order.setSplitProcedureFee(txsPreOrder.getSplitProcedureFee());
        order.setSplitProcedureRate(txsPreOrder.getSplitProcedureRate());
        if (null == order.getUserId() && null != txsPreOrder.getUserId()) {//主要用于解决银联js时,userId在txsPreOrder有值,而txsPayTradeOrder无值的问题
        	order.setUserId(txsPreOrder.getUserId());
        }
//        buildTradeOrderWithBusinessInst(order, businessInst);
        SettCycleRuleInst settCycleRuleInst = businessInst.getSettCycleRuleInst();
        if (settCycleRuleInst != null) {
            order.setSettCycleRuleCode(settCycleRuleInst.getSettCycleRuleCode());
        }
        order.setBusinessInstId(businessInst.getBusinessExamId());
        order.setBusinessCode(businessInst.getBusinessCode());


        if (null != req.getPayer() && req.getPayer() instanceof QuickPayTrader) {
            try {
                //quickPayTraderBak = (QuickPayTrader) req.getPayer();
                QuickPayTrader quickPayTrader = new QuickPayTrader();
                ReflectUtils.copyPropertiesIgnoreNull(quickPayTrader, traderBak);
                if (StringUtils.isNotBlank(quickPayTrader.getBankCardNo())) {
                    String bankCardNo = quickPayTrader.getBankCardNo();
                    buildCardInfo(bankCardNo, order);
                    quickPayTrader.setBankCardNo("");
                }
                if (StringUtils.isNotBlank(quickPayTrader.getCardNo())) {
                    String certNo = quickPayTrader.getCardNo();
                    order.setCertNo(hessianService.encryptData(certNo));
                    order.setCertNoMosaic(com.epaylinks.efps.common.util.StringUtils.mosaic(certNo, '*', 6, 4));
                    order.setCertNoHash(MD5Utils.getMD5(certNo));
                    quickPayTrader.setCardNo("");
                }
                if (StringUtils.isNotBlank(quickPayTrader.getCustomerName())) {
                    String cardOwner = quickPayTrader.getCustomerName();
                    order.setCardOwner(hessianService.encryptData(cardOwner));
                    order.setCardOwnerMosaic(com.epaylinks.efps.common.util.StringUtils.mosaic(cardOwner, '*', 0, 1));
                    order.setCardOwnerHash(MD5Utils.getMD5(cardOwner));
                    quickPayTrader.setCustomerName("");
                }

                if (StringUtils.isNotBlank(quickPayTrader.getPhone())) {
                    quickPayTrader.setPhone(hessianService.encryptData(quickPayTrader.getPhone()));
                }

                if (StringUtils.isNotBlank(quickPayTrader.getCvn2())) {
                    quickPayTrader.setCvn2(hessianService.encryptData(quickPayTrader.getCvn2()));
                }

                if (StringUtils.isNotBlank(quickPayTrader.getExpired())) {
                    quickPayTrader.setExpired(hessianService.encryptData(quickPayTrader.getExpired()));
                }

                order.setPayer(quickPayTrader.toString());
            } catch (MalformedURLException e) {
                logException(e);
            }
        } else if (null != req.getPayer() && (req.getPayer() instanceof PersonalBankingTrader
                || TraderType.getEnum(req.getPayer().getTypeStr()) == TraderType.PersonalBanking)) {

            PersonalBankingTrader personalBankingTrader = new PersonalBankingTrader();
            ReflectUtils.copyPropertiesIgnoreNull(personalBankingTrader, traderBak);
            order.setBankCode(personalBankingTrader.getBankCode());

            order.setPayer(personalBankingTrader.toString());
        } else if (null != req.getPayer() && req.getPayer() instanceof UnionOnlineTrader) {
            UnionOnlineTrader unionOnlineTrader = new UnionOnlineTrader();
            ReflectUtils.copyPropertiesIgnoreNull(unionOnlineTrader, traderBak);
            String bankCardNo = unionOnlineTrader.getBankCardNo();
            buildCardInfo(bankCardNo, order);
            unionOnlineTrader.setBankCardNo("");
            order.setBankCode(unionOnlineTrader.getBankCode());
            order.setPayer(unionOnlineTrader.toString());

        } else {
            order.setPayer(req.getPayer().toString());
        }

        order.setCashAmount(req.getAmount());
        order.setAmount(req.getAmount());
        order.setCurrencyType(req.getCurrencyType());
        order.setProcedureFee(req.getProcedureFee());
        order.setPayerType(req.getPayer().getType().name());
        order.setPayerId(req.getPayer().getTraderId());
        order.setCreateTime(now);
        order.setState(TxsConstants.PayState.IN_PROCESS.code);
        order.setTransactionType(req.getTransactionType());
        order.setRemark(req.getRemark());
        order.setRepayState(TxsConstants.RepayState.NOT_REPAY_ORDER.code);
        order.setSrcChannelType(req.getSrcChannelType());
        order.setAttachData(req.getAttachData());
        order.setClientIp(req.getClientIp());
        order.setNotifyUrl(req.getNotifyUrl());
        order.setOrderInfo(req.getOrderInfo());
        order.setRedirectUrl(req.getRedirectUrl());
        order.setSettlementState(TxsConstants.SettleMentState.NOT_PROCESS.code);
        order.setTerminalNo(req.getTerminalNo());
        if (StringUtils.isNotBlank(req.getTerminalNo())) {
            CumTerminalInfo cumTerminalInfo = cumCacheService.queryCumTerminalInfo(req.getTerminalNo());
            if (cumTerminalInfo != null) {
                order.setTerminalName(cumTerminalInfo.getTerminalName());
            }
        }
        order.setTransactionStartTime(req.getTransactionStartTime());
        order.setTransactionEndTime(req.getTransactionEndTime());
        order.setBusinessInstIdFst(businessInst.getBusinessExamId());
        order.setBusinessCodeFst(businessInst.getBusinessCode());
        order.setSecondCharged(Constants.SECOND_CHARGED.NO.code);
        order.setAgentCustomerCode(req.getAgentCustomerCode());
        order.setCustomername(cumCacheService.getCustomerInfo(req.getCustomerCode(), req.getCustomerCode(), "1").getName());
        if (StringUtils.isNotBlank(req.getAgentCustomerCode())) {
            CustomerInfo angentCustomer = cumCacheService.getCustomerInfo(req.getAgentCustomerCode(), req.getAgentCustomerCode(), "1");
            if (angentCustomer != null) {
                String agentCustomerName = angentCustomer.getName();
                order.setAgentCustomerName(agentCustomerName);
            }

        }
        order.setPayPassWay(req.getPayPassWay());
        order.setRedirectFailUrl(req.getRedirectFailUrl());
        if (StringUtils.isBlank(businessInst.getNoCreditcards()) ||
                StringUtils.equals(businessInst.getNoCreditcards(), TxsConstants.NoCreditCards.FALSE.code)) {
            if (req.getNoCreditCards()) {
                order.setNoCreditCards(TxsConstants.NoCreditCards.TRUE.code);
            } else {
                order.setNoCreditCards(TxsConstants.NoCreditCards.FALSE.code);
            }
        } else if (StringUtils.equals(businessInst.getNoCreditcards(), TxsConstants.NoCreditCards.TRUE.code)) {
            order.setNoCreditCards(TxsConstants.NoCreditCards.TRUE.code);
        }

        order.setApiVersion(req.getVersion());

        CustomerInfo customerInfo = cumCacheService.getCustomerInfo(req.getCustomerCode(), req.getCustomerCode(), UserType.PAS_USER.code);
        order.setBusinessMan(customerInfo.getBusinessMan());
        order.setBusinessManId(customerInfo.getBusinessManId());
        order.setCompanyName(customerInfo.getCompanyName());
        order.setCompanyId(customerInfo.getCompanyId());
        
        order.setD1HolidayCharged(req.getD1HolidayCharged());
        order.setD1HolidayChargePer(req.getD1HolidayChargePer());
        order.setD1HolidayChargeRate(req.getD1HolidayChargeRate());
        order.setChargedByBankCodeArea(req.getChargedByBankCodeArea());
        
        order.setCashAmount(order.getAmount());
        if(serverEnv != null){
            order.setServerIp(serverEnv.getServerIp());
        }
        else {
            order.setServerIp(ServerEnv.SERVER_IP);
        }

        txsPayTradeOrderMapper.insertSelective(order);
        if (null != traderBak) {
            order.setPayer(traderBak.toString());
            req.setPayer(traderBak);
        }

        if (null != req.getPayer().toString()) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(req.getPayer()));
            
            String bankIcon = null;
            String bankCardAttr = null;
            String cardType = null;
            if (jsonObject != null) {
                bankIcon = jsonObject.getString("bankIcon");
                bankCardAttr = jsonObject.getString("bankCardAttr");
                if (!StringUtils.isEmpty(bankCardAttr)) {
                    if (TxsConstants.CardType.DEBIT.code.equals(bankCardAttr)) {
                        cardType = "D";
                    } else {
                        cardType = "C";
                    }
                }
                if (StringUtils.isNotBlank(cardType)) {
                    order.setCardType(cardType);
                }
                if (StringUtils.isNotBlank(bankIcon)) {
                    order.setBankCode(bankIcon);
                }
            }
        }

        return order;
    }
//    /**
//     *  根据业务实例CustomerBusinessInstance，重新变更TxsPayTradeOrder记录
//     * @param order
//     * @param businessInst
//     */
//    public void buildTradeOrderWithBusinessInst(TxsPayTradeOrder order, CustomerBusinessInstance businessInst) {
//    	SettCycleRuleInst settCycleRuleInst = businessInst.getSettCycleRuleInst();
//        if (settCycleRuleInst != null) {
//            order.setSettCycleRuleCode(settCycleRuleInst.getSettCycleRuleCode());
//        }
//        order.setBusinessInstId(businessInst.getBusinessExamId());
//        order.setBusinessCode(businessInst.getBusinessCode());
//        
//        order.setProcedureRate(businessInst.getRateParam());
//        order.setRateMode(businessInst.getRateMode());
//
//        order.setMaxFee(businessInst.getMaxFee());
//        order.setMinFee(businessInst.getMinFee());
//        order.setFeePer(businessInst.getFeePer());
//        //BusinessInstIdFst 卓林加这种东西 干什么 
////        order.setBusinessInstIdFst(businessInst.getBusinessExamId());
////        order.setBusinessCodeFst(businessInst.getBusinessCode());
//        
//    }
//
    private void buildCardInfo(String bankCardNo, TxsPayTradeOrder order) {
        try {
            if (StringUtils.isNotEmpty(bankCardNo)) {
                TxsCardBin txsCardBin = self.findCardBinInfo(bankCardNo);
                order.setBankCode(txsCardBin.getBankIcon());
                order.setCardNoEnc(hessianService.encryptData(bankCardNo));
                order.setCardNoMosaic(com.epaylinks.efps.common.util.StringUtils.mosaic(bankCardNo, '*', 6, 4));
                order.setQuickpayCardno(MD5Utils.getMD5(bankCardNo));
                if (StringUtils.equals(txsCardBin.getCardType(), "借记卡")) {
                    order.setCardType(TxsConstants.OrderCardType.DEBIT.code);
                } else {
                    order.setCardType(TxsConstants.OrderCardType.CREDIT.code);
                }
            }

        } catch (MalformedURLException e) {
            logException(e);
            throw new AppException(TxsCode.ENCRYPT_DECRYPT_ERROR.code);
        }

    }
//
//    /**
//     * 不会更新缓存中的request，生成TxsTraderOrder并插入数据库，然后调用支付的相关支付接口，如果不抛出异常，说明调用成�?会抛出异�?
//     *
//     * @param payToken
//     * @param request
//     * @return
//     */
//    @Logable(businessTag = "payment")
//    public PaymentResponse pay(String payToken, TxsPayRequest request, String businessCode) {
//        TxsPayRequest req = self.combineRequestWithCachedReq(payToken, request);
//
//        self.checkTxsPreOrderSuccessOrFail(req.getCustomerCode(), req.getOutTradeNo());
//
//        if ((req.getTransactionEndTime().getTime() - new Date().getTime()) < 0) {
//            throw new AppException(TxsCode.NOW_MORETHAN_ENDTIME.code, TxsCode.NOW_MORETHAN_ENDTIME.message);
//        }
//
//        CustomerBusinessInstance businessInst = self.calcProcedure(req, businessCode);
//
//        List<CumBusinessParamInst> cumBusinessParamInsts = cumCacheService.getCustomerBusinessParamInst(businessInst.getBusinessExamId());
//
//        checkBusinessParam(req.getAmount(), cumBusinessParamInsts);
//
//        if (StringUtils.isNotBlank(businessInst.getBusinessCode())
//                && businessInst.getBusinessCode().endsWith(Constants.EfpsBasicPayService.WXNATIVE.code)) {
//            //微信主扫
//            String wxNativeTojs = getBusinessParamInst(cumBusinessParamInsts, Constants.BusinessParamCode.WX_NATIVE_TOJS.code);
//            if ("1".equals(wxNativeTojs)) {
//                PaymentResponse paymentResponse = new PaymentResponse();
//                paymentResponse.setContentType(TxsConstants.ContentType.QRCODE.code);
//
//                String url = MessageFormat.format("{0}?token={1}", cashierUrl, payToken);
//                JSONObject tradeQrcode = new JSONObject();
//                tradeQrcode.put("code_url", url);
//                paymentResponse.setContent(tradeQrcode.toString());
//
//                return paymentResponse;
//            }
//        }
//
//
//        request.setProcedureFee(req.getProcedureFee());    //传到外面以便在接口返�?
//
//        if (StringUtils.isEmpty(req.getTradeCustomerCode()) || req.getTradeCustomerCode().equals(req.getCustomerCode())) {//交易商户没值，才走子商户，交易商户其实就是另外一种形式的子商户
//            chooseSubCustomerCodeMode(req, request, businessInst);
//        }
//
//        self.getAgentCustomerCode(req, businessInst);
//        TxsPayTradeOrder order = self.createAndInsertTxsPayTradeOrder(req, businessInst);
//        order.setSubCustomerCode(req.getSubCustomerCode());
//        self.riskCalc(req, order);//使用缓存中的req的原因是保证有Trader
//        return self.callPaySystemAndUpdateOrder(order, req);
//    }
//
//    private void chooseSubCustomerCodeMode(TxsPayRequest cachedReq, TxsPayRequest request, CustomerBusinessInstance businessInst) {
//        Long channelCategoryId = 0l;
//        if (StringUtils.equals(cachedReq.getPayMethod(), PayMethod.UNION_SCANINGPAY.code) ||
//                StringUtils.equals(cachedReq.getPayMethod(), PayMethod.UNIONSWEEPCODE.code) ||
//                StringUtils.equals(cachedReq.getPayMethod(), PayMethod.UNION_JS.code)) {
//            channelCategoryId = Constants.ChannelCategory.UNION_QRCODE.code;
//        } else if (PayMethod.PROTOCOL_PAY.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.PROTOCOL_PAY_CREDIT.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.QUICK_PAY.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.QUICK_PAY_CREDIT.code.equals(cachedReq.getPayMethod()) ||
//                StringUtils.equals(cachedReq.getPayMethod(), PayMethod.FZ_NOCARD_PAY.code) ||
//                StringUtils.equals(cachedReq.getPayMethod(), PayMethod.FZ_NOCARD_PAY_CREDIT.code)) {
//            channelCategoryId = Constants.ChannelCategory.QUICK_PAY.code;
//        } else if (PayMethod.WECHAT_PUBLIC.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.WXSWEEPCODE.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.WXAPP_PAY.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.WXH5_PAY.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.WECHAT_SCANINGPAY.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.WX_MINI_PROGRAM.code.equals(cachedReq.getPayMethod())) {
//            channelCategoryId = Constants.ChannelCategory.WXPAY_OFFLINE.code;
//
//        } else if (PayMethod.ALIPAY_LIFE.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.ALISWEEPCODE.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.ALI_SCANINGPAY.code.equals(cachedReq.getPayMethod())) {
//            channelCategoryId = Constants.ChannelCategory.ALIPAY_OFFLINE.code;
//        } else if (PayMethod.UNION_ONLINE.code.equals(cachedReq.getPayMethod()) ||
//                PayMethod.UNION_ONLINE_CREDIT.code.equals(cachedReq.getPayMethod())) {
//            channelCategoryId = Constants.ChannelCategory.UNION_ONLINE_STANDARD.code;
//        }
//
//        if (channelCategoryId != 0) {
//            String subCustomerCode = cumService.getMerchantCode(cachedReq.getCustomerCode(), cachedReq.getSubCustomerCode(), channelCategoryId, "0", cachedReq.getAmount());
//            if (cachedReq.getPayer() != null && StringUtils.isNotBlank(cachedReq.getSubCustomerCode()) && StringUtils.isBlank(subCustomerCode)) {
//                throw new AppException(TxsCode.SUBMERCHANTCODE_NOT_MATCH.code, TxsCode.SUBMERCHANTCODE_NOT_MATCH.message);
//            }
//
//            //此步目的为，如果能查到子商户号，则设置为子商户号�?如果传的子商户号有误，或者没配子商户，则设置为null，即不走子商户号模式，业务需�?
//            cachedReq.setSubCustomerCode(subCustomerCode);
//
//            Trader trader = request.getPayer();
//            if (trader != null) {
//                request.getPayer().setSubCustomerCode(subCustomerCode);
//                request.getPayer().setChannelCategoryId(channelCategoryId);
//                cachedReq.getPayer().setSubCustomerCode(subCustomerCode);
//                cachedReq.getPayer().setChannelCategoryId(channelCategoryId);
//            }
//        }
//    }
//
//    /**
//     * 风控处理
//     * 交易风控处理放在确认支付流程的原因：
//     * 风控过程有金额，笔数，银行卡等信息，银行卡号信息只有在确认支付的过程才会有（例如统一下单�?
//     *
//     * @param txsPayRequest
//     * @param amount
//     */
//    public void riskCalc(TxsPayRequest txsPayRequest, TxsPayTradeOrder order) {
//        Map<String, String> indexs = new HashMap<>();
//        indexs.put(TxsConstants.RcIndex.AMOUNT.code, order.getAmount() + "");
//        indexs.put(TxsConstants.RcIndex.TRADE_NUM.code, "1");
//        Trader trader = txsPayRequest.getPayer();
//        PayerService payerService = (PayerService) SpringContextUtils.getBean(trader.getType().name());
//        Map<String, String> businessTargetIds = payerService.checkBusinessTagerType(trader);
//        businessTargetIds.put(TxsConstants.BusinessTagerType.CUSTOMER_CODE.code, order.getCustomerCode());
//
//        if (TraderType.QuickPay.equals(trader.getType())) {
//            QuickPayTrader quickPayTrader = (QuickPayTrader) trader;
//            businessTargetIds.put(TxsConstants.BusinessTagerType.IDENTITY_CARD.code, quickPayTrader.getCardNo());
//        }
//
//        if (businessTargetIds == null || businessTargetIds.isEmpty()) {
//            return;
//        }
//        CommonOuterResponse result = CommonOuterResponse.fail(TxsCode.RC_NOTPASS.code, TxsCode.RC_NOTPASS.message);
//        try {
//            RcCalculateRequest rcCalculateRequest = riskCalcService.calculate(Constants.rcBusinessType.GATEWAY_PAY.code, order.getBusinessCode(), order.getPayMethod(), order.getOutTradeNo(),
//                    order.getTransactionNo(), indexs, businessTargetIds);
//            List<RcCalculateRequest> rcCalculateRequests = new ArrayList<>();
//            rcCalculateRequests.add(rcCalculateRequest);
//            result = rcService.calculateResponse(rcCalculateRequests);
//        } catch (Exception e) {
//            // TODO: handle exception
//            //风控过程中抛异常
////            String errorCode = FeignExceptionUtils.parseException(e, TxsCode.FEIGN_RCEXCEPTION.code);
////            self.updateTxsPayOrderFailed(order.getId(), errorCode, e);
////            throw new AppException(errorCode);
//            self.logException("调用风控系统异常:" + order.getTransactionNo());
//            self.logException(e);
//            result = CommonOuterResponse.success(CommonOuterResponse.SUCCEE, null);
//        }
//        if (!result.isSuccess()) {
//            //风控不过
//            AppException e = new AppException(result.getReturnCode(), result.getReturnMsg());
//            self.updateTxsPayOrderAndPreOrderFailed(order, result.getReturnCode(), e, result.getReturnMsg());
//            throw e;
//        }
//    }
//
//    /**
//     * 获取代理商客户编�?
//     *
//     * @param req
//     * @param businessInst
//     */
//    @Logable(businessTag = "getAgentCustomerCode", outputArgs=false)
//    public void getAgentCustomerCode(TxsPayRequest req, CustomerBusinessInstance businessInst) {
//        List<CumBusinessParamInst> cumBusinessParamInsts = req.getCumBusinessParamInsts();
//        if (cumBusinessParamInsts != null && !cumBusinessParamInsts.isEmpty()) {
//            for (CumBusinessParamInst cumBusinessParamInst : cumBusinessParamInsts) {
//                if (StringUtils.equals(businessInst.getBusinessExamId(), cumBusinessParamInst.getBusinessExamId())
//                        && Constants.BusinessParamCode.AGENT_CUSTOMER_CODE.code.equals(cumBusinessParamInst.getCode())) {
//                    req.setAgentCustomerCode(cumBusinessParamInst.getValue());
//                    break;
//                }
//            }
//        }
//    }
//
//    /**
//     * 确认支付接口，不会抛出异�?businessCode:可空，如果为空，则使用支付方式确定业务编�?
//     *
//     * @param payToken
//     * @param request
//     * @param businessCode:可空，如果为空，则使用支付方式确定业务编�?
//     * @return
//     */
//    @Logable(businessTag = "confirmPay")
//    public PaymentResponse confirmPay(String payToken, TxsPayRequest request, String businessCode) {
//        PaymentResponse paymentResponse = new PaymentResponse();
//        try {
//            //self.checkTxsPreOrderSuccessOrFail(request.getCustomerCode(), request.getOutTradeNo()); //该动作放在下一行合并缓存后去做
//
//            paymentResponse = self.pay(payToken, request, businessCode);
//        } catch (Exception e) {
//            e.printStackTrace();
//            // 调用支付系统异常，说明获取执行支付所需参数失败，但是并不需要修改PreOrder的状态，因为用户可以在界面选择重新支付
//            // 各种异常的处理方式一致，不用分开�?
//            returnCodeUtil.buildResponse(paymentResponse, e, TxsConstants.detailReturnCode.RETURN_FAIL.code);
//        }
//        return paymentResponse;
//    }
//
//    /**
//     * 更新数据库订单状态为失败，ex参数仅仅是为了记录日志避免无法查询异常信�?
//     *
//     * @param txsOrderId
//     */
//    @Logable(businessTag = "updateTxsPayFailed", errorCode = "0981")
//    public void updateTxsPayOrderFailed(Long dbId, String errorCode, String errorMsg, Exception ex) {
//        try {
//            TxsPayTradeOrder o = new TxsPayTradeOrder();
//            o.setEndTime(new Date());
//            o.setErrorCode(errorCode);
//            o.setErrorMsg(errorMsg);
//
//            String preOrderState = TxsConstants.PreOrderState.IN_PROCESS.code;
//            if ("0101".equals(errorCode) || "0201".equals(errorCode)
//                    || TxsCode.FEIGN_PAYEXCEPTION.code.equals(errorCode)
//                    || TxsCode.FAIL.code.equals(errorCode)) {
//                o.setState(TxsConstants.PayState.IN_PROCESS.code);
//                preOrderState = TxsConstants.PreOrderState.IN_PROCESS.code;
//            } else {
//                o.setState(TxsConstants.PayState.FAIL.code);
//                preOrderState = TxsConstants.PreOrderState.TRANSACTION_FAIL.code;
//            }
//
//            o.setId(dbId);
//            this.txsPayTradeOrderMapper.updateByPrimaryKeySelective(o);
//
//            TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByPrimaryKey(dbId);
//            if (PayMethod.PROTOCOL_PAY.code.equals(txsPayTradeOrder.getPayMethod())
//                    || PayMethod.PROTOCOL_PAY_CREDIT.code.equals(txsPayTradeOrder.getPayMethod())
//                    || PayMethod.FZ_NOCARD_PAY.code.equals(txsPayTradeOrder.getPayMethod())
//                    || PayMethod.FZ_NOCARD_PAY_CREDIT.code.equals(txsPayTradeOrder.getPayMethod())) {
//                TxsPreOrder pre = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(txsPayTradeOrder.getOutTradeNo(), txsPayTradeOrder.getCustomerCode());
//
//                pre.setUpdateTime(new Date());
//                pre.setPayState(preOrderState);
//                pre.setTransactionNo(txsPayTradeOrder.getTransactionNo());
//
//                pre.setBusinessCode(txsPayTradeOrder.getBusinessCode());
//                pre.setChannelRespCode(txsPayTradeOrder.getErrorCode());
//                pre.setChannelRespMsg((txsPayTradeOrder.getErrorMsg() == null || txsPayTradeOrder.getErrorMsg().length() > 100) ? null : txsPayTradeOrder.getErrorMsg());
//
//                txsPreOrderMapper.updateByPrimaryKeySelectiveForTradeOrderSuccess(pre);
//            }
//        } catch (Exception e) {
//            self.logException(e);
//        }
//    }
//
//    /**
//     * 更新数据库订单状态为失败，ex参数仅仅是为了记录日志避免无法查询异常信�?
//     *
//     * @param txsOrderId
//     */
//    @Logable(businessTag = "updateTxsPayFailed", errorCode = "0981")
//    public void updateTxsPayOrderFailed(Long dbId, String errorCode, Exception ex, String... errorMessage) {
//        try {
//            TxsPayTradeOrder o = new TxsPayTradeOrder();
//            o.setEndTime(new Date());
//            o.setErrorCode(errorCode);
//            if (errorMessage != null) {
//                o.setErrorMsg((errorMessage[0].length() > 100) ? errorMessage[0].substring(0, 100) : errorMessage[0]);
//            }
//
//            if ("0101".equals(errorCode) || "0201".equals(errorCode)
//                    || TxsCode.FEIGN_PAYEXCEPTION.code.equals(errorCode)
//                    || TxsCode.FAIL.code.equals(errorCode)) {
//                o.setState(TxsConstants.PayState.IN_PROCESS.code);
//            } else {
//                o.setState(TxsConstants.PayState.FAIL.code);
//            }
//
//            o.setId(dbId);
//
//            this.txsPayTradeOrderMapper.updateByPrimaryKeySelective(o);
//        } catch (Exception e) {
//            self.logException(e);
//        }
//    }
//
//    /**
//     * 更新数据库订单状态为失败，ex参数仅仅是为了记录日志避免无法查询异常信�?
//     *
//     * @param txsOrderId
//     */
//    @Logable(businessTag = "updateTxsPayFailed", errorCode = "0981", outputArgs=false)
//    @Transactional
//    public void updateTxsPayOrderAndPreOrderFailed(TxsPayTradeOrder order, String errorCode, Exception ex, String... errorMessage) {
//        try {
//            Date now = new Date();
//            TxsPayTradeOrder o = new TxsPayTradeOrder();
//            o.setEndTime(now);
//            o.setErrorCode(errorCode);
//            if (errorMessage != null) {
//                o.setErrorMsg((errorMessage[0].length() > 100) ? errorMessage[0].substring(0, 100) : errorMessage[0]);
//            }
//
//            if ("0101".equals(errorCode) || "0201".equals(errorCode)
//                    || TxsCode.FEIGN_PAYEXCEPTION.code.equals(errorCode)
//                    || TxsCode.FAIL.code.equals(errorCode)) {
//                o.setState(TxsConstants.PayState.IN_PROCESS.code);
//            } else {
//                o.setState(TxsConstants.PayState.FAIL.code);
//
//                TxsPreOrder txsPreOrderUpd = new TxsPreOrder();
//                txsPreOrderUpd.setCustomerCode(order.getCustomerCode());
//                txsPreOrderUpd.setOutTradeNo(order.getOutTradeNo());
//                txsPreOrderUpd.setPayState(TxsConstants.PreOrderState.TRANSACTION_FAIL.code);
//
//                txsPreOrderUpd.setTransactionNo(order.getTransactionNo());
//
//                txsPreOrderUpd.setBusinessCode(order.getBusinessCode());
//
//                txsPreOrderUpd.setErrorCode(errorCode);
//                txsPreOrderUpd.setUpdateTime(now);
//                if (errorMessage != null) {
//                    txsPreOrderUpd.setErrorMsg(o.getErrorMsg());
//                }
//                txsPreOrderMapper.updateFinnalStateForUnCashier(txsPreOrderUpd);
//            }
//
//            o.setId(order.getId());
//            this.txsPayTradeOrderMapper.updateByPrimaryKeySelective(o);
//
//        } catch (Exception e) {
//            self.logException(e);
//        }
//    }
//
    /**
     * 更新预下单表
     *
     * @param
     */
    @Transactional
    public void updateTxsPreOrderState(TxsPayTradeOrder txsPayTradeOrder,  TxsPreOrder txsPreOrder) {
        try {

//			txsPreOrder.setEndTime(new Date());//确认支付过程不应该更新终态时�?
            txsPreOrder.setUpdateTime(new Date());//更新UpdateTime

//			txsPreOrder.setTransactionNo(txsPayTradeOrder.getTransactionNo());
            txsPreOrder.setProcedureFee(txsPayTradeOrder.getProcedureFee());
            txsPreOrder.setProcedureRate(txsPayTradeOrder.getProcedureRate());
            txsPreOrder.setRateMode(txsPayTradeOrder.getRateMode());
            txsPreOrder.setBusinessCode(txsPayTradeOrder.getBusinessCode());
            txsPreOrder.setBusinessCodeFst(txsPayTradeOrder.getBusinessCode());
            txsPreOrder.setBusinessInstIdFst(txsPayTradeOrder.getBusinessInstIdFst());
            txsPreOrder.setSecondCharged(txsPayTradeOrder.getSecondCharged());
            txsPreOrder.setAgentCustomerCode(txsPayTradeOrder.getAgentCustomerCode());
            txsPreOrder.setAgentCustomerName(txsPayTradeOrder.getAgentCustomerName());
            txsPreOrder.setOrderId(txsPayTradeOrder.getOrderId());
            txsPreOrder.setPayPassWay(txsPayTradeOrder.getPayPassWay());
            txsPreOrder.setQuickpayCardno(txsPayTradeOrder.getQuickpayCardno());

            txsPreOrder.setCardNoEnc(txsPayTradeOrder.getCardNoEnc());
            txsPreOrder.setCardNoMosaic(txsPayTradeOrder.getCardNoMosaic());
            txsPreOrder.setCertNo(txsPayTradeOrder.getCertNo());
            txsPreOrder.setCertNoHash(txsPayTradeOrder.getCertNoHash());
            txsPreOrder.setCertNoMosaic(txsPayTradeOrder.getCertNoMosaic());
            txsPreOrder.setCardOwner(txsPayTradeOrder.getCardOwner());
            txsPreOrder.setCardOwnerHash(txsPayTradeOrder.getCardOwnerHash());
            txsPreOrder.setCardOwnerMosaic(txsPayTradeOrder.getCardOwnerMosaic());
            txsPreOrder.setBankCode(txsPayTradeOrder.getBankCode());
            txsPreOrder.setTerminalName(txsPayTradeOrder.getTerminalName());
            txsPreOrder.setFeePer(txsPayTradeOrder.getFeePer());
            
            txsPreOrder.setD1HolidayCharged(txsPayTradeOrder.getD1HolidayCharged());
            txsPreOrder.setD1HolidayChargePer(txsPayTradeOrder.getD1HolidayChargePer());
            txsPreOrder.setD1HolidayChargeRate(txsPayTradeOrder.getD1HolidayChargeRate());
            txsPreOrder.setChargedByBankCodeArea(txsPayTradeOrder.getChargedByBankCodeArea());


            Date now = new Date();
            //更新txsPayTradeOrder表状态
            txsPayTradeOrder.setState(TxsConstants.PayState.SUCCESS.code);
            txsPayTradeOrder.setUpdateTime(now);
            txsPayTradeOrder.setEndTime(now);
            txsPayTradeOrder.setTransactionEndTime(now);
            txsPayTradeOrder.setSettlementState(TxsConstants.SettleMentState.SUCCESS.code);
            txsPayTradeOrderMapper.updateByPrimaryKeySelective(txsPayTradeOrder);

            //更新txsPreOrder表状态
            txsPreOrder.setUpdateTime(now);
            txsPreOrder.setPayState(TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code);
            txsPreOrder.setEndTime(txsPayTradeOrder.getEndTime());
            txsPreOrder.setTransactionEndTime(txsPayTradeOrder.getTransactionEndTime());
            txsPreOrder.setTransactionNo(txsPayTradeOrder.getTransactionNo());
            txsPreOrder.setSettlementState(TxsConstants.SettleMentState.SUCCESS.code);
            txsPreOrderMapper.updateByPrimaryKeySelective(txsPreOrder);

        } catch (Exception e) {
            self.logException(e);
        }
    }

    @Logable(businessTag = "logException")
    public void logException(Exception e) {
        // TODO Auto-generated method stub

    }

    @Logable(businessTag = "transaction_business_exception")
    public void logException(String error) {
        // TODO Auto-generated method stub

    }
//
//    @Logable(businessTag = "insideTransfer")
//    public PaymentResponse insideTransfer(TxsPayRequest request) {
//        request.publicPropCheck(transactionTime, endTimeSecond);
////		request.setCumBusinessParamInsts(null);
//        TxsPreOrder txsPreOrderCheck = self.checkCumStatusAndGetPreOrder(request.getOutTradeNo(), request.getCustomerCode());
//        // 当前时间
//        Date now = new Date();
//        request.setCreateTime(now);
//        //下单
//        if (txsPreOrderCheck != null) {// 重复支付
//            self.processRepeatRequest(request, now, txsPreOrderCheck);
//        } else {// 非重复支�?
//            self.processNotRepeatRequest(request, now);
//        }
//        CustomerBusinessInstance businessInst = self.calcProcedure(request, Constants.EfpsBasicPayService.ACCOUNT_PAY.code);
//
//        self.getAgentCustomerCode(request, businessInst);
//        TxsPayTradeOrder order = self.createAndInsertTxsPayTradeOrder(request, businessInst);
////		self.riskCalc(request, order);//保证有Trader... TODO  账户内转
//        return self.callPaySystemAndUpdateOrder(order, request);
//    }
//    
//
//   
//
//    @Logable(businessTag = "getValidTime")
//    public Long getValidTime(Long tranStartTime, Long tranEndTime, String payMethod) {
//        if (null == tranStartTime || null == tranEndTime || (tranEndTime < tranStartTime)) {
//            return 30L;
//        }
//
//        Long diff = tranEndTime - tranStartTime;
//        Long min = diff / 1000 / 60;
//        return min;
//    }
//
//
//    /**
//     * 根据商户号,支付方法,业务类型,支付类型得到该商户的业务实例
//     *
//     * @return
//     */
////    public CustomerBusinessInstance getBusinessInstId(String customerCode, String payMethod, String businessCode, String transactionType) {
////        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
////                CustomerBusinessInstance.STATUS_VALID, customerCode, UserType.PAS_USER.code);
////        CustomerBusinessInstance businessInst = self.decideBusinessInst(customerInstanceList, payMethod, businessCode, transactionType);
////
////        return businessInst;
////    }
//
//
//    /**
//     * 更新txsPreOrder订单为失败.不抛异常. 适用还未记txs_pay_trade_order表就已经失败的情交
//     *
//     * @param 商户号, 商户订单号
//     */
//    @Logable(businessTag = "updateTxsPreOrderFailed")
//    public Integer updateTxsPreOrderFailed(String customerCode, String outTradeNo, String errorCode, String errorMsg) {
//
//        if (null == customerCode || null == outTradeNo) {
//            return -3;
//        }
//
//
//        try {
//
//            TxsPreOrder pre = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(outTradeNo, customerCode);
//
//            if (null == pre) {
//                return -2;
//            }
//
//            pre.setUpdateTime(new Date());
//            pre.setPayState(TxsConstants.PreOrderState.TRANSACTION_FAIL.code);
//
//            pre.setChannelRespCode(errorCode);
//            pre.setChannelRespMsg(errorMsg.length() > 100 ? null : errorMsg);
//
//            Integer rows = txsPreOrderMapper.updateByPrimaryKeySelectiveForTradeOrderSuccess(pre);
//
//            return rows;
//
//        } catch (Exception e) {
//            self.logException(e);
//            return -1;
//        }
//
//    }
//
//    /**
//     * 若该笔商户单已经成功或失败,则直接抛异常
//     *
//     * @param 商户号, 商户订单号
//     */
//    @Logable(businessTag = "countTxsPreOrderSuccessOrFail")
//    public void checkTxsPreOrderSuccessOrFail(String customerCode, String outTradeNo) {
//
//        int successOrFailCnt = txsPreOrderMapper.selectCountSuccessOrFail(outTradeNo, customerCode);
//        if (successOrFailCnt > 0) {
//            AppException e = new AppException(TxsCode.ORIG_ORDER_DONE.code, TxsCode.ORIG_ORDER_DONE.message);
//            throw e;
//        }
//    }
//
//    public String getBusinessParamInst(List<CumBusinessParamInst> businessParamInstList, String code) {
//        String value = null;
//        if (code == null || businessParamInstList == null) {
//            return value;
//        }
//        for (CumBusinessParamInst bpi : businessParamInstList) {
//            //判断主商户最高分润比例
//            if (code.equalsIgnoreCase(bpi.getCode())) {
//                //先不判断是扣除谁的手续费
//                value = bpi.getValue();
//                break;
//            }
//        }
//        return value;
//    }
//    /**
//     * 校验订单位置授权
//     * @param unifiedPaymentRequest
//     */
//    public void checkOrderLocation(UnifiedPaymentRequest unifiedPaymentRequest) {
//    	CustomerInfo customerInfo = cumCacheService.getCustomerInfo(unifiedPaymentRequest.getCustomerCode(), unifiedPaymentRequest.getCustomerCode(), "1");
//    	CustomerLocationConfig customerLocationConfig = null;
//    	if(StringUtils.isBlank(customerInfo.getPlatCustomerCode())) {
//    		customerLocationConfig = custService.queryLocationConfig(customerInfo.getCustomerCode());
//    	}
//    	else {
//    		customerLocationConfig = custService.queryLocationConfig(customerInfo.getPlatCustomerCode());
//    	}
//    	
//		if(customerLocationConfig != null && "1".equals(customerLocationConfig.getCheckPayLocation())) {
//			String locationToken = unifiedPaymentRequest.getLocationToken();
//			if(StringUtils.isEmpty(locationToken)) {
//				throw new AppException(TxsCode.LOCATION_EMPTY.code);
//			}
//			OrderLocation orderLocation = transactionTokenService.getOrderLocation(locationToken);
//			if(orderLocation == null) {
//				throw new AppException(TxsCode.LOCATION_EMPTY.code);
//			}
//			//此处只校验商户单好了，加上商户号怕有父子商户号问题不好校验，只用订单问题不大，不会拿到其他商户的locationToken
//			if(!unifiedPaymentRequest.getOutTradeNo().equals(orderLocation.getOutTradeNo())) {
//				throw new AppException(TxsCode.LOCATION_NOT_MATCH.code);
//			}
//			if(orderLocation.getLatitude() == null || orderLocation.getLongitude() == null) {
//				throw new AppException(TxsCode.LOCATION_NOT_AUTH.code);
//			}
//			
//			Double distance = MathUtil.getDistance(orderLocation.getLatitude(), orderLocation.getLongitude(),
//					customerInfo.getLatitude(), customerInfo.getLongitude());
//			//实际距离大于限制的交易距离
//			if(customerLocationConfig.getPayLocationRange() != null && 
//					customerLocationConfig.getPayLocationRange().compareTo(distance.longValue()) < 0) {
//				throw new AppException(TxsCode.LOCATION_UNSUPPORT.code);
//			}
//		}
//    }
//    
//    /**
//     * 校验订单位置授权
//     * @param orderLocation
//     */
//    public void checkOrderLocationRealTime(OrderLocation orderLocation) {
//    	if(orderLocation == null) {
//			throw new AppException(TxsCode.LOCATION_EMPTY.code);
//		}
//    	CustomerInfo customerInfo = cumCacheService.getCustomerInfo(orderLocation.getCustomerCode(), orderLocation.getCustomerCode(), "1");
//    	CustomerLocationConfig customerLocationConfig = null;
//    	if(StringUtils.isBlank(customerInfo.getPlatCustomerCode())) {
//    		customerLocationConfig = custService.queryLocationConfig(customerInfo.getCustomerCode());
//    	}
//    	else {
//    		customerLocationConfig = custService.queryLocationConfig(customerInfo.getPlatCustomerCode());
//    	}
//    	
//		if(customerLocationConfig != null && "1".equals(customerLocationConfig.getCheckPayLocation())) {
//			
//			if(orderLocation.getLatitude() == null || orderLocation.getLongitude() == null) {
//				throw new AppException(TxsCode.LOCATION_NOT_AUTH.code);
//			}
//			
//			Double distance = MathUtil.getDistance(orderLocation.getLatitude(), orderLocation.getLongitude(),
//					customerInfo.getLatitude(), customerInfo.getLongitude());
//			//实际距离大于限制的交易距离
//			if(customerLocationConfig.getPayLocationRange() != null && 
//					customerLocationConfig.getPayLocationRange().compareTo(distance.longValue()) < 0) {
//				throw new AppException(TxsCode.LOCATION_UNSUPPORT.code);
//			}
//		}
//    }
//    
//    public Trader generateTrader(String payMethod, UnifiedPaymentRequest unifiedPaymentRequest) {
//    	Trader trader = null;
//    	if(PayMethod.WECHAT_PUBLIC.code.equals(payMethod) || 
//    			PayMethod.WECHAT_PUBLIC.code.equals(payMethod)) {
//    		trader = new WeChatSubscriptionTrader(unifiedPaymentRequest.getSubAppId(), unifiedPaymentRequest.getUserId(), null);
//    	}
//    	else if(PayMethod.ALIPAY_LIFE.code.equals(payMethod)) {
//    		trader = new AliLifeWindowTrader(unifiedPaymentRequest.getSubAppId(), unifiedPaymentRequest.getUserId());
//    	}
//    	else if(PayMethod.UNION_JS.code.equals(payMethod)) {
//    		trader = new UnionJsTrader(unifiedPaymentRequest.getUserId(), "商品");
//    	}
//    	return trader;
//    }
//    /**
//     * 下单确认一体化统一支付
//     * @param customerCodeHead
//     * @param unifiedPaymentRequest
//     * @return
//     */
//    @Logable(businessTag = "unifiedAndConfirmPayment")
//    public PaymentResponse unifiedAndConfirmPayment(String customerCodeHead,UnifiedPaymentRequest unifiedPaymentRequest) {
//    	UnifiedPaymentResponse unifiedPaymentResponse = new UnifiedPaymentResponse();
//		PaymentResponse paymentResponse = new PaymentResponse();
//		paymentResponse.setOutTradeNo(unifiedPaymentRequest.getOutTradeNo());
//		paymentResponse.setCustomerCode(unifiedPaymentRequest.getCustomerCode());
//		
//    	Trader trader = self.generateTrader(unifiedPaymentRequest.getPayMethod(), unifiedPaymentRequest);
//		unifiedPaymentRequest.setPayer(trader);
//		unifiedPaymentResponse = self.unifiedPayForCashier(customerCodeHead, unifiedPaymentRequest,ZFTransaction);
//		
//		if(TxsCode.SUCCESS.code.equals(unifiedPaymentResponse.getReturnCode())) {
//			TxsPayRequest payRequest = new TxsPayRequest();
//			paymentResponse = self.confirmPay(unifiedPaymentResponse.getOrderToken(), payRequest, null);
//		}
//		else {
//			paymentResponse.setReturnCode(unifiedPaymentResponse.getReturnCode());
//			paymentResponse.setReturnMsg(unifiedPaymentResponse.getReturnMsg());
//		}
//		
//		return paymentResponse;
//    }
//    
//   
//    
//    
//    /*
//     * 二次计费较底部接口
//     * 返回成功表示其计费是二次计费的
//     */
//    @Logable(businessTag = "calcProcedureFeeForTxsSecond")
//    public ProcedureResponse calcProcedureFeeForTxsSecond(String customerCode, Long amount,
//                                                          String businessCode, int orderCount, String userType, String bankIcon,
//                                                          String chargedByBankCodeArea,
//                                                          String tranDateyyyyMMdd, Short cardType) {
//
//        if (StringUtils.isEmpty(customerCode)
//                || StringUtils.isEmpty(businessCode)
//                || StringUtils.isEmpty(userType)
//                || null == amount) {
//            throw new AppException(TxsCode.PARAM_ERROR.code);
//        }
//
//        ProcedureResponse procedureResponse = new ProcedureResponse();
//
//        List<CustomerBusinessInstance> customerInstanceList = cumCacheService.getCustomerBusinessInsts(customerCode,
//                CustomerBusinessInstance.STATUS_VALID, customerCode, userType);
//        
//        CustomerBusinessInstance businessInst = null;
//        try{
//        	businessInst = decideBusinessInstForSecendCalcFee(customerInstanceList, businessCode, cardType);
//        }catch (AppException e) {
//        	procedureResponse.setReturnCode(e.getErrorCode());
//        	procedureResponse.setReturnMsg(e.getErrorMsg());
//        	return procedureResponse;
//        }
//        
//        if (null == businessInst) {
//        	procedureResponse.setReturnCode(TxsCode.SECOND_CHARGE_NONEED.code);
//        	procedureResponse.setReturnMsg(TxsCode.SECOND_CHARGE_NONEED.code);
//        	return procedureResponse;
//        }
//        
//        //
//        //判断是否要收取d1交易节假日上游手续费 begin
//        //
//        String dateyyyyMMdd = tranDateyyyyMMdd;
//        if (StringUtils.isEmpty(dateyyyyMMdd)) {
//        	Date date = new Date();
//        	dateyyyyMMdd = DateUtils.formatDate(date, "yyyyMMdd");
//        }
//        Boolean needChargeD1HolidayFloat = procedureAisistService.judgeNeedD1TranDoHolidayCharge(businessInst.getBusinessCode(), 
//        		(null == businessInst.getSettCycleRuleInst())? null:businessInst.getSettCycleRuleInst().getSettCycleRuleCode(), dateyyyyMMdd);
//        String chargeFloat = null;
//        if (needChargeD1HolidayFloat) {
//        	chargeFloat = CustomerBusinessInstance.CHARGE_D1_HOLIDAY_FEE;
//        }
//        
//        //
//        //判断是否要收取d1交易节假日上游手续费 end
//        //
//        
//        commonService.payLog("计费前:"+businessInst.getBusinessExamId()+"|"+amount+"|"+orderCount+"|"+bankIcon+"|"+chargeFloat);
//        Long procedureFee = businessInst.calcProcedureFee(amount, orderCount, bankIcon, chargeFloat);
//        procedureResponse.setProcedure(procedureFee);
//        procedureResponse.setCustomerBusinessInstance(businessInst);
//        procedureResponse.setChargedD1HolidayProcedure(chargeFloat);
//        procedureResponse.setChargedByBankCodeArea(chargedByBankCodeArea);
//
//        return procedureResponse;
//    }    
//    
//    
//    /*
//     * 返回成功表示已经经过重新计费
//     */
//    @Logable(businessTag = "secondCalcChargeForOrder")
//    public ProcedureResponse secondCalcChargeForOrder(String transactionNo, Short cardType) {
//    	ProcedureResponse response = new ProcedureResponse();
//    	TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNo(transactionNo);
//    	
//    	String customerCode =  txsPayTradeOrder.getCustomerCode();
//    	Long amount  =  txsPayTradeOrder.getAmount();
//        String businessCode  =  txsPayTradeOrder.getBusinessCode();
//        int orderCount = 1;
//        String userType = UserType.PAS_USER.code;
//        String bankIcon  =  txsPayTradeOrder.getBankCode();
//        String chargeFloat  =  txsPayTradeOrder.getD1HolidayCharged();
//        String chargedByBankCodeArea = txsPayTradeOrder.getChargedByBankCodeArea();
//        
//        Date createDate = txsPayTradeOrder.getCreateTime();
//        String tranDate = null;
//        try {
//        	tranDate = DateUtils.formatDate(createDate, "yyyyMMdd");
//        }catch(Exception e) {
//        	
//        }
//
//    	
//    	response = self.calcProcedureFeeForTxsSecond(customerCode, amount,
//                businessCode, orderCount, userType, bankIcon, chargedByBankCodeArea, 
//                tranDate, cardType);
//    	
//    	return response;
//    }
//    /*
//     * 返回成功表示已经经过重新计费
//     */
//    @Logable(businessTag = "calcProcedureAfterPay")
//    public ProcedureResponse calcProcedureAfterPay(ProcedureFeeCalcuRequest procedureFeeCalcuRequest) {
//    	ProcedureResponse response = new ProcedureResponse();
//    	TxsPayTradeOrder txsPayTradeOrder = txsPayTradeOrderMapper.selectByTransactionNo(procedureFeeCalcuRequest.getTransactionNo());
//    	
//    	if(txsPayTradeOrder == null) {
//    		throw new AppException(TxsCode.TRADEORDER_NOTFOUND.code);
//    	}
//    	//不能有这个限制，否则T+1订单，txs先成功后，pay再来重新计算手续费，得不到正确的费率
////    	if(Constants.PayState.SUCCESS.code.equals(txsPayTradeOrder.getState())) {
////    		throw new AppException(TxsCode.NOT_SUPPORT_RECHARGE_PRODUCE.code);
////    	}
//    	Long payAmount = txsPayTradeOrder.getAmount();
//    	
//    	boolean allowCashAmountCharge = splitPayService.allowCashAmountCharge(txsPayTradeOrder.getPayMethod(), procedureFeeCalcuRequest.getCashAmount(), txsPayTradeOrder.getAmount());
//    	if(procedureFeeCalcuRequest.getCashAmount() != null) {
//    		txsPayTradeOrder.setCashAmount(procedureFeeCalcuRequest.getCashAmount());
//    	}
//    	if(procedureFeeCalcuRequest.getCardType() != null) {
//    		txsPayTradeOrder.setCardType(procedureFeeCalcuRequest.getCardType());
//    	}
//    	if(allowCashAmountCharge) {
//    		payAmount = procedureFeeCalcuRequest.getCashAmount();
//    		txsPayTradeOrder.setIsSettWithCashAmount(Constants.IsSettWithCashAmount.YES.code);
//    	}
//    	txsPayTradeOrder.setCouponAmount(procedureFeeCalcuRequest.getCouponAmount());
//    	
//    	
//    	String customerCode =  txsPayTradeOrder.getCustomerCode();
//        String businessCode  =  txsPayTradeOrder.getBusinessCode();
//        
//        
//        ProcedureFeeResult procedureFeeResult = self.calcProcedure(customerCode, payAmount, null, businessCode, txsPayTradeOrder.getTransactionType(), 
//        		null, txsPayTradeOrder.getBankCode(), null, procedureFeeCalcuRequest.getCardType());
//        
//        Long procedureFee = procedureFeeResult.getProcedureFee();
//        CustomerBusinessInstance customerBusinessInstance = procedureFeeResult.getBusinessInst();
//        
//        txsPayTradeOrder.setProcedureFee(procedureFee);
//        txsPayTradeOrder.setChargedByBankCodeArea(procedureFeeResult.getChargedByBankCodeArea());
//        txsPayTradeOrder.setD1HolidayCharged(procedureFeeResult.getChargedD1HolidayProcedure());
//        // 万一改了手续费，虽然用的一个业务，还是用最新的费率参数
//        buildTradeOrderWithBusinessInst(txsPayTradeOrder, customerBusinessInstance);
//        
//    	txsPayTradeOrderMapper.updateByPrimaryKeySelective(txsPayTradeOrder);
//    	
//    	response.setCashAmount(txsPayTradeOrder.getCashAmount());
//    	response.setCouponAmount(txsPayTradeOrder.getCouponAmount());
//    	response.setAmount(txsPayTradeOrder.getAmount());
//    	response.setCustomerBusinessInstance(customerBusinessInstance);
//    	response.setChargedD1HolidayProcedure(procedureFeeResult.getChargedD1HolidayProcedure());
//    	response.setChargedByBankCodeArea(procedureFeeResult.getChargedByBankCodeArea());
//    	response.setProcedure(procedureFee);
//    	
//    	return response;
//    }
    
	/**
	 * 通过银行卡号找卡bin信息
	 * @param bankCardNo
	 * @return
	 */
	@Logable(businessTag = "findCardBinInfo")
	public TxsCardBin findCardBinInfo(String bankCardNo) {
		TxsCardBin txsCardBin = null;
		for (int i = 10; i > 1; i--) {
			String cardNoRange = bankCardNo.substring(0, i);
			txsCardBin = txsCardBinMapper.selectByCardNoRange(cardNoRange,bankCardNo.length());
			if (txsCardBin != null) {
				break;
			}
		}
		if (txsCardBin == null) {
			throw new AppException(TxsCode.BANKCARDNO_INVALID.code);
		}
		return txsCardBin;
	}
}
