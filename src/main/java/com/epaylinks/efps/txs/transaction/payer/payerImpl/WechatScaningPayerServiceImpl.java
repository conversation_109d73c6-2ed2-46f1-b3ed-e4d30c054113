package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.WeChatAuthCodeTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;

@Service("WeChatAuthCode")
public class WechatScaningPayerServiceImpl implements PayerService {

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		if (map == null || map.isEmpty()) {
			return null;
		}
		WeChatAuthCodeTrader weChatAuthCodeTrader = new WeChatAuthCodeTrader();
		weChatAuthCodeTrader.setAuthCode(MapUtils.getString(map, "authCode"));
		weChatAuthCodeTrader.setType(TraderType.WeChatAuthCode);
		weChatAuthCodeTrader.setSubAppId(MapUtils.getString(map, "subAppId"));
		return weChatAuthCodeTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		Payer payer = new Payer();
		payer.setPayerId(payerJson.getString("authCode"));
		payer.setPayerType(TraderType.WeChatAuthCode.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
