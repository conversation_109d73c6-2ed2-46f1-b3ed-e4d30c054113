package com.epaylinks.efps.txs.transaction.domain.split;

import com.epaylinks.efps.txs.constants.RegexpValidFinals;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

public class AccountSplitConfirmRequest {
    /**
     * 版本号
     */
    private String version;

    /**
     * 商户订单号
     */
    @NotBlank(message="商户订单号不能为空")
    @Size(min = 1, max = 32, message = "订单号长度要求1到32之间。")
    private String outTradeNo;

    /**
     * 商户号
     */
    @NotBlank(message="商户号不能为空")
    private String customerCode;


    /**
     *分账信息列表
     */
    private List<SplitInfo> splitInfoList;

    /**
     * 提交类型
     */
    @NotBlank(message="提交类型不能为空")
    @Pattern(regexp = RegexpValidFinals.ComfirmType, message = RegexpValidFinals.Message.ComfirmType)
    private String comfirmType;


    /**
     * 随机字符串
     */
    @NotBlank(message="随机字符串不能为空")
    private String nonceStr;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public List<SplitInfo> getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(List<SplitInfo> splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

    public String getComfirmType() {
        return comfirmType;
    }

    public void setComfirmType(String comfirmType) {
        this.comfirmType = comfirmType;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }
}
