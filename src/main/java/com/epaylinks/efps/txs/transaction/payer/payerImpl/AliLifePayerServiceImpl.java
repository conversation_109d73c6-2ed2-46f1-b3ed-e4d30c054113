package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.AliLifeWindowTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;

@Service("AliLifeWindow")
public class AliLifePayerServiceImpl implements PayerService {
	
	public static final String BUYER_LOGON_ID="buyerLogonId";
	public static final String BUYER_ID="buyerId";

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
        AliLifeWindowTrader aliLifeWindowTrader = new AliLifeWindowTrader();
        aliLifeWindowTrader.setType(TraderType.AliLifeWindow);
        aliLifeWindowTrader.setBuyerId(MapUtils.getString(map, BUYER_ID));
        aliLifeWindowTrader.setBuyerLogonId(MapUtils.getString(map, BUYER_LOGON_ID));
        return aliLifeWindowTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO Auto-generated method stub
		Payer payer = new Payer();
		payer.setPayerType(TraderType.AliLifeWindow.name());
		if (payerJson.containsKey(BUYER_ID)) {
			payer.setPayerId(payerJson.getString(BUYER_ID));
		} else if (payerJson.containsKey(BUYER_LOGON_ID)) {
			payer.setPayerId(payerJson.getString(BUYER_LOGON_ID));
		}
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// 支付宝生活号支付
		if (!payer.containsKey("type")) {
			return false;
		}
		if (!payer.containsKey(BUYER_LOGON_ID) && !payer.containsKey(BUYER_ID)) {
			return false;
		}
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
