package com.epaylinks.efps.txs.transaction.domain.notify;

import java.util.List;

import com.epaylinks.efps.txs.transaction.domain.SplitResultInfo;



public class SplitResultNotify {
	String customerCode;
	String outTradeNo;
	String transactionNo;
	/**
	 * 分账订单号
	 */
	private String splitTransactionNo;

	/**
	 * 撤销订单号
	 */
	private String revokeTransactionNo;
	Long amount;
	Long realAmount;
	String splitState;
	String splitTime;
	String settCycle;
	Long settCycleInterval;
	Long procedureFee;
	String attachData;
	List<SplitResultInfo> splitResultInfoList;
	String nonceStr;
	private String outSplitTradeNo;
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public Long getRealAmount() {
		return realAmount;
	}
	public void setRealAmount(Long realAmount) {
		this.realAmount = realAmount;
	}
	public String getSplitState() {
		return splitState;
	}
	public void setSplitState(String splitState) {
		this.splitState = splitState;
	}
	public String getSplitTime() {
		return splitTime;
	}
	public void setSplitTime(String splitTime) {
		this.splitTime = splitTime;
	}
//	public String getSettCycle() {
//		return settCycle;
//	}
	public void setSettCycle(String settCycle) {
		this.settCycle = settCycle;
	}
//	public Long getSettCycleInterval() {
//		return settCycleInterval;
//	}
	public void setSettCycleInterval(Long settCycleInterval) {
		this.settCycleInterval = settCycleInterval;
	}
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}
	public String getAttachData() {
		return attachData;
	}
	public void setAttachData(String attachData) {
		this.attachData = attachData;
	}
	public List<SplitResultInfo> getSplitResultInfoList() {
		return splitResultInfoList;
	}
	public void setSplitResultInfoList(List<SplitResultInfo> splitResultInfoList) {
		this.splitResultInfoList = splitResultInfoList;
	}
	public String getNonceStr() {
		return nonceStr;
	}
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}

	public String getSplitTransactionNo() {
		return splitTransactionNo;
	}

	public void setSplitTransactionNo(String splitTransactionNo) {
		this.splitTransactionNo = splitTransactionNo;
	}

	public String getRevokeTransactionNo() {
		return revokeTransactionNo;
	}

	public void setRevokeTransactionNo(String revokeTransactionNo) {
		this.revokeTransactionNo = revokeTransactionNo;
	}

	public String getOutSplitTradeNo() {
		return outSplitTradeNo;
	}

	public void setOutSplitTradeNo(String outSplitTradeNo) {
		this.outSplitTradeNo = outSplitTradeNo;
	}
}
