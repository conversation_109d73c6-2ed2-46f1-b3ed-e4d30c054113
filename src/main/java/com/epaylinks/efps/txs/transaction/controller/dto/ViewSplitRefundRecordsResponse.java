package com.epaylinks.efps.txs.transaction.controller.dto;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord;

import java.util.List;

/**
 * 分账退款订单的记录查看 - 返回结果
 */
public class ViewSplitRefundRecordsResponse extends CommonResponse {

	private List<TxsRefundSplitRecord> rows;

    private int total;

    public List<TxsRefundSplitRecord> getRows() {
        return rows;
    }

    public void setRows(List<TxsRefundSplitRecord> rows) {
        this.rows = rows;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
