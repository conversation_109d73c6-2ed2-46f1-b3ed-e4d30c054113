package com.epaylinks.efps.txs.transaction.service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.dao.TxsPreOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 交易查询服务
 * 
 * <AUTHOR>
 *
 */
@Service
public class TransactionQueryService {
	@Autowired
	TransactionQueryService self;
	@Autowired
	private TxsPreOrderMapper txsPreOrderMapper;

	/**
	 * 根据入参查询支付结果
	 * outTradeNo和transactionNo至少要有一个有值，若都有值，以transactionNo为准
	 * @param customerCode
	 * @param outTradeNo
	 * @param transactionNo
	 * @return
	 */
	public TxsPreOrder queryTxsPreOrder(String customerCode, String outTradeNo, String transactionNo) {
		TxsPreOrder preOrder = null;
		if (StringUtils.isNotBlank(transactionNo)) {
			preOrder = txsPreOrderMapper.selectByTransactionNo(transactionNo);
			
		} else {
			preOrder = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(outTradeNo, customerCode);
		}
		if(preOrder != null) {//醉醉的做法。迎合
			preOrder.setPayState(preOrderState2PayTradeOrderState(preOrder.getPayState()).code);
		}
		
		return preOrder;
	}

	TxsConstants.PayState preOrderState2PayTradeOrderState(String preOrderState) {// TxsConstants.PreOrderState state) {
		if (preOrderState.equals(TxsConstants.PreOrderState.OVERTIME_FAIL.code))
			return TxsConstants.PayState.FAIL;
		else if (preOrderState.equals(TxsConstants.PreOrderState.IN_PROCESS.code))
			return TxsConstants.PayState.IN_PROCESS;
		else if (preOrderState.equals(TxsConstants.PreOrderState.WAIT_TOPAY.code))
			return TxsConstants.PayState.IN_PROCESS;
		else if (preOrderState.equals(TxsConstants.PreOrderState.TRANSACTION_FAIL.code))
			return TxsConstants.PayState.FAIL;
		else if (preOrderState.equals(TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code))
			return TxsConstants.PayState.SUCCESS;
		else if (preOrderState.equals(TxsConstants.PreOrderState.CANCEL.code))
			return TxsConstants.PayState.CANCEL;
		else if (preOrderState.equals(TxsConstants.PreOrderState.CLOSE.code))
			return TxsConstants.PayState.CLOSE;
		else
			throw new AppException(TxsConstants.INVALID_PARAM);
	}

}
