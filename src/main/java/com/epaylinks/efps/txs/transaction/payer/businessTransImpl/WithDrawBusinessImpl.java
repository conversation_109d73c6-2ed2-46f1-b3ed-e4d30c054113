package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsWithdrawTradeOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("WithDrawBusiness")
public class WithDrawBusinessImpl implements BusinessTransactionCheck {

	@Autowired
	private TxsWithdrawTradeOrderMapper txsWithdrawTradeOrderMapper;
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		// TODO Auto-generated method stub
		List<TxsWithdrawTradeOrder> txsWithdrawTradeOrders = txsWithdrawTradeOrderMapper.selectByBusinessInstId(businessExamId);
		if (txsWithdrawTradeOrders != null && !txsWithdrawTradeOrders.isEmpty()) {
			return true;
		}
		return false;
	}

}
