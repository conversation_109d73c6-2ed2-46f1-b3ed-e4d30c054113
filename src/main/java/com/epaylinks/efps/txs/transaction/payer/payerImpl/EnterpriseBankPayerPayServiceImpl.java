package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.BankTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;
@Service("EnterpriseBanking")
public class EnterpriseBankPayerPayServiceImpl implements PayerService {

	@Override
	public Trader getPayer(Map<String, Object> map) {
		BankTrader bankTrader = new BankTrader();
		bankTrader.setBankCode(MapUtils.getString(map, "bankCode"));
		bankTrader.setType(TraderType.EnterpriseBanking);
		return bankTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO Auto-generated method stub
		Payer payer = new Payer();
		payer.setPayerId(payerJson.getString("bankCode"));
		payer.setPayerType(TraderType.EnterpriseBanking.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}
}
