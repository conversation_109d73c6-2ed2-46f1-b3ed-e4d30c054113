package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsRefundSplitRecordMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitRecordMapper;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRecord;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("BFZBusiness")
public class BFZBusinessImpl implements BusinessTransactionCheck {

	@Autowired
	private TxsSplitRecordMapper txsSplitRecordMapper;
	
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		List<TxsSplitRecord> txsSplitRecords = txsSplitRecordMapper.selectByBusinessInstId(businessExamId);
		if (txsSplitRecords != null && !txsSplitRecords.isEmpty()) {
			return true;
		}
		return false;
	}

}
