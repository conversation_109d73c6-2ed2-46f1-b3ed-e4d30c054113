package com.epaylinks.efps.txs.transaction.controller.dto;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 分账退款信息DTO
 */
public class SplitRefundInfoDTO {

    private Long id;

    private String outTradeNo;
    @FieldAnnotation(fieldName="商户退款申请单号")
    private String outRefundNo;
    @FieldAnnotation(fieldName="商户编号")
    private String customerCode;

    private String terminalNo;

    private String refundDesc;
    @FieldAnnotation(fieldName="原订单金额（元）", yuanHandler = true)
    private Long totalFee;

    @FieldAnnotation(fieldName="申请退款金额（元）", yuanHandler = true)
    private Long refundFee;

    private String refundCurrency;

    private String channelType;

    private String notifyUrl;

    private String redirectUrl;
    @FieldAnnotation(fieldName="易票联退款单号")
    private String transactionNo;
    @FieldAnnotation(fieldName="退款状态", dictionaries="00:退款成功,01:退款失败,02:未退款,03:处理中（网关支付待网银上游通知）")
    private String payState;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="退款申请时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @FieldAnnotation(fieldName="商户名称")
    private String customername;

    private String businessInstId;

    private String businessCode;

    private String errorCode;
    
    private String errorMsg;

    private String splitInfoList;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    @FieldAnnotation(fieldName="退款手续费（元）", yuanHandler = true)
    private Long procedureFee;
    @FieldAnnotation(fieldName="退回收单手续费（元）", yuanHandler = true)
    private Long backpayProcedurefee;
    @FieldAnnotation(fieldName="原订单收单手续费（元）", yuanHandler = true)
    private Long payProcedurefee;
    @FieldAnnotation(fieldName="易票联原订单编号")
    private String payTransactionNo;
    @FieldAnnotation(fieldName="上游退款单号")
    private String channelOrderNo;
    @FieldAnnotation(fieldName="上游渠道")
    private String channelName;

    // 分账退款记录的信息

    @FieldAnnotation(fieldName="反分账记录单号")
    private String recordTransactionNo;
    @FieldAnnotation(fieldName="反分账被分账客户编号")
    private String recordCustomerCode;
    @FieldAnnotation(fieldName="反分账被分账客户名称")
    private String recordCustomerName;
    @FieldAnnotation(fieldName="反分账金额", yuanHandler = true)
    private Long recordAmount;
    @FieldAnnotation(fieldName="反分账状态")
    private String recordState;

    @FieldAnnotation(fieldName="原支付方式",dictionaries="0:账号支付,1:微信公众号支付,2:个人网银_贷记卡,3:个人网银_借记卡,"
            + "4:支付宝生活号支付,5:代付,6:微信扫码支付,7:支付宝扫码支付,8:快捷支付,9:微信app支付,10:微信H5支付,13:微信被扫,14:支付宝被扫,20:手机银联支付,22:企业银联")
    private String payMethod;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getRefundDesc() {
        return refundDesc;
    }

    public void setRefundDesc(String refundDesc) {
        this.refundDesc = refundDesc;
    }

    public Long getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Long totalFee) {
        this.totalFee = totalFee;
    }

    public Long getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Long refundFee) {
        this.refundFee = refundFee;
    }

    public String getRefundCurrency() {
        return refundCurrency;
    }

    public void setRefundCurrency(String refundCurrency) {
        this.refundCurrency = refundCurrency;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCustomername() {
        return customername;
    }

    public void setCustomername(String customername) {
        this.customername = customername;
    }

    public String getBusinessInstId() {
        return businessInstId;
    }

    public void setBusinessInstId(String businessInstId) {
        this.businessInstId = businessInstId;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(String splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public Long getBackpayProcedurefee() {
        return backpayProcedurefee;
    }

    public void setBackpayProcedurefee(Long backpayProcedurefee) {
        this.backpayProcedurefee = backpayProcedurefee;
    }

    public Long getPayProcedurefee() {
        return payProcedurefee;
    }

    public void setPayProcedurefee(Long payProcedurefee) {
        this.payProcedurefee = payProcedurefee;
    }

    public String getPayTransactionNo() {
        return payTransactionNo;
    }

    public void setPayTransactionNo(String payTransactionNo) {
        this.payTransactionNo = payTransactionNo;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getRecordTransactionNo() {
        return recordTransactionNo;
    }

    public void setRecordTransactionNo(String recordTransactionNo) {
        this.recordTransactionNo = recordTransactionNo;
    }

    public String getRecordCustomerCode() {
        return recordCustomerCode;
    }

    public void setRecordCustomerCode(String recordCustomerCode) {
        this.recordCustomerCode = recordCustomerCode;
    }

    public String getRecordCustomerName() {
        return recordCustomerName;
    }

    public void setRecordCustomerName(String recordCustomerName) {
        this.recordCustomerName = recordCustomerName;
    }

    public Long getRecordAmount() {
        return recordAmount;
    }

    public void setRecordAmount(Long recordAmount) {
        this.recordAmount = recordAmount;
    }

    public String getRecordState() {
        return recordState;
    }

    public void setRecordState(String recordState) {
        this.recordState = recordState;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }
    
    public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	/**
     * 设置默认值
     * @param c 默认值
     */
    public void initForSplitRecordWithDefaultChar(String c) {
        this.outTradeNo = c;
        this.outRefundNo = c;
        this.customerCode = c;
        this.terminalNo = c;
        this.refundDesc = c;
        this.refundCurrency = c;
        this.channelType = c;
        this.notifyUrl = c;
        this.redirectUrl = c;
        this.transactionNo = c;
        this.payState = c;
        this.customername = c;
        this.payTransactionNo = c;
        this.payMethod = c;
    }
}