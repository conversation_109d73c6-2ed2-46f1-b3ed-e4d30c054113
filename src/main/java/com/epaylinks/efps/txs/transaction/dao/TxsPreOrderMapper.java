package com.epaylinks.efps.txs.transaction.dao;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

@Mapper
public interface TxsPreOrderMapper {
	int deleteByPrimaryKey(Long orderId);

	int insert(TxsPreOrder record);

	int insertSelective(TxsPreOrder record);

	TxsPreOrder selectByPrimaryKey(Long orderId);

	int updateByPrimaryKeySelective(TxsPreOrder record);
	
	/**
	 * 更新订单，其特点在于增加了条件：sysdate<=TRANSACTION_END_TIME and PAY_STATE!='1'
	 * 适用于两个场景：（1）接收到支付的某条支付订单支付最终结果的异步通知；
	 * @param record
	 * @return
	 */
	int updateByPrimaryKeySelectiveForTradeOrderSuccess(TxsPreOrder record);
	
	int updateAmountsByOutTradeNoAndCustomerCode(TxsPreOrder record);
	
	/**
	 * 更新非收银台订单，txs_pay_trade_order失败时，直接更新pre_order表
	 * @param record
	 * @return
	 */
	int updateFinnalStateForUnCashier(TxsPreOrder record);

	/**
	 * 更新订单，其特点在于增加了条件: PAY_STATE!='1'   适用场景:重复支付时更新订单数据
	 * @param record
	 * @return
	 */
	int updateByPrimaryKeySelectiveForRepeatRequest(TxsPreOrder record);
	
	/**
	 * 根据交易单号更新结算状态
	 * @param record
	 * @return
	 */
	int updateByTransactionNo(TxsPreOrder record);
	
	
	TxsPreOrder selectByTransactionNo(@Param("transactionNo")String transactionNo);
	
	/**
	 * 根据外部单号，客户编码，交易类型
	 * @param outTradeNo
	 * @param customerCode
	 * @param transactionType
	 * @param payState
	 * @return
	 */
	TxsPreOrder selectByParam(@Param("outTradeNo") String outTradeNo, @Param("customerCode") String customerCode,
			@Param("transactionType") String transactionType);

	/**
	 * 根据外部单号，客户编码,更新订单状态
	 * @param record
	 * @return
	 */
	int updateStateByOutTradeNoAndCustomerCode(TxsPreOrder record);
	
	/**
	 * 根据商户订单号和客户编码查询预下单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	TxsPreOrder selectByOutTradeNoAndCustomerCode(@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);
	
	/**
	 * 根据商户订单号和客户编码查询预下单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	TxsPreOrder selectTxsPreOrderByThreeParam(@Param("transactionNo") String transactionNo,@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);
	
	/**
	 * 根据商户订单号和客户编码查询预下单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	TxsPreOrder selectTxsPreOrderByTransactionNoAndCustomerCode(@Param("transactionNo") String transactionNo,
			@Param("customerCode") String customerCode);
	
	/**
	 * 根据外部单号和客户编码查询状态为成功的预下单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	List<TxsPreOrder> selectByOutTradeNoToResultQuery(@Param("outTradeNo") String outTradeNo, @Param("payState") String payState,
			@Param("customerCode") String customerCode);
	
	/**
	 * 使用入参conditionOrder中非空的字段作为条件查询
	 * @param conditionOrder
	 * @return
	 */
	List<TxsPreOrder> selectByCondition(TxsPreOrder conditionOrder);
	
	
	/**
	 * 根据外部单号和客户编码查询预下单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	List<TxsPreOrder> selectTxsPreOrderByOutTradeNoAndCustomerCode(@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);
	
	/**
	 * 根据交易截止时间处理超时失败订单(预下单成功的订单)
	 * @param map
	 * @return
	 */
	List<TxsPreOrder>  selectOrderByStateAndEndTime(Map map);
	
	int updateStateByEndTime(Map map);
	
	/**
     * 不分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsPreOrder> selectByNotPage(Map map);
    
    /**
     * 分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsPreOrder> selectByPage(Map map);

	int selectCount(Map map);
	/**
	 * 查询分页统计信息
	 * @param map
	 * @return
	 */
	PageResult selectPageCountInfo(Map map);
	
	long selectSumProcedureFee(Map map);
	
	long selectSumAmount(Map map);
	/**
	 * 更新PreOrder的退款中金额，
	 * 结果等于0可能为携带条件“原本的退款中金额+原本的已退款金额+currencyRefundFee小于等于原订单金额” 
	 * @param transactionNo
	 * @param currencyRefundFee
	 * @return
	 */
	int updateAmountByTransactionNo(@Param("transactionNo")String transactionNo,
			@Param("currencyRefundFee") Long currencyRefundFee);
	/**
	 * 成功修改金额（已退款加一笔，退款中减一笔）
	 * @param txsPreOrder
	 * @return
	 */
	int updateAddAmountById(TxsPreOrder txsPreOrder);


	/**
	 * 失败，退款中减掉数据
	 * @param txsPreOrder
	 * @return
	 */
	int updateReduceAmountById(TxsPreOrder txsPreOrder);
	/**
	 * 根据日期查找状态为state的预下单数据
	 * @param code
	 * @param date
	 * @return
	 */
	List<TxsPreOrder> selectByCustomerCodeAndStateAndEndTimeInDate(@Param("customerCode") String customerCode, @Param("payState")String payState, @Param("endTime")Date endTime);
	/**
	 * 根据日期以及交易类型查找状态为state的预下单数据
	 * @param code
	 * @param date
	 * @return
	 */
	List<TxsPreOrder> selectByCustomerCodeAndStateAndTypeAndEndTimeInDate(
			@Param("customerCode")String customerCode, 
			@Param("transactionType")String transactionType,
			@Param("payState")String payState, @Param("endTime")Date endTime);
	/**
	 * 查询某个用户在某个时间段的交易金额（支付状态为处理中，或支付成功）
	 * @param customerCode
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Long selectSumAmountByOneDayAndCustomerCode(@Param("customerCode")String customerCode,
			@Param("beginTime")Date beginTime,@Param("endTime") Date endTime);
	/**
	 * 根据日期查找商户的预下单数据
	 * @param code
	 * @param date
	 * @return
	 */
	List<TxsPreOrder> selectByCustomerCodeAndEndTimeInDate(@Param("customerCode") String customerCode , @Param("endTime")Date endTime);
	
	/**
	 * 根据外部单号，更新撤销相关的四个字段,若cancel_state已经为成功,则更新结果为0条
	 * @param record
	 * @return
	 */
	int updateCancelInfoByOutTradeNoAndCustomerCode(TxsPreOrder record);
	
	
	/**
	 * 根据商户订单号和客户编码查询预下单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	int selectCountSuccessOrFail(@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);
	
	int selectTotalRefundAmt(@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);
	
	
	int selectByChannelOrder(@Param("channelOrder")String channelOrder);

	int updateFirstMultiSplit(@Param("orderId") Long orderId,
								@Param("multiOrderAmount")Long multiOrderAmount);

	long selectTotalDivideAmount(@Param("orderId") Long orderId);

	/**
	 * 拆单分账增加拆单中的金额
	 * @param txsPreOrder
	 * @return
	 */
	int updateAddDividIngAmountById(@Param("dividingAmountToAdd")Long dividingAmountToAdd, @Param("orderId") Long orderId) ;

	/**
	 * 成功,由dividing 挪到 divided
	 * 挪后dividing要大于等于0
	 * 总额不变的!
	 * @param txsPreOrder
	 * @return
	 */
	int updateAddAddDividedForSuccess(@Param("amount")Long amount, @Param("outTradeNo") String outTradeNo, @Param("customerCode") String customerCode) ;

	/**
	 * 失败,由dividing 减掉
	 * 挪后dividing要大于等于0
	 * @param txsPreOrder
	 * @return
	 */
	int updateAddReduceDividingForFail(@Param("amount")Long amount, @Param("outTradeNo") String outTradeNo, @Param("customerCode") String customerCode) ;

	int updateSyncDivToPayTradeOrder(@Param("outTradeNo") String outTradeNo, @Param("customerCode") String customerCode) ;



	/**
	 * 校准拆分中
	 * @param amount
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	int adjustDividing(@Param("outTradeNo") String outTradeNo, @Param("customerCode") String customerCode) ;

	/**
	 * 校准已拆分
	 * @param amount
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	int adjustDivided(@Param("outTradeNo") String outTradeNo, @Param("customerCode") String customerCode) ;

	List<Map<String, String>> selectOutTradeCusomerForMulitiOrder(@Param("daysAgo") Integer daysAgo, @Param("searchDays") Integer searchDays);


}