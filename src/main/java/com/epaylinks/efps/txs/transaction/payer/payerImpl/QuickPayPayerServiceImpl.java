package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.QuickPayTrader;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;
@Service("QuickPay")
public class QuickPayPayerServiceImpl implements PayerService{

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		QuickPayTrader quickPayTrader = new QuickPayTrader();
		
		quickPayTrader.setTraderId(MapUtils.getString(map, "traderId"));
        quickPayTrader.setSignSequence(MapUtils.getString(map, "signSequence"));
        quickPayTrader.setChannelCode(MapUtils.getString(map, "channelCode"));
        quickPayTrader.setBankCardNo(MapUtils.getString(map, "bankCardNo"));
        quickPayTrader.setCardType(MapUtils.getString(map, "cardType"));
        quickPayTrader.setCardNo(MapUtils.getString(map, "cardNo"));
        quickPayTrader.setCustomerName(MapUtils.getString(map, "customerName"));
        quickPayTrader.setPhone(MapUtils.getString(map, "phone"));
        quickPayTrader.setCvn2(MapUtils.getString(map, "cvn2"));
        quickPayTrader.setExpired(MapUtils.getString(map, "expired"));
        quickPayTrader.setBankIcon(MapUtils.getString(map, "bankIcon"));
        
		quickPayTrader.setType(TraderType.QuickPay);
		return quickPayTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO Auto-generated method stub
		Payer payer = new Payer();
		payer.setPayerType(TraderType.QuickPay.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		QuickPayTrader quickPayTrader = (QuickPayTrader) trader;
		Map<String, String> businessTargetIds = new HashMap<>();
		businessTargetIds.put(TxsConstants.BusinessTagerType.BANK_CARD.code, quickPayTrader.getBankCardNo());
		businessTargetIds.put(TxsConstants.BusinessTagerType.CELL_PHONE_NUMBER.code, quickPayTrader.getPhone());
		businessTargetIds.put(TxsConstants.BusinessTagerType.IDENTITY_CARD.code, quickPayTrader.getCardNo());
		return businessTargetIds;
	}

}
