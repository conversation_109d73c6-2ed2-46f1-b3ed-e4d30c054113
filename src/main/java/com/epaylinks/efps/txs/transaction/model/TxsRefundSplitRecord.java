package com.epaylinks.efps.txs.transaction.model;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class TxsRefundSplitRecord {
    private Long id;

    @FieldAnnotation(fieldName="原易票联退款单号")
    private String transactionNo;

    private String sourceCustomerCode;
    @FieldAnnotation(fieldName="分账客户编号")
    private String customerCode;

    @FieldAnnotation(fieldName="退款状态", dictionaries="1:未退款,2:退款失败,3:退款成功")
    private String state;

    @FieldAnnotation(fieldName="实际退款分账金额", yuanHandler = true)
    private Long amount;

    private Long procedurefee;

    private String errorCode;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    private String businessInstId;
    @FieldAnnotation(fieldName="分账客户名称")
    private String customerName;
    
    @FieldAnnotation(fieldName="总退款金额", yuanHandler = true)
    private Long refundAllAmount;
    @FieldAnnotation(fieldName="退回手续费", yuanHandler = true)
    private Long backPayProcedureFee;
    
    
    @FieldAnnotation(fieldName="申请退款金额", yuanHandler = true)
    private Long apiAmount;

    /**
     * 业务员
     */
    private String businessMan;

    /**
     * 业务员ID
     */
    private Long businessManId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司ID
     */
    private Long companyId;
    
	/**
	 * 现金支付金额，分
	 */
	private Long cashAmount;
	/**
	 * 代金券金额，分
	 */
	private Long couponAmount; 
	
	/**
	 * 实退金额,接口减去优惠,但未减手续费
	 */
	private Long realRefundFee;

    /**
     * 交易类型
     */
    private String transactionType;

    @ApiModelProperty(value="分账属性(0：分账主体商户 1：常规分账对象 2：收单手续费扣除对象 3：分账手续费扣除对象)", dataType = "String")
    private String splitAttr;

    /**
     * 商户退款单号
     */
    private String outRefundNo;
    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 原订单实际退款金额，分
     */
    private Long payRealRefundFee;

    public String getSplitAttr() {
        return splitAttr;
    }

    public void setSplitAttr(String splitAttr) {
        this.splitAttr = splitAttr;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public Long getPayRealRefundFee() {
        return payRealRefundFee;
    }

    public void setPayRealRefundFee(Long payRealRefundFee) {
        this.payRealRefundFee = payRealRefundFee;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getSourceCustomerCode() {
        return sourceCustomerCode;
    }

    public void setSourceCustomerCode(String sourceCustomerCode) {
        this.sourceCustomerCode = sourceCustomerCode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getProcedurefee() {
        return procedurefee;
    }

    public void setProcedurefee(Long procedurefee) {
        this.procedurefee = procedurefee;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBusinessInstId() {
        return businessInstId;
    }

    public void setBusinessInstId(String businessInstId) {
        this.businessInstId = businessInstId;
    }

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public Long getRefundAllAmount() {
		return refundAllAmount;
	}

	public void setRefundAllAmount(Long refundAllAmount) {
		this.refundAllAmount = refundAllAmount;
	}

	public Long getBackPayProcedureFee() {
		return backPayProcedureFee;
	}

	public void setBackPayProcedureFee(Long backPayProcedureFee) {
		this.backPayProcedureFee = backPayProcedureFee;
	}

	public Long getApiAmount() {
		return apiAmount;
	}

	public void setApiAmount(Long apiAmount) {
		this.apiAmount = apiAmount;
	}


    public String getBusinessMan() {
        return businessMan;
    }

    public void setBusinessMan(String businessMan) {
        this.businessMan = businessMan;
    }

    public Long getBusinessManId() {
        return businessManId;
    }

    public void setBusinessManId(Long businessManId) {
        this.businessManId = businessManId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}

	public Long getRealRefundFee() {
		return realRefundFee;
	}

	public void setRealRefundFee(Long realRefundFee) {
		this.realRefundFee = realRefundFee;
	}

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }
}