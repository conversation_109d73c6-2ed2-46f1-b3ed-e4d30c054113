package com.epaylinks.efps.txs.transaction.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;
 
import org.springframework.transaction.annotation.Transactional; 
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import com.epaylinks.efps.txs.transaction.model.TxsWithdrawTradeOrder;
import com.github.pagehelper.Page;
@Mapper
public interface TxsWithdrawTradeOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TxsWithdrawTradeOrder record);

    int insertSelective(TxsWithdrawTradeOrder record);

    TxsWithdrawTradeOrder selectByPrimaryKey(Long id);
    
    TxsWithdrawTradeOrder selectByTransaction(@Param("transactionNo")String transactionNo);
    /**
     * 根据商户号 + 商户订单号 查询
     * @param customerCode
     * @param outTradeNo
     * @return
     */
    TxsWithdrawTradeOrder selectByCustomerCodeAndOutTradeNo(@Param("customerCode")String customerCode, @Param("outTradeNo")String outTradeNo);
    
    @Transactional(readOnly = true)
    List<TxsWithdrawTradeOrder> selectBySelective(Map map);

    /**
     * 分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    Page<TxsWithdrawTradeOrder> selectByPage(Map map);

    /**
     * 不分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    Page<TxsWithdrawTradeOrder> selectByNotPage(Map map);

    int updateByPrimaryKeySelective(TxsWithdrawTradeOrder record);

    int updateByPrimaryKey(TxsWithdrawTradeOrder record);
    
    int updateOprRemarkByTansactionNo(TxsWithdrawTradeOrder record);

    List<TxsWithdrawTradeOrder> selectByOutTradeNo(String outTradeNo);

	int selectCount(Map map);
	/**
	 * 查询分页统计信息
	 * @param map
	 * @return
	 */
	PageResult selectPageCountInfo(Map map);
	
	List<TxsWithdrawTradeOrder> selectByBusinessInstId(@Param("businessInstId") String businessInstId);

	List<TxsWithdrawTradeOrder> selectByCustomerCodeAndStateAndCreateTimeInDate(
			@Param("customerCode")String customerCode, @Param("payState")String payState, 
			@Param("endTime")Date endTime);
	
	/**
	 * 根据客户编码和外部单号查询存在的外部单号
	 * @param customerCode
	 * @param outTradeNo
	 * @return
	 */
	List<String> selectRepeatOrderNoByCustomerCodeAndOutTradeNo(@Param("customerCode") String customerCode,
			@Param("outTradeNoList")List<String> outTradeNoList);
	
	
	int selectCountOrderByBatchAndState(@Param("batchNo") String batchNo,@Param("payState") String payState);
	
	long selectSumAmountByBatchAndState(@Param("batchNo") String batchNo,@Param("payState") String payState);

	List<TxsWithdrawTradeOrder> selectByBatchNo(@Param("batchNo") String batchNo);
	
	List<TxsWithdrawTradeOrder> selectByParentCustomerAndDate(
			@Param("customerCode")String customerCode,  
			@Param("startTime")Date startTime,
			@Param("endTime")Date endTime);

	List<TxsWithdrawTradeOrder> selectByCustomerCodeAndEndTimeInDateByMonth(
			@Param("customerCode")String customerCode,
			@Param("endTime")Date endTime);



    List<TxsWithdrawTradeOrder> selectAuditList(@Param("startTime") Date startTime,@Param("endTime")Date endTime);
}