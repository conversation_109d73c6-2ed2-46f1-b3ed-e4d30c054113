package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;

public class TxsRefundPreOrder {
    private Long id;
    
    private String outTradeNo;
    @FieldAnnotation(fieldName="商户退款申请单号")
    private String outRefundNo;
    @FieldAnnotation(fieldName="商户编号")
    private String customerCode;

    private String terminalNo;

    private String refundDesc;
    @FieldAnnotation(fieldName="原订单金额（元）", yuanHandler = true)
    private Long totalFee;

    @FieldAnnotation(fieldName="申请退款金额（元）", yuanHandler = true)
    private Long refundFee;

    private String refundCurrency;

    private String channelType;

    private String notifyUrl;

    private String redirectUrl;
    @FieldAnnotation(fieldName="易票联退款单号")
    private String transactionNo;
    @FieldAnnotation(fieldName="退款状态", dictionaries="00:退款成功,01:退款失败,02:未退款,03:处理中（网关支付待网银上游通知）")
    private String payState;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="退款申请时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @FieldAnnotation(fieldName="商户名称")
    private String customername;

    private String businessInstId;

    private String businessCode;
    
    private String businessName;

    private String errorCode;
    
    private String errorMsg;
    
    private String splitInfoList;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    @FieldAnnotation(fieldName="退款手续费（元）", yuanHandler = true)
    private Long procedureFee;	//退款本身的手续费
    @FieldAnnotation(fieldName="退回收单手续费（元）", yuanHandler = true)
    private Long backpayProcedurefee;	//当笔退回的手续费
    @FieldAnnotation(fieldName="原订单收单手续费（元）", yuanHandler = true)
    private Long payProcedurefee;	//原收单交易的手续费
    @FieldAnnotation(fieldName="易票联原订单编号")
    private String payTransactionNo;
    @FieldAnnotation(fieldName="上游退款单号")
    private String channelOrderNo;
    @FieldAnnotation(fieldName="上游渠道")
    private String channelName;
    @FieldAnnotation(fieldName="原支付方式",dictionaries="0:账号支付,1:微信公众号支付,2:个人网银_贷记卡,3:个人网银_借记卡,"
    		+ "4:支付宝生活号支付,5:代付,6:微信扫码支付,7:支付宝扫码支付,8:快捷支付,9:微信app支付,10:微信H5支付,13:微信被扫,14:支付宝被扫,20:手机银联支付,22:企业银联,24:银联扫码主扫,25:银联扫码被扫")
    private String payMethod;
    @FieldAnnotation(fieldName="支付通道")
    private String payPassWay;
    
    private String agentCustomerCode;
    @FieldAnnotation(fieldName="所属代理商")
    private String agentCustomerName;
    
    private Date endTime;
    
    private String sourceType;
    
    private String isBackRefundProcedure;
    
    /**
     * 收单机构号
     */
    private String acqOrgCode;

    /**
     * 收单机构服务商 ID
     */
    private String acqSpId;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 终端信息，终端 IP、经纬度等信息， JSON 格式
     */
    private String termInfo;

    /**
     * 地区信息
     */
    private String areaInfo;

    /**
     * 用户标识，支付宝服务窗为用户的 buyer_id，微信公众号为用户的 openid，银联 JS 支付为用户的 userId
     */
    private String userId;

    /**
     * 子商户公众号id
     */
    private String subAppId;

    /**
     * 第三方平台商户号，如银联云闪付
     */
    private String platformCustomerCode;

    /**
     * 商户门店编号
     */
    private String storeId;

    /**
     * 商户操作员编号
     */
    private String operatorId;

    /**
     * 服务商自定义域
     */
    private String reqReserved;

    /**
     * 第三方平台自定义域，如云闪付
     */
    private String cupsReqReserved;

    /**
     * 付款 APP 名称
     */
    private String payerAppName;

    /**
     * 买家付款金额
     */
    private Long actualPayAmount;

    /**
     * 可打折金额，支付宝特有参数，单位：分
     */
    private Long discountableAmount;

    /**
     * 应结订单金额
     */
    private Long settlementAmount;

    /**
     * 交易来源1:EPSP平台,3:云闪付开放平台，4:旧系统
     */
    private String sourceChannel;
    /**
     * 二维码发行方,1： 服务商
     */
    private String qrCodeIssuer;
    /**
     * 费率(下划线隔开，第二部分表示封顶值，示例：30_10，不同的费率有不同的参数格式，对于固定费率：30表示30分钱；对于按比例：30表示费率为万分之30，封顶10分钱
     */
    private String procedureRate;

    /**
     * 费率模式(1：固定费率，2：按比例)
     */
    private Short rateMode;
    
    //马赛克卡号
    private String cardNoMosaic; //CARD_NO_MOSAIC; 打马赛克的卡号
    
    //加密过的卡号
    private String cardNoEnc; //CARD_NO_ENC; 加密的卡号
    
    //卡种类
    private String cardType;	//CARD_TYPE;  
    
    /**
     * 终端名称
     */
    private String terminalName;
    /**
     * 业务类型：0：网关交易（走EPSP的交易）1：银行卡收单交易
     */
    private String businessType;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 凭证号
     */
    private String sysnum;

	/**
	 * 上游流水号
	 * @return
	 */
	private String channelTradeNo;


	/**
	 * 业务员
	 */
	private String businessMan;

	/**
	 * 业务员ID
	 */
	private Long businessManId;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 公司ID
	 */
	private Long companyId;

	
	private Long backSplitProcedurefee;	//当笔退回的分账手续费

	private Long origTotalSplitProcedurefee;	//原交易的总分账手续费
	
	/**
	 * 现金支付金额，分
	 */
	private Long cashAmount;
	/**
	 * 代金券金额，分
	 */
	private Long couponAmount;
	
		/**
	 * 是否按现金支付金额结算，1-是，0-否
	 */
	private String isSettWithCashAmount;	
	
	/**
	 * 实际退款金额即
	 * IS_SETT_WITH_CASH_AMOUNT = 1 取 CASH_AMOUNT，IS_SETT_WITH_CASH_AMOUNT != 1 取 REFUND_FEE
	 *  */
	private Long realRefundFee;
	
	private String orderInfo;

	private String remark;

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getSysnum() {
		return sysnum;
	}

	public void setSysnum(String sysnum) {
		this.sysnum = sysnum;
	}

	public String getTerminalName() {
		return terminalName;
	}

	public void setTerminalName(String terminalName) {
		this.terminalName = terminalName;
	}
    
    public String getCardNoMosaic() {
		return cardNoMosaic;
	}

	public void setCardNoMosaic(String cardNoMosaic) {
		this.cardNoMosaic = cardNoMosaic;
	}

	public String getCardNoEnc() {
		return cardNoEnc;
	}

	public void setCardNoEnc(String cardNoEnc) {
		this.cardNoEnc = cardNoEnc;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getRefundDesc() {
        return refundDesc;
    }

    public void setRefundDesc(String refundDesc) {
        this.refundDesc = refundDesc;
    }

    public Long getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Long totalFee) {
        this.totalFee = totalFee;
    }

    public Long getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Long refundFee) {
        this.refundFee = refundFee;
    }

    public String getRefundCurrency() {
        return refundCurrency;
    }

    public void setRefundCurrency(String refundCurrency) {
        this.refundCurrency = refundCurrency;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCustomername() {
        return customername;
    }

    public void setCustomername(String customername) {
        this.customername = customername;
    }

    public String getBusinessInstId() {
        return businessInstId;
    }

    public void setBusinessInstId(String businessInstId) {
        this.businessInstId = businessInstId;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

	public String getSplitInfoList() {
		return splitInfoList;
	}

	public void setSplitInfoList(String splitInfoList) {
		this.splitInfoList = splitInfoList;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public Long getBackpayProcedurefee() {
		return backpayProcedurefee;
	}

	public void setBackpayProcedurefee(Long backpayProcedurefee) {
		this.backpayProcedurefee = backpayProcedurefee;
	}

	public Long getPayProcedurefee() {
		return payProcedurefee;
	}

	public void setPayProcedurefee(Long payProcedurefee) {
		this.payProcedurefee = payProcedurefee;
	}

	public String getPayTransactionNo() {
		return payTransactionNo;
	}

	public void setPayTransactionNo(String payTransactionNo) {
		this.payTransactionNo = payTransactionNo;
	}

	public String getChannelOrderNo() {
		return channelOrderNo;
	}

	public void setChannelOrderNo(String channelOrderNo) {
		this.channelOrderNo = channelOrderNo;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String getPayPassWay() {
		return payPassWay;
	}

	public void setPayPassWay(String payPassWay) {
		this.payPassWay = payPassWay;
	}

	public String getAgentCustomerCode() {
		return agentCustomerCode;
	}

	public void setAgentCustomerCode(String agentCustomerCode) {
		this.agentCustomerCode = agentCustomerCode;
	}

	public String getAgentCustomerName() {
		return agentCustomerName;
	}

	public void setAgentCustomerName(String agentCustomerName) {
		this.agentCustomerName = agentCustomerName;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	public String getIsBackRefundProcedure() {
		return isBackRefundProcedure;
	}

	public void setIsBackRefundProcedure(String isBackRefundProcedure) {
		this.isBackRefundProcedure = isBackRefundProcedure;
	}

	public String getBusinessName() {
		return businessName;
	}

	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	public String getAcqOrgCode() {
		return acqOrgCode;
	}

	public void setAcqOrgCode(String acqOrgCode) {
		this.acqOrgCode = acqOrgCode;
	}

	public String getAcqSpId() {
		return acqSpId;
	}

	public void setAcqSpId(String acqSpId) {
		this.acqSpId = acqSpId;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getPlatformCustomerCode() {
		return platformCustomerCode;
	}

	public void setPlatformCustomerCode(String platformCustomerCode) {
		this.platformCustomerCode = platformCustomerCode;
	}

	public Long getActualPayAmount() {
		return actualPayAmount;
	}

	public void setActualPayAmount(Long actualPayAmount) {
		this.actualPayAmount = actualPayAmount;
	}

	public Long getDiscountableAmount() {
		return discountableAmount;
	}

	public void setDiscountableAmount(Long discountableAmount) {
		this.discountableAmount = discountableAmount;
	}

	public Long getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(Long settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public String getChannelTradeNo() {
		return channelTradeNo;
	}

	public void setChannelTradeNo(String channelTradeNo) {
		this.channelTradeNo = channelTradeNo;
	}

	@Override
	public String toString() {
		return "TxsRefundPreOrder [id=" + id + ", outTradeNo=" + outTradeNo + ", outRefundNo=" + outRefundNo
				+ ", customerCode=" + customerCode + ", terminalNo=" + terminalNo + ", refundDesc=" + refundDesc
				+ ", totalFee=" + totalFee + ", refundFee=" + refundFee + ", refundCurrency=" + refundCurrency
				+ ", channelType=" + channelType + ", notifyUrl=" + notifyUrl + ", redirectUrl=" + redirectUrl
				+ ", transactionNo=" + transactionNo + ", payState=" + payState + ", createTime=" + createTime
				+ ", customername=" + customername + ", businessInstId=" + businessInstId + ", businessCode="
				+ businessCode + ", businessName=" + businessName + ", errorCode=" + errorCode + ", errorMsg="
				+ errorMsg + ", splitInfoList=" + splitInfoList + ", updateTime=" + updateTime + ", procedureFee="
				+ procedureFee + ", backpayProcedurefee=" + backpayProcedurefee + ", payProcedurefee=" + payProcedurefee
				+ ", payTransactionNo=" + payTransactionNo + ", channelOrderNo=" + channelOrderNo + ", channelName="
				+ channelName + ", payMethod=" + payMethod + ", payPassWay=" + payPassWay + ", agentCustomerCode="
				+ agentCustomerCode + ", agentCustomerName=" + agentCustomerName + ", endTime=" + endTime
				+ ", sourceType=" + sourceType + ", isBackRefundProcedure=" + isBackRefundProcedure + ", acqOrgCode="
				+ acqOrgCode + ", acqSpId=" + acqSpId + ", orderType=" + orderType + ", termInfo=" + termInfo
				+ ", areaInfo=" + areaInfo + ", userId=" + userId + ", subAppId=" + subAppId + ", platformCustomerCode="
				+ platformCustomerCode + ", storeId=" + storeId + ", operatorId=" + operatorId + ", reqReserved="
				+ reqReserved + ", cupsReqReserved=" + cupsReqReserved + ", payerAppName=" + payerAppName
				+ ", actualPayAmount=" + actualPayAmount + ", discountableAmount=" + discountableAmount
				+ ", settlementAmount=" + settlementAmount + ", sourceChannel=" + sourceChannel + ", qrCodeIssuer="
				+ qrCodeIssuer + ", procedureRate=" + procedureRate + ", rateMode=" + rateMode + ", cardNoMosaic="
				+ cardNoMosaic + ", cardNoEnc=" + cardNoEnc + ", cardType=" + cardType + ", terminalName="
				+ terminalName + ", businessType=" + businessType + ", batchNo=" + batchNo + ", sysnum=" + sysnum
				+ ", channelTradeNo=" + channelTradeNo + ", businessMan=" + businessMan + ", businessManId="
				+ businessManId + ", companyName=" + companyName + ", companyId=" + companyId
				+ ", backSplitProcedurefee=" + backSplitProcedurefee + ", origTotalSplitProcedurefee="
				+ origTotalSplitProcedurefee + ", cashAmount=" + cashAmount + ", couponAmount=" + couponAmount
				+ ", isSettWithCashAmount=" + isSettWithCashAmount + ", realRefundFee=" + realRefundFee + ", orderInfo="
				+ orderInfo + "]";
	}

	public String getTermInfo() {
		return termInfo;
	}

	public void setTermInfo(String termInfo) {
		this.termInfo = termInfo;
	}

	public String getAreaInfo() {
		return areaInfo;
	}

	public void setAreaInfo(String areaInfo) {
		this.areaInfo = areaInfo;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getSubAppId() {
		return subAppId;
	}

	public void setSubAppId(String subAppId) {
		this.subAppId = subAppId;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(String operatorId) {
		this.operatorId = operatorId;
	}

	public String getReqReserved() {
		return reqReserved;
	}

	public void setReqReserved(String reqReserved) {
		this.reqReserved = reqReserved;
	}

	public String getCupsReqReserved() {
		return cupsReqReserved;
	}

	public void setCupsReqReserved(String cupsReqReserved) {
		this.cupsReqReserved = cupsReqReserved;
	}

	public String getPayerAppName() {
		return payerAppName;
	}

	public void setPayerAppName(String payerAppName) {
		this.payerAppName = payerAppName;
	}

	public String getQrCodeIssuer() {
		return qrCodeIssuer;
	}

	public void setQrCodeIssuer(String qrCodeIssuer) {
		this.qrCodeIssuer = qrCodeIssuer;
	}

	public String getProcedureRate() {
		return procedureRate;
	}

	public void setProcedureRate(String procedureRate) {
		this.procedureRate = procedureRate;
	}

	public Short getRateMode() {
		return rateMode;
	}

	public void setRateMode(Short rateMode) {
		this.rateMode = rateMode;
	}

	public Long getBackSplitProcedurefee() {
		return backSplitProcedurefee;
	}

	public void setBackSplitProcedurefee(Long backSplitProcedurefee) {
		this.backSplitProcedurefee = backSplitProcedurefee;
	}

	public Long getOrigTotalSplitProcedurefee() {
		return origTotalSplitProcedurefee;
	}

	public void setOrigTotalSplitProcedurefee(Long origTotalSplitProcedurefee) {
		this.origTotalSplitProcedurefee = origTotalSplitProcedurefee;
	}

	public String getBusinessMan() {
		return businessMan;
	}

	public void setBusinessMan(String businessMan) {
		this.businessMan = businessMan;
	}

	public Long getBusinessManId() {
		return businessManId;
	}

	public void setBusinessManId(Long businessManId) {
		this.businessManId = businessManId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}

	public String getIsSettWithCashAmount() {
		return isSettWithCashAmount;
	}

	public void setIsSettWithCashAmount(String isSettWithCashAmount) {
		this.isSettWithCashAmount = isSettWithCashAmount;
	}

	public Long getRealRefundFee() {
		return realRefundFee;
	}

	public void setRealRefundFee(Long realRefundFee) {
		this.realRefundFee = realRefundFee;
	}

	public String getOrderInfo() {
		return orderInfo;
	}

	public void setOrderInfo(String orderInfo) {
		this.orderInfo = orderInfo;
	}
	
	
}