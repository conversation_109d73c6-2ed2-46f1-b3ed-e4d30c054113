package com.epaylinks.efps.txs.transaction.domain.split;

import com.epaylinks.efps.common.business.CommonOuterResponse;

/**
 * 分账关系响应
 * <AUTHOR>
 *
 */
public class SplitRelationResponse extends CommonOuterResponse<String> {
	/**
     * 分账关系序列号
     */
	private String splitRelationId;
	
	/**
	 * 请求流水号
	 */
	private String outTradeNo;
	/**
     * 所属商户号
     */
    private String belongCustomerCode;
    
	public SplitRelationResponse(String outTradeNo, String belongCustomerCode) {
		super();
		this.outTradeNo = outTradeNo;
		this.belongCustomerCode = belongCustomerCode;
	}
	public SplitRelationResponse() {
		super();
	}

	public String getSplitRelationId() {
		return splitRelationId;
	}
	public void setSplitRelationId(String splitRelationId) {
		this.splitRelationId = splitRelationId;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getBelongCustomerCode() {
		return belongCustomerCode;
	}
	public void setBelongCustomerCode(String belongCustomerCode) {
		this.belongCustomerCode = belongCustomerCode;
	}
}
