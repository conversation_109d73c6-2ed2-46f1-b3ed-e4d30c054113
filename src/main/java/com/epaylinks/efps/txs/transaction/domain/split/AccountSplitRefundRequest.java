package com.epaylinks.efps.txs.transaction.domain.split;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

public class AccountSplitRefundRequest {
    /**
     * 版本号
     */
    private String version;

    /**
     *
     * 商户退款单号
     */
    @NotBlank
    @Size(max = 32, message = "最长32个字符")
    private String outRefundNo;
    /**
     * 原支付交易的商户订单号
     */
    private String outTradeNo;
    private String transactionNo;

    /**
     * 商户号
     */
    @NotBlank(message="商户号不能为空")
    @Size(max = 32, message = "最长32个字符")
    private String customerCode;

    /**
     * 附加数据
     */
    private String attachData;

    /**
     * 随机字符串
     */
    @NotBlank(message="随机字符串不能为空")
    private String nonceStr;

    private Long refundAmount;

    private List<SplitInfo> splitInfoList;

    /**
     * 退款比例 0-100
     */
    private Integer ratio;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Long refundAmount) {
        this.refundAmount = refundAmount;
    }

    public List<SplitInfo> getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(List<SplitInfo> splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

    public Integer getRatio() {
        return ratio;
    }

    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }
}
