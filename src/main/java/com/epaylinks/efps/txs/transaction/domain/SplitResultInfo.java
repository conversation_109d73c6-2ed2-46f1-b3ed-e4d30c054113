package com.epaylinks.efps.txs.transaction.domain;

public class SplitResultInfo {
	String customerCode;
	Long amount;
	Long procedureFee;

	private Integer isProcedureCustomer;

	private Integer settleCycle;

	private String SettleState;
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public Integer getIsProcedureCustomer() {
		return isProcedureCustomer;
	}

	public void setIsProcedureCustomer(Integer isProcedureCustomer) {
		this.isProcedureCustomer = isProcedureCustomer;
	}

	public Integer getSettleCycle() {
		return settleCycle;
	}

	public void setSettleCycle(Integer settleCycle) {
		this.settleCycle = settleCycle;
	}

	public String getSettleState() {
		return SettleState;
	}

	public void setSettleState(String settleState) {
		SettleState = settleState;
	}
}
