package com.epaylinks.efps.txs.transaction.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import com.epaylinks.efps.txs.transaction.model.TxsSplitOrder;

@Mapper
public interface TxsSplitOrderMapper {
	int deleteByPrimaryKey(String transactionNo);
	//必须使用	int insertSelective(TxsSplitOrder record);替换insert
	//int insert(TxsSplitOrder record);

	int insertSelective(TxsSplitOrder record);

	TxsSplitOrder selectByPrimaryKey(String transactionNo);

	int updateByPrimaryKeySelective(TxsSplitOrder record);

	int updateByPrimaryKey(TxsSplitOrder record);

	List<TxsSplitOrder> selectByOutTradeNoAndCustomerCode(@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);

	TxsSplitOrder selectByThree(@Param("outTradeNo") String outTradeNo,
								@Param("customerCode") String customerCode,
								@Param("outSplitTradeNo") String outSplitTradeNo);

	/**
	 * 根据交易单号和客户编码查询
	 * @param transactionNo
	 * @param customerCode
	 * @return
	 */
	TxsSplitOrder selectByTransactionNoAndCustomerCode(@Param("transactionNo") String transactionNo,
			@Param("customerCode") String customerCode);
	/**
	 * 根据外部单号，交易状态，客户编码查询
	 * @param outTradeNo
	 * @param state
	 * @param customerCode
	 * @return
	 */
	List<TxsSplitOrder> selectByOutTradeNoToResultQuery(@Param("outTradeNo") String outTradeNo,
			@Param("state") String state, @Param("customerCode")String customerCode);
	
	/**
	 * 根据外部单号和客户编码查询分账账单
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	List<TxsSplitOrder> selectByOutTradeNoAndCustomerCodes(@Param("outTradeNo") String outTradeNo,
			@Param("customerCode") String customerCode);
	
	/**
	 * 分账订单金额是否大于退款中的金额与已退款金额之和
	 * @param transactionNo
	 * @param currencyRefundFee
	 * @return
	 */
	int updateAmountByTransactionNo(@Param("transactionNo")String transactionNo,
			@Param("currencyRefundFee") Long currencyRefundFee);
	
	/**
	 * 失败，退款中减掉数据
	 * @param txsPreOrder
	 * @return
	 */
	int updateReduceAmountByTransactionNo(TxsSplitOrder txsSplitOrder);

	/**
	 * 成功，退款中减掉数据，已退款中加入数据
	 * @param txsPreOrder
	 * @return
	 */
	int updateAddAmountByTransactionNo(TxsSplitOrder txsSplitOrder);

	int updateConfirmByTransactionNo(TxsSplitOrder txsSplitOrder);
	
	List<TxsSplitOrder> selectByBusinessInstId(@Param("businessInstId") String businessInstId);

	/**
	 * 根据外部订单号查询分账订单
	 * @param outTradeNo
	 * @return
	 */
	TxsSplitOrder selectByOutTradeNo(String outTradeNo);
	
	List<TxsSplitOrder> selectByCustomerCodeAndStateAndEndTimeInDate(
			@Param("customerCode")String customerCode, 
			@Param("state")String state, @Param("endTime")Date endTime);
	
	List<TxsSplitOrder> selectByOutTradeNoList(@Param("outTradeNoList")List<String> outTradeNo);
	
	List<TxsSplitOrder> selectAll();

	List<String> selectNoSucessSyncZhfz(@Param("zhfzCheckEndMinutes")int zhfzCheckEndMinutes, @Param("zhfzCheckBeginDay")Date zhfzCheckBeginDay, @Param("zhfzCheckRowSize")int zhfzCheckRowSize);

    List<TxsSplitOrder> selectNoSucessOrder(int fzjyAuditNumber);
    
    int updateState51To03(@Param("transactionNo")String transactionNo);

	int insertIntoUq(@Param("uq")String uq);

	/**
	 *
	 */
	int updateStateNotSuccess(@Param("transactionNo")String transactionNo,
									@Param("endTime") Date endTime,
									@Param("updateTime") Date updateTime,
									@Param("errorCode")String errorCode,
									@Param("state")String state);

	/**
	 * 将账户分账之分步分账,由00(分账成功)更新至04(预分账成功)
	 * @param transactionNo
	 * @return
	 */
	int updateAccountSplit00to04(@Param("transactionNo")String transactionNo);

	/**
	 * 根据交易单号和客户编码查询
	 * @param transactionNo
	 * @param customerCode
	 * @return
	 */
	TxsSplitOrder selectByTransactionNo(@Param("transactionNo") String transactionNo);

	int updateSetControlling(@Param("transactionNo")String transactionNo , @Param("controlling")String controlling );
}