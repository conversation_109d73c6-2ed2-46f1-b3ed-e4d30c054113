package com.epaylinks.efps.txs.transaction.model;

public class PasBankCardBin {
    private String recordId;

    private String cardNoRange;

    private String cardNoRangeLen;

    private String issueBankNo;

    private String bankIcon;

    private String issueBankName;

    private String cardName;

    private String applyRange;

    private String cardNoLen;

    private String cardType;

    private String issueBankAccount;

    private String selfUse;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getCardNoRange() {
        return cardNoRange;
    }

    public void setCardNoRange(String cardNoRange) {
        this.cardNoRange = cardNoRange;
    }

    public String getCardNoRangeLen() {
        return cardNoRangeLen;
    }

    public void setCardNoRangeLen(String cardNoRangeLen) {
        this.cardNoRangeLen = cardNoRangeLen;
    }

    public String getIssueBankNo() {
        return issueBankNo;
    }

    public void setIssueBankNo(String issueBankNo) {
        this.issueBankNo = issueBankNo;
    }

    public String getBankIcon() {
        return bankIcon;
    }

    public void setBankIcon(String bankIcon) {
        this.bankIcon = bankIcon;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getApplyRange() {
        return applyRange;
    }

    public void setApplyRange(String applyRange) {
        this.applyRange = applyRange;
    }

    public String getCardNoLen() {
        return cardNoLen;
    }

    public void setCardNoLen(String cardNoLen) {
        this.cardNoLen = cardNoLen;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getIssueBankAccount() {
        return issueBankAccount;
    }

    public void setIssueBankAccount(String issueBankAccount) {
        this.issueBankAccount = issueBankAccount;
    }

    public String getSelfUse() {
        return selfUse;
    }

    public void setSelfUse(String selfUse) {
        this.selfUse = selfUse;
    }
}