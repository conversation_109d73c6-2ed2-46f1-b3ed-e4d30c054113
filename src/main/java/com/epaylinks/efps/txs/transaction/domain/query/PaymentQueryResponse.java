package com.epaylinks.efps.txs.transaction.domain.query;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;

/**
 * 支付结果查询响应
 * <AUTHOR>
 *
 */
public class PaymentQueryResponse extends CommonOuterResponse {
	private String customerCode;
	private String outTradeNo;
	private String transactionNo;
	private Long amount;
	private String payState;
	private String payTime;
	private String settCycle;
	private Long settCycleInterval;
	private Long procedureFee;
	private String attachData;
	private String nonceStr;
	
	private String channelOrder;
	private String openId;
	private String buyerLogonId;
	private String buyerId;
	private String terminalNo;
	private String nfcTagId;
	private String payMethod;
	/**
	 * 现金支付金额，分
	 */
	private Long cashAmount;
	/**
	 * 代金券金额，分
	 */
	private Long couponAmount;
	private JSONObject payerInfo;
	private JSONObject promotionDetail; 
	private String terminalName ;
	private String remark;

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	public String getPayState() {
		return payState;
	}
	public void setPayState(String payState) {
		this.payState = payState;
	}
	public String getPayTime() {
		return payTime;
	}
	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}
//	public String getSettCycle() {
//		return settCycle;
//	}
	public void setSettCycle(String settCycle) {
		this.settCycle = settCycle;
	}
//	public Long getSettCycleInterval() {
//		return settCycleInterval;
//	}
	public void setSettCycleInterval(Long settCycleInterval) {
		this.settCycleInterval = settCycleInterval;
	}
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}
	public String getAttachData() {
		return attachData;
	}
	public void setAttachData(String attachData) {
		this.attachData = attachData;
	}
	public String getNonceStr() {
		return nonceStr;
	}
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
	public String getChannelOrder() {
		return channelOrder;
	}
	public void setChannelOrder(String channelOrder) {
		this.channelOrder = channelOrder;
	}
	public String getOpenId() {
		return openId;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}
	public String getBuyerLogonId() {
		return buyerLogonId;
	}
	public void setBuyerLogonId(String buyerLogonId) {
		this.buyerLogonId = buyerLogonId;
	}
	public String getBuyerId() {
		return buyerId;
	}
	public void setBuyerId(String buyerId) {
		this.buyerId = buyerId;
	}

	public String getTerminalNo() {
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo) {
		this.terminalNo = terminalNo;
	}

	public String getNfcTagId() {
		return nfcTagId;
	}

	public void setNfcTagId(String nfcTagId) {
		this.nfcTagId = nfcTagId;
	}

	public JSONObject getPayerInfo() {
		return payerInfo;
	}

	public void setPayerInfo(JSONObject payerInfo) {
		this.payerInfo = payerInfo;
	}

	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}

	public JSONObject getPromotionDetail() {
		return promotionDetail;
	}

	public void setPromotionDetail(JSONObject promotionDetail) {
		this.promotionDetail = promotionDetail;
	}

	public String getTerminalName() {
		return terminalName;
	}

	public void setTerminalName(String terminalName) {
		this.terminalName = terminalName;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	
}
