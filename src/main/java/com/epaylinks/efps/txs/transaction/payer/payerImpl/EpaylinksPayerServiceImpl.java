package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.EpaylinksTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;
@Service("EpaylinksCustomer")
public class EpaylinksPayerServiceImpl implements PayerService{

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		EpaylinksTrader epaylinksTrader = new EpaylinksTrader();
		epaylinksTrader.setCustomerCode(MapUtils.getString(map, "customerCode"));
		epaylinksTrader.setType(TraderType.EpaylinksCustomer);
		return epaylinksTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		// TODO 内转的支付方式
		Payer payer = new Payer();
		payer.setPayerType(TraderType.EpaylinksCustomer.name());
		payer.setPayerId(payerJson.getString(""));
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
