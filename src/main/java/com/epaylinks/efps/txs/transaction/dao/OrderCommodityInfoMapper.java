package com.epaylinks.efps.txs.transaction.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.txs.transaction.model.OrderCommodityInfo;

@Mapper
public interface OrderCommodityInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderCommodityInfo record);

    int insertSelective(OrderCommodityInfo record);

    OrderCommodityInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderCommodityInfo record);

    int updateByPrimaryKey(OrderCommodityInfo record);
    
    List<OrderCommodityInfo> selectByCustomerCodeAndCommodityTradeNo(@Param("customerCode")String customerCode, @Param("commodityOrderNo")String commodityOrderNo);
}