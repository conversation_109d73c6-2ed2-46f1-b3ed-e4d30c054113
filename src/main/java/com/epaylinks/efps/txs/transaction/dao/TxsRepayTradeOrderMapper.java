package com.epaylinks.efps.txs.transaction.dao;

import com.epaylinks.efps.txs.transaction.model.TxsRepayTradeOrder;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

@Mapper
public interface TxsRepayTradeOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TxsRepayTradeOrder record);

    int insertSelective(TxsRepayTradeOrder record);

    TxsRepayTradeOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TxsRepayTradeOrder record);
    /**
     * 防重复更新状态
     * @param record
     * @return
     */
    int updateByPrimaryKeyForRepeatUpdate(TxsRepayTradeOrder record);

    int updateByPrimaryKey(TxsRepayTradeOrder record);
    
    /**
     * 不分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsRepayTradeOrder> selectByNotPage(Map map);
    
    /**
     * 分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsRepayTradeOrder> selectByPage(Map map);
    
    int selectCount(Map map);
    /**
     * 统计手续费
     * @param map
     * @return
     */
	long selectSumProcedureFee(Map map);
	/**
	 * 统计交易金额
	 * @param map
	 * @return
	 */
	long selectSumAmount(Map map);
    
    TxsRepayTradeOrder selectByTransactionNo(@Param("transactionNo")String transactionNo);
    
    TxsRepayTradeOrder selectByRefundTransactionNo(@Param("refundTransactionNo")String refundTransactionNo);
}