package com.epaylinks.efps.txs.transaction.domain.query;
/**
 * 交易记录实体
 *
 */
public class TransactionRecord {
	/**
	 * 交易记录id（主键）
	 */
	private Integer transactionId;
	/**
	 * 商户id
	 */
	private Long storeId;
	/**
	 * 支付日期时间
	 */
	private String payDateTime;
	/**
	 * 商户订单号
	 */
	private String orderId;
	/**
	 * 易票联订单号
	 */
	private String epaylinksOrderId;
	/**
	 * 交易金额
	 */
	private double amount;
	/**
	 * 手续费
	 */
	private double serviceCharge;
	/**
	 * 交易类型
	 */
	private String transType;
	/**
	 * 交易状态
	 */
	private Integer status;
	/**
	 * 备注
	 */
	private String remarks;
	public String getPayDateTime() {
		return payDateTime;
	}
	public void setPayDateTime(String payDateTime) {
		this.payDateTime = payDateTime;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public double getAmount() {
		return amount;
	}
	public void setAmount(double amount) {
		this.amount = amount;
	}
	public double getServiceCharge() {
		return serviceCharge;
	}
	public void setServiceCharge(double serviceCharge) {
		this.serviceCharge = serviceCharge;
	}
	public String getTransType() {
		return transType;
	}
	public void setTransType(String transType) {
		this.transType = transType;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public String getEpaylinksOrderId() {
		return epaylinksOrderId;
	}
	public void setEpaylinksOrderId(String epaylinksOrderId) {
		this.epaylinksOrderId = epaylinksOrderId;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
}
