package com.epaylinks.efps.txs.transaction.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.cust.model.AuthInfo;
@Mapper
public interface AuthInfoMapper {
    int deleteByPrimaryKey(Long authId);

    int insert(AuthInfo record);

    int insertSelective(AuthInfo record);

    AuthInfo selectByPrimaryKey(Long authId);
    
    List<AuthInfo> selectByCustCodeAndType(
    		@Param("customerCode") String customerCode,
            @Param("authCustomerCode") String authCustomerCode,
            @Param("authType") String authType,
            @Param("uri") String uri);
    
    int countByCustCodeAndType(
    		@Param("customerCode") String customerCode,
            @Param("authCustomerCode") String authCustomerCode,
            @Param("authType") String authType,
            @Param("uri") String uri);

    int updateByPrimaryKeySelective(AuthInfo record);

    int updateByPrimaryKey(AuthInfo record);

    String selectAuthCustomerCode(
            @Param("authCustomerCode") String authCustomerCode,
            @Param("authType") String authType);
}