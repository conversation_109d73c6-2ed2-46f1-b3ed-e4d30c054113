package com.epaylinks.efps.txs.transaction.domain.split;

import com.epaylinks.efps.common.business.CommonOuterResponse;

/**
 * 分账响应
 * <AUTHOR>
 *
 * @date 2018年1月24日 上午10:26:32
 */
public class FenZhangResponse extends CommonOuterResponse{
	@Deprecated
	private String outTrandNo;
	private String nonceStr;
	private String outTradeNo;

	private String outSplitTradeNo;
	public String getOutTrandNo() {
		return outTrandNo;
	}
	public void setOutTrandNo(String outTrandNo) {
		this.outTradeNo = outTrandNo;
		this.outTrandNo = outTrandNo;
	}
	@Override
	public String getNonceStr() {
		return nonceStr;
	}
	@Override
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTrandNo = outTradeNo;
		this.outTradeNo = outTradeNo;
	}

	public String getOutSplitTradeNo() {
		return outSplitTradeNo;
	}

	public void setOutSplitTradeNo(String outSplitTradeNo) {
		this.outSplitTradeNo = outSplitTradeNo;
	}
}
