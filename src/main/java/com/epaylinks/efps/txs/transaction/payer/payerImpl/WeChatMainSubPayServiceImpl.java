package com.epaylinks.efps.txs.transaction.payer.payerImpl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.Trader;
import com.epaylinks.efps.common.business.pay.request.TraderType;
import com.epaylinks.efps.common.business.pay.request.trader.WeChatSubscriptionTrader;
import com.epaylinks.efps.txs.transaction.domain.Payer;
import com.epaylinks.efps.txs.transaction.payer.PayerService;
@Service("WeChatSubscription")
public class WeChatMainSubPayServiceImpl implements PayerService{

	@Override
	public Trader getPayer(Map<String, Object> map) {
		// TODO Auto-generated method stub
		if (map == null || map.isEmpty()) {
			return null;
		}
		WeChatSubscriptionTrader weChatSubscriptionTrader = new WeChatSubscriptionTrader();
		weChatSubscriptionTrader.setAppId(MapUtils.getString(map, "appId"));
		weChatSubscriptionTrader.setOpenId(MapUtils.getString(map, "openId"));
		weChatSubscriptionTrader.setMchId(MapUtils.getString(map, "mchId"));
		weChatSubscriptionTrader.setType(TraderType.WeChatSubscription);
		return weChatSubscriptionTrader;
	}

	@Override
	public Payer setPayer(JSONObject payerJson) {
		Payer payer = new Payer();
		payer.setPayerId(payerJson.getString("appId") + "_" + payerJson.getString("openId"));
		payer.setPayerType(TraderType.WeChatSubscription.name());
		return payer;
	}

	@Override
	public boolean checkPayMethodHasParam(JSONObject payer) {
		if (!payer.containsKey("type") || !payer.containsKey("appId") || !payer.containsKey("openId")) {
			// 异常
			return false;
		}
		return true;
	}

	@Override
	public Map<String, String> checkBusinessTagerType(Trader trader) {
		// TODO Auto-generated method stub
		return new HashMap<>();
	}

}
