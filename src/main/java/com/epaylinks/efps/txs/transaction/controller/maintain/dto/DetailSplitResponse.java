package com.epaylinks.efps.txs.transaction.controller.maintain.dto;

import java.util.Map;

import com.epaylinks.efps.common.business.CommonOuterResponse;

public class DetailSplitResponse extends CommonOuterResponse {

	private Map<String, String> notExecTransaction;

	public Map<String, String> getNotExecTransaction() {
		return notExecTransaction;
	}

	public void setNotExecTransaction(Map<String, String> notExecTransaction) {
		this.notExecTransaction = notExecTransaction;
	}

}
