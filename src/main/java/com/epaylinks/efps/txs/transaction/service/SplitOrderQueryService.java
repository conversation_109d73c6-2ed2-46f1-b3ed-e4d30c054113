package com.epaylinks.efps.txs.transaction.service;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsSplitOrder;

@Service
public class SplitOrderQueryService {
	@Autowired
	private TxsSplitOrderMapper txsSplitOrderMapper;
	
	/**
	 * 根据客户编码和外部单号查询
	 * @param outTradeNo
	 * @param customerCode
	 * @return
	 */
	public List<TxsSplitOrder> selectByOutTradeNoAndCustomerCodes(String outTradeNo,
			String customerCode){
		return txsSplitOrderMapper.selectByOutTradeNoAndCustomerCodes(outTradeNo, customerCode);
	}

	/**
	 * 根据交易单号和客户编码查询分账订单
	 * @param transactionNo
	 * @param customerCode
	 * @return
	 */
	public TxsSplitOrder selectSplitOrderByTransactionNoAndCustomerCode(String transactionNo, String customerCode) {
		return txsSplitOrderMapper.selectByTransactionNoAndCustomerCode(transactionNo, customerCode);
	}

	/**
	 * 根据外部单号，交易状态，客户编码查询分账账单
	 * @param outTradeNo
	 * @param state
	 * @param customerCode
	 * @return
	 */
	public List<TxsSplitOrder> selectByOutTradeNoToResultQuery(String outTradeNo,
			 String state, String customerCode){
		return txsSplitOrderMapper.selectByOutTradeNoToResultQuery(outTradeNo, state, customerCode);
	}
}
