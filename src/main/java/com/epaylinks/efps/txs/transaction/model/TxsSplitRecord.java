package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

/**
 * 分账记录
 * <AUTHOR>
 *
 * @date 2018年1月24日 上午11:08:26
 */
public class TxsSplitRecord implements Comparable<TxsSplitRecord>{
    private Long id;

    private String transactionNo;

    private String sourceCustomerCode;
    @FieldAnnotation(fieldName="商户编号")
    private String customerCode;
    @FieldAnnotation(fieldName="分账状态", dictionaries="1:未执行,2:分账失败,3:分账成功,4:已撤销")
    private String state;
    @FieldAnnotation(fieldName="分账金额", yuanHandler = true)
    private Long amount;

    private Long procedurefee;

    private String errorCode;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Date updateTime;
    
    private String businessInstId;

    private Long refundFee;
    
    private Long refundingFee;
    
    private Long origAmount;
    
    private Long origRefundFee;
    
    private Long origRefundingFee;
    @FieldAnnotation(fieldName="商户名称")
    private String customerName;
    @FieldAnnotation(fieldName="原易票联订单号")
    private String payTransactionNo;
    @FieldAnnotation(fieldName="订单金额", yuanHandler = true)
    private Long payAmount;
    @FieldAnnotation(fieldName="手续费", yuanHandler = true)
    private Long payProcedureFee;
    
    private String sourceCustomerName;
    /**
     * 分账比例
     */
    private Double splitRatio;
    /**
     * 分账手续费
     */
    private Long splitProcedureFee;
    
    //分账主体商户号
    private String splitMain;
    
    //分账主体商户名称
    private String splitMainName;
    
    private String splitModel;

	/**
	 * 业务员
	 */
	private String businessMan;

	/**
	 * 业务员ID
	 */
	private Long businessManId;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 公司ID
	 */
	private Long companyId;
	/**
	 * 现金支付金额，分
	 */
	private Long cashAmount;

	/**
	 * 代金券金额，分
	 */
	private Long couponAmount;
	/**
	 * 撤销订单号
	 */
	private String revokeTransactionNo;

	/**
	 * 交易类型
	 */
	private String transactionType;

	@ApiModelProperty(value="分账属性(0：分账主体商户 1：常规分账对象 2：收单手续费扣除对象 3：分账手续费扣除对象)", dataType = "String")
	private String splitAttr;

	private String outTradeNo;

	/**
	 * 原订单实收金额，分
	 */
	private Long payCashAmount;

	/**
	 * 结算时间
	 * 拆单分账的结算周期
	 * 拆单分账时必须
	 * 最小值为商户交易业务结算周期时间，最大30天
	 */
	private Integer settleCycle;

	/**
	 *是否第一笔拆单. 1-是
	 * @return
	 */
	private String isFirst;

	/**
	 *商户分账单号
	 * @return
	 */
	private String outSplitTradeNo;

	/**
	 *结算状态
	 * @return
	 */
	private String settleState;

	/**
	 * 结算类型,继承自txsPreOrder, 用于前端展示
	 * T+N,存T; D+N存D; realTime,存D
	 */
	private String settleCycleType;

	/**
	 * 1-表示还在管控中的退货,记账时周期要传X
	 */
	private String controlling;

    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getSourceCustomerCode() {
        return sourceCustomerCode;
    }

    public void setSourceCustomerCode(String sourceCustomerCode) {
        this.sourceCustomerCode = sourceCustomerCode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getAmount() {
		return amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

	public Long getProcedurefee() {
		return procedurefee;
	}

	public void setProcedurefee(Long procedurefee) {
		this.procedurefee = procedurefee;
	}

	public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public String getBusinessInstId() {
		return businessInstId;
	}

	public void setBusinessInstId(String businessInstId) {
		this.businessInstId = businessInstId;
	}
	public Long getRefundFee() {
		return refundFee;
	}

	public void setRefundFee(Long refundFee) {
		this.refundFee = refundFee;
	}

	public Long getRefundingFee() {
		return refundingFee;
	}

	public void setRefundingFee(Long refundingFee) {
		this.refundingFee = refundingFee;
	}

	public Long getOrigAmount() {
		return origAmount;
	}

	public void setOrigAmount(Long origAmount) {
		this.origAmount = origAmount;
	}

	public Long getOrigRefundFee() {
		return origRefundFee;
	}

	public void setOrigRefundFee(Long origRefundFee) {
		this.origRefundFee = origRefundFee;
	}

	public Long getOrigRefundingFee() {
		return origRefundingFee;
	}

	public void setOrigRefundingFee(Long origRefundingFee) {
		this.origRefundingFee = origRefundingFee;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getPayTransactionNo() {
		return payTransactionNo;
	}

	public void setPayTransactionNo(String payTransactionNo) {
		this.payTransactionNo = payTransactionNo;
	}

	public Long getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}

	public Long getPayProcedureFee() {
		return payProcedureFee;
	}

	public void setPayProcedureFee(Long payProcedureFee) {
		this.payProcedureFee = payProcedureFee;
	}

	public String getSourceCustomerName() {
		return sourceCustomerName;
	}

	public void setSourceCustomerName(String sourceCustomerName) {
		this.sourceCustomerName = sourceCustomerName;
	}

	public Double getSplitRatio() {
		return splitRatio;
	}

	public void setSplitRatio(Double splitRatio) {
		this.splitRatio = splitRatio;
	}

	public Long getSplitProcedureFee() {
		return splitProcedureFee;
	}

	public void setSplitProcedureFee(Long splitProcedureFee) {
		this.splitProcedureFee = splitProcedureFee;
	}

	public String getSplitMain() {
		return splitMain;
	}

	public void setSplitMain(String splitMain) {
		this.splitMain = splitMain;
	}

	public String getSplitMainName() {
		return splitMainName;
	}

	public void setSplitMainName(String splitMainName) {
		this.splitMainName = splitMainName;
	}

	public String getSplitModel() {
		return splitModel;
	}

	public void setSplitModel(String splitModel) {
		this.splitModel = splitModel;
	}

	public String getBusinessMan() {
		return businessMan;
	}

	public void setBusinessMan(String businessMan) {
		this.businessMan = businessMan;
	}

	public Long getBusinessManId() {
		return businessManId;
	}

	public void setBusinessManId(Long businessManId) {
		this.businessManId = businessManId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}

	public String getRevokeTransactionNo() {
		return revokeTransactionNo;
	}

	public void setRevokeTransactionNo(String revokeTransactionNo) {
		this.revokeTransactionNo = revokeTransactionNo;
	}

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public Long getPayCashAmount() {
		return payCashAmount;
	}

	public void setPayCashAmount(Long payCashAmount) {
		this.payCashAmount = payCashAmount;
	}

	public String getSplitAttr() {
		return splitAttr;
	}

	public void setSplitAttr(String splitAttr) {
		this.splitAttr = splitAttr;
	}

	public Integer getSettleCycle() {
		return settleCycle;
	}

	public void setSettleCycle(Integer settleCycle) {
		this.settleCycle = settleCycle;
	}

	public String getIsFirst() {
		return isFirst;
	}

	public void setIsFirst(String isFirst) {
		this.isFirst = isFirst;
	}

	public String getOutSplitTradeNo() {
		return outSplitTradeNo;
	}

	public void setOutSplitTradeNo(String outSplitTradeNo) {
		this.outSplitTradeNo = outSplitTradeNo;
	}

	@Override
    public int compareTo(TxsSplitRecord sr) {
		if(this.id == null) {
			return -1;
		}
        return this.id.compareTo(sr.id);
    }

	public String getSettleState() {
		return settleState;
	}

	public void setSettleState(String settleState) {
		this.settleState = settleState;
	}

	public String getSettleCycleType() {
		return settleCycleType;
	}

	public void setSettleCycleType(String settleCycleType) {
		this.settleCycleType = settleCycleType;
	}

	public String getControlling() {
		return controlling;
	}

	public void setControlling(String controlling) {
		this.controlling = controlling;
	}
}