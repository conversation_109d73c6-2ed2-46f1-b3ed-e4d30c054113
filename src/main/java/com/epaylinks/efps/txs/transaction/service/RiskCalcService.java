package com.epaylinks.efps.txs.transaction.service;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.txs.service.RcService;
import com.epaylinks.efps.txs.service.dto.rc.RcCalculateRequest;

@Service
public class RiskCalcService {
	
//	@Logable(businessTag = "calculate")
	public RcCalculateRequest calculate(String transactionType ,String businessCode, String payMethod, String outTradeNo , String transactionNo , 
			Map<String, String> indexs , Map<String, String> businessTargetIds) {
		RcCalculateRequest rcCalculateRequest = new RcCalculateRequest();
		rcCalculateRequest.setBusinessType(transactionType);
		rcCalculateRequest.setOutTradeNo(outTradeNo);
		rcCalculateRequest.setTransactionNo(transactionNo);
		rcCalculateRequest.setIndexs(indexs);
		rcCalculateRequest.setBusinessTargetIds(businessTargetIds);
		rcCalculateRequest.setBusinessCode(businessCode);
		rcCalculateRequest.setPayMethod(payMethod);
		return rcCalculateRequest;
	}
}
