package com.epaylinks.efps.txs.transaction.dao;


import com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TxsMultiOrderRefundAmountMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TxsMultiOrderRefundAmount record);

    int insertSelective(TxsMultiOrderRefundAmount record);

    TxsMultiOrderRefundAmount selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TxsMultiOrderRefundAmount record);

    int updateByPrimaryKey(TxsMultiOrderRefundAmount record);

    int countByTransactionNoAndCustomer(@Param("transactionNo")String transactionNo, @Param("customerCode")String customerCode);

    int updateAddRefunding(@Param("transactionNo")String transactionNo,@Param("customerCode")String customerCode, @Param("amount")Long amount);

    int updateRefundIngToDoneForSuccess(@Param("transactionNo")String transactionNo,@Param("customerCode")String customerCode, @Param("amount")Long amount);

    int updateReduceRefunding(@Param("transactionNo")String transactionNo,@Param("customerCode")String customerCode, @Param("amount")Long amount);

    List<TxsMultiOrderRefundAmount> selectByTransactionNo(@Param("transactionNo")String transactionNo);
}
