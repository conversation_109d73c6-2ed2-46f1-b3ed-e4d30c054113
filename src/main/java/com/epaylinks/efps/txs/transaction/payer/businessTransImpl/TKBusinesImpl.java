package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsRefundTradeOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("TKBusiness")
public class TKBusinesImpl implements BusinessTransactionCheck {
	@Autowired
	private TxsRefundTradeOrderMapper txsRefundTradeOrderMapper;
	
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		// TODO Auto-generated method stub
		List<TxsRefundTradeOrder> txsRefundTradeOrders = txsRefundTradeOrderMapper.selectByBusinessInstId(businessExamId);
		if (txsRefundTradeOrders != null && !txsRefundTradeOrders.isEmpty()) {
			return true;
		}
		return false;
	}

}
