package com.epaylinks.efps.txs.transaction.dao;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;
@Mapper
public interface TxsSplitRecordMapper {

    int insertSelective(TxsSplitRecord record);

    int updateByPrimaryKeySelective(TxsSplitRecord record);

    /**
     * 根据交易单号查询分账记录
     * @param transactionNo
     * @return
     */
    List<TxsSplitRecord> selectByTransactionNo(@Param("transactionNo") String transactionNo);
    
    TxsSplitRecord selectByTransactionAndCustomerCode(@Param("transactionNo") String transactionNo,
    		@Param("customerCode") String customerCode);

	List<TxsSplitRecord> selectByBusinessInstId(@Param("businessInstId") String businessInstId);

    int updateStateNotSuccess(@Param("id")Long id,
                              @Param("updateTime") Date updateTime,
                              @Param("errorCode")String errorCode,
                              @Param("state")String state);

    int updatePaytransactionNoByTransactionNoWhenNull(@Param("transactionNo") String transactionNo, @Param("paytransactionNo") String paytransactionNo);

    boolean fenBuFenZhangHasRecords(@Param("transactionNo")String transactionNo );

    int updateSetControlling(@Param("transactionNo")String transactionNo , @Param("controlling")String controlling );

}
