package com.epaylinks.efps.txs.transaction.model;

import java.util.Date;

public class TxsRefundRollbackSplitOrder {
    private String transactionNo;

    private String outRefundTradeNo;

    private String customerCode;

    private String state;

    private Long amount;

    private Long procedureFee;

    private Long realAmount;

    private Date endTime;

    private String errorCode;

    private Date createTime;

    private Date updateTime;

    private String attachData;

    private String businessInstId;

    private Long slaveAmountSum;

    private String businessCode;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getOutRefundTradeNo() {
        return outRefundTradeNo;
    }

    public void setOutRefundTradeNo(String outRefundTradeNo) {
        this.outRefundTradeNo = outRefundTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public Long getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(Long realAmount) {
        this.realAmount = realAmount;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public String getBusinessInstId() {
        return businessInstId;
    }

    public void setBusinessInstId(String businessInstId) {
        this.businessInstId = businessInstId;
    }

    public Long getSlaveAmountSum() {
        return slaveAmountSum;
    }

    public void setSlaveAmountSum(Long slaveAmountSum) {
        this.slaveAmountSum = slaveAmountSum;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }
}