package com.epaylinks.efps.txs.transaction.service;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.txs.transaction.dao.AuthInfoMapper;
import org.thymeleaf.util.StringUtils;

@Service
public class AuthInfoService {
	@Autowired 
	private AuthInfoMapper authInfoMapper;
	@Autowired
	private AuthInfoService self;
	/**
	 * 如果uri中有两个//,去掉一个,以统一测试环境和生产环境的不同分隔符处理
	 * @param request
	 * @return
	 */
	public static String getUri(HttpServletRequest request)
	{
		String uri = request.getRequestURI();
		if(uri.startsWith("//"))
			return uri.substring(1);
		else
			return uri;
	}
	
	public String getUri() {
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
	    HttpServletRequest request = servletRequestAttributes.getRequest();
	    
	    return request.getHeader("x-uri");
	    
	}
	
	public boolean checkAuthInfo(String customerCode, String authCustomerCode) {
		boolean result = false ;
		String uri = self.getUri();
		int authCount = authInfoMapper.countByCustCodeAndType(customerCode, authCustomerCode, Constants.AuthType.zf.code, uri);
		
		if(authCount > 0) {
			result = true;
		}
		return result;
	}

	public boolean checkAuthInfoByType(String customerCode, String authCustomerCode, String authType) {
		boolean result = false ;
		int authCount = authInfoMapper.countByCustCodeAndType(customerCode, authCustomerCode, authType, null);

		if(authCount > 0) {
			result = true;
		}
		return result;
	}

	/**
	 * 找出账户分账的授权者
	 * 注意,这部分代码不能随便改动
	 * 只有这种授权类型才是平台商的方式授权的
	 * @param customerCode
	 * @param authCustomerCode
	 * @param authType
	 * @return
	 */
	public String getAccountSplitProcedureCustomerAuthCustomer(String customerCode, String platCustomerCode) {
		String procedureCustomercode= authInfoMapper.selectAuthCustomerCode(customerCode, Constants.AuthType.accSplitFeePayer.code);
		if(StringUtils.isEmpty(procedureCustomercode) && !StringUtils.isEmpty(platCustomerCode)){
			procedureCustomercode= authInfoMapper.selectAuthCustomerCode(platCustomerCode, Constants.AuthType.accSplitFeePayer.code);
		}
	return  procedureCustomercode;
	}
	
	
}
