package com.epaylinks.efps.txs.transaction.service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.txs.service.UaaService;

@Service
public class CommonService {
	
	@Autowired
	private UaaService uaaService;
	
	@Autowired
	private CommonService self;
	
	
	@Logable(businessTag = "payLogManually-onlyArgs", outputResult = false)
	public String payLog(String message) {
		return message;
	}
	
	@Logable(businessTag = "txslogException")
	public void logException(Exception e) {
		// TODO Auto-generated method stub

	}
	
	public Object decryptData(Object object, String ...decryptFields) throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		
		Method method = null;
		Map<String, String> encyptMap = new HashMap<>();
		for(String decryptField: decryptFields) {
			method = object.getClass().getMethod("get" + decryptField.substring(0, 1).toUpperCase() + decryptField.substring(1));
			
			Object value;
            value = method.invoke(object);
            if (value != null && !"".equals(value.toString())) {
                encyptMap.put(decryptField, value.toString());
            }
		}
		
		Map<String, String> encryptResult = uaaService.decryptByEpspPrivateKey(encyptMap);
		
		for(Map.Entry<String, String> entry: encryptResult.entrySet()) {
			method = object.getClass().getMethod("set" + entry.getKey().substring(0, 1).toUpperCase() + entry.getKey().substring(1), String.class);
            method.invoke(object, entry.getValue());
		}
		
		return object;
	}
	
}
