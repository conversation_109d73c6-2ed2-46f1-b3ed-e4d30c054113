package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsPayTradeOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("PayBusiness")
public class PayBusinessImpl implements BusinessTransactionCheck {
	@Autowired
	private TxsPayTradeOrderMapper txsPayTradeOrderMapper;
	
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		TxsPayTradeOrder txsPayTradeOrder = new TxsPayTradeOrder();
		txsPayTradeOrder.setBusinessInstId(businessExamId);
		List<TxsPayTradeOrder> txsPayTradeOrders = txsPayTradeOrderMapper.selectByCondition(txsPayTradeOrder);
		if (txsPayTradeOrders != null && !txsPayTradeOrders.isEmpty()) {
			return true;
		}
		return false;
	}

}
