package com.epaylinks.efps.txs.transaction.model;

import com.alibaba.fastjson.JSON;

import java.util.Date;
/**
 * 分账订单表
 * <AUTHOR>
 *
 * @date 2018年1月24日 上午10:57:44
 */
public class TxsSplitOrder {
	/**
	 * 主键
	 */
    private String transactionNo;

    private String outTradeNo;

    private String customerCode;

    private String notifyUrl;

    private String state;

    private Long amount;

    private Long procedureFee;

    private Long realAmount;

    private Date endTime;

    private String errorCode;

    private Date createTime;

    private Date updateTime;

    private String attachData;

    private String splitInfoList;
    
    private String businessInstId;
    /**
     * 已退款金额
     */
    private Long refundFee;
    /**
     * 正在退款中金额
     */
    private Long refundingFee;
    
    /**用来扣手续费的子商户
     * 可以是分账商户自身
     */
    private String procedureCustomerCode;
    
    /**
     * 注意这里的含义变化了，是指不用付手续费的那些客户的分账金额之和
     * 历史原因，不改名字了
     */
    private Long salveAmountSum;
    
    private String oriBusinessCode;
    
    /**
     * 分账模式，0-集中分账；1:普通分账，2:其他分账
     */
    private String splitModel;
    
    private String splitProcedureCustomerCode; //SPLIT_PROCEDURE_CUSTOMER_CODE;
    
    /**
     * 分账主体商户号
     */
    private String splitMain;
    /**
	 * 现金支付金额，分
	 */
	private Long cashAmount;
	/**
	 * 代金券金额，分
	 */
	private Long couponAmount;
	/**
	 * 结算周期
	 */
	private String settCycleRuleCode;
	/**
	 * 跨境平台商户号
	 */
	private String crossPlatCustomerCode;
	/**
	 * 手续费扣除方式0-付款方扣，1-收款方扣
	 */
	private String procedureType;

	/**
	 * 撤销订单号
	 */
	private String revokeTransactionNo;

	/**
	 * 分账类型：0普通分账，1-分步分账
	 */
	private String splitType;


	/**
	 * 交易类型
	 */
	private String transactionType;

	/**
	 *
	 * @return
	 */
	private String isAccountSplitAuth;

	/**
	 *是否第一笔拆单. 1-是
	 * @return
	 */
	private String isFirst;

	/**
	 *商户分账单号
	 * @return
	 */
	private String outSplitTradeNo;

	/**
	 *1-表示还在管控中的退货,记账时周期要传X
	 * @return
	 */
	private String controlling;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getAmount() {
		return amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public Long getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(Long realAmount) {
		this.realAmount = realAmount;
	}

	public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public String getSplitInfoList() {
        return splitInfoList;
    }

    public void setSplitInfoList(String splitInfoList) {
        this.splitInfoList = splitInfoList;
    }

	public String getBusinessInstId() {
		return businessInstId;
	}

	public void setBusinessInstId(String businessInstId) {
		this.businessInstId = businessInstId;
	}

	public Long getRefundFee() {
		return refundFee;
	}

	public void setRefundFee(Long refundFee) {
		this.refundFee = refundFee;
	}

	public Long getRefundingFee() {
		return refundingFee;
	}

	public void setRefundingFee(Long refundingFee) {
		this.refundingFee = refundingFee;
	}

	public String getProcedureCustomerCode() {
		return procedureCustomerCode;
	}

	public void setProcedureCustomerCode(String procedureCustomerCode) {
		this.procedureCustomerCode = procedureCustomerCode;
	}

	public Long getSalveAmountSum() {
		return salveAmountSum;
	}

	public void setSalveAmountSum(Long salveAmountSum) {
		this.salveAmountSum = salveAmountSum;
	}

	public String getOriBusinessCode() {
		return oriBusinessCode;
	}

	public void setOriBusinessCode(String oriBusinessCode) {
		this.oriBusinessCode = oriBusinessCode;
	}

	public String getSplitModel() {
		return splitModel;
	}

	public void setSplitModel(String splitModel) {
		this.splitModel = splitModel;
	}

	public String getSplitProcedureCustomerCode() {
		return splitProcedureCustomerCode;
	}

	public void setSplitProcedureCustomerCode(String splitProcedureCustomerCode) {
		this.splitProcedureCustomerCode = splitProcedureCustomerCode;
	}

	public String getSplitMain() {
		return splitMain;
	}

	public void setSplitMain(String splitMain) {
		this.splitMain = splitMain;
	}
	

	public Long getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(Long cashAmount) {
		this.cashAmount = cashAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}
	

	public String getSettCycleRuleCode() {
		return settCycleRuleCode;
	}

	public void setSettCycleRuleCode(String settCycleRuleCode) {
		this.settCycleRuleCode = settCycleRuleCode;
	}

	public String getCrossPlatCustomerCode() {
		return crossPlatCustomerCode;
	}

	public void setCrossPlatCustomerCode(String crossPlatCustomerCode) {
		this.crossPlatCustomerCode = crossPlatCustomerCode;
	}

	public String getProcedureType() {
		return procedureType;
	}

	public void setProcedureType(String procedureType) {
		this.procedureType = procedureType;
	}

	public String getRevokeTransactionNo() {
		return revokeTransactionNo;
	}

	public void setRevokeTransactionNo(String revokeTransactionNo) {
		this.revokeTransactionNo = revokeTransactionNo;
	}


	public String getSplitType() {
		return splitType;
	}

	public void setSplitType(String splitType) {
		this.splitType = splitType;
	}


	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getIsAccountSplitAuth() {
		return isAccountSplitAuth;
	}

	public void setIsAccountSplitAuth(String isAccountSplitAuth) {
		this.isAccountSplitAuth = isAccountSplitAuth;
	}

	public String getIsFirst() {
		return isFirst;
	}

	public void setIsFirst(String isFirst) {
		this.isFirst = isFirst;
	}

	public String getOutSplitTradeNo() {
		return outSplitTradeNo;
	}

	public void setOutSplitTradeNo(String outSplitTradeNo) {
		this.outSplitTradeNo = outSplitTradeNo;
	}

	public String getControlling() {
		return controlling;
	}

	public void setControlling(String controlling) {
		this.controlling = controlling;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}


}