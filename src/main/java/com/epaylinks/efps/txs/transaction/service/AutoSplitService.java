package com.epaylinks.efps.txs.transaction.service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.dao.TxsMultiOrderRefundAmountMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsPreOrderMapper;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.domain.split.FenZhangRequest;
import com.epaylinks.efps.txs.transaction.domain.split.FenZhangResponse;
import com.epaylinks.efps.txs.transaction.domain.split.SplitInfo;
import com.epaylinks.efps.txs.transaction.model.TxsMultiOrderRefundAmount;
import com.epaylinks.efps.txs.transaction.model.TxsPreOrder;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.internal.engine.messageinterpolation.InterpolationTerm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AutoSplitService {


    @Autowired
    private SplitPayService splitPayService;

    @Autowired
    private TxsPreOrderMapper txsPreOrderMapper;

    @Autowired
    private AutoSplitService self;

    @Value("${MultiOrderForceSplit.daysAgo:30}")
    private Integer daysAgo;

    @Value("${MultiOrderForceSplit.searchDays:1}")
    private Integer searchDays;

    @Autowired
    private CommonService commonService;

    @Logable(businessTag = "processAuto")
    public void processAuto(){
        List<Map<String, String>> list = self.fetchNeedManualPres(daysAgo, searchDays);
        self.processForPreOrderList(list);
    }

    @Logable(businessTag = "processAutoSpecial")
    public void processAutoSpecial(String outTradeNo, String customerCode, Integer searchDaysSpecial){


        List<Map<String, String>> list = new ArrayList<>();
        if(StringUtils.isNotEmpty(outTradeNo) && StringUtils.isNotEmpty(customerCode)){
            TxsPreOrder pre = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(outTradeNo, customerCode);
            if(null == pre){
                return ;
            }
            Map<String, String> map = new HashMap<>();
            map.put("OUTTRADENO", pre.getOutTradeNo());
            map.put("CUSTOMERCODE", pre.getCustomerCode());
            list.add(map);
        }else{
            Integer sqlSearchDays = searchDays;
            if(null != searchDaysSpecial){
                sqlSearchDays = searchDaysSpecial;
                if(sqlSearchDays.intValue()>7){
                    return;
                }
            }
            list = self.fetchNeedManualPres(daysAgo, sqlSearchDays);
        }

        self.processForPreOrderList(list);
    }

    /**
     * 1.时间范围: endTime大于等于sysdate - daysAgo - searchDays, 小于等于sydate - daysAgo
     * 2.endTime为非空
     * 3.交易状态为成功
     * 4.结算状态为已结算
     * 5.splitModel为4拆单分账
     * 那些超过30天才有endTime,或者交易成功后30天都不结算的，这里命中不了的.可以通过swagger发或者调接口搞定
     * @param daysAgo
     * @param searchDays
     * @return
     */
    public List<Map<String, String>>fetchNeedManualPres(Integer daysAgo, Integer searchDays){
        List<Map<String, String>>  list= txsPreOrderMapper.selectOutTradeCusomerForMulitiOrder(daysAgo, searchDays);
        return list;
    }

    public int processForPreOrderList(List<Map<String,String>> list){
        if(null == list || list.size() == 0){
            return 0;
        }

        int i = 0;
        for(Map map: list){
            String outTradeNo = (String)map.get("OUTTRADENO");
            String customerCode = (String)map.get("CUSTOMERCODE");
            try {
                self.processOne(outTradeNo, customerCode);
                i++;
            }catch (Exception e){
                commonService.logException(e);
            }

        }

        return i;

    }

    @Logable(businessTag = "processOne")
    public int processOne(String outTradeNo, String customerCode){


        //先同步一下
        splitPayService.adjustDividedDividing(outTradeNo, customerCode);
        TxsPreOrder txsPreOrder = txsPreOrderMapper.selectByOutTradeNoAndCustomerCode(outTradeNo, customerCode);

        if(null == txsPreOrder
                || !TxsConstants.SplitModel.MULTI_ORDER.code.equals(txsPreOrder.getSplitModel())
                || !TxsConstants.SettleMentState.SUCCESS.code.equals(txsPreOrder.getSettlementState())
                || !TxsConstants.PreOrderState.TRANSACTION_SUCCESS.code.equals(txsPreOrder.getPayState())){
            return 0;
        }

        Long dividingAmount = (null == txsPreOrder.getDividingAmount()?0L:txsPreOrder.getDividingAmount());
        Long dividedAmount = (null == txsPreOrder.getDividedAmount()?0L:txsPreOrder.getDividedAmount());

        Long splitAmount = txsPreOrder.getAmount()-dividingAmount-dividedAmount;


        List<SplitInfo> splitInfos = new ArrayList<>();
        SplitInfo splitInfo = new SplitInfo();
        splitInfo.setCustomerCode(customerCode);
        splitInfo.setAmount(splitAmount);
        // 金额相等,表示一笔都未拆,承担收单和分账手续费; 不等,说明已拆,不再承担手续费
        splitInfo.setIsProcedureCustomer(splitAmount.longValue()==txsPreOrder.getAmount()? 3:0);
        splitInfo.setSettleCycle(0);
        splitInfos.add(splitInfo);


        FenZhangRequest request = new FenZhangRequest();
        String uuid1 = UUIDUtils.uuid();
        String outSplitTradeNo = "AUTO"+uuid1.substring(4);
        request.setOutSplitTradeNo(outSplitTradeNo);
        request.setNonceStr(UUIDUtils.uuid());
        request.setAttachData("30天强制执行");
        request.setSplitInfoList(splitInfos);
        request.setCustomerCode(customerCode);
        if(StringUtils.isNotEmpty(txsPreOrder.getNotifyUrl())){
            request.setNotifyUrl(txsPreOrder.getNotifyUrl());
        }else{
            request.setNotifyUrl("null");
        }

        request.setOutTradeNo(outTradeNo);

        FenZhangResponse response = splitPayService.splitPay(customerCode, request);
        return 1;
    }


}
