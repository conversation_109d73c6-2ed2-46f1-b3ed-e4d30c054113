package com.epaylinks.efps.txs.transaction.controller;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelation;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationAddRequest;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationQueryRequest;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationQueryResponse;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationResponse;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationUpdateRequest;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRelation;
import com.epaylinks.efps.txs.transaction.service.CommonService;
import com.epaylinks.efps.txs.transaction.service.SplitRelationService;
import com.epaylinks.efps.txs.transaction.service.TransactionService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 分账关系控制器
 *
 * <AUTHOR>
 */

@RestController
@Api(value = "SplitRelationController", description = "分账关系控制器")
@RequestMapping("/splitRelation")
public class SplitRelationController {

	@Autowired
	private ReturnCodeUtil returnCodeUtil;
	@Autowired
	private CommonService commonService;
	@Autowired
	private SplitRelationService splitRelationService;
	@Autowired
	private TransactionService transactionService;


	/**
	 *创建分账关系
	 */
	@PostMapping("/add")
	@Logable(businessTag = "splitRelation.add")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "创建分账关系", notes = "创建分账关系", httpMethod = "POST")
	@ResponseBody
	@ApiImplicitParams({})
	public SplitRelationResponse add(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) @Valid SplitRelationAddRequest splitRelationAddRequest, BindingResult error) {
		SplitRelationResponse response = new SplitRelationResponse(splitRelationAddRequest.getOutTradeNo(),splitRelationAddRequest.getBelongCustomerCode());
		try {
			if (error.hasErrors()) {
				response.setReturnCode(TxsCode.PARAM_ERROR.code);
				response.setReturnMsg(error.getFieldError().getDefaultMessage());
				return response;
	        }
//			if(customerCodeHead == null || !customerCodeHead.equals(splitRelationAddRequest.getBelongCustomerCode())) {
//				response.setReturnCode(TxsCode.CUSTOMER_ATYPISM.code);
//				response.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.message);
//				return response;
//			}
			transactionService.checkCustomerCodeAndHead(customerCodeHead, splitRelationAddRequest.getBelongCustomerCode());
			response = splitRelationService.add(splitRelationAddRequest);
		} catch (Exception e) {
			e.printStackTrace();
			commonService.logException(e);
			returnCodeUtil.buildResponse(response, e, TxsCode.FAIL.code);
		}
		
		return response;
	}
	
	/**
	 *修改分账关系
	 */
	@PostMapping("/update")
	@Logable(businessTag = "splitRelation.update")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "修改分账关系", notes = "修改分账关系", httpMethod = "POST")
	@ResponseBody
	@ApiImplicitParams({})
	public SplitRelationResponse update(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) @Valid SplitRelationUpdateRequest splitRelationUpdateRequest, BindingResult error) {
		SplitRelationResponse response = new SplitRelationResponse();
		try {
			if (error.hasErrors()) {
				response.setReturnCode(TxsCode.PARAM_ERROR.code);
				response.setReturnMsg(error.getFieldError().getDefaultMessage());
				return response;
	        }
//			if(customerCodeHead == null || !customerCodeHead.equals(splitRelationUpdateRequest.getBelongCustomerCode())) {
//				response.setReturnCode(TxsCode.CUSTOMER_ATYPISM.code);
//				response.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.message);
//				return response;
//			}
			transactionService.checkCustomerCodeAndHead(customerCodeHead, splitRelationUpdateRequest.getBelongCustomerCode());
			response = splitRelationService.update(splitRelationUpdateRequest);
		} catch (Exception e) {
			e.printStackTrace();
			commonService.logException(e);
			returnCodeUtil.buildResponse(response, e, TxsCode.FAIL.code);
		}
		response.setSplitRelationId(splitRelationUpdateRequest.getSplitRelationId());
		
		return response;
	}
	/**
	 *查询分账关系
	 */
	@PostMapping("/query")
	@Logable(businessTag = "splitRelation.query")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "查询分账关系", notes = "查询分账关系", httpMethod = "POST")
	@ResponseBody
	@ApiImplicitParams({})
	public SplitRelationQueryResponse query(
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
			@RequestBody(required = true) @Valid SplitRelationQueryRequest splitRelationQueryRequest, BindingResult error) {
		SplitRelationQueryResponse response = new SplitRelationQueryResponse();
		try {
			String splitRelationId = splitRelationQueryRequest.getSplitRelationId();
			response.setSplitRelationId(splitRelationId);
			response.setBelongCustomerCode(splitRelationQueryRequest.getBelongCustomerCode());
			if (error.hasErrors()) {
				response.setReturnCode(TxsCode.PARAM_ERROR.code);
				response.setReturnMsg(error.getFieldError().getDefaultMessage());
				return response;
	        }
//			if(customerCodeHead == null || !customerCodeHead.equals(splitRelationQueryRequest.getBelongCustomerCode())) {
//				response.setReturnCode(TxsCode.CUSTOMER_ATYPISM.code);
//				response.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.message);
//				return response;
//			}
			transactionService.checkCustomerCodeAndHead(customerCodeHead, splitRelationQueryRequest.getBelongCustomerCode());
			List<TxsSplitRelation> txsSplitRelationList = splitRelationService.query(customerCodeHead, splitRelationId);
			List<SplitRelation> splitRelationList = new ArrayList<>();
			for(TxsSplitRelation item: txsSplitRelationList) {
				SplitRelation splitRelation = new SplitRelation(item.getCustomerCode(), item.getIsProcedureCustomer(), item.getRatio());
				splitRelationList.add(splitRelation);
				response.setBelongCustomerCode(item.getBelongCustomerCode());
			}
			response.setSplitRelationList(splitRelationList);
		} catch (Exception e) {
			e.printStackTrace();
			commonService.logException(e);
			returnCodeUtil.buildResponse(response, e, TxsCode.FAIL.code);
		}
		
		return response;
	}
}
