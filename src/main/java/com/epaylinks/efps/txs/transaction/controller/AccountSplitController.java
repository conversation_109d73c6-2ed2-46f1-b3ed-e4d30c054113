package com.epaylinks.efps.txs.transaction.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.business.cum.CustomerInfo;
import com.epaylinks.efps.common.business.cum.service.impl.CumCacheServiceImpl;
import com.epaylinks.efps.common.business.domain.CustomerBusinessInfo;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.constants.TxsConstants;
import com.epaylinks.efps.txs.transaction.domain.global.GlobalSplitRequest;
import com.epaylinks.efps.txs.transaction.domain.global.GlobalSplitResponse;
import com.epaylinks.efps.txs.transaction.domain.split.*;
import com.epaylinks.efps.txs.transaction.service.AccountSplitService;
import com.epaylinks.efps.txs.transaction.service.TransactionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@Api(value = "AccountSplitController", description = "账户分账")
@RequestMapping("/split")
public class AccountSplitController {
    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AccountSplitService accountSplitService;

    @Autowired
    private ReturnCodeUtil returnCodeUtil;

    @Autowired
    private CumCacheServiceImpl cumCacheService;

    @Value("${accountSplit.refundCustomerCode:***************}")
    private String refundCustomerCode;

    @Value("${accountSplit.refundLimit:true}")
    private Boolean refundLimit;

    private static final Logger logger = LoggerFactory.getLogger(AccountSplitController.class);

    @PostMapping("/accountSplit")
    @Logable(businessTag = "AccountSplitController.accountSplit")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "账户分账接口", httpMethod = "POST")
    @ApiImplicitParams({})
    public AccountSplitResponse accountSplit(@RequestHeader(value = "x-customer-code") String customerCodeHead,
                                             @RequestBody @Valid AccountSplitRequest accountSplitRequest){
        AccountSplitResponse accountSplitResponse = new AccountSplitResponse();
        accountSplitResponse.setNonceStr(UUIDUtils.uuid());
        accountSplitResponse.setOutTradeNo(accountSplitRequest.getOutTradeNo());
        accountSplitResponse.setCustomerCode(accountSplitRequest.getCustomerCode());
        if(!customerCodeHead.equalsIgnoreCase(accountSplitRequest.getCustomerCode())) {
            CustomerInfo customerInfo = cumCacheService.getCustomerInfo(accountSplitRequest.getCustomerCode(),accountSplitRequest.getCustomerCode(),"1");
            if(!customerCodeHead.equalsIgnoreCase(customerInfo.getPlatCustomerCode())){
                accountSplitResponse.setReturnCode(TxsCode.CUSTOMER_ATYPISM.getCode());
                accountSplitResponse.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.getMessage() + "分账失败");
                return accountSplitResponse;
            }
        }
        try {
            
            accountSplitResponse = accountSplitService.accountSplit(accountSplitRequest, accountSplitResponse);
        } catch (Exception e) {
            e.printStackTrace();
            transactionService.logException(e);
            accountSplitResponse.setOutTradeNo(accountSplitRequest.getOutTradeNo());
            if(e instanceof AppException){
                String errorCode = ((AppException) e).getErrorCode();
                String errorMsg = ((AppException) e).getErrorMsg();
                if(StringUtils.isEmpty(errorMsg)){
                    errorMsg = TxsCode.getComment(errorCode);
                }
                accountSplitResponse.setReturnCode(errorCode);
                accountSplitResponse.setReturnMsg(errorMsg);
            }else {
                logger.error("账户分账异常", e);
                returnCodeUtil.buildResponse(accountSplitResponse, e, TxsCode.FAIL.code);
            }
        }
        return accountSplitResponse;
    }

    @PostMapping("/accountSplitConfirm")
    @Logable(businessTag = "AccountSplitController.accountSplitConfirm")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "账户分账确认接口", httpMethod = "POST")
    @ApiImplicitParams({})
    public AccountSplitResponse accountSplitConfirm(@RequestHeader(value = "x-customer-code") String customerCodeHead,
                                             @RequestBody @Valid AccountSplitConfirmRequest accountSplitRequest){
        AccountSplitResponse accountSplitResponse = new AccountSplitResponse();
        accountSplitResponse.setNonceStr(UUIDUtils.uuid());
        accountSplitResponse.setOutTradeNo(accountSplitRequest.getOutTradeNo());
        if(!customerCodeHead.equalsIgnoreCase(accountSplitRequest.getCustomerCode())) {
            CustomerInfo customerInfo = cumCacheService.getCustomerInfo(accountSplitRequest.getCustomerCode(),accountSplitRequest.getCustomerCode(),"1");
            if(!customerCodeHead.equalsIgnoreCase(customerInfo.getPlatCustomerCode())){
                accountSplitResponse.setReturnCode(TxsCode.CUSTOMER_ATYPISM.getCode());
                accountSplitResponse.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.getMessage() + "分账失败");
                return accountSplitResponse;
            }
        }
        try {

            accountSplitResponse = accountSplitService.accountSplitConfirm(accountSplitRequest);
        } catch (Exception e) {
            e.printStackTrace();
            transactionService.logException(e);
            accountSplitResponse.setOutTradeNo(accountSplitRequest.getOutTradeNo());
            if(e instanceof AppException){
                String errorCode = ((AppException) e).getErrorCode();
                String errorMsg = ((AppException) e).getErrorMsg();
                if(StringUtils.isEmpty(errorMsg)){
                    errorMsg = TxsCode.getComment(errorCode);
                }
                accountSplitResponse.setReturnCode(errorCode);
                accountSplitResponse.setReturnMsg(errorMsg);
            }else {
                logger.error("账户分账确认接口", e);
                returnCodeUtil.buildResponse(accountSplitResponse, e, TxsCode.FAIL.code);
            }
        }
        return accountSplitResponse;
    }

    @PostMapping("/globalSplit")
    @Logable(businessTag = "AccountSplitController.globalSplit")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "国际收款接口", httpMethod = "POST")
    @ApiImplicitParams({})
    public GlobalSplitResponse globalSplit(@RequestHeader(value = "x-customer-code") String customerCodeHead,
                                             @RequestBody @Valid GlobalSplitRequest globalSplitRequest){
        GlobalSplitResponse accountSplitResponse = new GlobalSplitResponse();
        accountSplitResponse.setNonceStr(UUIDUtils.uuid());
        accountSplitResponse.setOutTradeNo(globalSplitRequest.getOutTradeNo());
        accountSplitResponse.setCommodityAmount(globalSplitRequest.getCommodityAmount());
        if(!customerCodeHead.equalsIgnoreCase(globalSplitRequest.getCustomerCode())) {
            CustomerInfo customerInfo = cumCacheService.getCustomerInfo(globalSplitRequest.getCustomerCode(),globalSplitRequest.getCustomerCode(),"1");
            if(!customerCodeHead.equalsIgnoreCase(customerInfo.getPlatCustomerCode())){
                accountSplitResponse.setReturnCode(TxsCode.CUSTOMER_ATYPISM.getCode());
                accountSplitResponse.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.getMessage());
                return accountSplitResponse;
            }
        }
        try {

            accountSplitResponse = accountSplitService.globalSplit(globalSplitRequest);
        } catch (Exception e) {
            transactionService.logException(e);
            if(e instanceof AppException){
                String errorCode = ((AppException) e).getErrorCode();
                String errorMsg = ((AppException) e).getErrorMsg();
                if(StringUtils.isEmpty(errorMsg)){
                    errorMsg = TxsCode.getComment(errorCode);
                }
                accountSplitResponse.setReturnCode(errorCode);
                accountSplitResponse.setReturnMsg(errorMsg);
            }else {
                logger.error("国际收款异常", e);
                returnCodeUtil.buildResponse(accountSplitResponse, e, TxsCode.FAIL.code);
            }
        }
        return accountSplitResponse;
    }

    /**
     * 账户分账退款接口
     * @param customerCodeHead
     * @return
     */
    @PostMapping("/accountPay/refund")
    @Logable(businessTag = "TransactionAccountPayController.refund")
    @ApiOperation(value = "账户分账退款接口", httpMethod = "POST")
    @ApiImplicitParams({})
    public AccountSplitRefundResponse refund(
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestBody @Valid AccountSplitRefundRequest request){
        AccountSplitRefundResponse accountSplitRefundResponse = new AccountSplitRefundResponse();
        try {
            accountSplitRefundResponse.setNonceStr(UUIDUtils.uuid());
            accountSplitRefundResponse.setCustomerCode(request.getCustomerCode());
            accountSplitRefundResponse.setOutRefundNo(request.getOutRefundNo());
            accountSplitRefundResponse.setOutTradeNo(request.getOutTradeNo());
            accountSplitRefundResponse.setTransactionNo(request.getTransactionNo());
            if(!customerCodeHead.equalsIgnoreCase(request.getCustomerCode())) {
                CustomerInfo customerInfo = cumCacheService.getCustomerInfo(request.getCustomerCode(),request.getCustomerCode(),"1");
                if(!customerCodeHead.equalsIgnoreCase(customerInfo.getPlatCustomerCode())){
                    accountSplitRefundResponse.setReturnCode(TxsCode.CUSTOMER_ATYPISM.getCode());
                    accountSplitRefundResponse.setReturnMsg(TxsCode.CUSTOMER_ATYPISM.getMessage());
                    return accountSplitRefundResponse;
                }
            }
            if (refundLimit && !refundCustomerCode.contains(customerCodeHead)) {
                CustomerBusinessInfo customerBusinessInfo = cumCacheService.queryBusinessInfoByBusinessCode(customerCodeHead, TxsConstants.EfpsAccountService.ACCOUNT_SPLIT.code);
                //判断是否允许退款
                if(customerBusinessInfo != null && TxsConstants.isAllowRefund.NO.code.equals(customerBusinessInfo.getCanRefund())){
                    accountSplitRefundResponse.setReturnCode(TxsCode.NOT_ALLOW_REFUND.getCode());
                    accountSplitRefundResponse.setReturnMsg(TxsCode.NOT_ALLOW_REFUND.getMessage());
                    return accountSplitRefundResponse;
                }
            }
            if (StringUtils.isBlank(request.getOutTradeNo()) && StringUtils.isBlank(request.getTransactionNo())) {
                accountSplitRefundResponse.setReturnCode(TxsCode.OUTTRADENO_TRANSACTIONNO_ISNULL.getCode());
                accountSplitRefundResponse.setReturnMsg(TxsCode.OUTTRADENO_TRANSACTIONNO_ISNULL.getMessage());
                return accountSplitRefundResponse;
            }
            return accountSplitService.refund(request, accountSplitRefundResponse);
        }catch (Exception e) {
            e.printStackTrace();
            logger.error("账户分账退款接口", e);
            transactionService.logException(e);
            returnCodeUtil.buildResponse(accountSplitRefundResponse, e, TxsCode.FAIL.code);
        }
        return accountSplitRefundResponse;
    }

    /**
     * 账户分账撤销接口
     * @param userType
     * @param userId
     * @param customerCodeHead
     * @param customerCode
     * @param outTradeNo
     * @param transactionNo
     * @return
     */
    @PostMapping("/accountPay/revoke")
    @Logable(businessTag = "TransactionAccountPayController.revoke")
    public CommonResponse revoke(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) Long userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "customerCode", required = false) String customerCode,
            @RequestParam(value = "outTradeNo", required = false)  String outTradeNo,
            @RequestParam(value = "transactionNo") String transactionNo){

        CommonResponse accountSplitRefundResponse = new CommonResponse();
        if (org.apache.commons.lang.StringUtils.isBlank(userType)) {
            throw new AppException(TxsCode.NOTSUPPORT_USERTYPE.code);
        }
        if (UserType.PPS_USER.code.equals(userType) && !org.apache.commons.lang.StringUtils.equalsIgnoreCase(customerCode, customerCodeHead)) {
            throw new AppException(TxsCode.CUSTOMER_ATYPISM.code);
        }
        try{
            return accountSplitService.revoke(customerCode,outTradeNo,transactionNo);
        }catch (Exception e){
            transactionService.logException(e);
            logger.error("revoke", e);
            returnCodeUtil.buildResponse(accountSplitRefundResponse, e, TxsCode.FAIL.code);
            return accountSplitRefundResponse;
        }
    }

    @PostMapping("reviewSplitState")
    @Logable(businessTag = "reviewSplitState")
    @Exceptionable
    @ApiOperation(value = "同步账户分账状态", notes = "同步账户分账状态", httpMethod = "POST")
    @Validatable
    public CommonOuterResponse<List<String>> reviewSplitState(@RequestParam(required = true) List<String> transactionNoList) {

        return accountSplitService.reviewSplitState(transactionNoList);

    }
}
