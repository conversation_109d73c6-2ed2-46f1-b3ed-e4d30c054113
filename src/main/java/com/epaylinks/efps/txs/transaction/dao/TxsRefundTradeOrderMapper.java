package com.epaylinks.efps.txs.transaction.dao;

import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.txs.transaction.model.TxsRefundTradeOrder;
import com.github.pagehelper.Page;
@Mapper
public interface TxsRefundTradeOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TxsRefundTradeOrder record);

    int insertSelective(TxsRefundTradeOrder record);

    TxsRefundTradeOrder selectByPrimaryKey(Long id);
    
    @Transactional(readOnly = true)
    List<TxsRefundTradeOrder> selectBySelective(Map map);

    /**
     * 分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    Page<TxsRefundTradeOrder> selectByPage(Map map);

    /**
     * 不分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    Page<TxsRefundTradeOrder> selectByNotPage(Map map);

    int updateByPrimaryKeySelective(TxsRefundTradeOrder record);

    int updateByPrimaryKey(TxsRefundTradeOrder record);
    
    List<TxsRefundTradeOrder> selectByOutRefundNo(String outRefundNo);
    
    List<TxsRefundTradeOrder> selectByOutTradeNoAndPayState(@Param("outTradeNo")String outTradeNo, @Param("payState")String payState);

	int selectCount(Map map);
	
	List<TxsRefundTradeOrder> selectByBusinessInstId(@Param("businessInstId") String businessInstId);

    List<TxsRefundTradeOrder> queryByOutRefundNoList(@Param("outRefundNoList") List<String> outRefundNoList);
    
    TxsRefundTradeOrder refundQueryByCustomerAndOutRefundNo(@Param("outRefundNo")String outRefundNo, @Param("customerCode")String customerCode);
}