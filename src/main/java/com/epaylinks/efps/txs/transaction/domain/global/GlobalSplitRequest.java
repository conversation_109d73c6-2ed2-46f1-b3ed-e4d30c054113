package com.epaylinks.efps.txs.transaction.domain.global;

import com.epaylinks.efps.txs.transaction.domain.split.CommodityInfo;
import com.epaylinks.efps.txs.transaction.domain.split.SplitInfo;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

public class GlobalSplitRequest {
    /**
     * 版本号
     */
    private String version;

    /**
     * 商户订单号
     */
    @NotBlank(message="商户订单号不能为空")
    @Size(min = 1, max = 32, message = "订单号长度要求1到32之间。")
    private String outTradeNo;

    /**
     * 商户号
     */
    @NotBlank(message="商户号不能为空")
    private String customerCode;

    /**
     * 收款金额
     * 单位分，必需大于0
     */
    @NotNull(message = "收款金额不能为空")
    @Min(value = 0,message = "收款金额必需大于0")
    private Long commodityAmount;

    /**
     * 手续费扣除方式0-付款方扣，1-收款方扣
     */
    private String procedureType;

    /**
     * 商品信息列表
     */
    private List<CommodityInfo> commodityInfoList;


    /**
     * 收款结果通知地址
     */
    @NotBlank(message="收款结果通知地址不能为空")
    private String notifyUrl;

    /**
     * 附加数据
     */
    private String attachData;


    /**
     * 业务类型
     */
    @NotBlank(message="业务类型不能为空")
    private String businessType;

    /**
     * 跨境平台商户号
     */
    private String crossPlatCustomerCode;
    /**
     * 收款方
     */
    private String receiveCustomerCode;

    /**
     * 随机字符串
     */
    @NotBlank(message="随机字符串不能为空")
    private String nonceStr;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getCommodityAmount() {
        return commodityAmount;
    }

    public void setCommodityAmount(Long commodityAmount) {
        this.commodityAmount = commodityAmount;
    }

    public List<CommodityInfo> getCommodityInfoList() {
        return commodityInfoList;
    }

    public void setCommodityInfoList(List<CommodityInfo> commodityInfoList) {
        this.commodityInfoList = commodityInfoList;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getAttachData() {
        return attachData;
    }

    public void setAttachData(String attachData) {
        this.attachData = attachData;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getReceiveCustomerCode() {
        return receiveCustomerCode;
    }

    public void setReceiveCustomerCode(String receiveCustomerCode) {
        this.receiveCustomerCode = receiveCustomerCode;
    }

    public String getCrossPlatCustomerCode() {
        return crossPlatCustomerCode;
    }

    public void setCrossPlatCustomerCode(String crossPlatCustomerCode) {
        this.crossPlatCustomerCode = crossPlatCustomerCode;
    }

    public String getProcedureType() {
        return procedureType;
    }

    public void setProcedureType(String procedureType) {
        this.procedureType = procedureType;
    }
}
