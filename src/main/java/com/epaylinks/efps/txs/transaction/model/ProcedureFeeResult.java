package com.epaylinks.efps.txs.transaction.model;
/**
 * 计算手续费返回的结果
 * <AUTHOR>
 *
 */

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.cum.customerBusiness.CustomerBusinessInstance;

public class ProcedureFeeResult {
	Long procedureFee;
	CustomerBusinessInstance businessInst;//所使用的业务实例
	
	//1表示会判断结果为要收取节假日上浮手续费.但实际有无收取,要看业务有无设置这个值。
	private String chargedD1HolidayProcedure;
	
	//1表它是按银行计价且是按区域性银行计价。
	private String chargedByBankCodeArea;
	public Long getProcedureFee() {
		return procedureFee;
	}
	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}
	public CustomerBusinessInstance getBusinessInst() {
		return businessInst;
	}
	public void setBusinessInst(CustomerBusinessInstance businessInst) {
		this.businessInst = businessInst;
	}
	public String getChargedD1HolidayProcedure() {
		return chargedD1HolidayProcedure;
	}
	public void setChargedD1HolidayProcedure(String chargedD1HolidayProcedure) {
		this.chargedD1HolidayProcedure = chargedD1HolidayProcedure;
	}
	public String getChargedByBankCodeArea() {
		return chargedByBankCodeArea;
	}
	public void setChargedByBankCodeArea(String chargedByBankCodeArea) {
		this.chargedByBankCodeArea = chargedByBankCodeArea;
	}
	@Override
	public String toString()
	{
		return JSON.toJSONString(this);
	}
}
