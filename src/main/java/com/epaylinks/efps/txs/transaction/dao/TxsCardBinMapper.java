package com.epaylinks.efps.txs.transaction.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.common.business.txs.TxsCardBin;


@Mapper
public interface TxsCardBinMapper {
    int insert(TxsCardBin record);

    int insertSelective(TxsCardBin record);
    
    TxsCardBin selectByCardNoRange(@Param("cardNoRange")String cardNoRange,@Param("cardNoLen") int cardNoLen);
    
    List<TxsCardBin> selectByBankIconList(@Param("bankIconList")List<String> bankIconList);
    
    List<TxsCardBin> selectByBankIcon(@Param("bankIcon")String bankIcon);
    
    List<TxsCardBin> selectAll();
}