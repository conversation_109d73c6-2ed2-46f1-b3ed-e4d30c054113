package com.epaylinks.efps.txs.transaction.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.txs.transaction.model.CumBusiness;

@Mapper
public interface CumBusinessMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CumBusiness record);

    int insertSelective(CumBusiness record);

    CumBusiness selectByPrimaryKey(Short id);

    int updateByPrimaryKeySelective(CumBusiness record);

    int updateByPrimaryKey(CumBusiness record);
    
	CumBusiness selectByBusinessCode(@Param("businessCode")String businessCode);

 
}
