package com.epaylinks.efps.txs.transaction.domain.split;

public class SplitRecord {
    private String customerCode;
    //分账金额
    private Long amount;
    //分账对象属性(0：分账主体商户 1：常规分账对象 2：收单手续费扣除对象 3：分账手续费扣除对象 4：收单及分账手续费扣除对象)
    private String splitAttr;
    //结算周期
    private String settleCycle;
    //结算状态
    private String settleState;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getSplitAttr() {
        return splitAttr;
    }

    public void setSplitAttr(String splitAttr) {
        this.splitAttr = splitAttr;
    }

    public String getSettleCycle() {
        return settleCycle;
    }

    public void setSettleCycle(String settleCycle) {
        this.settleCycle = settleCycle;
    }

    public String getSettleState() {
        return settleState;
    }

    public void setSettleState(String settleState) {
        this.settleState = settleState;
    }
}
