package com.epaylinks.efps.txs.transaction.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.txs.transaction.service.AccountSplitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "ManualController", description = "手工操作控制器")
@RequestMapping("/manual")
public class ManualController {

    @Autowired
    private AccountSplitService accountSplitService;

    @Logable(businessTag = "syncFenBuFangState")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "transactionNo", value = "订单号", required = true, dataType = "String", paramType = "query")})
    @ApiOperation(value = "对于账户分账的预分账,分账确认模式的.按订单号检查记账侧是否已经记账成功.若已成功,则将txsSplitOrder,txsSplitRecord置成功", httpMethod = "POST")
    @Exceptionable
    @PostMapping("/syncFenBuFangState")
    public CommonOuterResponse syncFenBuFangState(@RequestParam(required = false)String transactionNo) {
        return accountSplitService.syncFenBuFangState(transactionNo);
    }
}
