package com.epaylinks.efps.txs.transaction.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceCategory;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.txs.constants.TxsCode;
import com.epaylinks.efps.txs.service.CumService;
import com.epaylinks.efps.txs.transaction.dao.TxsSplitRelationMapper;
import com.epaylinks.efps.txs.transaction.domain.split.SplitInfo;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelation;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationAddRequest;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationResponse;
import com.epaylinks.efps.txs.transaction.domain.split.SplitRelationUpdateRequest;
import com.epaylinks.efps.txs.transaction.model.TxsSplitRelation;
import com.epaylinks.efps.txs.util.ReflectUtils;

/**
 * 分账关系处理
 * 
 * <AUTHOR>
 *
 */

@Service
public class SplitRelationService {

	@Value("${FZTransaction}")
	private String FZTransaction;
	@Value("${FZJYTransaction}")
	private String FZJYTransaction;
	@Value("${fz.systemId}")
	private String systemId;

	@Autowired
	private SplitRelationService self;
	
	
	@Autowired
	private TxsSplitRelationMapper txsSplitRelationMapper;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private CumService cumService;
	@Autowired
	private SplitPayService splitPayService;

	/**
	 * 分账服务
	 * 
	 * @param belongCustomerCode
	 * @param txsSplitRelations
	 * @return
	 */
	public SplitRelationResponse add(SplitRelationAddRequest splitRelationAddRequest) {
		SplitRelationResponse response = new SplitRelationResponse(splitRelationAddRequest.getOutTradeNo(), splitRelationAddRequest.getBelongCustomerCode());
		
		String belongCustomerCode = splitRelationAddRequest.getBelongCustomerCode();
		List<SplitRelation> splitRelationList = splitRelationAddRequest.getSplitRelationList();
		
		List<TxsSplitRelation> txsSplitRelations = new ArrayList<>();
		List<SplitInfo> splitInfoList = new ArrayList<>();
		
		String splitRelationId = createRelationId();
		Long checkAmount = 1L;
		int ratioSum = 0;
		for(SplitRelation item: splitRelationList) {
			TxsSplitRelation txsSplitRelation = new TxsSplitRelation();
			//客户传递数据，复制到存储db的bean
			ReflectUtils.copyPropertiesIgnoreNull(txsSplitRelation, item);
			Long id = sequenceService.nextValue(SequenceCategory.splitRelation.name);
			txsSplitRelation.setId(id);
			txsSplitRelation.setCreateTime(new Date());
			txsSplitRelation.setSplitRelationId(splitRelationId);
			txsSplitRelation.setBelongCustomerCode(belongCustomerCode);
			
			txsSplitRelations.add(txsSplitRelation);
			
			ratioSum += txsSplitRelation.getRatio();
			
			SplitInfo splitInfo = new SplitInfo();
			splitInfo.setAmount(checkAmount);//只是共用下校验方法，金额随便填一个能通过的就行
			splitInfo.setCustomerCode(txsSplitRelation.getCustomerCode());
			splitInfo.setIsProcedureCustomer(txsSplitRelation.getIsProcedureCustomer());
			
			splitInfoList.add(splitInfo);
		}
		if(ratioSum != 10000) {
			response.setReturnCode(TxsCode.SPLIT_RATIO_ERROR.code);
			response.setReturnMsg(TxsCode.SPLIT_RATIO_ERROR.message);
			return response;
		}
		
		splitPayService.checkSplitInfoParamIsNotNull(splitInfoList, null);
		CommonOuterResponse checkResponse = splitPayService.checkSplitDetail(splitInfoList, belongCustomerCode, belongCustomerCode, checkAmount, null,null);
		if(!checkResponse.isSuccess()) {
			response.setReturnCode(checkResponse.getReturnCode());
			response.setReturnMsg(checkResponse.getReturnMsg());
			
			return response;
		}
		splitPayService.checkCustomersBusiness(belongCustomerCode,splitInfoList);
		//校验通过后，再插入表记录
		insertSplitRelation(txsSplitRelations);
		
		response.setSplitRelationId(splitRelationId);
		return response;
		
	}
	/**
	 * 批量插入分账关系数据
	 * @param txsSplitRelations
	 */
	@Transactional
	public void insertSplitRelation(List<TxsSplitRelation> txsSplitRelations) {
		for(TxsSplitRelation txsSplitRelation: txsSplitRelations) {
			txsSplitRelationMapper.insertSelective(txsSplitRelation);
		}
	}
	/**
	 * 修改分账关系
	 * 
	 * @param belongCustomerCode
	 * @param txsSplitRelations
	 * @return
	 */
	public SplitRelationResponse update(SplitRelationUpdateRequest splitRelationUpdateRequest) {
		SplitRelationResponse response = new SplitRelationResponse(splitRelationUpdateRequest.getOutTradeNo(), splitRelationUpdateRequest.getBelongCustomerCode());
		
		String belongCustomerCode = splitRelationUpdateRequest.getBelongCustomerCode();
		List<SplitRelation> splitRelationList = splitRelationUpdateRequest.getSplitRelationList();
		
		List<TxsSplitRelation> txsSplitRelations = new ArrayList<>();
		
		List<SplitInfo> splitInfoList = new ArrayList<>();
		
		String splitRelationId = splitRelationUpdateRequest.getSplitRelationId();
		Long checkAmount = 1L;
		int ratioSum = 0;
		Date now = new Date();
		
		for(SplitRelation item : splitRelationList) {
			TxsSplitRelation txsSplitRelation = new TxsSplitRelation();
			//客户传递数据，复制到存储db的bean
			ReflectUtils.copyPropertiesIgnoreNull(txsSplitRelation, item);
			
			Long id = sequenceService.nextValue(SequenceCategory.splitRelation.name);
			txsSplitRelation.setId(id);
			txsSplitRelation.setSplitRelationId(splitRelationId);
			txsSplitRelation.setBelongCustomerCode(belongCustomerCode);
			txsSplitRelation.setCreateTime(now);
			txsSplitRelation.setUpdateTime(now);
			
			txsSplitRelations.add(txsSplitRelation);
			
			ratioSum += txsSplitRelation.getRatio();
			
			if(StringUtils.isBlank(txsSplitRelation.getSplitRelationId())) {
				throw new AppException(TxsCode.SPLIT_RELATION_ID_EMPTY.code);
			}
			
			SplitInfo splitInfo = new SplitInfo();
			splitInfo.setAmount(checkAmount);//只是共用下校验方法，金额随便填一个能通过的就行
			splitInfo.setCustomerCode(txsSplitRelation.getCustomerCode());
			splitInfo.setIsProcedureCustomer(txsSplitRelation.getIsProcedureCustomer());
			
			splitInfoList.add(splitInfo);
		}
		
		List<TxsSplitRelation> txsSplitRelationList = txsSplitRelationMapper.selectByRelationId(splitRelationId);
		if(txsSplitRelationList == null) {
			throw new AppException(TxsCode.SPLIT_RELATION_NOT_EXIST.code);
		}
		//同一个关系号只会属于一个商户，故取一条记录判断即可
		String systemBelongCustomerCode = txsSplitRelationList.get(0).getBelongCustomerCode();
		
		if(!systemBelongCustomerCode.equals(belongCustomerCode)) {
			//关系编号只有所属商户才能删除
			throw new AppException(TxsCode.SPLIT_RELATION_BELONG_ERROR.code);
		}
		
		if(ratioSum != 10000) {
			response.setReturnCode(TxsCode.SPLIT_RATIO_ERROR.code);
			response.setReturnMsg(TxsCode.SPLIT_RATIO_ERROR.message);
		}
		splitPayService.checkSplitInfoParamIsNotNull(splitInfoList, null);
		CommonOuterResponse checkResponse = splitPayService.checkSplitDetail(splitInfoList, belongCustomerCode, belongCustomerCode, checkAmount, null,null);
		if(!checkResponse.isSuccess()) {
			response.setReturnCode(checkResponse.getReturnCode());
			response.setReturnMsg(checkResponse.getReturnMsg());
			
			return response;
		}
		splitPayService.checkCustomersBusiness(belongCustomerCode,splitInfoList);
		//校验通过后，再插入表记录
		updateSplitRelation(splitRelationId, systemBelongCustomerCode, txsSplitRelations);
		
		response.setSplitRelationId(splitRelationId);
		return response;
		
	}
	/**
	 * 批量修改分账关系数据
	 * @param txsSplitRelations
	 */
	@Transactional
	public void updateSplitRelation(String splitRelationId, String belongCustomerCode, List<TxsSplitRelation> txsSplitRelations) {
		//先删除，再插入
		txsSplitRelationMapper.deleteByRelationId(splitRelationId, belongCustomerCode);
		for(TxsSplitRelation txsSplitRelation: txsSplitRelations) {
			txsSplitRelationMapper.insertSelective(txsSplitRelation);
		}
	}
	
	public List<TxsSplitRelation> query(String customerCode, String splitRelationId) {
		List<TxsSplitRelation> splitRelationList = txsSplitRelationMapper.selectByRelationId(splitRelationId);
		if(splitRelationList == null || splitRelationList.size() == 0) {
			throw new AppException(TxsCode.SPLIT_RELATION_NOT_EXIST.code);
		}
		//同一个关系号只会属于一个商户，故取一条记录判断即可
		String belongCustomerCode = splitRelationList.get(0).getBelongCustomerCode();
		boolean parentCheck = cumService.checkIsParentCustomerCode(customerCode, belongCustomerCode);
		
		if(!customerCode.equals(belongCustomerCode) && !parentCheck) {
			//关系编号所属商户既不是商户本身，也不是商户父商户
			throw new AppException(TxsCode.SPLIT_RELATION_BELONG_ERROR.code);
		}
		return splitRelationList;
	}
	
	private String createRelationId() {
		Date now = new Date();
		int length = 3;//seqnce和随机数长度
		String sequence = com.epaylinks.efps.common.util.StringUtils.formatStr(
				sequenceService.nextValue(SequenceCategory.protocol.name) + "", length, '0');//数据库seqence序列
		String dateString = DateFormatUtils.format(now, "yyMMdd");//日期
		String timeString = DateFormatUtils.format(now, "HHmmssSSS");//时间
		String timeHash = com.epaylinks.efps.common.util.StringUtils.formatStr(
				Math.abs(timeString.hashCode()) + "", 7, '0');//根据当前时间生成的随机数，Math.abs(hash(hhmmssSSS))，长7位，不足左边边补0，超出取右边7位
		String random = "" + (new Random().nextInt(900) + 100);//随机数
		
		StringBuffer randomAndSequence = new StringBuffer();
		for(int i = 0; i < length; i++) {
			randomAndSequence.append(random.charAt(i)).append(sequence.charAt(i));//随机数和sequnce序列交叉处理
		}
		
		StringBuffer tempNo = new StringBuffer("");//初始化前缀
		tempNo.append(dateString).append(timeHash).append(randomAndSequence);
//		System.out.println(dateString + "-" + timeString + "-" + sequence  + "-" + random  + "----" + tempTransactionNo.toString());
		return tempNo.toString();
	}

	
}
