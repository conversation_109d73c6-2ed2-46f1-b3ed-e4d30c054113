package com.epaylinks.efps.txs.transaction.dao;

import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TxsRefundSplitRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(TxsRefundSplitRecord record);

    TxsRefundSplitRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TxsRefundSplitRecord record);

    List<TxsRefundSplitRecord> selectByBusinessInstId(@Param("businessInstId") String businessInstId);

    List<TxsRefundSplitRecord> queryRefundSplitRecordByOutRefundNo(@Param("outRefundNo") String outRefundNo);

	List<TxsRefundSplitRecord> selectByTransactionNo(@Param("transactionNo")String transactionNo);
	
	List<TxsRefundSplitRecord> selectByTransactionNoCustomerCodeAmt(@Param("transactionNo")String transactionNo, 
			@Param("customerCode")String customerCode, 
			@Param("apiAmount")Long apiAmount);
	
}