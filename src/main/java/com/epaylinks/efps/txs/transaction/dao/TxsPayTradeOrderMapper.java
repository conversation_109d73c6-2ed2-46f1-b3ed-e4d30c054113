package com.epaylinks.efps.txs.transaction.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.txs.transaction.model.TxsPayTradeOrder;
import com.github.pagehelper.Page;

@Mapper
@Transactional
public interface TxsPayTradeOrderMapper {
	int deleteByPrimaryKey(Long id);

    int insert(TxsPayTradeOrder record);

    int insertSelective(TxsPayTradeOrder record);

    @Transactional(readOnly = true)
    TxsPayTradeOrder selectByPrimaryKey(Long id);

    @Transactional(readOnly = true)
    List<TxsPayTradeOrder> selectByMap(Map<String, Object> map);
    
    @Transactional(readOnly = true)
    List<TxsPayTradeOrder> selectBySelective(Map map);
    
    /**
     * 分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsPayTradeOrder> selectByPage(Map map);
     
    /**
     * 不分页结果
     * @param map
     * @return
     */
    @Transactional(readOnly = true)
    List<TxsPayTradeOrder> selectByNotPage(Map map);
    
    int updateByPrimaryKeySelective(TxsPayTradeOrder record);
    
    List<TxsPayTradeOrder> selectByOutTradeNo(String outTradeNo);
    
    TxsPayTradeOrder selectByTransactionNo(@Param("transactionNo")String transactionNo);
   
    TxsPayTradeOrder selectByTransactionNoAndCustomerCode(@Param("transactionNo")String transactionNo, @Param("customerCode")String customerCode);
    /**
     * 在周期内查询所有不重复客户编码
     * @param map
     * @return
     */
    List<String> selectCustomerCode(Map map);
    /**
     * 在周期内查询所有支付订单
     * @param map
     * @return
     */
    List<TxsPayTradeOrder> selectTxsTradePayOrderByCycleTime(Map map);
    
    List<String> selectPayMethod(Map map);
    
    List<TxsPayTradeOrder> selectTxsTradePayOrder(Map map);
    
    List<TxsPayTradeOrder> selectByOutTradeNoToResultQuery(@Param("outTradeNo")String outTradeNo, @Param("state") String state, @Param("customerCode")String customerCode);
    
    List<TxsPayTradeOrder> selectByOutTradeAndCustCode(@Param("outTradeNo")String outTradeNo, @Param("customerCode")String customerCode);

    /**
     * 以入参cond中所有非空字段作为条件查询
     * @param cond
     * @return
     */
	List<TxsPayTradeOrder> selectByCondition(TxsPayTradeOrder cond);
	
	List<TxsPayTradeOrder> selectByOrderId(@Param("orderId") Long orderId);
	
	/**
	 * 判断是否重复更新
	 * @param record
	 * @return
	 */
	int updateByPrimaryKeyForRepeatUpdate(TxsPayTradeOrder txsPayTradeOrder);
	/**
	 * 
	 * 批量查询交易订单号
	 * @param transactionNos
	 * @return
	 */
	List<TxsPayTradeOrder> selectByTransactionNos(@Param("transactionNos")List<String> transactionNos);

    List<TxsPayTradeOrder> selectByOutTradeNoAndCustomerCode(@Param("outTradeNo") String outTradeNo,@Param("customerCode") String customerCode,@Param("state")String state);

    List<TxsPayTradeOrder> selectAuditList(@Param("startTime") Date startTime,@Param("endTime")Date endTime);
    
    int updateByPrimaryKeyForCancelInfo(TxsPayTradeOrder txsPayTradeOrder);
    
    int selectByChannelOrder(@Param("channelOrder")String channelOrder);
    
    List<String> selectNfcCustomerCode(Map map);
    
    Map<String, Object>selectNfcStaticsCode(Map map);
}