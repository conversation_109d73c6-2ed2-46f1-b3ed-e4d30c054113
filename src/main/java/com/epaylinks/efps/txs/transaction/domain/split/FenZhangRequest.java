package com.epaylinks.efps.txs.transaction.domain.split;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
/**
 * 分账请求
 * <AUTHOR>
 *
 * @date 2018年1月24日 上午10:23:16
 */
public class FenZhangRequest {
	private String outTradeNo;
	private String customerCode;

	/**
	 * 商户分账单号	String(32)	C	拆单分账时必须
	 */
	private String outSplitTradeNo;
	/**
	 * splitInfoList中每个元素为一个分账信息，其字段如下，
	 * 非拆单分账需保证所有amount之和等于原交易订单的支付金额，
	 * 拆单分账单次分账金额总和小于或等于原交易订单出资商出资金额。
	 * 所有amount之和小于或等于原交易订单支付金额，所有拆单分账的所有amount之和等于原交易订单支付金额。支持多次拆分，
	 * 拆单有效期30天，从支付时间开始算到30天订单未拆分完成，系统自动拆单，分账给交易商户本身，手续费由交易商户承担。
	 */
	private List<SplitInfo> splitInfoList;
	private String notifyUrl;
	private String attachData;
	private String nonceStr;
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public List<SplitInfo> getSplitInfoList() {
		return splitInfoList;
	}
	public void setSplitInfoList(List<SplitInfo> splitInfoList) {
		this.splitInfoList = splitInfoList;
	}
	public String getNotifyUrl() {
		return notifyUrl;
	}
	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}
	public String getAttachData() {
		return attachData;
	}
	public void setAttachData(String attachData) {
		this.attachData = attachData;
	}
	public String getNonceStr() {
		return nonceStr;
	}
	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}

	public String getOutSplitTradeNo() {
		return outSplitTradeNo;
	}

	public void setOutSplitTradeNo(String outSplitTradeNo) {
		this.outSplitTradeNo = outSplitTradeNo;
	}
}
