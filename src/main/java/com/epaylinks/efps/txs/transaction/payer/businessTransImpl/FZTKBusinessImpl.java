package com.epaylinks.efps.txs.transaction.payer.businessTransImpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.txs.transaction.dao.TxsRefundSplitOrderMapper;
import com.epaylinks.efps.txs.transaction.model.TxsRefundSplitOrder;
import com.epaylinks.efps.txs.transaction.payer.BusinessTransactionCheck;

@Service("FZTKBusiness")
public class FZTKBusinessImpl implements BusinessTransactionCheck {
	@Autowired
	private TxsRefundSplitOrderMapper txsRefundSplitOrderMapper;
	@Override
	public boolean checkBusinessTransaction(String businessExamId) {
		List<TxsRefundSplitOrder> txsRefundSplitOrders = txsRefundSplitOrderMapper.selectByBusinessInstId(businessExamId);
		if (txsRefundSplitOrders != null && !txsRefundSplitOrders.isEmpty()) {
			return true;
		}
		return false;
	}

}
