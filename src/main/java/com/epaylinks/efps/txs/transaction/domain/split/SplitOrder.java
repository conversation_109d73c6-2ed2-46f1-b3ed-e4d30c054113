package com.epaylinks.efps.txs.transaction.domain.split;

import java.util.List;

public class SplitOrder {
    //商户分账单号
    private String outSplitTradeNo;
    //分账订单号
    private String splitTransactionNo;
    //本次分账金额
    private Long amount;
    //分账详情
    private List<SplitRecord> splitResultInfoList;
    //分账结果
    private String splitState;
    //分账完成时间 YYYYMMDDHHMMSS
    private String splitTime;

    public String getOutSplitTradeNo() {
        return outSplitTradeNo;
    }

    public void setOutSplitTradeNo(String outSplitTradeNo) {
        this.outSplitTradeNo = outSplitTradeNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public List<SplitRecord> getSplitResultInfoList() {
        return splitResultInfoList;
    }

    public void setSplitResultInfoList(List<SplitRecord> splitResultInfoList) {
        this.splitResultInfoList = splitResultInfoList;
    }

    public String getSplitState() {
        return splitState;
    }

    public void setSplitState(String splitState) {
        this.splitState = splitState;
    }

    public String getSplitTime() {
        return splitTime;
    }

    public void setSplitTime(String splitTime) {
        this.splitTime = splitTime;
    }

    public String getSplitTransactionNo() {
        return splitTransactionNo;
    }

    public void setSplitTransactionNo(String splitTransactionNo) {
        this.splitTransactionNo = splitTransactionNo;
    }
}
