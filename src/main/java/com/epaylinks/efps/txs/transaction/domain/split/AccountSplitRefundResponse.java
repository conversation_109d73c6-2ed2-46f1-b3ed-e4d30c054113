package com.epaylinks.efps.txs.transaction.domain.split;

import com.epaylinks.efps.common.business.CommonOuterResponse;

public class AccountSplitRefundResponse extends CommonOuterResponse {

    private String customerCode;


    /**
     * 商户退款号
     */
    private String outRefundNo;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 易票联订单号
     */
    private String transactionNo;

    /**
     * 随机字符串
     */
    private String nonceStr;

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    @Override
    public String getNonceStr() {
        return nonceStr;
    }

    @Override
    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }
}
