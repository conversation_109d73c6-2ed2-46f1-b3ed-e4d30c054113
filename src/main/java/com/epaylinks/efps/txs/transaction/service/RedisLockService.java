package com.epaylinks.efps.txs.transaction.service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.myredis.MyRedisBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class RedisLockService {

    @Autowired
    private MyRedisBusinessService myRedisBusinessService;

    /**
     * 配置在common
     */
    @Value("${eplRedisMutex.ReturnAndSplit.KeyPre}")
    private String mutexReturnSplitPre;

    /**
     * 超时时间,单位为秒
     */
    @Value("${eplRedisMutex.ReturnAndSplit.TimeOutSecond}")
    private Long returnAndSplitTimeOutSecond;

    /**
     * 设置退货/分账互拆. 在分账订单的退货前,分账前设置
     * @param payTransactionNo  交易订单号
     * @return 是否设置成功. true表示成功,获得了锁
     */
    @Logable(businessTag = "mutex")
    public boolean setReturnSplitMutex(String payTransactionNo) {
        String actualKey = mutexReturnSplitPre + payTransactionNo;
        return myRedisBusinessService.setIfAbsent(actualKey, new Date(), returnAndSplitTimeOutSecond, TimeUnit.SECONDS);
    }

    /**
     * 解除退货/分账互拆. 在分账订单的退货前,分账后合适位置设置
     * @param payTransactionNo 交易订单号
     */
    @Logable(businessTag = "delMutex")
    public void deleteReturnSplitMutex(String payTransactionNo){
        String actualKey = mutexReturnSplitPre + payTransactionNo;
        myRedisBusinessService.delete(actualKey);
        return;
    }
}