package com.epaylinks.efps.txs.transaction.domain.split;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

public class SplitRelationUpdateRequest {
	/**
     * 分账关系序列号
     */
	@NotBlank(message="分账关系序列号不能为空")
    private String splitRelationId;
    /**
     * 所属商户号
     */
	@NotBlank(message="所属商户号不能为空")
    private String belongCustomerCode;
    /**
     * 分账关系数据
     */
	@NotNull(message="分账关系数据不能为空")
    private List<SplitRelation> splitRelationList;
	/**
	 * 请求流水号
	 */
	@NotBlank(message="请求流水号不能为空")
	private String outTradeNo;
    
	public String getSplitRelationId() {
		return splitRelationId;
	}
	public void setSplitRelationId(String splitRelationId) {
		this.splitRelationId = splitRelationId;
	}
	public String getBelongCustomerCode() {
		return belongCustomerCode;
	}
	public void setBelongCustomerCode(String belongCustomerCode) {
		this.belongCustomerCode = belongCustomerCode;
	}
	public List<SplitRelation> getSplitRelationList() {
		return splitRelationList;
	}
	public void setSplitRelationList(List<SplitRelation> splitRelationList) {
		this.splitRelationList = splitRelationList;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	
}
