package com.epaylinks.efps.txs.transaction.domain.split;

public class CommodityInfo {

    /**
     * 商品订单号
     */
    private String commodityOrderNo;
    /**
     * 商品订单金额
     */
    private Long commodityOrderAmount;
    /**
     * 商品订单时间
     */
    private String commodityOrderTime;
    /**
     * 商品描述
     */
    private String commodityDescription;

    /**
     * 订单类型. 不传或者0, 线上交易; 1-线下, posp那边
     * 如果是线下，要检查是否实名
     */
    private String commodityOrderType;

    public String getCommodityOrderNo() {
        return commodityOrderNo;
    }

    public void setCommodityOrderNo(String commodityOrderNo) {
        this.commodityOrderNo = commodityOrderNo;
    }

    public Long getCommodityOrderAmount() {
        return commodityOrderAmount;
    }

    public void setCommodityOrderAmount(Long commodityOrderAmount) {
        this.commodityOrderAmount = commodityOrderAmount;
    }

    public String getCommodityOrderTime() {
        return commodityOrderTime;
    }

    public void setCommodityOrderTime(String commodityOrderTime) {
        this.commodityOrderTime = commodityOrderTime;
    }

    public String getCommodityDescription() {
        return commodityDescription;
    }

    public void setCommodityDescription(String commodityDescription) {
        this.commodityDescription = commodityDescription;
    }

    public String getCommodityOrderType() {
        return commodityOrderType;
    }

    public void setCommodityOrderType(String commodityOrderType) {
        this.commodityOrderType = commodityOrderType;
    }
}
