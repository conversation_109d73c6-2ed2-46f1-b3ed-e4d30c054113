package com.epaylinks.efps.txs.transaction.domain.split;

import org.hibernate.validator.constraints.NotBlank;

public class SplitRelationQueryRequest {
	/**
     * 分账关系序列号
     */
	@NotBlank(message="分账关系序列号不能为空")
    private String splitRelationId;
    /**
     * 所属商户号
     */
	@NotBlank(message="所属商户号不能为空")
    private String belongCustomerCode;
    
	public String getSplitRelationId() {
		return splitRelationId;
	}
	public void setSplitRelationId(String splitRelationId) {
		this.splitRelationId = splitRelationId;
	}
	public String getBelongCustomerCode() {
		return belongCustomerCode;
	}
	public void setBelongCustomerCode(String belongCustomerCode) {
		this.belongCustomerCode = belongCustomerCode;
	}
}
