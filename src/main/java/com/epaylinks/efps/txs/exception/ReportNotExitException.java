package com.epaylinks.efps.txs.exception;

import com.epaylinks.efps.common.exception.AppException;
/**
 * 报表记录不存在异常
 * <AUTHOR>
 *
 */
public class ReportNotExitException extends AppException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	
	public ReportNotExitException(String errorCode) {
		super(errorCode);
	}
	
	
	public ReportNotExitException(String errorCode , Throwable cause) {
		super(errorCode , cause);
	}
	
}
