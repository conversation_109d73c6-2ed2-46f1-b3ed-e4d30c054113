package com.epaylinks.efps;

/**
 * 描述 ：
 *
 * <AUTHOR>
 * @date 2018/1/30.
 */

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * 描述 ：自定义拦截器，获取请求参数中的transactionNo
 *
 * <AUTHOR>
 * @date 2018/1/30.
 */
public class TransactionNoFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filter<PERSON>hain) throws ServletException, IOException {
        if (!(request instanceof CustomServletRequestWrapper)) {
            request = new CustomServletRequestWrapper(request);
        }
        String transactionNo = request.getParameter("transactionNo");
        if (StringUtils.isNotBlank(transactionNo)) {
            request.setAttribute("transactionNo", transactionNo);
        } else {
            try {
                String inputStr = IOUtils.toString(new InputStreamReader(request.getInputStream(), "UTF-8"));
                transactionNo = getValue(inputStr, "transactionNo");
                if (StringUtils.isNotBlank(transactionNo)) {
                    request.setAttribute("transactionNo", transactionNo);
                }
            } catch (Exception e) {

            }
        }
        filterChain.doFilter(request, response);
    }

    /**
     * 获取字段对应的值
     *
     * @param str
     * @param key
     * @return
     */
    private String getValue(String str, String key) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(key)) {
            return null;
        }
        if (!StringUtils.contains(str, key)) {
            return null;
        }
        String subString = StringUtils.substringAfter(str, key);
        if (StringUtils.isBlank(subString)) {
            return null;
        }
        String sub = StringUtils.substringAfter(subString, "\"").trim();
        if (StringUtils.isBlank(sub)) {
            return null;
        }
        sub = StringUtils.substringAfter(subString, ":").trim();
        if (StringUtils.isBlank(sub)) {
            return sub;
        }
        String[] strings = StringUtils.substringsBetween(sub, "\"", "\"");
        if (strings == null || strings.length == 0) {
            return null;
        }
        return strings[0];
    }
}